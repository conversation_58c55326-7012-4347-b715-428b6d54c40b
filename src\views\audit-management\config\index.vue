<template>
  <div class="scroll-y with-flex">
    <QueryForm
      :form-list="formList"
      :loading="loading"
      @search="handleSearch"
      @reset="reset"
    />

    <div v-loading="loading" class="sticky-table">
      <CustomTable
        ref="tableRef"
        :columns="columns"
        :operate-list="operateList"
        :data="tableData"
        :table-method="getList"
        :index-method="indexMethod"
      />
    </div>
  </div>
</template>

<script setup lang="tsx">
import { ref, reactive, toRef } from 'vue'
import { ElMessage, ElButton, ElMessageBox } from 'element-plus'
import { useRoute, useRouter } from 'vue-router'
import { storeToRefs } from 'pinia'
import { useParamsStore } from '@/store'
import useLoading from '@/hooks/use-loading'
import useTryCatch from '@/hooks/use-try-catch'
import QueryForm from '@/components/QueryForm/index.vue'
import CustomTable from '@/components/CustomTable/index.vue'
import { getConfigList, enableConfig, disableConfig } from '@/api/audit-management'
import type { searchParams, listItem } from '@/api/audit-management'
import Listener from '@/utils/site-channel-listeners'

defineOptions({ name: 'ReviewConfig' })

const route = useRoute()
const router = useRouter()
const { userList, site_id_all, channel_item_all } = storeToRefs(useParamsStore())
const { loading, setLoading } = useLoading()

const queryParams_raw = {
  type: '',
  name: '',
  start_time: '',
  end_time: '',
  user_name: '',
  status: '',
}
const typeList = [
  {
    label: '发布系统',
    value: 1
  },
  {
    label: '对外协助',
    value: 2
  },
]
const statusList = [
  {
    label: '启用',
    value: 1
  },
  {
    label: '禁用',
    value: 2
  },
  {
    label: '未启用',
    value: 3
  },
]

const queryParams = reactive<searchParams>({ 
  ...queryParams_raw,
  site_id: site_id_all.value,
  channel_id: channel_item_all.value.channel_id,
})
const tableData = ref<listItem[]>([])
const tableRef = ref()
const formList = ref([
  {
    label: '业务类型',
    clearable: true,
    placeholder: '业务类型',
    value: toRef(queryParams, 'type'),
    component: 'select',
    selections: typeList
  },
  {
    label: '标题',
    clearable: true,
    placeholder: '标题',
    value: toRef(queryParams, 'name'),
    component: 'input',
  },
  {
    label: '更新人',
    clearable: true,
    placeholder: '更新人',
    value: toRef(queryParams, 'user_name'),
    component: 'select',
    labelKey: 'real_name',
    valueKey: 'real_name',
    selections: userList
  },
  {
    label: '状态',
    clearable: true,
    placeholder: '状态',
    value: toRef(queryParams, 'status'),
    component: 'select',
    selections: statusList
  },
  {
    label: '时间日期',
    clearable: true,
    start_placeholder: '开始日期',
    end_placeholder: '结束日期',
    placeholder: '审核人',
    value: '',
    component: 'datetimerange',
    append: true,
    dateChange: (value: [string, string] | null) => {
      if (value) {
        queryParams.start_time = value[0]
        queryParams.end_time = value[1]
      } else {
        queryParams.start_time = ''
        queryParams.end_time = ''
      }
      
    }
  },
])
const columns = ref([
  {
    title: '流程ID',
    dataKey: 'entity_id',
    width: 200
  },
  {
    title: '业务类型',
    dataKey: 'type_name',
  },
  {
    title: '标题',
    dataKey: 'name',
    showOverflow: true,
  },
  {
    title: '渠道名',
    dataKey: 'channel_name',
    showOverflowTooltip: true,
  },
  {
    title: '审核人',
    dataKey: 'handlers',
  },
  {
    title: '通知方式',
    dataKey: 'notice_type',
  },
  {
    title: '更新人',
    dataKey: 'user_name_e',
  },
  {
    title: '更新时间',
    dataKey: 'edit_time',
    width: 170,
  },
  {
    title: '状态',
    dataKey: 'status_name',
    width: 100,
    cellRenderer: (scope: { row: listItem }) => {
      return (
        <span style={ { color: scope.row.status === 1 ? 'var(--el-color-success)' : scope.row.status === 2 ? 'var(--el-color-error)' : '' } }>
          {scope.row.status_name}
        </span>
      )
    }
  },
  {
    title: '操作',
    dataKey: '',
    width: 160,
    cellRenderer: (scope: { row: listItem }) => {
      return (
        <>
          <ElButton
            type='primary'
            plain
            onClick={() => {
              handleJumpEdit(scope.row)
            }}
          >
            编辑
          </ElButton>
          <ElButton
            type={ scope.row.status === 1 ? 'danger' : 'success' }
            plain
            onClick={() => {
              handleOperate(scope.row)
            }}
          >
            { scope.row.status === 1 ? '禁用' : '启用' }
          </ElButton>
        </>
      )
    }
  },
])
const operateList = ref([
  {
    title: '流程配置',
    button: true,
    append: true,
    method: () => {
      handleAdd()
    }
  }
])

const getList = ( { page, pageSize } ) => {
  useTryCatch( async () => {
    setLoading(true)
    const params = {
      q: { ...queryParams, site_id: site_id_all.value, channel_id: channel_item_all.value.channel_id },
      page,
      limit: pageSize
    }
    const res = await getConfigList(params)
    if (res.code === 200) {
      tableData.value = res.data.list
      tableRef.value.setTotal(res.data.total)
    } else {
      ElMessage.error(res.msg)
    }
    setLoading(false)
  }, () => setLoading(false))
}
const reset = () => {
  Object.entries(queryParams_raw).forEach(( [ key, value ] ) => {
    queryParams[key] = value
  })
}
const handleSearch = () => {
  tableRef.value.setPage(1)
  const { page, pageSize } = tableRef.value
  getList( { page, pageSize } )
}
const handleAdd = () => {
  router.push({
    name: 'ReviewConfigDetail',
    query: {
      type: 'add'
    }
  })
}
const indexMethod = (index: number) => {
  const { page, pageSize } = tableRef.value.getPagination()
  return (page - 1) * pageSize + index + 1
}
const handleJumpEdit = (row: listItem) => {
  router.push({
    name: 'ReviewConfigDetail',
    query: {
      entity_id: row.entity_id
    }
  })
}
const handleOperate = (row: listItem) => {
  const { status, entity_id } = row
  ElMessageBox.confirm(
    `确认${status === 1 ? '禁用' : '启用'}?`,
    '提示',
    {
      type: 'warning',
    }
  ).then( () => {
    useTryCatch( async () => {
      const api = status === 1 ? disableConfig : enableConfig
      const params = {
        entity_id
      }
      const res = await api(params)
      if (res.code === 200) {
        ElMessage.success(res.msg)
        handleSearch()
      } else {
        ElMessage.error(res.msg)
      }
    } )
  }).catch(() => {})
}

Listener(route, () => {
  handleSearch()
})
</script>