<template>
  <div class="scroll-y with-flex">
    <QueryForm 
      :form-list="formList"
      :loading="loading"
      @search="handleSearch"
      @reset="handleReset"
    />
    <div v-loading="loading" class="sticky-table" style="position: relative">
      <CustomTable 
        ref="tableRef"
        :columns="columns"
        :data="tableData"
        :operate-list="operateList"
        :table-method="getList"
      />

      <el-result
        v-show="site_id && !queryParams.channel_id"
        icon="warning"
        title="确定站点后，请选择渠道!" 
        sub-title="您还未选择渠道"
        style="position: absolute; bottom: 0; width: 100%; height: 100%; background-color: var(--bg-layout); z-index: 19"
      />
    </div>

    <OperateModal 
      v-if="loadModal"
      :title="title"
      :row="currentRow"
      @close="loadModal = false"
      @save="handleSearch"
    />
  </div>
</template>

<script setup lang="tsx">
import { ref, reactive, toRef, defineAsyncComponent } from 'vue'
import { ElButton, ElMessage, ElMessageBox } from 'element-plus'
import { storeToRefs } from 'pinia'
import { useParamsStore } from '@/store'
import useTryCatch from '@/hooks/use-try-catch'
import useLoading from '@/hooks/use-loading'
import { getChannelList } from '@/api/params'
import { getRegisterList, deleteRegister } from '@/api/web-config'
import type { registerQueryParams, registerListItem } from '@/api/web-config'

import QueryForm from '@/components/QueryForm/index.vue'
import CustomTable from '@/components/CustomTable/index.vue'
const OperateModal = defineAsyncComponent( () => import( './components/operateModal.vue' ) )

defineOptions( { name: 'WebRegisterGuide' } )

const { userList, webList } = storeToRefs(useParamsStore())
const { loading, setLoading } = useLoading()
const channelList = ref([])
const site_id = ref<string|number>('')

const queryParams_raw = {
  id: '',
  title: '',
  channel_id: '',
  user_id_a: '',
  start_time: '',
  end_time: '',
}

const queryParams = reactive<registerQueryParams>({
  ...queryParams_raw
})

const formList = ref([
  {
    label: '配置ID',
    placeholder: '配置ID',
    value: toRef(queryParams, 'id'),
    component: 'input',
  },
  {
    label: '弹窗标题',
    placeholder: '弹窗标题',
    value: toRef(queryParams, 'title'),
    component: 'input',
  },
  {
    label: '所属站点',
    placeholder: '所属站点',
    value: site_id,
    component: 'select',
    labelKey: 'cms_site_name',
    valueKey: 'cms_site_id',
    selections: webList,
    handleChange: () => {
      queryParams.channel_id = ''
      if (site_id.value) {
        useTryCatch( async () => {
          const res = await getChannelList(site_id.value as number)
          if (res.code === 200) {
            channelList.value = res.data
          }
        } )
      } else {
        channelList.value = []
      }
    }
  },
  {
    label: '所属渠道',
    placeholder: '所属渠道',
    value: toRef(queryParams, 'channel_id'),
    component: 'select',
    labelKey: 'channel_code',
    valueKey: 'channel_id',
    selections: channelList,
    handleChange: () => {
      handleSearch()
    }
  },
  {
    label: '创建人',
    clearable: true,
    placeholder: '创建人',
    value: toRef(queryParams, 'user_id_a'),
    component: 'select',
    labelKey: 'real_name',
    valueKey: 'wsId',
    selections: userList
  },
  {
    label: '时间日期',
    clearable: true,
    start_placeholder: '开始日期',
    end_placeholder: '结束日期',
    value: ref(''),
    component: 'datetimerange',
    append: true,
    dateChange: (value: [string, string] | null) => {
      if (value) {
        queryParams.start_time = value[0]
        queryParams.end_time = value[1]
      } else {
        queryParams.start_time = ''
        queryParams.end_time = ''
      }
    }
  }
])

const tableRef = ref()
const tableData = ref<registerListItem[]>([])
const loadModal = ref(false)
const title = ref('')
const currentRow = ref<registerListItem|null>(null)
const columns = ref([
  {
    title: '配置ID',
    dataKey: 'id',
    width: 100
  },
  {
    title: '弹窗标题',
    dataKey: 'title',
    minWidth: 150
  },
  {
    title: '应用位置(站点渠道)',
    dataKey: 'channel_name',
    minWidth: 150
  },
  {
    title: '创建人',
    dataKey: 'user_id_a_name',
    width: 120
  },
  {
    title: '创建时间',
    dataKey: 'add_time',
    width: 200,
    cellRenderer: (scope: { row: registerListItem }) => (
      <>
        { new Date(scope.row.add_time).toLocaleString().replace(/\//g, '-') }
      </>
    )
  },
  {
    title: '操作',
    dataKey: '',
    width: 240,
    cellRenderer: (scope: { row: registerListItem }) => (
      <>
        <ElButton type='primary' plain onClick={() => handleEdit(scope.row, true)}>查看</ElButton>
        <ElButton type='primary' plain onClick={() => handleEdit(scope.row)}>编辑</ElButton>
        <ElButton type='danger' plain onClick={() => handleDelete(scope.row.id)}>删除</ElButton>
      </>
    )
  },
])
const operateList = ref([
  {
    title: '新增',
    button: true,
    append: true,
    method: () => {
      title.value = '新增'
      currentRow.value = null
      loadModal.value = true
    }
  }
])

const getList = ( { page, pageSize } ) => {
  useTryCatch( async () => {
    setLoading(true)

    const params = {
      ...queryParams,
      page,
      page_size: pageSize
    }
    const res = await getRegisterList(params)
    if (res.code === 200) {
      tableData.value = res.data.list
      tableRef.value?.setTotal(res.data.total)
    } else {
      ElMessage.error(res.msg)
    }

    setLoading(false)
  }, () => setLoading(false) )
}

const refresh = () => {
  const { page, pageSize } = tableRef.value?.getPagination()
  getList({ page, pageSize })
}

const handleSearch = () => {
  tableRef.value?.setPage(1)
  refresh()
}

const handleReset = () => {
  Object.keys(queryParams_raw).forEach((key) => {
    queryParams[key] = queryParams_raw[key]
  })
}

const handleEdit = (row: registerListItem, readonly = false) => {
  currentRow.value = row
  loadModal.value = true
  title.value = readonly ? '详情' : '编辑'
}

const handleDelete = (id: number) => {
  ElMessageBox.confirm('删除当前弹窗配置可能影响相关网页, 确定删除?', '删除', {
    confirmButtonText: '确认',
    cancelButtonText: '取消',
    type: 'warning',
    callback: (action: string) => {
      if (action === 'confirm') {
        useTryCatch( async () => {
          setLoading(true)
          const res = await deleteRegister(id)
          if (res.code === 200) {
            ElMessage.success('删除成功')
            refresh()
          } else {
            ElMessage.error(res.msg)
          }
          setLoading(false)
        }, () => setLoading(false) )
      }
    }
  })
}
</script>