body {
  margin: 1rem;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen,
    Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
  line-height: 1.4;
}

table {
  border-collapse: collapse;
}

/* Apply a default padding if legacy cellpadding attribute is missing */
table:not([cellpadding]) th,
table:not([cellpadding]) td {
  padding: 0.4rem;
}

/* Set default table styles if a table has a positive border attribute
   and no inline css */

table[border]:not([border='0']):not([style*='border-width']) th,
table[border]:not([border='0']):not([style*='border-width']) td {
  border-width: 1px;
}

/* Set default table styles if a table has a positive border attribute
   and no inline css */

table[border]:not([border='0']):not([style*='border-style']) th,
table[border]:not([border='0']):not([style*='border-style']) td {
  border-style: solid;
}

/* Set default table styles if a table has a positive border attribute
   and no inline css */

table[border]:not([border='0']):not([style*='border-color']) th,
table[border]:not([border='0']):not([style*='border-color']) td {
  border-color: #ccc;
}

figure {
  display: table;
  margin: 1rem auto;
}

figure figcaption {
  display: block;
  margin-top: 0.25rem;
  color: #999;
  text-align: center;
}

hr {
  border-color: #ccc;
  border-style: solid;
  border-width: 1px 0 0 0;
}

code {
  padding: 0.1rem 0.2rem;
  background-color: #e8e8e8;
  border-radius: 3px;
}

.mce-content-body:not([dir='rtl']) blockquote {
  margin-left: 1.5rem;
  padding-left: 1rem;
  border-left: 2px solid #ccc;
}

.mce-content-body[dir='rtl'] blockquote {
  margin-right: 1.5rem;
  padding-right: 1rem;
  border-right: 2px solid #ccc;
}
