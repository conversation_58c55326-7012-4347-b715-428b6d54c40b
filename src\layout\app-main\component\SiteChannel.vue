<template>
  <el-space :size="12">
    <el-select
      v-model="site_id_all"
      filterable
      @change="handleSiteChange"
      style="width: 160px"
    >
      <el-option
        v-for="d in webList"
        :key="d.cms_site_id"
        :label="d.cms_site_name"
        :value="d.cms_site_id"
      >
        <template #default>
          <div class="rowBC">
            <span>{{ d.cms_site_name }}</span>
            <span @click.stop="handleToplist(d)" class="top-icon-container">
              <template v-if="topIdMap.has(d.cms_site_id)">
                <SvgIcon icon-class="star-fill" class="top-icon" />
              </template>
              <template v-else>
                <SvgIcon icon-class="star" class="top-icon" />
              </template>
            </span>
          </div>
        </template>
      </el-option>
    </el-select>
    <div style="position: relative">
      <el-select
        v-model="channel_item_all"
        value-key="channel_id"
        placeholder="所属渠道"
        filterable
        clearable
        @change="handleChannelChange"
        style="width: 160px"
      >
        <el-option
          v-for="d in channelList"
          :key="d.channel_id"
          :label="d.channel_code"
          :value="d"
        />
      </el-select>
      <span
        ref="hostRef" 
        class="host-info with-hand"
        :class="{ hide: !channel_item_all.host }"
        title="可拖拽调整位置, 双击可复制"
        @dblclick="copyValueToClipboard(channel_item_all.host)"
      >
        {{ channel_item_all.host }}
      </span>
    </div>
    <template v-if="topList.length">
      <el-scrollbar style="max-width: 800px;">
        <el-space :size="8" nowrap>
          <span 
            v-for="(d, index) in topList"
            class="top-item with-hand"
            :class="{ active: d.cms_site_id === site_id_all }"
            @click="handleSiteClick(d.cms_site_id)"
          > 
            {{ d.cms_site_name }} 
          </span>
        </el-space>
      </el-scrollbar>
    </template>
  </el-space>
</template>

<script setup lang="ts">
import { computed, ref } from 'vue'
import { ElMessage } from 'element-plus'
import { useRoute } from 'vue-router'
import { useUserStroe, useParamsStore } from '@/store'
import useLoading from '@/hooks/use-loading'
import useTryCatch from '@/hooks/use-try-catch'
import { useDraggable } from '@/hooks/use-draggable'
import { copyValueToClipboard } from '@/hooks/use-common'
import emitter from '@/utils/bus'
import { setUserSites } from '@/api/user'
import type { channelItem, siteItem } from '@/store/modules/params/types'

import SvgIcon from '@/icons/SvgIcon.vue'

const route = useRoute()
const { site_id_all, channel_item_all, webList, channelList } = storeToRefs(useParamsStore())
const { topList, userInfo } = storeToRefs(useUserStroe())
const { getChannelList, siteChannelListeners, setLocalChannelItemAll, getSiteDetailInfo, setSiteIDAll } = useParamsStore()
const { loading, setLoading } = useLoading()

const isDropdownSite = ref(false)
const dropdownRef = ref()
const hostRef = ref<HTMLElement|undefined>()

const siteChannelMap: { number: channelItem } = JSON.parse(window.localStorage.getItem('siteChannelMap') || '{}')

const handleSiteChannelListen = () => {
  for ( const item of siteChannelListeners ) {
    if (item[0] !== route.name) {
      siteChannelListeners.set(item[0], true)
    }
  }
}

const handleSiteClick = (val: number) => {
  setSiteIDAll(val)
  handleSiteChange(val)
}

const handleSiteChange = async (val: number) => {
  window.localStorage.setItem('site_id', `${val}`)
  if (route.name) {
    siteChannelListeners.set(route.name, false)
  }
  // 顺序不能更改
  await getChannelList(siteChannelMap[val])
  handleChannelChange()
  getSiteDetailInfo()
}

const handleSiteChange1 = (val: number) => {
  isDropdownSite.value = false
  handleSiteChange(val)
}
const handleSiteChange2 = (val: number) => {
  isDropdownSite.value = true
  dropdownRef.value?.handleClose()
  handleSiteChange(val)
}

const handleIsDropdownSite = () => {
  if (topList.value.length > 3 && topIdMap.value.has(site_id_all.value)) {
    const index = topIdMap.value.get(site_id_all.value)
    if (index && index > 2) {
      isDropdownSite.value = true
    }
  }
}

// 发布订阅模式统一响应渠道变化
const handleChannelChange = () => {
  const { name } = route
  if (name) {
    emitter.emit(name)
  }
  siteChannelMap[site_id_all.value] = channel_item_all.value || {}
  handleSiteChannelListen()
  setLocalChannelItemAll()
  window.localStorage.setItem('siteChannelMap', JSON.stringify(siteChannelMap))
}

const handleToplist = (item: siteItem) => {
  if (loading.value) {
    return ElMessage.warning('请勿短时间内频繁操作')
  }
  useTryCatch( async () => {
    setLoading(true)
    if (topIdMap.value.has(item.cms_site_id)) {
      const index = topIdMap.value.get(item.cms_site_id) as number
      topList.value.splice(index, 1)
    } else if (topList.value.length < 8) {
      topList.value.push(item)
    } else {
      setLoading(false)
      return ElMessage.warning('常用站点最多不能超过8个')
    }

    const site_ids = topList.value.map(({ cms_site_id }) => cms_site_id).join(',')
    await setUserSites({ site_ids, wsId: userInfo.value.wsid as string })
    setLoading(false)
  }, () => setLoading(false) )
}

const topIdMap = computed(() => {
  const idSet = new Map<number, number>()
  topList.value.forEach((item, index) => idSet.set(item.cms_site_id, index))
  return idSet
})

handleIsDropdownSite()

useDraggable(hostRef, hostRef, computed(() => ref(true).value))
</script>

<style lang="scss" scoped>
.host-info {
  position: absolute;
  z-index: 9;
  bottom: -24px;
  left: 0;
  color: var(--el-color-primary);
  font-size: 12px;
  border-radius: 4px;
  padding: 2px 8px;
  background-color: var(--el-bg-color);
  box-shadow: 0px 6px 30px 5px rgba(0, 0, 0, 0.05), 0px 16px 24px 2px rgba(0, 0, 0, 0.04), 0px 8px 10px -5px rgba(0, 0, 0, 0.08);
  border: 1px solid var(--el-border-color-light);
  &.hide {
    opacity: 0;
    pointer-events: none;
  }
}
.top-icon-container {
  .top-icon {
    cursor: pointer;
    &:hover {
      color: var(--el-color-primary);
    }
  }
}
.top-item {
  white-space: nowrap;
  padding: 8px 12px;
  border-radius: 4px;
  font-size: 12px;
  background-color: var(--el-color-info-light-8);
  transition: background-color .3s;
  &.active, &:hover {
    background-color: var(--el-color-primary);
    color: var(--el-color-white);
  }
}
</style>
