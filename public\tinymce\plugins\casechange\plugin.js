/*!
 * Tiny Case Change plugin
 *
 * Copyright (c) 2023 Ephox Corporation DBA Tiny Technologies, Inc.
 * Licensed under the Tiny commercial license. See https://www.tiny.cloud/legal/
 *
 * Version: 7.1.1-1
 */

!function(){"use strict";const e=e=>parseInt(e,10),t=(e,t)=>{const n=e-t;return 0===n?0:n>0?1:-1},n=(e,t,n)=>({major:e,minor:t,patch:n}),r=t=>{const r=/([0-9]+)\.([0-9]+)\.([0-9]+)(?:(\-.+)?)/.exec(t);return r?n(e(r[1]),e(r[2]),e(r[3])):n(0,0,0)},o=("array",e=>"array"===(e=>{const t=typeof e;return null===e?"null":"object"===t&&Array.isArray(e)?"array":"object"===t&&(n=r=e,(o=String).prototype.isPrototypeOf(n)||(null===(s=r.constructor)||void 0===s?void 0:s.name)===o.name)?"string":t;var n,r,o,s})(e));const s=e=>null==e,a=e=>!s(e),c=(!0,()=>true);class i{constructor(e,t){this.tag=e,this.value=t}static some(e){return new i(!0,e)}static none(){return i.singletonNone}fold(e,t){return this.tag?t(this.value):e()}isSome(){return this.tag}isNone(){return!this.tag}map(e){return this.tag?i.some(e(this.value)):i.none()}bind(e){return this.tag?e(this.value):i.none()}exists(e){return this.tag&&e(this.value)}forall(e){return!this.tag||e(this.value)}filter(e){return!this.tag||e(this.value)?this:i.none()}getOr(e){return this.tag?this.value:e}or(e){return this.tag?this:e}getOrThunk(e){return this.tag?this.value:e()}orThunk(e){return this.tag?this:e()}getOrDie(e){if(this.tag)return this.value;throw new Error(null!=e?e:"Called getOrDie on None")}static from(e){return a(e)?i.some(e):i.none()}getOrNull(){return this.tag?this.value:null}getOrUndefined(){return this.value}each(e){this.tag&&e(this.value)}toArray(){return this.tag?[this.value]:[]}toString(){return this.tag?`some(${this.value})`:"none()"}}i.singletonNone=new i(!1);const u=Array.prototype.push,l=(e,t)=>{const n=e.length,r=new Array(n);for(let o=0;o<n;o++){const n=e[o];r[o]=t(n,o)}return r},g=(e,t)=>{for(let n=0,r=e.length;n<r;n++)t(e[n],n)},h=(e,t)=>{for(let n=e.length-1;n>=0;n--)t(e[n],n)},d=(e,t)=>t>=0&&t<e.length?i.some(e[t]):i.none(),m=e=>d(e,0),p="[-'\\.\u2018\u2019\u2024\ufe52\uff07\uff0e]",f="[:\xb7\xb7\u05f4\u2027\ufe13\ufe55\uff1a]",y="[\xb1+*/,;;\u0589\u060c\u060d\u066c\u07f8\u2044\ufe10\ufe14\ufe50\ufe54\uff0c\uff1b]",C="[0-9\u0660-\u0669\u066b\u06f0-\u06f9\u07c0-\u07c9\u0966-\u096f\u09e6-\u09ef\u0a66-\u0a6f\u0ae6-\u0aef\u0b66-\u0b6f\u0be6-\u0bef\u0c66-\u0c6f\u0ce6-\u0cef\u0d66-\u0d6f\u0e50-\u0e59\u0ed0-\u0ed9\u0f20-\u0f29\u1040-\u1049\u1090-\u1099\u17e0-\u17e9\u1810-\u1819\u1946-\u194f\u19d0-\u19d9\u1a80-\u1a89\u1a90-\u1a99\u1b50-\u1b59\u1bb0-\u1bb9\u1c40-\u1c49\u1c50-\u1c59\ua620-\ua629\ua8d0-\ua8d9\ua900-\ua909\ua9d0-\ua9d9\uaa50-\uaa59\uabf0-\uabf9]",v="\\r",w="\\n",E="[\v\f\x85\u2028\u2029]",R="[\u0300-\u036f\u0483-\u0489\u0591-\u05bd\u05bf\u05c1\u05c2\u05c4\u05c5\u05c7\u0610-\u061a\u064b-\u065f\u0670\u06d6-\u06dc\u06df-\u06e4\u06e7\u06e8\u06ea-\u06ed\u0711\u0730-\u074a\u07a6-\u07b0\u07eb-\u07f3\u0816-\u0819\u081b-\u0823\u0825-\u0827\u0829-\u082d\u0859-\u085b\u0900-\u0903\u093a-\u093c\u093e-\u094f\u0951-\u0957\u0962\u0963\u0981-\u0983\u09bc\u09be-\u09c4\u09c7\u09c8\u09cb-\u09cd\u09d7\u09e2\u09e3\u0a01-\u0a03\u0a3c\u0a3e-\u0a42\u0a47\u0a48\u0a4b-\u0a4d\u0a51\u0a70\u0a71\u0a75\u0a81-\u0a83\u0abc\u0abe-\u0ac5\u0ac7-\u0ac9\u0acb-\u0acd\u0ae2\u0ae3\u0b01-\u0b03\u0b3c\u0b3e-\u0b44\u0b47\u0b48\u0b4b-\u0b4d\u0b56\u0b57\u0b62\u0b63\u0b82\u0bbe-\u0bc2\u0bc6-\u0bc8\u0bca-\u0bcd\u0bd7\u0c01-\u0c03\u0c3e-\u0c44\u0c46-\u0c48\u0c4a-\u0c4d\u0c55\u0c56\u0c62\u0c63\u0c82\u0c83\u0cbc\u0cbe-\u0cc4\u0cc6-\u0cc8\u0cca-\u0ccd\u0cd5\u0cd6\u0ce2\u0ce3\u0d02\u0d03\u0d3e-\u0d44\u0d46-\u0d48\u0d4a-\u0d4d\u0d57\u0d62\u0d63\u0d82\u0d83\u0dca\u0dcf-\u0dd4\u0dd6\u0dd8-\u0ddf\u0df2\u0df3\u0e31\u0e34-\u0e3a\u0e47-\u0e4e\u0eb1\u0eb4-\u0eb9\u0ebb\u0ebc\u0ec8-\u0ecd\u0f18\u0f19\u0f35\u0f37\u0f39\u0f3e\u0f3f\u0f71-\u0f84\u0f86\u0f87\u0f8d-\u0f97\u0f99-\u0fbc\u0fc6\u102b-\u103e\u1056-\u1059\u105e-\u1060\u1062-\u1064\u1067-\u106d\u1071-\u1074\u1082-\u108d\u108f\u109a-\u109d\u135d-\u135f\u1712-\u1714\u1732-\u1734\u1752\u1753\u1772\u1773\u17b6-\u17d3\u17dd\u180b-\u180d\u18a9\u1920-\u192b\u1930-\u193b\u19b0-\u19c0\u19c8\u19c9\u1a17-\u1a1b\u1a55-\u1a5e\u1a60-\u1a7c\u1a7f\u1b00-\u1b04\u1b34-\u1b44\u1b6b-\u1b73\u1b80-\u1b82\u1ba1-\u1baa\u1be6-\u1bf3\u1c24-\u1c37\u1cd0-\u1cd2\u1cd4-\u1ce8\u1ced\u1cf2\u1dc0-\u1de6\u1dfc-\u1dff\u200c\u200d\u20d0-\u20f0\u2cef-\u2cf1\u2d7f\u2de0-\u2dff\u302a-\u302f\u3099\u309a\ua66f-\ua672\ua67c\ua67d\ua6f0\ua6f1\ua802\ua806\ua80b\ua823-\ua827\ua880\ua881\ua8b4-\ua8c4\ua8e0-\ua8f1\ua926-\ua92d\ua947-\ua953\ua980-\ua983\ua9b3-\ua9c0\uaa29-\uaa36\uaa43\uaa4c\uaa4d\uaa7b\uaab0\uaab2-\uaab4\uaab7\uaab8\uaabe\uaabf\uaac1\uabe3-\uabea\uabec\uabed\ufb1e\ufe00-\ufe0f\ufe20-\ufe26\uff9e\uff9f]",T="[\xad\u0600-\u0603\u06dd\u070f\u17b4\u17b5\u200e\u200f\u202a-\u202e\u2060-\u2064\u206a-\u206f\ufeff\ufff9-\ufffb]",x="[\u3031-\u3035\u309b\u309c\u30a0-\u30fa\u30fc-\u30ff\u31f0-\u31ff\u32d0-\u32fe\u3300-\u3357\uff66-\uff9d]",k="[=_\u203f\u2040\u2054\ufe33\ufe34\ufe4d-\ufe4f\uff3f\u2200-\u22ff<>]",N="[~\u2116|!-*+-\\/:;?@\\[-`{}\xa1\xab\xb7\xbb\xbf;\xb7\u055a-\u055f\u0589\u058a\u05be\u05c0\u05c3\u05c6\u05f3\u05f4\u0609\u060a\u060c\u060d\u061b\u061e\u061f\u066a-\u066d\u06d4\u0700-\u070d\u07f7-\u07f9\u0830-\u083e\u085e\u0964\u0965\u0970\u0df4\u0e4f\u0e5a\u0e5b\u0f04-\u0f12\u0f3a-\u0f3d\u0f85\u0fd0-\u0fd4\u0fd9\u0fda\u104a-\u104f\u10fb\u1361-\u1368\u1400\u166d\u166e\u169b\u169c\u16eb-\u16ed\u1735\u1736\u17d4-\u17d6\u17d8-\u17da\u1800-\u180a\u1944\u1945\u1a1e\u1a1f\u1aa0-\u1aa6\u1aa8-\u1aad\u1b5a-\u1b60\u1bfc-\u1bff\u1c3b-\u1c3f\u1c7e\u1c7f\u1cd3\u2010-\u2027\u2030-\u2043\u2045-\u2051\u2053-\u205e\u207d\u207e\u208d\u208e\u3008\u3009\u2768-\u2775\u27c5\u27c6\u27e6-\u27ef\u2983-\u2998\u29d8-\u29db\u29fc\u29fd\u2cf9-\u2cfc\u2cfe\u2cff\u2d70\u2e00-\u2e2e\u2e30\u2e31\u3001-\u3003\u3008-\u3011\u3014-\u301f\u3030\u303d\u30a0\u30fb\ua4fe\ua4ff\ua60d-\ua60f\ua673\ua67e\ua6f2-\ua6f7\ua874-\ua877\ua8ce\ua8cf\ua8f8-\ua8fa\ua92e\ua92f\ua95f\ua9c1-\ua9cd\ua9de\ua9df\uaa5c-\uaa5f\uaade\uaadf\uabeb\ufd3e\ufd3f\ufe10-\ufe19\ufe30-\ufe52\ufe54-\ufe61\ufe63\ufe68\ufe6a\ufe6b\uff01-\uff03\uff05-\uff0a\uff0c-\uff0f\uff1a\uff1b\uff1f\uff20\uff3b-\uff3d\uff3f\uff5b\uff5d\uff5f-\uff65]",O=10,_=[new RegExp("[A-Za-z\xaa\xb5\xba\xc0-\xd6\xd8-\xf6\xf8-\u02c1\u02c6-\u02d1\u02e0-\u02e4\u02ec\u02ee\u0370-\u0374\u0376\u0377\u037a-\u037d\u0386\u0388-\u038a\u038c\u038e-\u03a1\u03a3-\u03f5\u03f7-\u0481\u048a-\u0527\u0531-\u0556\u0559\u0561-\u0587\u05d0-\u05ea\u05f0-\u05f3\u0620-\u064a\u066e\u066f\u0671-\u06d3\u06d5\u06e5\u06e6\u06ee\u06ef\u06fa-\u06fc\u06ff\u0710\u0712-\u072f\u074d-\u07a5\u07b1\u07ca-\u07ea\u07f4\u07f5\u07fa\u0800-\u0815\u081a\u0824\u0828\u0840-\u0858\u0904-\u0939\u093d\u0950\u0958-\u0961\u0971-\u0977\u0979-\u097f\u0985-\u098c\u098f\u0990\u0993-\u09a8\u09aa-\u09b0\u09b2\u09b6-\u09b9\u09bd\u09ce\u09dc\u09dd\u09df-\u09e1\u09f0\u09f1\u0a05-\u0a0a\u0a0f\u0a10\u0a13-\u0a28\u0a2a-\u0a30\u0a32\u0a33\u0a35\u0a36\u0a38\u0a39\u0a59-\u0a5c\u0a5e\u0a72-\u0a74\u0a85-\u0a8d\u0a8f-\u0a91\u0a93-\u0aa8\u0aaa-\u0ab0\u0ab2\u0ab3\u0ab5-\u0ab9\u0abd\u0ad0\u0ae0\u0ae1\u0b05-\u0b0c\u0b0f\u0b10\u0b13-\u0b28\u0b2a-\u0b30\u0b32\u0b33\u0b35-\u0b39\u0b3d\u0b5c\u0b5d\u0b5f-\u0b61\u0b71\u0b83\u0b85-\u0b8a\u0b8e-\u0b90\u0b92-\u0b95\u0b99\u0b9a\u0b9c\u0b9e\u0b9f\u0ba3\u0ba4\u0ba8-\u0baa\u0bae-\u0bb9\u0bd0\u0c05-\u0c0c\u0c0e-\u0c10\u0c12-\u0c28\u0c2a-\u0c33\u0c35-\u0c39\u0c3d\u0c58\u0c59\u0c60\u0c61\u0c85-\u0c8c\u0c8e-\u0c90\u0c92-\u0ca8\u0caa-\u0cb3\u0cb5-\u0cb9\u0cbd\u0cde\u0ce0\u0ce1\u0cf1\u0cf2\u0d05-\u0d0c\u0d0e-\u0d10\u0d12-\u0d3a\u0d3d\u0d4e\u0d60\u0d61\u0d7a-\u0d7f\u0d85-\u0d96\u0d9a-\u0db1\u0db3-\u0dbb\u0dbd\u0dc0-\u0dc6\u0f00\u0f40-\u0f47\u0f49-\u0f6c\u0f88-\u0f8c\u10a0-\u10c5\u10d0-\u10fa\u10fc\u1100-\u1248\u124a-\u124d\u1250-\u1256\u1258\u125a-\u125d\u1260-\u1288\u128a-\u128d\u1290-\u12b0\u12b2-\u12b5\u12b8-\u12be\u12c0\u12c2-\u12c5\u12c8-\u12d6\u12d8-\u1310\u1312-\u1315\u1318-\u135a\u1380-\u138f\u13a0-\u13f4\u1401-\u166c\u166f-\u167f\u1681-\u169a\u16a0-\u16ea\u16ee-\u16f0\u1700-\u170c\u170e-\u1711\u1720-\u1731\u1740-\u1751\u1760-\u176c\u176e-\u1770\u1820-\u1877\u1880-\u18a8\u18aa\u18b0-\u18f5\u1900-\u191c\u1a00-\u1a16\u1b05-\u1b33\u1b45-\u1b4b\u1b83-\u1ba0\u1bae\u1baf\u1bc0-\u1be5\u1c00-\u1c23\u1c4d-\u1c4f\u1c5a-\u1c7d\u1ce9-\u1cec\u1cee-\u1cf1\u1d00-\u1dbf\u1e00-\u1f15\u1f18-\u1f1d\u1f20-\u1f45\u1f48-\u1f4d\u1f50-\u1f57\u1f59\u1f5b\u1f5d\u1f5f-\u1f7d\u1f80-\u1fb4\u1fb6-\u1fbc\u1fbe\u1fc2-\u1fc4\u1fc6-\u1fcc\u1fd0-\u1fd3\u1fd6-\u1fdb\u1fe0-\u1fec\u1ff2-\u1ff4\u1ff6-\u1ffc\u2071\u207f\u2090-\u209c\u2102\u2107\u210a-\u2113\u2115\u2119-\u211d\u2124\u2126\u2128\u212a-\u212d\u212f-\u2139\u213c-\u213f\u2145-\u2149\u214e\u2160-\u2188\u24b6-\u24e9\u2c00-\u2c2e\u2c30-\u2c5e\u2c60-\u2ce4\u2ceb-\u2cee\u2d00-\u2d25\u2d30-\u2d65\u2d6f\u2d80-\u2d96\u2da0-\u2da6\u2da8-\u2dae\u2db0-\u2db6\u2db8-\u2dbe\u2dc0-\u2dc6\u2dc8-\u2dce\u2dd0-\u2dd6\u2dd8-\u2dde\u2e2f\u3005\u303b\u303c\u3105-\u312d\u3131-\u318e\u31a0-\u31ba\ua000-\ua48c\ua4d0-\ua4fd\ua500-\ua60c\ua610-\ua61f\ua62a\ua62b\ua640-\ua66e\ua67f-\ua697\ua6a0-\ua6ef\ua717-\ua71f\ua722-\ua788\ua78b-\ua78e\ua790\ua791\ua7a0-\ua7a9\ua7fa-\ua801\ua803-\ua805\ua807-\ua80a\ua80c-\ua822\ua840-\ua873\ua882-\ua8b3\ua8f2-\ua8f7\ua8fb\ua90a-\ua925\ua930-\ua946\ua960-\ua97c\ua984-\ua9b2\ua9cf\uaa00-\uaa28\uaa40-\uaa42\uaa44-\uaa4b\uab01-\uab06\uab09-\uab0e\uab11-\uab16\uab20-\uab26\uab28-\uab2e\uabc0-\uabe2\uac00-\ud7a3\ud7b0-\ud7c6\ud7cb-\ud7fb\ufb00-\ufb06\ufb13-\ufb17\ufb1d\ufb1f-\ufb28\ufb2a-\ufb36\ufb38-\ufb3c\ufb3e\ufb40\ufb41\ufb43\ufb44\ufb46-\ufbb1\ufbd3-\ufd3d\ufd50-\ufd8f\ufd92-\ufdc7\ufdf0-\ufdfb\ufe70-\ufe74\ufe76-\ufefc\uff21-\uff3a\uff41-\uff5a\uffa0-\uffbe\uffc2-\uffc7\uffca-\uffcf\uffd2-\uffd7\uffda-\uffdc]"),new RegExp(p),new RegExp(f),new RegExp(y),new RegExp(C),new RegExp(v),new RegExp(w),new RegExp(E),new RegExp(R),new RegExp(T),new RegExp(x),new RegExp(k),new RegExp("@")],b=new RegExp("^"+N+"$"),A=_,S=e=>{let t=13;const n=A.length;for(let r=0;r<n;++r){const n=A[r];if(n&&n.test(e)){t=r;break}}return t},B=(e,t)=>{const n=e[t],r=e[t+1];if(t<0||t>e.length-1&&0!==t)return!1;if(0===n&&0===r)return!1;const o=e[t+2];if(0===n&&(2===r||1===r||12===r)&&0===o)return!1;const s=e[t-1];return(2!==n&&1!==n&&12!==r||0!==r||0!==s)&&(4!==n&&0!==n||4!==r&&0!==r)&&(3!==n&&1!==n||4!==r||4!==s)&&(4!==n||3!==r&&1!==r||4!==o)&&(8!==n&&9!==n||0!==r&&4!==r&&r!==O&&8!==r&&9!==r)&&(8!==r&&(9!==r||0!==o&&4!==o&&o!==O&&8!==o&&9!==o)||0!==n&&4!==n&&n!==O&&8!==n&&9!==n)&&(5!==n||6!==r)&&(7===n||5===n||6===n||7===r||5===r||6===r||(n!==O||r!==O)&&(11!==r||0!==n&&4!==n&&n!==O&&11!==n)&&(11!==n||0!==r&&4!==r&&r!==O)&&12!==n)},P=/^\s+$/,j=b,D=e=>"http"===e||"https"===e,L=(e,t)=>{const n=((e,t)=>{let n;for(n=t;n<e.length&&!P.test(e[n]);n++);return n})(e,t+1);return"://"===e.slice(t+1,n).join("").substr(0,3)?n:t},U=(e,t,n)=>((e,t,n)=>{n={includeWhitespace:!1,includePunctuation:!1,...n};const r=l(e,t);return((e,t,n,r)=>{const o=[],s=[];let a=[];for(let c=0;c<n.length;++c)if(a.push(e[c]),B(n,c)){const n=t[c];if((r.includeWhitespace||!P.test(n))&&(r.includePunctuation||!j.test(n))){const n=c-a.length+1,r=c+1,i=t.slice(n,r).join("");if(D(i)){const n=L(t,c),o=e.slice(r,n);Array.prototype.push.apply(a,o),c=n}o.push(a),s.push({start:n,end:r})}a=[]}return{words:o,indices:s}})(e,r,(e=>{const t=(e=>{const t={};return n=>{if(t[n])return t[n];{const r=e(n);return t[n]=r,r}}})(S);return l(e,t)})(r),n)})(e,t,n).words,I=(e,t)=>i.from(e.childNodes[t]),M=e=>e.char,W=e=>l(e.data.split(""),((t,n)=>({char:t,node:e,offset:n}))),z=e=>{const t=e.schema.getVoidElements(),n=e.schema.getBlockElements();return e=>!!t[e.nodeName]||!!n[e.nodeName]},V=(e,t,n)=>{const r=z(t),o=t.getBody(),s=new tinymce.dom.TreeWalker(e,o);let c=s.current();if(c)for(;a(c)&&!r(c)&&s[n]();){const e=s.current();if(a(e)&&r(e))break;c=s.current()}return a(c)?c:e},$=(e,t)=>{const n=((e,t)=>{const n=I(t.startContainer,t.startOffset).getOr(t.startContainer),r=I(t.endContainer,t.endOffset).getOr(t.endContainer);return{start:V(n,e,"prev"),end:V(r,e,"next")}})(e,t),r=((e,t)=>{const n=z(t),r=t.getBody(),o=new tinymce.dom.TreeWalker(e.start,r),a=[];let c=[];do{const e=o.current();s(e)||"false"===t.dom.getContentEditableParent(e)||((i=e)&&3===i.nodeType?c.push(e):n(e)&&(a.push(c),c=[]))}while(o.current()!==e.end&&o.next());var i;return a.push(c),a})(n,e);return l(r,(e=>{const t=(e=>{const t=[];for(let n=0,r=e.length;n<r;++n){if(!o(e[n]))throw new Error("Arr.flatten item "+n+" was not an array, input: "+e);u.apply(t,e[n])}return t})(l(e,W));return U(t,M)}))},q=e=>{const t=e.char.toLocaleUpperCase();e.node.replaceData(e.offset,1,t)},Z=e=>{const t=e.char.toLocaleLowerCase();e.node.replaceData(e.offset,1,t)},F=(e,t)=>{const n=document.createRange();n.setStart(t.node,t.offset),n.setEnd(t.node,t.offset+t.char.length);const r=1===n.compareBoundaryPoints(Range.END_TO_END,e),o=-1===n.compareBoundaryPoints(Range.START_TO_START,e);return!r&&!o},G=(e,t)=>{return(n=m(t),s=t,r=d(s,s.length-1),o=(t,n)=>{const r=document.createRange();r.setStart(t.node,t.offset),r.setEnd(n.node,n.offset+n.char.length);const o=-1===r.compareBoundaryPoints(Range.START_TO_START,e),s=1===r.compareBoundaryPoints(Range.END_TO_END,e);return o&&s},n.isSome()&&r.isSome()?i.some(o(n.getOrDie(),r.getOrDie())):i.none()).getOr(!1);var n,r,o,s},H=(e,t)=>{h(e,(e=>{h(e,t)}))},J=(e,t)=>{H(e,(e=>{h(e,t)}))},K=e=>{const t=l(e.dom.select("td[data-mce-selected],th[data-mce-selected]"),(e=>{const t=document.createRange();return t.selectNodeContents(e),t}));return t.length>0?t:[e.selection.getRng()]},Q=(e,t)=>{true&&e.undoManager.transact(t)},X=(e,t,n)=>{const r=n.collapsed?c:function(e,...t){return(...n)=>{const r=t.concat(n);return e.apply(null,r)}}(F,n),o=(e,n)=>{return n>0&&((e,t)=>{for(let n=0,r=e.length;n<r;n++)if(t(e[n],n))return!0;return!1})(t,(r=e,e=>(e=>{return t=(e,t)=>e+t.char,n="",g(e,((e,r)=>{n=t(n,e)})),n;var t,n})(r).toLowerCase()===e.toLowerCase()));var r};H($(e,n),((e,t)=>{h(((e,t)=>{const n=[];for(let r=0,o=e.length;r<o;r++){const o=e[r];t(o,r)&&n.push(o)}return n})(e.slice(1),r),Z),m(e).filter(r).each((n=>{o(e,t)?Z(n):q(n)}))}))},Y=("casechange_title_case_minors",e=>e.options.get("casechange_title_case_minors"));const ee=e=>{e.addCommand("mceUpperCase",(()=>{(e=>{Q(e,(()=>{const t=e.selection.getBookmark();g(K(e),(t=>{((e,t)=>{t.collapsed?((e,t)=>{H($(e,t),(e=>{G(t,e)&&g(e,q)}))})(e,t):((e,t)=>{J($(e,t),(e=>{F(t,e)&&q(e)}))})(e,t)})(e,t)})),e.focus(),e.selection.moveToBookmark(t)}))})(e)})),e.addCommand("mceLowerCase",(()=>{(e=>{Q(e,(()=>{const t=e.selection.getBookmark();g(K(e),(t=>{((e,t)=>{t.collapsed?((e,t)=>{H($(e,t),(e=>{G(t,e)&&g(e,Z)}))})(e,t):((e,t)=>{J($(e,t),(e=>{F(t,e)&&Z(e)}))})(e,t)})(e,t)})),e.focus(),e.selection.moveToBookmark(t)}))})(e)})),e.addCommand("mceTitleCase",(()=>{const t=Y(e);((e,t)=>{Q(e,(()=>{const n=e.selection.getBookmark();g(K(e),(n=>{X(e,t,n)})),e.selection.moveToBookmark(n),e.focus()}))})(e,t)}))},te=(e,t,n)=>()=>{n.set(t),e.execCommand(t)},ne=(e,t)=>e===t.get(),re=(e,t,n,r)=>({type:"togglemenuitem",text:n,active:ne(t,r),onAction:te(e,t,r),onSetup:t=>{const n=()=>{const n=true;t.setEnabled(!e.readonly&&n)};return e.on("NodeChange",n),n(),()=>e.off("NodeChange",n)}});tinymce.PluginManager.add("casechange",(e=>{if(((e,n)=>!!e&&-1===((e,n)=>{const r=t(e.major,n.major);if(0!==r)return r;const o=t(e.minor,n.minor);if(0!==o)return o;const s=t(e.patch,n.patch);return 0!==s?s:0})((e=>r((e=>[e.majorVersion,e.minorVersion].join(".").split(".").slice(0,3).join("."))(e)))(e),r(n)))(tinymce,"6.0.0"))return void console.error("The casechange plugin requires at least version 6.0.0 of TinyMCE");const n=(e=>{let t="mceUpperCase";return{get:()=>t,set:e=>{t=e}}})();(e=>{(0,e.options.register)("casechange_title_case_minors",{processor:"string[]",default:["at","by","in","of","on","up","to","en","re","vs","but","off","out","via","bar","mid","per","pro","qua","til","from","into","unto","with","amid","anit","atop","down","less","like","near","over","past","plus","sans","save","than","thru","till","upon","for","and","nor","but","or","yet","so","an","a","some","the"]})})(e),((e,t)=>{e.ui.registry.addNestedMenuItem("casechange",{text:"Capitalization",getSubmenuItems:()=>[re(e,"mceLowerCase","lowercase",t),re(e,"mceUpperCase","UPPERCASE",t),re(e,"mceTitleCase","Title Case",t)]})})(e,n),((e,t)=>{e.ui.registry.addSplitButton("casechange",{tooltip:"Capitalization",icon:"change-case",onAction:()=>{e.execCommand(t.get())},onItemAction:(n,r)=>{t.set(r),e.execCommand(r)},onSetup:t=>{const n=()=>{const n=true;t.setEnabled(!e.readonly&&n)};return e.on("NodeChange",n),n(),()=>e.off("NodeChange",n)},select:e=>e===t.get(),fetch:e=>{e([{type:"choiceitem",text:"lowercase",value:"mceLowerCase"},{type:"choiceitem",text:"UPPERCASE",value:"mceUpperCase"},{type:"choiceitem",text:"Title Case",value:"mceTitleCase"}])}})})(e,n),ee(e)}))}();