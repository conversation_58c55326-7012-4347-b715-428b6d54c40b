import { h, ref } from 'vue'
import { ElMessageBox, ElCheckbox } from 'element-plus'
import { getHost } from '@/hooks/use-env'
import { stringify } from '@/utils/common'

const exportList = [
  {
    key: 'tpl_page_id',
    label: '页面id',
    selected: true
  },
  {
    key: 'tpl_id',
    label: '模板id',
    selected: true
  },
  {
    key: 'level',
    label: '页面等级',
    selected: true
  },
  {
    key: 'url',
    label: '页面URL',
    selected: true
  },
  {
    key: 'online_url',
    label: '完整页面URL',
    selected: true
  },
  {
    key: 'title',
    label: '页面标题',
    selected: true
  },
  {
    key: 'description',
    label: '页面描述',
    selected: true
  },
  {
    key: 'channel_code',
    label: '所属渠道',
    selected: true
  },
  {
    key: 'page_state',
    label: '页面状态',
    selected: true
  },
  {
    key: 'user_name_a',
    label: '创建人',
    selected: true
  },
  {
    key: 'add_time',
    label: '创建时间',
    selected: true
  },
  {
    key: 'user_name_e',
    label: '更新人',
    selected: true
  },
  {
    key: 'edit_time',
    label: '更新时间',
    selected: true
  },
  {
    key: 'user_name_c',
    label: '生成人',
    selected: true
  },
  {
    key: 'create_time',
    label: '生成时间',
    selected: true
  }
]
const selectAll = ref(true)

export const exportDownload = (queryParams: any) => {
  const checkList = ref(exportList)
  ElMessageBox({
    title: '请确认需要导出的字段',
    showConfirmButton: true,
    showCancelButton: true,
    confirmButtonText: '确认',
    cancelButtonText: '取消',
    customStyle: {
      '--el-messagebox-width': '600px'
    },
    callback: (action: string) => {
      if (action === 'confirm') {
        const fileds = checkList.value.filter(( { selected } ) => selected).map(( { key } ) => key).join(',')

        const qsParams = stringify({
          ...queryParams,
          is_export: 1,
          export_fields: fileds,
        })

        const url = `${getHost()}/api/v1/templates/pages?${qsParams}`
        location.href = url
      }
    },
    message: () => {
      return h('div', checkList.value.map(( item ) => h(ElCheckbox, {
        key: item.key,
        modelValue: item.selected,
        label: item.label,
        onChange: (value: any) => {
          item.selected = value
        }
      })).concat([
        h('div', { style: { padding: '24px 0 5px', textAlign: 'center' } }, h(ElCheckbox, {
          label: '全选',
          modelValue: selectAll.value,
          onChange: (value: any) => {
            selectAll.value = value
            checkList.value.forEach(( item ) => item.selected = value)
          }
        }))
      ]))
    }
  })
}