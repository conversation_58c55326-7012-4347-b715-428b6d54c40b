<template>
  <div class="scroll-y with-flex">
    <div style="overflow: hidden; padding-bottom: 24px;flex-shrink: 0;">
      <div class="rowBC" style="margin: 0 -12px">
        <div 
          v-for="(d ,index) in cardList"
          :key="index"
          class="card-item"
        >
          <div 
            class="audit-card"
            @click="handleJump(index)"
            :style="{ '--image': `url(${d.image})` }"
          >
            <div class="content">
              <div class="title"> {{ d.title }} </div>
              <div class="count"> {{ d.count }} </div>
              <div class="link"> 进入列表 </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <QueryForm
      :form-list="formList"
      :loading="loading"
      @search="handleSearch"
      @reset="reset"
      style="border-bottom: none;"
    />
    <div v-loading="loading" class="sticky-table">
      <CustomTable
        ref="tableRef"
        :columns="columns"
        :data="tableData"
        :table-method="getList"
      />
    </div>
  </div>
</template>

<script setup lang="tsx">
import { ref, reactive, toRef } from 'vue'
import { ElMessage, ElButton, ElMessageBox } from 'element-plus'
import { storeToRefs } from 'pinia'
import { useRoute, useRouter } from 'vue-router'
import { useParamsStore, useUserStroe } from '@/store'
import useLoading from '@/hooks/use-loading'
import useTryCatch from '@/hooks/use-try-catch'
import QueryForm from '@/components/QueryForm/index.vue'
import CustomTable from '@/components/CustomTable/index.vue'
import { getAuditList, getStat, taskRevoke } from '@/api/audit-management'
import type { searchParams, listItem } from '@/api/audit-management'
import Listener from '@/utils/site-channel-listeners'

import AuditApply from '@/assets/images/audit-apply.png'
import AuditTodo from '@/assets/images/audit-todo.png'
import AuditDone from '@/assets/images/audit-done.png'

defineOptions({ name: 'ReviewCenter' })

const route = useRoute()
const router = useRouter()
const { userList, site_id_all, channel_item_all } = storeToRefs(useParamsStore())
const { userInfo } = useUserStroe()
const { loading, setLoading } = useLoading(true)
/* 非响应式参数定义 */
const typeList = [
  {
    label: '发布系统',
    value: 1
  },
  {
    label: '对外协助',
    value: 2
  },
]
const statusList = [
  {
    label: '流程中',
    value: 2
  },
  {
    label: '已通过',
    value: 3
  },
  {
    label: '已拒绝',
    value: 4
  },
  {
    label: '已撤销',
    value: 5
  },
]
const classList = [
  {
    label: '自动审核',
    value: 1
  },
  {
    label: '人工审核',
    value: 2
  }
]
const queryParams_raw = {
  type: '',
  name: '',
  status: 2,
  user_name: '',
  handle_user_name: '',
  start_time: '',
  end_time: '',
  task_id: '',
  class: 2
}
/* 非响应式参数定义 */
/* 参数定义 */
const queryParams = reactive<searchParams>({
  ...queryParams_raw,
  site_id: site_id_all.value,
  channel_id: channel_item_all.value.channel_id,
})
const tableRef = ref()
const formList = ref([
  {
    label: '流程id',
    clearable: true,
    placeholder: '流程id',
    value: toRef(queryParams, 'task_id'),
    component: 'input',
  },
  {
    label: '业务类型',
    clearable: true,
    placeholder: '业务类型',
    value: toRef(queryParams, 'type'),
    component: 'select',
    selections: typeList
  },
  {
    label: '标题',
    clearable: true,
    placeholder: '标题',
    value: toRef(queryParams, 'name'),
    component: 'input',
    append: true,
  },
  {
    label: '审核状态',
    clearable: true,
    placeholder: '审核状态',
    value: toRef(queryParams, 'status'),
    component: 'select',
    selections: statusList
  },
  {
    label: '审核类型',
    clearable: true,
    placeholder: '审核类型',
    value: toRef(queryParams, 'class'),
    component: 'select',
    selections: classList
  },
  {
    label: '审核人',
    clearable: true,
    placeholder: '审核人',
    value: toRef(queryParams, 'handle_user_name'),
    component: 'select',
    labelKey: 'real_name',
    valueKey: 'real_name',
    selections: userList,
    append: true,
  },
  {
    label: '时间日期',
    clearable: true,
    start_placeholder: '开始日期',
    end_placeholder: '结束日期',
    placeholder: '审核人',
    value: '',
    component: 'datetimerange',
    append: true,
    dateChange: (value: [string, string] | null) => {
      if (value) {
        queryParams.start_time = value[0]
        queryParams.end_time = value[1]
      } else {
        queryParams.start_time = ''
        queryParams.end_time = ''
      }
    }
  }
])
const columns = ref([
  {
    title: '流程ID',
    dataKey: 'task_id',
    width: 200,
  },
  {
    title: '业务类型',
    dataKey: 'type_name',
  },
  {
    title: '标题',
    dataKey: 'name',
    minWidth: 300,
  },
  {
    title: '渠道名',
    dataKey: 'channel_name',
  },
  {
    title: '当前审核人',
    dataKey: 'handle_name',
  },
  {
    title: '审核状态',
    dataKey: 'status',
    cellRenderer: (scope: { row: listItem }) => {
      return (
        <span style={ { color: scope.row.status === 3 ? 'var(--el-color-success)' : scope.row.status === 4 ? 'var(--el-color-error)' : '' } }>
          {scope.row.status_name}
        </span>
      )
    }
  },
  {
    title: '申请人',
    dataKey: 'user_name',
  },
  {
    title: '申请时间',
    dataKey: 'add_time',
  },
  {
    title: '操作',
    dataKey: '',
    width: 200,
    cellRenderer: (scope: { row: listItem }) => {
      return (
        <>
          <ElButton
            type='primary'
            plain
            onClick={() => {
              handleJumpDetail(scope.row)
            }}
          >
            查看详情
          </ElButton>
          {
            (scope.row.status === 2 || scope.row.status === 5) && 
            <ElButton
              type={ scope.row.status === 5 ? 'info' : 'danger' }
              disabled={ scope.row.user_id !== userInfo.wsid || scope.row.status === 5 }
              onClick={() => handleRevoke(scope.row.task_id)}
              plain
              style='width: 72px;'
            >
              { scope.row.status === 5 ? '已撤销' : '撤销' }
            </ElButton>
          }
        </>
      )
    }
  },
])
const tableData = ref<listItem[]>([])
const cardList = ref([
  {
    title: '我的申请',
    count: 0,
    image: AuditApply
  },
  {
    title: '我的待办',
    count: 0,
    image: AuditTodo
  },
  {
    title: '我的已办',
    count: 0,
    image: AuditDone
  },
])

/* 参数定义 */
/* 方法定义 */
const getStats = () => {
  useTryCatch( async () => {
    const params = {
      q: {
        site_id: site_id_all.value,
        channel_id: channel_item_all.value.channel_id,
        user_id: userInfo.wsid as number,
      }
    }
    const res = await getStat(params)
    if (res.code === 200) {
      const { apply, handle } = res.data
      const sum = Object.values(apply as { draft_num: number; process_num: number; pass_num: number; reject_num: number }).reduce((prev, curr) => prev + curr, 0)

      cardList.value[0].count = sum
      cardList.value[1].count = handle.pending_num
      cardList.value[2].count = handle.done_num
    } else {
      ElMessage.error(res.msg)
    }
  })
}
const getList = ( { page, pageSize } ) => {
  useTryCatch( async () => {
    setLoading(true)
    const params = {
      q: { ...queryParams, site_id: site_id_all.value, channel_id: channel_item_all.value.channel_id },
      page,
      limit: pageSize
    }
    const res = await getAuditList(params)
    if (res.code === 200) {
      tableData.value = res.data.list
      tableRef.value.setTotal(res.data.total)
    } else {
      ElMessage.error(res.msg)
    }
    setLoading(false)
  }, () => setLoading(false))
}
const handleJump = (index: number) => {
  const routeParams = {
    name: index === 0 ? 'ReviewApply' : 'ReviewList',
    ...index === 0
    ? {}
    : {
      query: {
        typeName: index === 1 ? 'pending' : 'done'
      }
    }
  }
  router.push(routeParams)
}
const reset = () => {
  Object.entries(queryParams_raw).forEach(( [ key, value ] ) => {
    queryParams[key] = value
  })
}
const handleSearch = () => {
  tableRef.value.setPage(1)
  const { page, pageSize } = tableRef.value
  getList( { page, pageSize } )
}
const handleJumpDetail = (row: listItem) => {
  router.push({
    name: 'ReviewDetail',
    query: {
      id: row.task_id
    }
  })
}
const handleRevoke = (task_id: string) => {
  ElMessageBox.confirm('确认撤销当前流程吗?', '提示', {
    type: 'warning',
    callback: (action: string) => {
      if (action === 'confirm') {
        useTryCatch( async () => {
          setLoading(true)

          const res = await taskRevoke( { task_ids: task_id } )
          if (res.code === 200) {
            ElMessage.success('撤销成功!')
            handleSearch()
          } else {
            ElMessage.error(res.msg)
          }
          setLoading(false)
        }, () => setLoading(false) )
      }
    }
  })
}
/* 方法定义 */

// 监听渠道变化及缓存处理
Listener(route, () => {
  handleSearch()
  getStats()
})
// 初始化执行
getStats()
</script>

<style lang="scss">
.card-item {
  padding: 12px;
  width: 33.33%;
  max-width: calc(512px + 24px);
}
.audit-card {
  width: 100%;
  min-height: 120px;
  position: relative;
  background-image: var(--image);
  background-size: auto 100%;
  background-position: center;
  background-repeat: no-repeat;
  border-radius: 12px;
  overflow: hidden;
  cursor: pointer;
  .content {
    color: #fff;
    font-size: 14px;
    position: absolute;
    left: 0;
    top: 50%;
    width: 100%;
    padding-left: 24px;
    transform: translateY(-50%);
    .title {
      font-weight: 700;
      margin-bottom: 12px;
    }
    .count {
      font-weight: 700;
      font-size: 28px;
      margin-bottom: 24px;
    }
    .link {
      &::after {
        content: '>>';
        display: inline-block;
        animation-duration: 1s;
        animation-iteration-count: infinite;
      }
    }
  }
  &::after {
    content: '';
    display: block;
    padding-top: calc( 160 / 512 * 100% );
  }
  &:hover {
    .link {
      &::after {
        animation-name: jump-animaiton;
      }
    }
  }
}
</style>