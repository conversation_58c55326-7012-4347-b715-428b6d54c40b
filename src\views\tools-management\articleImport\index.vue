<template>
  <div class="scroll-y">
    <div v-show="channel_item_all.channel_id">
      <el-card shadow="never" style="max-width: 700px;">
        <template #default>
          <el-row align="middle" style="margin-bottom: 16px;">
            <el-col :span="5">
              <h3>导入文章类型：</h3>
            </el-col>
            <el-col :span="18">
              <el-radio-group v-model="uploadType">
                <el-radio label="word">
                  word格式 (业务场景：外包文章回收)
                </el-radio>
                <el-radio label="excel">
                  excel格式 (业务场景：内部文章迁移（变更模版，迁移站点）)
                </el-radio>
              </el-radio-group>
            </el-col>
          </el-row>
          <p v-show="uploadType === 'excel'"> 批量导入模板： <el-link type="primary" @click="handleDownload"> 下载批量导入模板 </el-link> (模板已更新,请重新下载) </p>
          <el-form ref="formRef" :model="formData" label-width="140px">
            <el-form-item label="模板类型:" prop="tpl_type">
              <el-select v-model="formData.tpl_type" disabled>
                <el-option 
                  v-for="(d, index) in tpl_list"
                  :key="index"
                  :label="d.label"
                  :value="d.value"
                />
              </el-select>
            </el-form-item>
            <el-form-item label="所选模板:" prop="tpl_id" :rules="{ required: true, message: '模板不能为空', trigger: 'change' }">
              <el-select v-model="formData.tpl_id" filterable>
                <el-option 
                  v-for="(d, index) in tempList"
                  :key="index"
                  :label="`${d.tpl_id} - ${d.name}`"
                  :value="d.tpl_id"
                />
              </el-select>
            </el-form-item>
          </el-form>

          <div v-show="uploadType === 'word'" class="mb-20px">
            <h3 class="mb-15px">导入文档指引：</h3>
            <el-form label-width="140px">
              <el-form-item  label="word文章导入模板:">
                <el-link type="primary" @click="handleDownloadWord"> word导入文档 </el-link>
              </el-form-item>
              <el-form-item  label="文档注意事项:">
                <el-link type="primary" @click="handleOuterJump"> 文档操作注意事项 </el-link>
              </el-form-item>
            </el-form>
          </div>

          <div v-show="uploadType === 'excel'" class="mb-30px pl-140px">
              <el-upload
                action="string"
                :auto-upload="false"
                :limit="1"
                :on-exceed="handleExceed"
                :on-remove="handleRemove"
                :on-change="handleChange"
                :file-list="fileList"
              >
                <el-button type="primary"> 点击上传 </el-button>
                <template #tip> <div class="el-upload__tip">只能上传xls/xlsx文件, 且不超过5M</div> </template>
              </el-upload>
            <template v-if="fileList.length > 0 && progress === 100">
              <el-progress :percentage="progress" />
              <div style="text-align: right; font-size: 12px; margin-top: 8px;">
                文件上传成功
              </div>
            </template>
          </div>

          <div v-show="uploadType === 'word'" class="mb-30px pl-140px">
              <el-upload
                action="string"
                accept=".docx"
                multiple
                :auto-upload="false"
                :limit="10"
                :on-exceed="handleExceed2"
                :on-change="handleChange2"
                :file-list="fileList2"
              >
                <el-button type="primary"> 上传文章 </el-button>
                <template #tip> <div class="el-upload__tip">请上传.docx文件,支持批量上传,单次上传上限为10篇</div> </template>
              </el-upload>
          </div>

          <div v-show="uploadType === 'excel'" class="text-center">
            <el-button type="primary" :loading="loading" :disabled="fileList.length === 0" @click="handleImport"> 导入 </el-button>
            <div style="font-size: 12px; padding-top: 12px;"> <span style="color: var(--el-color-primary);"> 注明：</span>文章批量导入方式由同步导入优化为异步导入，导入成功后会通过消息中心进行通知，请关注消息提醒。</div>
          </div>
          <div v-show="uploadType === 'word'" class="text-center">
            <el-button type="primary" :loading="loading" :disabled="fileList2.length === 0" @click="handleImport2"> 导入 </el-button>
            <div style="font-size: 12px; padding-top: 12px;"> <span style="color: var(--el-color-primary);"> 注明：</span>文章批量导入方式由同步导入优化为异步导入，导入成功后会通过消息中心进行通知，请关注消息提醒。</div>
          </div>
        </template>
      </el-card>
    </div>
    <el-result
      v-show="!channel_item_all.channel_id"
      icon="warning"
      title="请先选择具体渠道!" 
      sub-title="您还未选择渠道"
      class="custom-result custom-layout-box"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, h } from 'vue'
import { useRoute } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import { storeToRefs } from 'pinia'
import { useParamsStore } from '@/store'
import useTryCatch from '@/hooks/use-try-catch'
import useLoading from '@/hooks/use-loading'
import { getHost } from '@/hooks/use-env'
import { getListByType } from '@/api/page-management'
import { getAuthorList } from '@/api/article-management'
import { articleImport, wordBatchUpload } from '@/api/tools-management'
import type { listByType } from '@/api/page-management'
import type { authorListItem } from '@/api/article-management'
import type { UploadFile, UploadRawFile, FormInstance } from 'element-plus'
import emitter from '@/utils/bus'
import Listener from '@/utils/site-channel-listeners'
import { color } from 'echarts'

defineOptions ( { name: 'ArticleImport' } )

const route = useRoute()
const { site_id_all, channel_item_all } = storeToRefs(useParamsStore())
const { moduleTypeMap } = useParamsStore()
const { loading, setLoading } = useLoading()

const uploadType = ref('word')
const tpl_list = moduleTypeMap.article as {label:string;value:string}[]
const tempList = ref<listByType>([])
const authorList = ref<authorListItem[]>([])
const fileList = ref<UploadFile[]>([])
const fileList2 = ref<UploadRawFile[]>([])
const import_articles_file = ref<UploadRawFile|null>(null)
const progress = ref(0)
const formRef = ref<FormInstance | undefined>()

const formData = reactive({
  tpl_type: 'content',
  tpl_id: '',
})

const getAuthors = () => {
  useTryCatch( async () => {
    const params = {
      S: {
        language: '',
        author_id: '',
        site_id: site_id_all.value
      },
      page: 1,
      page_size: 1000
    }

    const res = await getAuthorList(params)
    if (res.code === 200) {
      authorList.value = res.data.items
    }
  } )
}

const handleTypeChange = () => {
  tempList.value = []
  useTryCatch( async () => {
    const params = {
      channel_id: channel_item_all.value.channel_id as number,
      type: formData.tpl_type,
      module: 'article'
    }
    const res = await getListByType(params)
    if (res.code === 200) {
      tempList.value = res.data
    } else {
      ElMessage.error(res.msg)
    }
  } )
}

const handleDownload = () => {
  const url = `${getHost()}/api/v1/tool/import_articles/tpl`
  location.href = url
}
const handleDownloadWord = () => {
  // const url = `${getHost()}/api/v1/tool/import_articles/tpl`
  // location.href = url
  window.open('https://www.wondershare.com/webcommon/word_to_html_template.docx', '_blank')
}
const handleOuterJump = () => {
  window.open('https://confluence.300624.cn/pages/viewpage.action?pageId=1391067425', '_blank')
}

const handleRemove = () => {
  fileList.value = []
  import_articles_file.value = null
}
const handleExceed = () => {
  ElMessage.warning('最多上传一个文件')
}
const handleChange = (file: UploadFile, list: UploadFile[]) => {
  fileList.value = list

  const type = file?.raw?.type
  const size = file.size as number
  const isMatch = type === 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' || type === 'application/vnd.ms-excel'
  const isLt5M = size / 1024 / 1024 < 5
  if (!isMatch) {
    return ElMessage.warning('只能上传xls/xlsx文件')
  }
  if (!isLt5M) {
    return ElMessage.warning('文件大小不能超过5M')
  }
  import_articles_file.value = file.raw as UploadRawFile
  progress.value = 100
}

const handleRemove2 = () => {
  fileList2.value = []
}
const handleExceed2 = () => {
  ElMessage.warning('最多上传10个文件')
}
const handleChange2 = (file: UploadFile, list: UploadFile[]) => {
  fileList2.value = list.map( file => file.raw as UploadRawFile )
}

const handleImport = async () => {
  if (formRef.value) {
    await formRef.value.validate(( valid ) => {
      if (valid) {
        useTryCatch( async () => {
        setLoading(true)
        const params = {
          ...formData,
          site_id: site_id_all.value,
          channel_id: channel_item_all.value.channel_id as number,
          import_articles_file: import_articles_file.value as UploadRawFile
        }

        const res = await articleImport(params)
        if (res.code === 200) {
          ElMessage.success(res.msg)
          formRef.value?.resetFields()
          handleRemove()
          emitter.emit('noticeUpdate')
        } else {
          ElMessage.error(res.msg)
        }
        setLoading(false)
      }, () => setLoading(false) )
      }
    })
  }
}
const handleImport2 = async () => {
  if (formRef.value) {
    await formRef.value.validate(( valid ) => {
      if (valid) {
        useTryCatch( async () => {
        setLoading(true)
        const params = {
          tpl_id: formData.tpl_id,
          site_id: site_id_all.value,
          channel_id: channel_item_all.value.channel_id as number,
        }

        const res = await wordBatchUpload(fileList2.value, params)
        if (res.code === 200) {
          ElMessage.success(res.msg)
          formRef.value?.resetFields()
          handleRemove2()
          emitter.emit('noticeUpdate')
        } else {
          ElMessage.error(res.msg)
        }
        if (res.data) {
          ElMessageBox({
            title: '导入成功',
            confirmButtonText: '确认',
            message: h('div', null, [
              h('p', { style: { marginBottom: '10px' } }, [
                h('span', null, '成功个数：'),
                h('strong', { style: { color: 'var(--el-color-success)' } }, res.data.success),
              ]),
              h('p', { style: { marginBottom: '10px' } }, [
                h('span', null, '失败个数：'),
                h('strong', { style: { color: 'var(--el-color-danger)' } }, res.data.fail),
              ]),
              h('p', null, [
                h('span', null, '失败原因：'),
                h('strong', null, res.data.fail_msg),
              ]),
            ]),
            callback: () => {
              
            }
          })
        }
        setLoading(false)
      }, () => setLoading(false) )
      }
    })
  }
}

if (channel_item_all.value.channel_id) {
  handleTypeChange()
}
Listener(route, () => {
  if (channel_item_all.value.channel_id) {
    handleTypeChange()
  }
})
</script>