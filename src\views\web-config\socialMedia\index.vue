<template>
  <div class="scroll-y with-flex">
    <QueryForm 
      :form-list="formList"
      :loading="loading"
      @search="handleSearch"
      @reset="handleReset"
    />
    <div v-loading="loading" class="sticky-table">
      <CustomTable 
        ref="tableRef"
        :operate-list="operateList"
        :columns="columns"
        :data="tableData"
        :table-method="getList"
      />
    </div>

    <OperateModal 
      v-if="loadModal"
      :row="currentRow"
      :readonly="readonly"
      @close="loadModal = false"
      @save="handleSearch"
    />
  </div>
</template>

<script setup lang="tsx">
import { ref, reactive, toRef, computed, defineAsyncComponent } from 'vue'
import { ElMessage, ElButton, ElMessageBox } from 'element-plus'
import { useRoute } from 'vue-router'
import { storeToRefs } from 'pinia'
import { useParamsStore, useTagsViewStore } from '@/store'
import useTryCatch from '@/hooks/use-try-catch'
import useLoading from '@/hooks/use-loading'
import { getCustomList, getAdList, customEnable, customDelete } from '@/api/web-config'
import { customQueryParams, customListItem, adItem, getCustomParams } from '@/api/web-config'
import Listener from '@/utils/site-channel-listeners'

import QueryForm from '@/components/QueryForm/index.vue'
import CustomTable from '@/components/CustomTable/index.vue'
const OperateModal = defineAsyncComponent( () => import( './components/operateModal.vue' ) )

defineOptions( { name: 'SocialMedia' } )

const route = useRoute()
const { loading, setLoading } = useLoading()
const { channel_item_all } = storeToRefs(useParamsStore())
const { userList } = useParamsStore()
const { changeViewTitle } = useTagsViewStore()

changeViewTitle( route, '定制列表' )

const queryParams_raw = {  
  position_id: '',
  title: '',
  system: '',
  add_user: '',
  country: '',
  device: '',
  scope_type: '',
  add_time_start: '',
  add_time_end: ''
}

const queryParams = reactive<customQueryParams>( {
  ...queryParams_raw
} )
const adList = ref<adItem[]>([])
const cascader = ref<number[]>([])
const deviceList = ref<any[]>([])
const countryList = ref<any[]>([])
const scopeTypeList = ref<any[]>([])
const timeRange = ref('')
const formList = ref([
  {
    placeholder: '推广类型/位置',
    value: cascader,
    component: 'cascader',
    selections: adList,
    props: { expandTrigger: 'hover', value: 'id', label: 'name', children: 'child' },
    hideClearable: true,
    handleChange: (value) => {
      if (value) {
        queryParams.position_id = value[1]
      } 
    }
  },
  {
    placeholder: '应用系统',
    value: toRef(queryParams, 'device'),
    component: 'select',
    selections: deviceList
  },
  {
    placeholder: '应用地区',
    value: toRef(queryParams, 'country'),
    component: 'select',
    selections: countryList,
    multiple: true
  },
  {
    placeholder: '应用范围',
    value: toRef(queryParams, 'scope_type'),
    component: 'select',
    selections: scopeTypeList
  },
  {
    placeholder: '创建人',
    value: toRef(queryParams, 'add_user'),
    component: 'select',
    labelKey: 'real_name',
    valueKey: 'user_name',
    selections: userList
  },
  {
    placeholder: '',
    start_placeholder: '开始日期',
    end_placeholder: '结束日期',
    value: timeRange,
    component: 'datetimerange',
    append: true,
    dateChange: (value: [string, string] | null) => {
      if (value) {
        queryParams.add_time_start = value[0]
        queryParams.add_time_end = value[1]
      } else {
        queryParams.add_time_start = ''
        queryParams.add_time_end = ''
      }
    }
  }
])

const tableRef = ref()
const tableData = ref<customListItem[]>([])
const loadModal = ref(false)
const readonly = ref(false)
const currentRow = ref<customListItem|null>(null)
const operateList = ref([
  {
    title: '新增配置组',
    button: true,
    append: true,
    method: () => {
      currentRow.value = null
      loadModal.value = true
      readonly.value = false
    },
    disabled: computed( () => !channel_item_all.value.channel_id ) 
  }
])
const columns = ref([
  {
    title: '配置组ID',
    dataKey: 'id',
    width: 150
  },
  {
    title: '标题',
    dataKey: 'title',
    minWidth: 200
  },
  {
    title: '应用系统',
    dataKey: 'device_name',
    width: 100
  },
  {
    title: '应用IP地区',
    dataKey: 'country_name',
    width: 150
  },
  {
    title: '应用范围',
    dataKey: 'scope_type_name',
    width: 150
  },
  {
    title: '创建人',
    dataKey: 'add_user_name',
    width: 100
  },
  {
    title: '创建时间',
    dataKey: 'add_time',
    width: 180
  },
  {
    title: '更新人',
    dataKey: 'edit_user_name',
    width: 100
  },
  {
    title: '更新时间',
    dataKey: 'edit_time',
    width: 180
  },
  {
    title: '状态',
    dataKey: 'line_state',
    width: 100,
    cellRenderer: ( scope: { row: customListItem } ) => (
      <span style={ { color: scope.row.line_state === 1 ? 'var(--el-color-success)' : 'var(--el-color-warning)' } }>
        { scope.row.line_state === 0 ? '未上线' : '已上线' }
      </span>
    )
  },
  {
    title: '操作',
    dataKey: '',
    width: 310,
    fixed: 'right',
    cellRenderer: ( scope: { row: customListItem } ) => (
      <>
        <ElButton type='primary' plain onClick={() => handleView(scope.row)}>查看</ElButton>
        <ElButton type='primary' plain onClick={() => handleEdit(scope.row)}>编辑</ElButton>
        <ElButton type='danger' plain onClick={() => handleDelete(scope.row)}>删除</ElButton>
        <ElButton type={ scope.row.line_state === 0 ? 'success' : 'warning' } plain onClick={() => handleOperate(scope.row)}>
          { scope.row.line_state === 0 ? '上线' : '下线' }
        </ElButton>
      </>
    )
  }
])

const handleReset = () => {
  timeRange.value = ''
  Object.entries(queryParams_raw).forEach(([ key, value ]) => {
    if (key !== 'position_id') {
      queryParams[key] = value
    }
  })
}

const refresh = () => {
  const { page, pageSize } = tableRef.value?.getPagination()
  getList({ page, pageSize })
}

const handleSearch = () => {
  tableRef.value?.setPage(1)
  refresh()
}

const getList = ( { page, pageSize } ) => {
  if (!channel_item_all.value.channel_id) {
    return ElMessage.warning('请先选择渠道')
  }
  useTryCatch( async () => {
    setLoading(true)

    if (adList.value.length === 0) {
      if (!sessionStorage.getItem('hasAdList')) {
        if (adList.value.length === 0) {
          const _res = await getAdList()
          if (_res.code === 200) {
            cascader.value = [_res.data[0].id, _res.data[0].child[0].id]
            adList.value = _res.data
            queryParams.position_id = cascader.value[1]

            sessionStorage.setItem('hasAdList', '1')
            sessionStorage.setItem('adList', JSON.stringify(_res.data))
          } else {
            ElMessage.error(_res.msg)
            setLoading(false)
            return
          }
        }
    } else {
        adList.value = JSON.parse(sessionStorage.getItem('adList') as string)
        cascader.value = [adList.value[0].id, adList.value[0].child[0].id]
        queryParams.position_id = cascader.value[1]
      }
    }
    
    const params= {
      ...queryParams,
      country: String(queryParams.country),
      device: String(queryParams.device),
      channel_id: channel_item_all.value.channel_id,
      page,
      page_size: pageSize
    }
    const res = await getCustomList(params)
    if (res.code === 200) {
      tableData.value = res.data.list
      tableRef.value?.setTotal(res.data.total)
    } else {
      ElMessage.error(res.msg)
    }

    setLoading(false)
  }, () => setLoading(false) )
}

const getParamList = () => {
  if (sessionStorage.getItem('hasConfList')) {
    countryList.value = JSON.parse(sessionStorage.getItem('countryList') as string)
    deviceList.value = JSON.parse(sessionStorage.getItem('deviceList') as string)
    scopeTypeList.value = JSON.parse(sessionStorage.getItem('scopeTypeList') as string)
    return
  }
  useTryCatch( async () => {
    const res = await getCustomParams()
    if (res.code === 200) {
      countryList.value = Object.entries(res.data.country).map( ( [ key, value ] ) => ( { label: value, value: key } ) )
      deviceList.value = Object.entries(res.data.device).map( ( [ key, value ] ) => ( { label: value, value: key } ) )
      scopeTypeList.value = Object.entries(res.data.scope_type).map( ( [ key, value ] ) => ( { label: value, value: key } ) )
      const popTimeList = Object.entries(res.data.pop_time).map( ( [ key, value ] ) => ( { label: value, value: key } ) )

      sessionStorage.setItem('hasConfList', '1')
      sessionStorage.setItem('countryList', JSON.stringify(countryList.value))
      sessionStorage.setItem('deviceList', JSON.stringify(deviceList.value))
      sessionStorage.setItem('scopeTypeList', JSON.stringify(scopeTypeList.value))
      sessionStorage.setItem('popTimeList', JSON.stringify(popTimeList))
    } else {
      ElMessage.error(res.msg)
    }
  } )
}

const handleView = ( row: customListItem ) => {
  currentRow.value = row
  loadModal.value = true
  readonly.value = true
}

const handleEdit = ( row: customListItem ) => {
  currentRow.value = row
  loadModal.value = true
  readonly.value = false
}

const handleDelete = ( row: customListItem ) => {
  ElMessageBox.confirm('请确认是否删除当前配置组', '删除', {
    confirmButtonText: '确认',
    cancelButtonText: '取消',
    type: 'warning',
    callback: (action: string) => {
      if (action === 'confirm') {
        useTryCatch( async () => {
          setLoading(true)
          const res = await customDelete(row.id)
          if (res.code === 200) {
            ElMessage.success('删除成功')
            refresh()
          } else {
            ElMessage.error(res.msg)
          }
          setLoading(false)
        }, () => setLoading(false) )
      }
    }
  })
}

const handleOperate = ( row: customListItem ) => {
  const statusText = row.line_state === 1 ? '下线' : '上线'
  ElMessageBox.confirm(`确定${statusText}"${row.title}"? ${statusText}后, 该配置内容将对应用页面生效`, `配置${statusText}` , {
    confirmButtonText: '确认',
    cancelButtonText: '取消',
    type: 'warning',
    callback: (action: string) => {
      if (action === 'confirm') {
        useTryCatch( async () => {
          setLoading(true)
          const res = await customEnable(row.id, row.line_state === 1 ? 0 : 1)
          if (res.code === 200) {
            ElMessage.success(res.msg)
            refresh()
          } else {
            ElMessage.error(res.msg)
          }
          setLoading(false)
        }, () => setLoading(false) )
      }
    }
  })
}

getParamList()
Listener( route, () => {
  if (channel_item_all.value.channel_id) {
    handleSearch()
  }
} )

</script>