<template>
  <el-dialog
    v-model="show"
    title="html填充模式"
    append-to-body
    align-center
    width="90%"
    :close-on-click-modal="false"
    @closed="emit('close')"
  >
    <template #default>
      <div v-show="step === 1">
        <div class="text-center" style="margin-bottom: 30px;">
          <el-radio-group v-model="mode">
            <el-radio label="free">自由填充模式</el-radio>
            <el-radio label="auto">自动填充模式</el-radio>
          </el-radio-group>
          <div v-show="mode === 'auto'">
            自动填充模式仅支持<strong>style、main、section、div、script</strong>五类标签, 使用前在标签起始处打上字段key标志即可 <br>
            例如: <strong>&lt;style key&gt;xxx&lt;/style&gt;</strong>; <strong>&lt;main key class="wsc-main"&gt;xxx&lt;/main&gt;</strong> <br>
            若匹配到内容将进行替换, 未匹配到内容将保留原有字段内容
            <div style="max-width: 500px; margin: 15px auto 0;">
              <div class="content-table el-table">
                <table>
                  <tr>
                    <td> 字段名称 </td>
                    <td> 字段key </td>
                  </tr>
                  <tr v-for="d in fieldMap">
                    <td> {{ d[1].input_label }} </td>
                    <td> {{ d[0] }} </td>
                  </tr>
                </table>
              </div>
            </div>
          </div>
        </div>
        <el-upload
          action="string"
          accept=".html"
          :auto-upload="false"
          :show-file-list="false"
          drag
          :on-change="handleFileChange"
        >
          <el-icon class="el-icon--upload"><upload-filled /></el-icon>
          <div class="el-upload__text">
            拖拽html到此处或 <em>点击上传</em>
          </div>
        </el-upload>
      </div>
      <div v-show="step > 1">
        <template v-if="mode === 'free'">
          <el-row :gutter="20">
            <el-col :span="12">
              <el-tabs v-model="activeName" @tab-change="handleTabChange">
                <el-tab-pane label="样式" name="style"></el-tab-pane>
                <el-tab-pane label="内容" name="content"></el-tab-pane>
                <el-tab-pane label="脚本" name="script"></el-tab-pane>
              </el-tabs>
              <el-table 
                ref="tableRef"
                height="600"
                :data="tableData" 
                @selection-change="handleSelectionChange"
              >
                <el-table-column type="selection" width="55" v-if="activeKey" />
                <el-table-column label=" ">
                  <template #default="scope">
                    <el-input v-model="scope.row" type="textarea" :rows="3" readonly />
                  </template>
                </el-table-column>
              </el-table>
            </el-col>
            <el-col :span="12">
              <div class="text-center">
                <el-radio-group v-model="activeKey" @change="handleRadioChange">
                  <el-radio v-for="d in fieldMap" :label="d[0]"> {{ d[1].input_label }} </el-radio>
                </el-radio-group>
              </div>
              <el-form label-suffix=":" label-width="100px">
                <el-form-item v-for="d in fieldMap" :label="d[1].input_label" :class="activeKey === d[0] ? 'active' : ''">
                  <el-input v-model="d[1].field_value" type="textarea" :rows="8" />
                </el-form-item>
              </el-form>
            </el-col>
          </el-row>
        </template>
        <template v-else>
          <el-form label-suffix=":" label-width="120px">
            <el-form-item v-for="d in fieldMap" :label="d[1].input_label">
              <el-input v-model="d[1].field_value" type="textarea" :rows="8" />
            </el-form-item>
          </el-form>
        </template>
      </div>
    </template>
    <template #footer>
      <div v-show="step > 1" class="text-center">
        <el-button @click="step = 1"> 返回上一步 </el-button>
        <el-button type="primary" @click="handleConfirm"> 确认 </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, nextTick } from 'vue'
import { ElMessage } from 'element-plus'
import { UploadFilled } from '@element-plus/icons-vue'
import type { fieldItem } from '@/api/template-management'
import type { UploadFile } from 'element-plus'

const props = defineProps<{
  tpl_fields: fieldItem[];
}>()
const emit = defineEmits(['close'])

const fieldMap = ref(new Map<string,fieldItem>())

props.tpl_fields.forEach((field) => {
  if (field.input_type === 'textarea') {
    fieldMap.value.set(field.field_name, { ...field })
  }
})

const mode = ref<'free'|'auto'>('free')
const step = ref(1)
const show = ref(true)
const activeName = ref('style')
const activeKey = ref('')

const styles = ref<string[]>([])
const scripts = ref<string[]>([])
const contents = ref<string[]>([])
const tableData = ref<string[]>([])
const tableRef = ref()
const changing = ref(false)

const regLink = /<link\s+(href="(.*?)"\s+rel="stylesheet"|rel="stylesheet"\s+href="(.*?)")(\s*?)\/?>/g
const regSrcScript = /<script\s+src="(.*?)"\s?><\/script>/g
const regStyle = /<style>([\s\S]*?)<\/style>/g
const regScript = /<script>([\s\S]*?)<\/script>/g
const regMain = /<main\s?([\s\S]*?)>([\s\S]*?)<\/main>/g
const tags = [ 'style', 'main', 'section', 'div', 'script' ]
const regGenerate = ( tag: string, key: string) => new RegExp(`<${tag}\\s+${key}([\\s\\S]*?)>([\\s\\S]*?)<\/${tag}>`, 'g')

const handleFileChange = (e: UploadFile) => {
  const reader = new FileReader()
  reader.readAsText(e.raw as Blob)
  reader.onloadend = function() {
    const content = reader.result as string
    if (mode.value === 'free') {
      handleResolveManuel(content)
    } else {
      handleResolveAuto(content)
    }
    step.value++
  }
}

const handleResolveManuel = (content: string) => {
  const links = content.match(regLink) || []
  const srcScripts = content.match(regSrcScript) || []
  const _styles = content.match(regStyle) || []
  const _scripts = content.match(regScript) || []
  const _contents = content.match(regMain) || []

  const keys = [...fieldMap.value.keys()].join('|')
  const reg = new RegExp(`\\s+(${keys})`)

  styles.value = [...links.map((text: string) => text.replace(reg, '')), ..._styles.map((text: string) => text.replace(reg, ''))]
  scripts.value = [...srcScripts.map((text: string) => text.replace(reg, '')), ..._scripts.map((text: string) => text.replace(reg, ''))]
  contents.value = [..._contents.map((text: string) => text.replace(reg, ''))]

  tableData.value = styles.value
}

const handleResolveAuto = (content: string) => {
  const resolveMap = new Map<string, string[]>();
  [...fieldMap.value.keys()].forEach((key) => {
    tags.forEach((tag) => {
      const reg = regGenerate(tag, key)
      const _contents = content.match(reg) || []
      const value = resolveMap.get(key) || []
      resolveMap.set(key, [...value, ..._contents.map((text: string) => text.replace(new RegExp(`\\s+${key}`), ''))])
    })
    const field = fieldMap.value.get(key)
    const resolveList = resolveMap.get(key) as string[]
    if (field && resolveList.length > 0) {
      field.field_value = resolveList.join('\n')
    }
  })
}

const handleChanging = () => {
  changing.value = true
  nextTick( () => {
    changing.value = false
  } )
}

const handleTabChange = () => {
  const list = activeName.value === 'style' ? styles.value : activeName.value === 'script' ? scripts.value : contents.value
  handleChanging()
  tableData.value = list
  activeKey.value = ''
}

const handleSelectionChange = (selection: string[]) => {
  const field = fieldMap.value.get(activeKey.value)
  if (field && !changing.value) {
    field.field_value = selection.join('\n')
  }
}

const handleRadioChange = () => {
  handleChanging()
  tableRef.value?.clearSelection()
}

const handleConfirm = () => {
  props.tpl_fields.forEach((item) => {
    const field = fieldMap.value.get(item.field_name)
    if (field) {
      item.field_value = field.field_value
    }
  })
  show.value = false
}

if (fieldMap.value.size === 0) {
  show.value = false
  ElMessage.warning('未检测到有可填充字段,请手动操作')
}
</script>

<style scoped lang="scss">
:deep(.active) {
  .el-form-item__label {
    color: var(--el-color-primary)
  }
}
</style>