<template>
  <el-dialog
    v-model="show"
    title="购买页信息字段历史"
    width="90%"
    append-to-body
    align-center
    :close-on-click-modal="false"
    @closed="() => emit('close')"
  >
    <el-form
      ref="formRef"
      label-suffix=":"
      label-width="140px"
    >
      <el-form-item label="卡片数">
        <el-select v-model="buyInfo.card_num" disabled>
          <el-option v-for="n in card_num" :key="n" :value="n" :label="n" />
        </el-select>
      </el-form-item>
      <el-form-item label="拓展卡片判别">
        <el-checkbox-group disabled v-model="ip_option" >
          <el-checkbox label="default" disabled>基础判别</el-checkbox>
          <el-checkbox label="ip_option" >ip判别</el-checkbox>
          <el-checkbox label="tab_switch" >tab切换</el-checkbox>
        </el-checkbox-group>
      </el-form-item>

      <div v-for="(card, key, index) in buyInfo.card_detail" :key="key"
        class="mb-20px"
        style="border-radius: 10px; border: 1px solid var(--el-border-color); padding: 16px;"
        v-show="buyInfo.card_num > index"
      >
        <h2 class="mb-20px">卡片{{ index + 1 }}</h2>
        <div class="mb-15px rowBC">
          <h3>基础购买链接组合-<span style="color: var(--el-color-primary);">基础判别</span></h3>
        </div>
        <el-table
          :data="card.default"
          border
        >
          <el-table-column label="Sku id" prop="sku_id" />
          <el-table-column label="Sku名称" prop="sku_name" />
          <el-table-column label="销售价格" prop="" />
          <el-table-column label="额外参数" prop="" />
          <el-table-column label="活动ID" prop="activity_id" />
          <el-table-column label="捆绑产品sku" prop="additional_sku" />
          <el-table-column label="是否ab test" prop="url_ab_testing" />
          <el-table-column label="购买链接" prop="url" width="500" />
        </el-table>
        <div v-show="buyInfo.ip_option.indexOf('ip_option') > -1">
          <div style="padding-top: 20px;" class="mb-15px rowBC">
            <h3>拓展判别组-<span style="color: var(--el-color-danger);">ip判别</span></h3>
          </div>
          <el-table
            :data="card.ip_option"
            border
          >
            <el-table-column label="Sku id" prop="sku_id" />
            <el-table-column label="Sku名称" prop="sku_name" />
            <el-table-column label="销售价格" prop="" />
            <el-table-column label="额外参数" prop="" />
            <el-table-column label="活动ID" prop="activity_id" />
            <el-table-column label="捆绑产品sku" prop="additional_sku" />
            <el-table-column label="是否ab test" prop="url_ab_testing" />
            <el-table-column label="购买链接" prop="url" width="500" />
          </el-table>
        </div>
        <div v-show="buyInfo.ip_option.indexOf('tab_switch') > -1">
          <div style="padding-top: 20px;" class="mb-15px rowBC">
            <h3>拓展判别组-<span style="color: var(--el-color-danger);">tab切换</span></h3>
          </div>
          <el-table
            :data="card.tab_switch"
            border
          >
            <el-table-column label="Sku id" prop="sku_id" />
            <el-table-column label="Sku名称" prop="sku_name" />
            <el-table-column label="销售价格" prop="" />
            <el-table-column label="额外参数" prop="" />
            <el-table-column label="活动ID" prop="activity_id" />
            <el-table-column label="捆绑产品sku" prop="additional_sku" />
            <el-table-column label="是否ab test" prop="url_ab_testing" />
            <el-table-column label="购买链接" prop="url" width="500" />
          </el-table>
        </div>
      </div>
    </el-form>

  </el-dialog>
</template>

<script setup lang="ts">
import { ref, reactive } from 'vue'
import type { buyInfo } from '@/api/buy-management'

const show = ref(true)

const emit = defineEmits(['save', 'close'])
const props = defineProps<{
  buyInfo?: buyInfo|null;
}>()

const ip_option = ref<string[]>([])

const buyInfo_raw = {
  card_num: 1,
  card_type: '',
  ip_option: 'default',
  card_detail: {
    card_1: {
      default: [],
      ip_option: [],
      tab_switch: [],
    }
  }
}

const card_num = ref(10)
const buyInfo = reactive<buyInfo>({
  ...props.buyInfo ? JSON.parse(JSON.stringify(props.buyInfo)) : buyInfo_raw
})

ip_option.value = buyInfo.ip_option.split(',')


</script>