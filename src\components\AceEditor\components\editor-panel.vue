<template>
  <el-config-provider :locale="zh">
    <transition name="fade" @after-leave="handleLeave" appear>
      <div 
        v-if="show"
        class="ace-editor-panel-container" 
        :class="{ 'ace-editor-modal': showModal }"
      >
        <div class="ace-editor-panel" :class="{ fullscreen: fullScreen }">
          <div class="top-func-bar">
            <div class="ace_editor-func-box" :class="{ hide: showCodeSplit }">
              <el-space>
                <div>
                  主题:
                  <el-select v-model="theme" popper-class="top-index" @change="handleThemeChange">
                    <el-option 
                      v-for="(d, index) in themeList"
                      :key="index"
                      :label="d.label"
                      :value="d.value"
                    />
                  </el-select>
                </div>
                <div>
                  字号:
                  <el-select v-model="fontSize" popper-class="top-index" @change="handleFontChange">
                    <el-option 
                      v-for="(d, index) in fontList"
                      :key="index"
                      :label="d.label"
                      :value="d.value"
                    />
                  </el-select>
                </div>
                <div>
                  拆分编辑器:
                  <el-select v-model="split" popper-class="top-index" @change="handleSplitChange">
                    <el-option 
                      v-for="(d, index) in splitList"
                      :key="index"
                      :label="d.label"
                      :value="d.value"
                    />
                  </el-select>
                </div>
                <el-button type="primary" @click="handleFind"> 查找 </el-button>
                <el-button type="primary" @click="showInsertBlock = true"> 插入代码块 </el-button>
              </el-space>
            </div>
            <div class="ace_editor-operate-box">
              <el-space style="margin-right: -8px;" :size="8">
                <SvgIcon 
                  style="width: 20px; cursor: pointer;" 
                  :icon-class=" !fullScreen ? 'fullscreen' : 'exit-fullscreen' " 
                  @click="toggleFullscreen"
                />
                <template v-if="!hideClose">
                  <Close style="width: 20px; cursor: pointer;" @click="handleClose" />
                </template>
              </el-space>
            </div>
          </div>
      
          <div :style="{ width: '100%', height: 'calc(100% - 104px)' }">
            <AceEditor 
              ref="AceEditorRef"
              :code="code"
              @change="handleCodeChange"
            />
          </div>
      
          <div class="bottom-func-bar text-center" v-show="!showCodeSplit">
            <template v-if="!hideBottom">
              <el-button type="primary" @click="handleSave"> 保存 </el-button>
              <el-button type="primary" @click="handleCompare"> 比较 </el-button>
              <el-button type="primary" @click="handleReset"> 还原 </el-button>
              <el-button type="primary" @click="handleCancel"> 取消 </el-button>
              <!-- <el-button type="primary"> 预览编辑模式 </el-button> -->
              <el-button type="primary" @click="handleShowCodeSplit"> 代码文本分离 </el-button>
            </template>
          </div>
      
          <div class="diff-panel" :style="{ transform: `translateX(${ showDiff ? '0' : '100%' })` }">
            <div class="diff-container">
              <AceDiff
                v-if="loadDiff"
                ref="AceDiffRef"
                v-bind="{ leftVal: code, rightVal: editCode, fontSize }"
              />
            </div>
            <div class="bottom-func-bar text-center">
              <el-button type="primary" :icon="Back" @click="handleCompare(false)"> 返回 </el-button>
            </div>
          </div>
  
          <template v-if="showCodeSplit">
            <div class="code-split-panel" style="top: 52px; height: calc(100% - 104px)" >
              <CodeSplit 
                ref="CodeSplitRef"
                :html-str="editCode"
                open-excel
                @success="ElMessage.success('导入成功')"
                @fail="ElMessage.error('导入失败')"
              />
            </div>
            <div class="bottom-func-bar text-center">
              <el-button type="primary" @click="handleCodeSplitSave"> 保存 </el-button>
              <el-button type="primary" @click="handleImportExcel"> 导入Excel </el-button>
              <el-button type="primary" @click="handleExportExcel"> 导出Excel </el-button>
            </div>
          </template>
        </div>
  
        <InsertBlock v-if="showInsertBlock" @close="showInsertBlock = false" @insert="handleInsert"/>
      </div>
    </transition>
  </el-config-provider>
</template>

<script setup lang="ts">
import { ref, nextTick, defineAsyncComponent, h } from 'vue'
import { ElMessageBox, ElMessage, ElSelect, ElOption } from 'element-plus'
import zh from 'element-plus/es/locale/lang/zh-cn'
import { Close, Back } from '@element-plus/icons-vue'
import { useConfigStore } from '@/store/config'

import SvgIcon from '@/icons/SvgIcon.vue'
import AceEditor from './editor.vue'
import AceDiff from './ace-diff.vue'

const CodeSplit = defineAsyncComponent(() => import('@/components/CodeSplit/index.vue'))
const InsertBlock = defineAsyncComponent(() => import('@/views/block-management/components/insertBlock.vue'))

const props = withDefaults(defineProps<{
  hideBottom?: boolean;
  hideClose?: boolean;
  code: string;
  url?: string;
  save?: Function;
  change?: Function;
  closed?: Function;
  showModal?: boolean;
}>(), {

})

const emit = defineEmits(['close', 'save'])
const CodeSplitRef = ref()
const showCodeSplit = ref(false)
const showInsertBlock = ref(false)

const { theme: configTheme } = useConfigStore()

const themeList = [
  {
    label: '白色',
    value: 'light',
  },
  {
    label: '黑色',
    value: 'dark',
  }
]
const fontList = [
  {
    label: '极小',
    value: 12,
  },
  {
    label: '小',
    value: 14,
  },
  {
    label: '常规',
    value: 16,
  },
  {
    label: '大',
    value: 20,
  },
  {
    label: '极大',
    value: 24,
  }
]
const splitList = [
  {
    label: '单编辑器模式',
    value: 'none',
  },
  {
    label: '水平模式',
    value: 'beside',
  },
  {
    label: '垂直模式',
    value: 'below',
  }
]

const show= ref(true)
const theme = ref(configTheme === 'dark' ? configTheme : 'light')
const fontSize = ref(16)
const split = ref('none')
const fullScreen = ref(false)
const AceEditorRef = ref()
const editCode = ref(props.code)
const AceDiffRef = ref()
const loadDiff = ref(false)
const showDiff = ref(false)

const handleThemeChange = (value: string) => {
  AceEditorRef.value?.handleTheme(value)
}
const handleFontChange = (value: number) => {
  AceEditorRef.value?.handleFontSize(value)
}
const handleSplitChange = (value: string) => {
  AceEditorRef.value?.handleSplit(value)
}
const handleFind = () => {
  AceEditorRef.value?.showFindWidget()
}
const toggleFullscreen = () => {
  fullScreen.value = !fullScreen.value
  nextTick(() => {
    AceEditorRef.value?.handleResize()
  })
}
const handleClose = () => {
  if (showCodeSplit.value) {
    showCodeSplit.value = false
  } else {
    show.value = false
  }
}
const handleLeave = () => {
  props.closed && props.closed()
  emit('close')
}
const handleSave = () => {
  const value = AceEditorRef.value?.getValue()
  props.save && props.save(value)
  emit('save', value)
  handleClose()
}
const handleCodeChange = (val: string) => {
  editCode.value = val
  props.change && props.change(val)
}
const handleCompare = (open = true) => {
  loadDiff.value = open
  showDiff.value = open
}
const handleReset = () => {
  ElMessageBox.confirm(
    '此操作将还原代码至打开状态，请确认是否继续？',
    '提示',
    {
      type: 'warning',
      callback: (action: string) => {
        if (action === 'confirm') {
          editCode.value = props.code
          AceEditorRef.value?.setValue(props.code)
        }
      }
    }
  )
  
}
const handleCancel = () => {
  handleClose()
  emit('close')
}

const handleShowCodeSplit = () => {
  if (AceEditorRef.value?.hasError()) {
    return ElMessage.warning('代码存在报错, 请修复后再进行此操作')
  }
  showCodeSplit.value = true
}
const handleCodeSplitSave = () => {
  showCodeSplit.value = false
  const htmlStr = CodeSplitRef.value?.getCompletHtmlStr()
  if (htmlStr) {
    editCode.value = htmlStr
    AceEditorRef.value?.setValue(htmlStr)
  }
}
const handleImportExcel = () => {
  CodeSplitRef.value?.handleImport()
}
const handleExportExcel = () => {
  const exportType = ref(1)
  const typeList: { label: string; value: number }[] = CodeSplitRef.value?.exportTypeList || []
  ElMessageBox({
    title: '请选择需要导出的类型',
    showConfirmButton: true,
    showCancelButton: true,
    confirmButtonText: '确认',
    cancelButtonText: '取消',
    message: () => h(ElSelect, {
      placeholder: '请选择导出类型',
      modelValue: exportType.value,
      onChange: (value) => exportType.value = value
    }, () => typeList.map( ( { label, value } ) => h(ElOption, {
      label,
      value
    }) )),
    callback: (action: string) => {
      if (action === 'confirm') {
        CodeSplitRef.value?.handleExport(exportType.value)
      }
    }
  })
}

const handleInsert = (content: string, row = 0) => {
  AceEditorRef.value?.handleInsert({row, column: 0}, content)
  showInsertBlock.value = false
  ElMessage.success('插入成功')
}
</script>

<style>
.top-index {
  z-index: 1051 !important;
}
.ace-editor-panel-container {
  height: 100%;
}
.ace-editor-modal {
  position: fixed;
  width: 100%; 
  height: 100%; 
  background-color: rgba(0,0,0,.3);
  right: 0; 
  bottom: 0; 
  z-index: 1049;
  padding: 50px;
}
.ace-editor-panel {
  height: 100%;
  background-color: var(--el-bg-color);
  border: 1px solid var(--el-border-color-light);
  border-radius: 4px;
  position: relative;
  overflow: hidden;
}
.ace-editor-panel.fullscreen {
  position: fixed;
  width: 100vw;
  height: 100vh;
  left: 0;
  top: 0;
  z-index: 1050;
}
.top-func-bar {
  display: flex;
  align-items: center;
  justify-content: space-between;
  font-size: 14px;
}
.ace_editor-func-box.hide {
  opacity: 0;
  pointer-events: none;
}
.bottom-func-bar {
  display: flex;
  align-items: center;
  justify-content: center;
}
.top-func-bar, .bottom-func-bar {
  padding: 8px 12px;
  height: 52px;
  background-color: var(--el-bg-color);
}
.diff-panel, .code-split-panel {
  position: absolute;
  left: 0;
  top: 0;
  z-index: 10;
  width: 100%;
  height: calc(100% - 52px);
  transition: transform .5s;
  background-color: var(--el-bg-color);
}
.diff-panel .diff-container {
  height: 100%;
}
</style>