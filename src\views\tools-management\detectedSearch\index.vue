<template>
  <div class="scroll-y with-flex">
    <QueryForm
      :form-list="formList"
      :loading="loading"
      @search="handleSearch"
      hide-reset
    />
    <el-tabs v-model="queryParams.entity_type" @tab-change="handleTabChange">
      <el-tab-pane label="文章" name="article">
        <template #label>
          <el-badge :value="totalMap.article" :hidden="!initData">文章</el-badge>
        </template>
      </el-tab-pane>
      <el-tab-pane label="模板" name="template">
        <template #label>
          <el-badge :value="totalMap.template" :hidden="!initData">模板</el-badge>
        </template>
      </el-tab-pane>
      <el-tab-pane label="页面" name="page">
        <template #label>
          <el-badge :value="totalMap.page" :hidden="!initData">页面</el-badge>
        </template>
      </el-tab-pane>
      <el-tab-pane label="块" name="block">
        <template #label>
          <el-badge :value="totalMap.block" :hidden="!initData">块</el-badge>
        </template>
      </el-tab-pane>
    </el-tabs>
    <div v-loading="loading" class="sticky-table" style="min-height: 300px;">
      <CustomTable
        ref="tableRef"
        :columns="columns"
        :operate-list="opreateList"
        :data="tableData"
        :table-method="getList"
      />
    </div>
  </div>
</template>

<script setup lang="tsx">
import { ref, reactive, toRef, computed } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import { storeToRefs } from 'pinia'
import { useParamsStore } from '@/store'
import { useBasicStore } from '@/store/basic'
import useLoading from '@/hooks/use-loading'
import useTryCatch from '@/hooks/use-try-catch'
import { getDownloadLocalToken } from '@/hooks/use-local-auth'
import { getHost } from '@/hooks/use-env'
import { detectedSearchList, getTotalNum } from '@/api/tools-management'
import type { detectedSearchListItem, detectedSearchQueryParams } from '@/api/tools-management'
import Listener from '@/utils/site-channel-listeners'
import { stringify } from '@/utils/common'

import QueryForm from '@/components/QueryForm/index.vue'
import CustomTable from '@/components/CustomTable/index.vue'

defineOptions ( { name: 'DetectedSearch' } )

const route = useRoute()
const router = useRouter()
const { site_id_all, channel_item_all } = storeToRefs(useParamsStore())
const basicStore = useBasicStore()
const { loading, setLoading } = useLoading()

const queryParams = reactive<detectedSearchQueryParams>({
  site_id: site_id_all.value,
  content: '',
  entity_type: 'article',
  page: 1,
  page_size: 10,
})

const entityMap = {
  'article': {
    id: '文章ID',
    name: '文章标题'
  },
  'template': {
    id: '模板ID',
    name: '模板名称'
  },
  'page': {
    id: '页面ID',
    name: '页面URL'
  },
  'block': {
    id: '块ID',
    name: '块名称'
  }
}
const routerMap = {
  'article': 'ArticleManageList',
  'template': 'TemplateManage',
  'page': 'PageManagement',
  'block': 'BlockManagement'
}
const initData = ref(false)
const totalMap = reactive({
  article: 0,
  template: 0,
  page: 0,
  block: 0,
})
const entity_ids = ref('')

const formList = ref([
  {
    label: '搜索内容',
    clearable: true,
    placeholder: '搜索内容, 不得少于5字节',
    value: toRef(queryParams, 'content'),
    component: 'input',
  },
])

const tableData = ref<detectedSearchListItem[]>([])
const tableRef = ref()
const columns = ref([
  {
    title: '所属渠道',
    dataKey: 'channel_code',
    width: 150,
    cellRenderer: ( scope: { row: detectedSearchListItem } ) => (<span style={ { color: scope.row.color } }> { scope.row.channel_code } </span>)
  },
  {
    title: computed(() => entityMap[queryParams.entity_type].id),
    dataKey: 'id',
    width: 150,
  },
  {
    title: computed(() => entityMap[queryParams.entity_type].name),
    dataKey: 'name',
    minWidth: 300,
  },
])
const opreateList = ref([
  {
    title: '导出搜索结果',
    method: () => {
      if (tableRef.value?.getTotal === 0) {
        ElMessage.warning('暂无可导出数据')
        return
      }
      
      const params = {
        ...queryParams,
        site_id: site_id_all.value,
        channel_id: channel_item_all.value.channel_id || ''
      }
      const qsParams = stringify(params)
      location.href = `${getHost()}/api/v1/tool/content?&ws-login-token=${getDownloadLocalToken()}&${qsParams}&export=1`
    },
  },
  {
    title: '查看搜索结果(当前分页)',
    method: () => {
      const name = routerMap[queryParams.entity_type]
      basicStore.delCachedView(name)
      router.push({
        name,
        query: {
          entity_ids: entity_ids.value
        }
      })
    },
  },
])

const handleSearch = () => {
  tableRef.value?.setPage(1)
  const { page, pageSize } = tableRef.value?.getPagination()
  getList( { page, pageSize } )
  getTotal()
}

const handleTabChange = () => {
  handleSearch()
}

const getList = ( { page, pageSize }, key?: 'article'|'template'|'page'|'block' ) => {
  if (!queryParams.content) {
    return
  }
  useTryCatch( async () => {
    setLoading(true)

    const params = {
      ...queryParams,
      site_id: site_id_all.value,
      channel_id: channel_item_all.value.channel_id,
      page,
      page_size: pageSize
    }
    if (key) {
      params.entity_type = key
    }
    const res = await detectedSearchList(params)
    if (res.code === 200) {
      tableData.value = res.data.entity_detail
      entity_ids.value = res.data.entity_ids
      tableRef.value?.setTotal(res.data.total)
    } else {
      ElMessage.error(res.msg)
    }
    initData.value = true
    setLoading(false)
  }, () => setLoading(false) )
}

const getTotal = () => {
  useTryCatch( async () => {
    const params = {
      site_id: site_id_all.value,
      content: queryParams.content,
      channel_id: channel_item_all.value.channel_id
    }

    const res = await getTotalNum(params)
    if (res.code === 200) {
     const data = res.data
     Object.entries(data).forEach(( [ key, value ] ) => {
      totalMap[key] = value
     })
    }
  } )
}

Listener(route, () => {
  handleSearch()
})
</script>

<style scoped>
:deep(.el-badge) {
  height: 20px;
  line-height: 20px;
}
:deep(.el-badge__content.is-fixed) {
  right: 0;
}
</style>