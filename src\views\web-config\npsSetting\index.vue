<template>
  <div class="scroll-y with-flex">
    <div v-loading="loading" class="sticky-table">
      <CustomTable
        ref="tableRef"
        :columns="columns"
        :data="tableData"
        :table-method="getList"
        :operate-list="operateList"
      />
    </div>

    <OperateModal 
      v-if="loadModal"
      :row="currentRow"
      @close="loadModal = false"
      @save="handleSearch"
    />
  </div>
</template>

<script setup lang="tsx">
import { ref, defineAsyncComponent } from 'vue'
import { ElMessage, ElButton, ElMessageBox } from 'element-plus'
import useTryCatch from '@/hooks/use-try-catch'
import useLoading from '@/hooks/use-loading'
import { getNpsList, enableNps, disableNps } from '@/api/web-config'
import type { npsListItem } from '@/api/web-config'

import CustomTable from '@/components/CustomTable/index.vue'
const OperateModal = defineAsyncComponent( () => import( './components/operateModal.vue' ) )

defineOptions( { name: 'NpsSetting' } )

const { loading, setLoading } = useLoading()

const tableRef = ref()
const columns = ref([
  {
    title: '策略ID',
    dataKey: 'id',
    width: 100,
  },
  {
    title: '策略名称',
    dataKey: 'name',
    minWidth: 150,
  },
  {
    title: '状态',
    dataKey: 'status',
    width: 100,
    cellRenderer: (scope: { row: npsListItem }) => (
      <span style={ { color: scope.row.status === 1 ? 'var(--el-color-success)' : 'var(--el-color-danger)'} }>
        { scope.row.status === 1 ? '启用' : '禁用' }
      </span>
    )
  },
  {
    title: '编辑时间',
    dataKey: 'edit_time',
    width: 180,
  },
  {
    title: '编辑人',
    dataKey: 'edit_user',
    width: 100,
  },
  {
    title: '操作',
    dataKey: '',
    width: 180,
    cellRenderer: (scope: { row: npsListItem }) => (
      <>
        <ElButton type='primary' plain onClick={() => handleEdit(scope.row)}> 编辑 </ElButton>
        <ElButton 
          type={ scope.row.status === 1 ? 'danger' : 'success' } 
          plain 
          onClick={() => { scope.row.status === 1 ? handleDisable(scope.row.id) : handleEnable(scope.row.id) }}
        >
          { scope.row.status === 1 ? '禁用' : '启用' }
        </ElButton>
      </>
    )
  },
])
const operateList = ref([
  {
    title: '新增',
    button: true,
    append: true,
    method: () => {
      currentRow.value = null
      loadModal.value = true
    }
  }
])

const tableData = ref<npsListItem[]>([])
const loadModal = ref(false)
const currentRow = ref<npsListItem|null>(null)

const handleRefresh = () => {
  const { page, pageSize} = tableRef.value?.getPagination()
  getList( { page, pageSize } )
}

const handleSearch = () => {
  tableRef.value?.setPage(1)
  handleRefresh()
}

const getList = ( { page, pageSize } ) => {
  useTryCatch( async () => {
    setLoading(true)

    const res = await getNpsList( { page, pageSize } )
    if (res.code === 200) {
      tableData.value = res.data.list
      tableRef.value?.setTotal(res.data.total)
    } else {
      ElMessage.error(res.msg)
    }

    setLoading(false)
  }, () => setLoading(false) )
}

const handleEdit = (row: npsListItem) => {
  currentRow.value = row
  loadModal.value = true
}

const handleEnable = (id: number) => {
  ElMessageBox.confirm('请确认是否启用', '提示', {
    confirmButtonText: '确认',
    cancelButtonText: '取消',
    type: 'warning',
    callback: (action: string) => {
      if (action === 'confirm') {
        useTryCatch( async () => {
          
          setLoading(true)

          const res = await enableNps( id )
          if (res.code === 200) {
            ElMessage.success('启用成功')
            handleRefresh()
          } else {
            ElMessage.error(res.msg)
          }

          setLoading(false)
        } )
      }
    }
  })
}

const handleDisable = (id: number) => {
  ElMessageBox.confirm('请确认是否禁用', '提示', {
    confirmButtonText: '确认',
    cancelButtonText: '取消',
    type: 'warning',
    callback: (action: string) => {
      if (action === 'confirm') {
        useTryCatch( async () => {
          
          setLoading(true)

          const res = await disableNps( id )
          if (res.code === 200) {
            ElMessage.success('禁用成功')
            handleRefresh()
          } else {
            ElMessage.error(res.msg)
          }

          setLoading(false)
        } )
      }
    }
  })
}
</script>