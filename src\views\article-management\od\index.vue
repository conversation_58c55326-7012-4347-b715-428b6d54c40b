<template>
  <div class="scroll-y with-flex">
    <div v-loading="loading" class="sticky-table">
      <CustomTable 
        ref="tableRef"
        :columns="columns"
        :data="tableData"
        :table-method="getList"
      />
    </div>
  </div>
</template>

<script setup lang="tsx">
import { ref, getCurrentInstance } from 'vue'
import { useRoute } from 'vue-router'
import { ElButton, ElMessage, ElMessageBox } from 'element-plus'
import { storeToRefs } from 'pinia'
import { useParamsStore } from '@/store'
import useTryCatch from '@/hooks/use-try-catch'
import useLoading from '@/hooks/use-loading'
import { getOdArticleList, auditOdArticle } from '@/api/article-management'
import type { odArticleItem } from '@/api/article-management'
import type { ComponentInternalInstance } from 'vue'
import Listener from '@/utils/site-channel-listeners'

import CustomTable from '@/components/CustomTable/index.vue'

defineOptions( { name: 'OdArticleList' } )

const { appContext } = getCurrentInstance() as ComponentInternalInstance
const route = useRoute()
const { site_id_all, channel_item_all } = storeToRefs(useParamsStore())
const { loading, setLoading } = useLoading()

const tableRef = ref()
const tableData = ref<odArticleItem[]>([])
const columns = ref([
  {
    title: '外包文章ID',
    dataKey: 'id',
  },
  {
    title: '所属渠道',
    dataKey: 'channel_code',
  },
  {
    title: '文章标题',
    dataKey: 'title',
    minWidth: 300,
  },
  {
    title: '外包写手',
    dataKey: 'user_name_a',
  },
  {
    title: '审核跟进',
    dataKey: 'user_name_c',
  },
  {
    title: '外包文章状态',
    dataKey: 'status',
    cellRenderer: ( scope: { row: odArticleItem } ) => (
      <span style={ { color: (scope.row.status === 4 || scope.row.status === 6) ? 'var(--el-color-primary)' : scope.row.status === 5 ? 'var(--el-color-danger)' : '' } }>
        { scope.row.status_desc }  
      </span>
    )
  },
  {
    title: '操作',
    dataKey: '',
    fixed: 'right',
    width: 200,
    cellRenderer: ( scope: { row: odArticleItem } ) => (
      <>
        <ElButton type='primary' plain onClick={() => handleAudit(scope.row.id)}> 审核 </ElButton>
        <ElButton type='primary' plain onClick={() => handleCodeReview(scope.row.content)}> 查看源代码 </ElButton>
      </>
    )
  }
])

const getList = ( { page, pageSize } ) => {
  useTryCatch( async () => {
    setLoading(true)
    const params = {
      site_id: site_id_all.value,
      channel_id: channel_item_all.value.channel_id || '',
      page,
      page_size: pageSize
    }
    const res = await getOdArticleList(params)
    if (res.code === 200) {
      tableData.value = res.data.items
      tableRef.value?.setTotal(res.data.total)
    } else {
      ElMessage.error(res.msg)
    }

    setLoading(false)
  }, () => setLoading(false) )
}
const refreshList = () => {
  const { page, pageSize } = tableRef?.value?.getPagination()
  getList( { page, pageSize } )
}

const handleAudit = (id: number) => {
  ElMessageBox.prompt('请输入审核意见', '外包文章审核', {
    confirmButtonText: '通过',
    cancelButtonText: '驳回',
    cancelButtonClass: 'el-button--danger',
    inputPlaceholder: '审核意见',
    distinguishCancelAndClose: true,
    callback: (action: any) => {
      const type = action.action
      const { value } = action
      if (type !== 'close') {
        useTryCatch( async () => {
          const params = {
            art_id: id,
            remark: value,
            status: type === 'confirm' ? 4 : 5
          }

          const res = await auditOdArticle(params)
          if (res.code === 200) {
            refreshList()
          } else {
            ElMessage.error(res.msg)
          }
        } )
      }
    }
  })
}
const handleCodeReview = (content: string) => {
  appContext.config.globalProperties.$Ace({
    code: content,
    showModal: true,
    hideBottom: true,
  })
}

Listener(route, () => {
  tableRef.value?.setPage(1)
  refreshList()
})
</script>