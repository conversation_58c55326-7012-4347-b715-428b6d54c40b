<template>
  <el-tooltip
    :content="tipContent"
    placement="top"
  >
    <div style="width: 22px; height: 23px; overflow: hidden;">
      <div 
        style="transition: transform .5s .1s; white-space: nowrap;" 
        :style="{ transform: `translateX(${theme === 'dark' ? '-22' : '0'}px)` }">
        <Sunny style="width: 22px; height: 23px;" @click="handleSetTheme('dark')" />
        <Moon style="width: 22px; height: 23px;" @click="handleSetTheme('lighting-theme')" />
      </div>
    </div>
  </el-tooltip>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { storeToRefs } from 'pinia/dist/pinia'
import { Moon, Sunny } from '@element-plus/icons-vue'
import { useConfigStore } from '@/store/config'
type themes = 'dark'|'lighting-theme'
const configStore = useConfigStore()

const { theme } = storeToRefs(configStore)
const handleSetTheme = (theme: themes) => {
  configStore.setTheme(theme)
}
const tipContent = computed(() => {
  return theme.value === 'dark' ? '切换至白色主题' : '切换至黑色主题' 
})

// 根据时间自动切换黑白主题， 7:00 , 19: 00
// const handleAutoTheme = () => {
//   const hour = new Date().getHours()
//   const theme: themes = (hour >= 7 && hour < 19) ? 'lighting-theme' : 'dark'
//   handleSetTheme(theme)
// }
// handleAutoTheme()
</script>
