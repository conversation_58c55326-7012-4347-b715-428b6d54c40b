<template>
  <div class="scroll-y with-flex">
    <QueryForm
      :form-list="formList"
      :loading="loading"
      @search="handleSearch"
      hide-reset
    />
    <div v-loading="loading" class="sticky-table">
      <CustomTable
        ref="tableRef"
        :columns="columns"
        :data="tableData"
        :table-method="getList"
      />
    </div>
  </div>
</template>

<script setup lang="tsx">
import { ref } from 'vue'
import { ElMessage } from 'element-plus'
import { useRoute } from 'vue-router'
import useLoading from '@/hooks/use-loading'
import useTryCatch from '@/hooks/use-try-catch'
import { articleImportDetailList } from '@/api/tools-management'
import type { articleDetailItem } from '@/api/tools-management'

import QueryForm from '@/components/QueryForm/index.vue'
import CustomTable from '@/components/CustomTable/index.vue'

defineOptions ( { name: 'ArticleImportDetail' } )

const route = useRoute()
const { loading, setLoading } = useLoading()
const { task_id } = route.query as unknown as { task_id: string }

const status = ref<string|number>('')
const tableRef = ref()
const tableData = ref<articleDetailItem[]>([])

const formList = ref([
  {
    label: '导入状态',
    clearable: true,
    placeholder: '导入状态',
    value: status,
    component: 'select',
    selections: [
      {
        value: 1,
        label: '成功'
      },
      {
        value: 2,
        label: '失败'
      }
    ]
  }
])
const columns = ref([
  {
    title: '文章标题',
    dataKey: 'title',
    minWidth: 300,
  },
  {
    title: 'Excel行数',
    dataKey: 'execl_index',
    width: 100,
  },
  {
    title: '导入状态',
    dataKey: 'status',
    cellRenderer: (scope: { row: articleDetailItem }) => (
      <span style={ { color: scope.row.status === 1 ? 'var(--el-color-success)' : 'var(--el-color-danger)' } }>
        导入{ scope.row.status === 1 ? '成功' : '失败' }
      </span>
    )
  },
  {
    title: '失败原因',
    dataKey: 'remark',
    minWidth: 300,
    cellRenderer: (scope: { row: articleDetailItem }) => (
      <span style={ { color: scope.row.status === 2 ? 'var(--el-color-danger)' : 'inherit' } }>
        { scope.row.status === 1 ? '/' : scope.row.remark }
      </span>
    )
  }
])

const handleSearch = () => {
  tableRef.value?.setPage(1)
  const { page, pageSize } = tableRef.value?.getPagination()
  getList( { page, pageSize } )
}

const getList = ( { page, pageSize } ) => {
  useTryCatch( async () => {
    setLoading(true)
    const params = {
      task_id,
      status: status.value,
      page,
      page_size: pageSize
    }

    const res = await articleImportDetailList(params)
    if (res.code === 200) {
      tableData.value = res.data.data
      tableRef.value?.setTotal(res.data.total)
    } else {
      ElMessage.error(res.msg)
    }

    setLoading(false)
  }, () => setLoading(false) )
}
</script>