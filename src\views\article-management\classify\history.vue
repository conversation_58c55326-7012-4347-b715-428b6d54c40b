<template>
  <div class="scroll-y with-flex">
    <div v-loading="loading" style="min-height: 100px;" class="sticky-table">
      <CustomTable
        ref="tableRef"
        :columns="columns"
        :data="tableData"
        :table-method="getList"
        :selectable="true"
        :operate-list="operateList"
        @selection-change="handleSelectionChange"
      />
    </div>
    <CompareModal 
      v-if="showCompare" 
      :cat_id="cat_id" 
      :version_ids="version_ids"
      @close="showCompare = false"
    />
  </div>
</template>

<script setup lang="tsx">
import { ref, computed, defineAsyncComponent } from 'vue'
import { ElButton, ElMessage, ElMessageBox } from 'element-plus'
import { useRoute, useRouter } from 'vue-router'
import { useParamsStore } from '@/store'
import { useBasicStore } from '@/store/basic'
import useTryCatch from '@/hooks/use-try-catch'
import useLoading from '@/hooks/use-loading'
import { getClassifyHistory, restoreClassifyVersion } from '@/api/article-management'
import type { historyListItem } from '@/api/article-management'

import CustomTable from '@/components/CustomTable/index.vue'
const CompareModal = defineAsyncComponent(() => import('./components/compareModal.vue'))

const route = useRoute()
const router = useRouter()
const { site_id_all } = useParamsStore()
const basicStore = useBasicStore()
const { loading, setLoading } = useLoading()

const { cat_id } = route.query as unknown as { cat_id: number }
const tableRef = ref()
const tableData = ref<historyListItem[]>([])
const tableSelections = ref<historyListItem[]>([])
const showCompare = ref(false)
const version_ids = ref('')
const columns = ref([
  {
    title: '文章分类ID',
    dataKey: 'entity_id',
    width: 180,
    showOverflowTooltip: true,
  },
  {
    title: '版本',
    dataKey: 'version',
    width: 180,
    showOverflowTooltip: true,
    cellRenderer: (scope: { row: historyListItem }) => (
      <>
        V{ scope.row.version }
        { scope.row.is_current_version ? '(当前版本)' : '' }
      </>
    )
  },
  {
    title: '备份时间',
    dataKey: 'edit_time',
    showOverflowTooltip: true,
  },
  {
    title: '最后编辑人员',
    dataKey: 'user_name',
    showOverflowTooltip: true,
  },
  {
    title: '备份说明',
    dataKey: 'remark',
    showOverflowTooltip: true,
  },
  {
    title: '操作',
    dataKey: '',
    fixed: 'right',
    width: 210,
    cellRenderer: (scope: { row: historyListItem }) => (
      <>
        <ElButton
          type='primary'
          plain
          disabled={scope.row.state==='disable' || scope.row.is_current_version}
          onClick={() => handleRestoreVersion(scope.row)}
        >
          还原此版本 
        </ElButton>
        <ElButton
          type='primary'
          plain
          onClick={() => handleDetailView(scope.row)}
        >
          查看 
        </ElButton>
      </>
    )
  }
])
const operateList = ref([
  {
    title: '刷新页面',
    method: () => {
      tableRef.value?.setPage(1)
      const { page, pageSize } = tableRef.value
      getList( { page, pageSize } )
    },
  },
  {
    title: '对比选中文章',
    method: () => {
      version_ids.value = tableSelections.value.map(( { version } ) => version).join(',')
      showCompare.value = true
    },
    disabled: computed(() => tableSelections.value.length !== 2)
  },
])

const getList = ( { page, pageSize } ) => {
  useTryCatch( async () => {
    setLoading(true)
    const params = {
      site_id: site_id_all,
      page,
      page_size: pageSize
    }

    const res = await getClassifyHistory(cat_id, params)
    if (res.code === 200) {
      const last_version = res.data.last_version as historyListItem
      const list = res.data.items as historyListItem[]
      
      for ( const item of list ) {
        if (item.id === last_version.id) {
          item.is_current_version = true
          break
        }
      }

      tableData.value = list
      tableRef.value?.setTotal(res.data.total)

    } else {
      ElMessage.error(res.msg)
    }
    setLoading(false)
  }, () => {
    handleBack()
    setLoading(false)
  } )
}
const handleSelectionChange = (selections: historyListItem[]) => {
  tableSelections.value = selections
}
const handleRestoreVersion = (row: historyListItem) => {
  ElMessageBox.confirm(
    `请确认是否是否还原至此版本`,
    '提示',
    {
      type: 'warning',
      confirmButtonText: '确认',
      cancelButtonText: '取消',
      callback: (action: string) => {
        if (action === 'confirm') {
          useTryCatch(
            async () => {
              const res = await restoreClassifyVersion(row.entity_id, row.version, { site_id: site_id_all })
              if (res.code === 200) {
                const { page, pageSize } = tableRef.value
                getList( { page, pageSize } )
                ElMessage.success('操作成功')
              } else {
                ElMessage.error(res.msg)
              }
            }
          )
        }
      }
    }
  )
}
const handleDetailView = (row :historyListItem) => {
  const name = 'ArticleClassifyOperate'
  basicStore.delCachedView(name)
  router.push({
    name,
    query: {
      type: 'view',
      cat_id: row.entity_id,
      version_id: row.version
    }
  })
}
const handleBack = () => {
  router.go(-1)
}
</script>