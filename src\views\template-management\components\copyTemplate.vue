<template>
  <el-dialog v-model="show" title="复用选中模板" width="750px" @closed="() => emit('closed')" append-to-body>
    <span>
      复用模板，会完全复用A渠道模板至B渠道，包括文章内容/页面内容会同步复用。如果A渠道复用至B渠道，B渠道已经存在子模板，则不会执行。只能复用启用状态下的模板。
    </span>
    <p>
      <Warning color="var(--el-color-error)" style="width: 16px; vertical-align: middle;" />
      复用时请注意：选择模板渠道要与原渠道保持一致
    </p>
    <el-form label-width="160px">
      <el-form-item label="已选中模板ID:">
        {{ row?.tpl_id }}
      </el-form-item>
      <el-form-item label="原渠道:" :rules="{ required: true }">
        {{ row?.channel_code }}
      </el-form-item>
      <el-form-item label="复制渠道:" :rules="{ required: true }">
        <el-select
          v-model="formData.target_channel_id"
          placeholder="复制渠道"
          clearable
        >
          <el-option
            v-for="(d, index) in channelList"
            :key="index"
            :label="d.channel_code"
            :value="d.channel_id"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="复用方式:" :rules="{ required: true }">
        <el-space :size="36">
          <el-radio-group v-model="formData.transfer_type">
            <el-radio label="all">全部复用</el-radio>
            <el-radio label="section">部分复用</el-radio>
          </el-radio-group>
          <el-popover :width="750">
            <template #reference>
              <el-link type="primary">复用说明</el-link>
            </template>
            <template #default>
              <p>(1) 复用模板：会复用模板的所有页面字段，块，及页面；当模板为文章模板时，还会同时复用页面对应的文章</p>
              <p>(2) 全部复用和部分复用的区别：两种方式都会复用所有的块和页面字段，差异在于：</p>
              <p>全部复用会复用模板下所有页面 <span style="color: var(--el-color-error)">（直接复用方便，但是会产生大量的不需要的页面和文章，导致冗余）</span></p>
              <p>部分复用根据输入的页面ID，选择性复用 <span style="color: var(--el-color-error)">（按需复用页面，减少不需要的冗余数据）</span></p>
            </template>
          </el-popover>
        </el-space>
      </el-form-item>
    </el-form>
    <div v-if="formData.transfer_type === 'section'">
      部分复用通过xls/xlsx文件，输入复用的模板的页面ID，则会复用该部分页面；快速查看复用页面教程：
      <el-link type="primary">
        <a href="http://confluence.300624.cn/pages/viewpage.action?pageId=272434683" target="_blank">http://confluence.300624.cn/pages/viewpage.action?pageId=272434683</a>
      </el-link>
      <div class="text-center">
        <div style="padding: 10px 0;">下载文件模板：<el-link type="primary" @click="handleDownload">点击下载</el-link></div>
        <el-upload
          action="string"
          :disbaled="loading"
          :auto-upload="false"
          :limit="1"
          :on-change="handleChange"
        >
          <el-button type="primary" :loading="loading">点击上传</el-button>
          <template #tip>
            <div class="el-upload__tip">
              只能上传xls/xlsx文件
            </div>
          </template>
        </el-upload>
      </div>
    </div>
    <span v-else>
      全部复用目前仅支持一次复用不超500条页面的模板；若模板页面超过500条，请走部分复用模式，并确认是否模板里所有页面都是需要复用的。
    </span>
    <template #footer>
      <div class="text-center">
        <el-button @click="handleClose"> 取消 </el-button>
        <el-button type="primary" :loading="loading" @click="handleConfirm"> 确定 </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { ElMessage } from 'element-plus'
import { Warning } from '@element-plus/icons-vue'
import { useParamsStore } from '@/store'
import useTryCatch from '@/hooks/use-try-catch'
import useLoading from '@/hooks/use-loading'
import { getHost } from '@/hooks/use-env'
import { getDownloadLocalToken } from '@/hooks/use-local-auth'
import { uploadFileTemplate, copyTemplate } from '@/api/template-management'
import type { UploadFile, UploadUserFile } from 'element-plus'
import type { listItem, copyTemplateData } from '@/api/template-management'

const props = defineProps<{
  row?: listItem;
}>()
const emit = defineEmits(['closed', 'success'])

const { channelList, site_id_all } = useParamsStore()
const { loading, setLoading } = useLoading()
const show = ref(true)
const formData = reactive<copyTemplateData>({
  site_id: site_id_all,
  entity_ids: `${props.row?.tpl_id as number}`,
  channel_id: props.row?.channel_id as number,
  target_channel_id: '',
  transfer_type: 'all',
  task_version: ''
})
const fileList = ref<UploadUserFile[]>([])

const handleClose = () => {
  show.value = false
}
const handleConfirm = () => {
  const { transfer_type, task_version, target_channel_id } = formData
  if (!target_channel_id) {
    return ElMessage.warning('请先选择复制渠道')
  }
  useTryCatch( async () => {
    setLoading(true)
    const params = {
      ...formData,
      task_version: transfer_type === 'all' ? '' : task_version
    }
    const res = await copyTemplate(params)
    if (res.code === 200) {
      handleClose()
      emit('success')
      ElMessage.success('复用模板成功')
    } else {
      ElMessage.error(res.msg)
    }
    setLoading(false)
  }, () => setLoading(false))
}
const handleDownload = () => {
  const token = getDownloadLocalToken()
  window.open(`${getHost()}/api/v1/tool/tpl_transfer/import_pages_tpl?ws-login-token=${token}`)
}
const handleChange = (file: UploadFile, list: UploadUserFile[]) => {
  const { raw } = file
  fileList.value = list
  if (raw?.type === 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet') {
    useTryCatch( async () => {
      setLoading(true)
      const { tpl_id } = props.row as listItem
      const params = {
        import_file: raw,
        site_id: site_id_all,
        entity_ids: `${tpl_id}`
      }
      const res = await uploadFileTemplate(params)
      if (res.code === 200) {
        formData.task_version = res.data.task_version
        ElMessage.success('上传成功')
      } else {
        ElMessage.error(res.msg)
      }
      setLoading(false)
    }, () => setLoading(false) )
  } else {
    ElMessage.warning('只能上传xls/xlsx文件')
  }
}
defineExpose({
  handleClose
})
</script>