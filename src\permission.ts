import router from '@/router'
import { progressClose, progressStart } from '@/hooks/use-menu'
import { langTitle } from '@/hooks/use-common'
import { setLocalToken, getWSDomainWsidToken, getDomainWsidToken } from '@/hooks/use-local-auth'
import { getCodeAuthLoginUrl, getAppName } from '@/hooks/use-env'
import { useCodeStore, useParamsStore, useUserStroe } from '@/store'
import { useBasicStore } from '@/store/basic'
import { getSysTokenByCode } from '@/api/user'
import type { cmsUserInfo } from '@/store/modules/user/types'

const APP_NAME = getAppName()

// 此处应该是兼容写法，后续应该由用户中心统一管理
const getUserWsid = (userInfo: cmsUserInfo): number|string => userInfo.wsid || localStorage.getItem('wsid') || getWSDomainWsidToken() || getDomainWsidToken()

router.beforeEach( async (to) => {
  progressStart()
  document.title = langTitle(to.meta?.title || 'Wondershare-cms40', true)
  const paramsStore = useParamsStore()
  const userStore = useUserStroe()
  const basicStore = useBasicStore()
  // 新增路由权限校验
  if (to.meta.forbidden) {
    return '/'
  }
  // 若已登录，则直接正常进行路由跳转
  if (userStore.userInfo.wsid && paramsStore.site_id_all) {
    return true
  }
  const codeStore = useCodeStore()
  // 未登录，按以下步骤进行权限校验及系统参数获取
  let urlHasToken = false
  const { access_token, expires_in, id_token, state, token_type, code } = to.query
  const keysLen = Object.keys(to.query).length

  if (access_token && expires_in && id_token && state && token_type) {
    urlHasToken = true
    // 设置授权token
    setLocalToken(`${token_type} ${access_token}`)
    // 设置id-token校验token
    setLocalToken(id_token, `${APP_NAME}IdToken`)
    setLocalToken(`${token_type} ${access_token}***${id_token}`, 'Admin-Token')
  }

  codeStore.setCodeToTokenSuccess(false)

  if (keysLen === 5 && urlHasToken) {
    return `${to.path}`
  }

  if (keysLen > 5 && urlHasToken) {
    return `${to.fullPath.split('&access_token=')[0]}`
  }

  // code认证模式
  if (code && state) {
    urlHasToken = true

    try {
      const res = await getSysTokenByCode(code as string)
      if (res.code) {
        codeStore.setCodeToTokenSuccess(true)
      }
    } catch (error) {
      codeStore.setCodeToTokenSuccess(false)
      throw new Error(`getSysTokenByCode err ${error}`)
    }
  } else {
    codeStore.setCodeToTokenSuccess(true)
  }

  if (keysLen === 2 && urlHasToken) {
    return `${to.path}`
  }
  if (keysLen > 2 && urlHasToken) {
    return `${to.fullPath.split('&code=')[0]}`
  }

  // 获取站点列表
  await paramsStore.getWebList()
  // 获取用户信息
  try {
    await userStore.getUserInfo()
    await userStore.getUserMenu()
    // 渲染权限路由
    basicStore.setAllRoutes(userStore.userMenu)
    // 获取常用站点列表
    userStore.getTopList(getUserWsid(userStore.userInfo))

    // 初始化全局参数，异步获取即可
    paramsStore.getUserList()
    paramsStore.getTemplates()

    return { ...to, replace: true }
  } catch (error) {
    if (codeStore.codeToTokenSuccess) {
      window.location.href = getCodeAuthLoginUrl(to.fullPath)
    }
  }
  
} )

router.afterEach(() => {
  progressClose()
})
