import { defineStore } from 'pinia'
import type { cmsUserInfo } from './types'
import type { siteItem } from '../params/types'
import { getMarkSiteList, getSysOauthMenu, getUserInfo } from '@/api/user'
import { userPermissionTree } from '@/api/site-config'
import type { menuData } from '@/api/site-config'

const useUserStroe = defineStore('user', {
  state: () => {
    return {
      userInfo: {} as cmsUserInfo,
      topList: [] as siteItem[],  // 用户常用站点
      menuList: [],             // 用户的路由表
      accessList: [],
      userMenu: [] as menuData[],
      banIds: ['4515']
    }
  },
  actions: {
    async getUserInfo() {
      try {
        const res = await getUserInfo()
        const { code, data, msg } = res
        if (code === 200) {
          this.userInfo = data
          this.userInfo.wsid = data.wsId

          const { channel_read } = data
          this.updateChannelReadSession(channel_read)
        } else {
          Promise.reject(msg)
        }
      } catch (error) {
        
      }
    },
    async getTopList(wsid: number|string) {
      try {
        const res = await getMarkSiteList(wsid)
        const { code, data, msg } = res
        if (code === 200) {
          const topList = [] as siteItem[]
          if (data) {
            data.forEach((item: { k: number, v: string }) => {
              topList.push({
                id: item.k,
                cms_site_id: item.k,
                cms_site_name: item.v,
                name: item.v
              })
            })

            this.topList = topList
          }
        } else {
          throw new Error(msg)
        }
      } catch (error) {
        throw new Error()
      }
    },
    async getMenuList() {
      try {
        const res = await getSysOauthMenu()
        const { code, data } = res
        if (code === 0) {
          this.menuList = data[0].children
        }
      } catch (error) {
        
      }
    },
    async getUserMenu() {
      try {
        const res = await userPermissionTree()
        const { code, data } = res
        if (code === 200) {
          this.userMenu = data
        }
      } catch (error) {
        
      }
    },
    updateChannelReadSession(channel_read: string) {
      let channel_read_str = ''
      if (channel_read) {
        JSON.parse(channel_read).forEach((item) => {
          channel_read_str += item.channel_ids + ','
        })
        channel_read_str = channel_read_str.replace(/,+$/, '')
        sessionStorage.setItem('channel_read', channel_read_str)
      }
    },
    isBanned() {
      const role_ids = this.userInfo.role_ids
      if (role_ids) {
        for ( const id of this.banIds ) {
          if (role_ids?.indexOf(id) > -1) {
            return true
          }
        }
      }
      return false
    }
  },
})

export default useUserStroe