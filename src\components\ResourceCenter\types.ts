import type { UploadRawFile } from 'element-plus'
import type Node from 'element-plus/es/components/tree/src/model/node'
import type { fileItem } from '@/api/resource-center'

export type imgTypeSupport = 'image/jpeg'|'image/png'|'image/gif'|'image/webp'|'image/svg+xml'|'image/bmp'|'image/x-icon'

export type videoSupportType = 'video/x-msvideo'|'video/mp4'|'video/mpeg'|'video/ogg'|'video/webm'|'video/x-ms-wmv'

export type selectFiles = Map<number, fileItem>

export type selectFileItem = {
  url: string;
  name: string;
  id: number;
  suffix: string;
  file: fileItem
}

export type ImgListItem = {
  raw: UploadRawFile;
  dataUrl: string|ArrayBuffer;
}

export type fileLogItem = {
  id: number;
  resource_id: number;
  action_type: number;  //操作类型： 1 上传 2 删除 3 重命名 4 覆盖 5压缩
  old_data: string;
  new_data: string;
  user_id_a: string;
  add_name: string;
  add_time: string;
  action_type_name: string;
}

export const imgTypeSupport = new Set(
  [
    'image/jpeg',
    'image/png',
    'image/gif',
    'image/webp',
    'image/svg+xml',
    'image/bmp',
    'image/x-icon',
  ]
)

export const videoSupportType = new Set(
  [
    'video/x-msvideo',
    'video/mp4',
    'video/mpeg',
    'video/ogg',
    'video/webm',
    'video/x-ms-wmv',
  ]
)

export const imgReg = new RegExp('jpg|png|svg|gif|ico|jpeg')

export const videoReg = new RegExp('mp4|webm|ts|mpeg|avi|ogv')

export const fileSuffixReg = new RegExp('7z|css|csv|doc|docx|html|ico|js|mp3|mp4|pdf|php|ppt|pptx|rar|txt|xls|xlsx|xml|zip|png|jpg|jpeg|svg|gif')

export const msgboxParams = {
  confirmButtonText: '确认',
  cancelButtonText: '取消',
  callback: () => {}
}

export const handleIsRootNode = (node: Node) => {
  const { parent } = node
  const { children } = parent.data

  return children ? false : true
}

export const getSelectFileItem = (item: fileItem): selectFileItem => ({
  url: import.meta.env.VITE_APP_ENV === 'prod' ? item.file_url : item.file_url.replace('https', 'http'),
  name: item.file_name,
  id: item.id,
  suffix: item.suffix,
  file: item
})

export const statusMapFilter: Map<number, string> = new Map(
  [
    [1, '待发布'],
    [2, '发布中'],
    [3, '已发布'],
    [4, '发布失败'],
  ]
)

export const statusMap: Map<number, string> = new Map(
  [
    [0, '待发布'],
    ...statusMapFilter
  ]
)