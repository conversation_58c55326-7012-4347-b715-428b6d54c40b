<template>
  <el-space :size="size" nowrap>
    <template v-for="(d, index) in operateList" :key="index">
      <template v-if="!d.hide">
        <el-tooltip :disabled="!d.tooltip || (d.disabled !== undefined && !d.disabled)" :content="d.tooltip">
          <div class="operate-button-wrapper" :class="{ mask: d.disabled }">
            <el-button
              size="default"
              :type="d.disabled ? 'default' : 'primary'"
              :disabled="d.disabled"
              :link="!d.button"
              @click="d.method"
            >
              <SvgIcon v-if="d.icon" :icon-class="d.icon" class="icon" :class="{ 'button-icon': d.button }"/>
              <span v-if="d.title" class="title">{{ d.title }}</span>
            </el-button>
          </div>
        </el-tooltip>
      </template>
    </template>
  </el-space>
</template>

<script setup lang="ts">
import type { operateItem } from './type'
import SvgIcon from '@/icons/SvgIcon.vue'

const props = defineProps<{
  operateList: operateItem[];
  size: number;
}>()
</script>