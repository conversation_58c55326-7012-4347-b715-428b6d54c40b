import axiosReq from './axios'
import qs from 'query-string'

/* cdn刷新 */
export type cdnQueryParams = {
  site_id: number;
  channel_id?: number;
  status?: number|string;  //1:已收录；2:执行中;3:执行成功；4:执行失败
  user_id?: number|string;
  url?: string;
  page?: number;
  page_size?: number;
}
export type cdnListItem = {
  id: number;
  name: string;
  channel_id: number;
  status: number;
  user_name: string;
  add_time: string;
  channel_code: string;
  file_count: number;
}
export type cdnDetailListItem = {
  id: number;
  task_id: number;
  page_id: number;
  page_url: string;
  line_url: string;
  channel_id: number;
  channel_code: string;
  state: number;
  remark: string;
  add_time: string;
  edit_time: string;
}
/* cdn刷新 */

/* 批量替换 */
export type replaceListItem = {
  type: string;
  type_id: number;
  type_field: string;
  type_field_custom: string;
  source: string;
  target: string;
  is_allow: number;  //是否可以替换：0 可以替换 1 没有找到数据 2 存在特殊字符 3 存在破坏html的单词
  allow_msg: string;
}
export type replaceTaskItem = {
  id: number;
  site_id: number;
  channel_id: number;
  status: number;
  user_id_a: number;
  user_id_e: number;
  add_user: string;
  edit_user: string;
  add_time: string;
  edit_time: string;
  status_name: string;
  page_id_str: string;
  blk_id_str: string;
}
export type replaceDetailItem = {
  id: number;
  site_id: number;
  channel_id: number;
  batch_id: number;
  type: number;
  type_id: number;
  type_field: string;
  type_field_custom: string;
  source: string;
  target: string;
  add_time: string;
  edit_time: string;
  entity_status: string;
}
/* 批量替换 */
/* 定向搜索 */
export type detectedSearchQueryParams = {
  site_id: number;
  channel_id?: number;
  entity_type: 'article'|'template'|'page'|'block';
  content: string;
  page: number;
  page_size: number;
}
export type detectedSearchListItem = {
  channel_code: string; 
  channel_name: string; 
  color: string; 
  id: number; 
  name: string;
}
export type detectedSearchItem = {
  entity_detail: detectedSearchListItem[];
  entity_ids: string;
  entity_type: string;
  entity_type_name: string;
  total: number;
}
/* 定向搜索 */
/* 文章批量导入 */
export type articleImportData = {
  site_id: number;
  channel_id: number;
  import_articles_file: Blob;
  tpl_type: string;
  tpl_id: number|string;
  author_id?: number|string;
}
export type articleDetailItem = {
  execl_index: number;
  remark: string;
  status: number;
  title: string;
}
/* 文章批量导入 */

export type siteFileQuery = {
  module: string; //1：cms生成文件，2：其他文件
  type: string; //文件类型，0：全部，1：页面文件，2：块文件，3：其他文件
  redundant_type: string; //冗余类型，0：全部，1：本地冗余，2：线上冗余，3：本地&线上冗余
}

export type siteFileListItem = {
  type: number;
  file_id: number;
  create_state: string;
  path: string;
  url: string;
  except_type: number;
  redundant_type: number;
}

export type site404Item = {
  add_time: string;
  channel_code: string;
  channel_id: number;
  count: number;
  edit_time: string;
  link_id: number;
  link_url: string;
}

export type site404QuoteItem = {
  channel_code: string;
  channel_id: number;
  tpl_page_id: number;
  file_url: string;
}

export type sitemapListItem = {
   channel_id: number;
   cycle_time: string;
   default_cycle_time: string;
   default_weight_value: string;
   level: string;
   module: string;
   name: string;
   site_id: number;
   tpl_id: number;
   type: string;
   weight_value: string;
}

export type shortlinkQueryParams = {
  long_link: string;
  short_code: string;
  status: string|number;
  page?: number;
  page_size?: number;
}

export type shortLinkListItem = {
  id: number;
  long_link: string;
  long_link_md5: string;
  short_code: string;
  status: number;  //状态 1 待上线 2 已上线 3 已禁用
  user_id_a: number;
  user_id_e: number;
  add_user: string;
  edit_user: string;
  add_time: string;
  edit_time: string;
  short_link: string;
  status_name: string;
}

export type artImportQueryParams = {
  id: string|number;
  site_id: number;
  channel_id?: number;
  status?: number|string;  //状态：1 待处理 2 处理中 3 已处理 4 任务失败  5 任务部分失败
  user_id_e?: number|string;
  page?: number;
  page_size?: number;
}

export interface artImportTaskItem {
  id: number;
  site_id: number;
  channel_id: number;
  status: number;  //状态：1 待处理 2 处理中 3 已处理 4 任务失败  5 任务部分失败
  user_id_a: number;
  user_id_e: number;
  add_user: string;
  edit_user: string;
  add_time: string;
  edit_time: string;
  finish_time: string;
}

export interface artImportTaskDetailItem extends artImportTaskItem {
  job_id: number;
  file_name: string;
  unique_no: number;
  tpl_id: number;
  page_id: number;
  art_id: number;
  art_name: string;
  page_preview_url: string;
  msg: string;
}

/* cdn刷新 */
// 页面cdn清除列表
export const getCdnList = (params: cdnQueryParams) => 
  axiosReq('get', '/api/v1/page/cdn_clear/list', {
    params,
    paramsSerializer: {
      serialize: (obj: cdnQueryParams) => qs.stringify(obj)
    }
  })

// 清除cdn任务详细列表
export const getCdnDetailList = (params: { id: number; state?: number|string; page?: number; page_size?: number; }) => 
  axiosReq('get', '/api/v1/page/cdn_clear/info', {
    params,
    paramsSerializer: {
      serialize: (obj: { id: number; state?: number|string }) => qs.stringify(obj)
    }
  })

// 页面cdn清除重试
export const refreshCdn = (id: number) => 
  axiosReq('post', '/api/v1/page/cdn_clear/again', { id })

// 新增页面cdn清除
export const clearCdn = (data: { site_id: number; channel_id: number; tpl_page_id: string; }) => 
  axiosReq('post', '/api/v1/page/cdn_clear', data)
/* cdn刷新 */

/* 批量替换 */
// 模板下载
export const downloadReplaceTemplate = (type: 'all'|'field') => 
  axiosReq('get', `/api/v1/tool/replace/template?type=${type}`)

// excel内容校验
export const checkExcel = (data: { site_id: number; channel_id: number; file: Blob }) => {
  const formData = new FormData()
  Object.entries(data).forEach( ( [key, value] ) => {
    if (key === 'file') {
      formData.append('file', value as Blob)
    } else {
      formData.append(key, typeof value === 'number' ? `${value}` : value)
    }
  } )
  return axiosReq('post', '/api/v1/tool/replace/check_excel', formData)
}
 
// 数据内容处理
export const replaceTask = (data: { site_id: number; channel_id: number; content: string}) => 
  axiosReq('post', '/api/v1/tool/replace/handle', data)

// 任务列表
export const replaceTaskList = (params: { site_id: number; channel_id?: number; page: number; page_size: number }) => 
  axiosReq('get', '/api/v1/tool/replace/list', {
    params,
    paramsSerializer: {
      serialize: (obj: { id: number; state?: number|string }) => qs.stringify(obj)
    }
  })

// 任务详情
export const replaceDetail = (batch_id: number) => 
  axiosReq('get', `/api/v1/tool/replace/detail?batch_id=${batch_id}`)

// 任务回退
export const replaceReset = (batch_id: number) => 
  axiosReq('post', '/api/v1/tool/replace/go_back', { batch_id })
/* 批量替换 */

/* 定向搜索 */
export const detectedSearchList = (params: detectedSearchQueryParams) => 
  axiosReq('get', '/api/v1/tool/content', {
    params,
    paramsSerializer: {
      serialize: (obj: detectedSearchQueryParams) => qs.stringify(obj)
    }
  })

// 获取列表数量
export const getTotalNum = (params: { site_id: Number; content: String; channel_id?: number }) => 
  axiosReq('get', '/api/v1/tool/content_total', {
    params,
    paramsSerializer: {
      serialize: (obj) => qs.stringify(obj)
    }
  })
/* 定向搜索 */

/* 文章批量导入 */
export const articleImport = (data: articleImportData) => {
  const formData = new FormData()
  Object.entries(data).forEach( ( [key, value] ) => {
    formData.append(key, typeof value === 'number' ? `${value}` : value)
  } )
  return axiosReq('post', '/api/v1/tool/import_articles', formData)
}
// 获取详情列表
export const articleImportDetailList = (params: { task_id: string|number; status: string|number; page: number; page_size: number }) => 
  axiosReq('get', '/api/v1/article/import_list', {
    params,
    paramsSerializer: {
      serialize: (obj: any) => qs.stringify(obj)
    }
  })
/* 文章批量导入 */

/* edm模板解析 */
export const resolveEDM = (data: { tpl: string; param: object }) => axiosReq('post', '/api/v1/interface/edm/html', data)
/* edm模板解析 */

/* 站点文件管理 */
export const siteFileList = (params: siteFileQuery) =>
  axiosReq('get', '/api/v1/tool/file_management', {
    params,
    paramsSerializer: {
      serialize: (obj: any) => qs.stringify(obj)
    }
  })
/* 站点文件管理 */

/* 404管理 */
export const site404List = (params: { site_id: number; channel_id: number; page: number; page_size: number }) => 
  axiosReq('get', '/api/v1/tool/error_links', {
    params,
    paramsSerializer: {
      serialize: (obj: any) => qs.stringify(obj)
    }
  })

export const site404QuoteList = (link_id: number, params: { site_id: number; page: number; page_size: number }) => 
  axiosReq('get', `/api/v1/tool/error_link/${link_id}/quotes`, {
    params,
    paramsSerializer: {
      serialize: (obj: any) => qs.stringify(obj)
    }
  })
/* 404管理 */

/* AI翻译测试 */

//  创建翻译任务
export const createTranslateTask = ( { art_id, to, type } ) =>
  axiosReq('get', `/api/v1/test/trans?art_id=${art_id}&to=${to}&type=${type}`)

/* AI翻译测试 */

/* sitemap管理 */
// 列表
export const sitemapList = (params: { site_id: number; channel_id: number; tpl_ids: string; name: string; type: string; page: number; page_size: number }) => 
  axiosReq('get', '/api/v1/sitemap/config/list', {
    params,
    paramsSerializer: {
      serialize: (obj: any) => qs.stringify(obj)
    }
  })
// 配置更新频率
export const editCycleTime = ( data: { site_id: number; channel_id: number; tpl_ids: string; cycle_time: string; } ) => 
  axiosReq('put', '/api/v1/sitemap/cycle/time', data)
// 配置权重
export const editWeightValue = ( data: { site_id: number; channel_id: number; tpl_ids: string; weight_value: string; } ) => 
  axiosReq('put', '/api/v1/sitemap/weight/value', data)
/* sitemap管理 */

/* 视频覆盖率统计 */
// 站点页面视频概况
export const videoOverview = (site_id: number, is_export: 0|1) =>
  axiosReq('get', `/api/v1/page/video_overview?site_id=${site_id}&is_export=${is_export}`)

// 获取视频页面id集
export const videoPageIds = (channel_id: number, video_type: 'youtube'|'other'|'all') => 
  axiosReq('get', `/api/v1/page/video_page_list?channel_id=${channel_id}&video_type=${video_type}`)

// 视频code列表
export const getVideoCodeList = (params: {site_id: number, channel_id: number, page?: number, page_size?: number}) => 
  axiosReq('get', `/api/v1/page/youtube_video_list`, {
    params,
    paramsSerializer: {
      serialize: (obj: any) => qs.stringify(obj)
    }
  })
/* 视频覆盖率统计 */

/* 短链生成工具 */
export const shortLinkList = (params: shortlinkQueryParams) =>
  axiosReq('get', '/api/v1/shortlink/list', {
    params,
    paramsSerializer: {
      serialize: (obj: any) => qs.stringify(obj)
    }
  })

// 添加短链
export const addShortLink = (data: { long_link: string; }) =>
  axiosReq('post', '/api/v1/shortlink/add', data)

// 编辑短链
export const editShortLink = (data: { id: number; long_link: string; }) =>
  axiosReq('post', '/api/v1/shortlink/edit', data)

// 启用短链
export const enableShortLink = (data: { id: number|string; }) =>
  axiosReq('post', '/api/v1/shortlink/enable', data)

// 禁用短链
export const disableShortLink = (data: { id: number|string; }) =>
  axiosReq('post', '/api/v1/shortlink/disable', data)

// 删除短链
export const deleteShortLink = (data: { id: number|string; }) =>
  axiosReq('post', '/api/v1/shortlink/delete', data)

// 批量导入短链
export const importShortLink = (data: { file: Blob; }) => {
  const formData = new FormData()
  Object.entries(data).forEach( ( [key, value] ) => {
    if (key === 'file') {
      formData.append('file', value as Blob)
    } else {
      formData.append(key, typeof value === 'number' ? `${value}` : value)
    }
  } )
  return axiosReq('post', '/api/v1/shortlink/export', formData)
}
/* 短链生成工具 */


/* word转html */
// word文档批量上传
export const wordBatchUpload = (data: Blob[], params: { site_id: number; channel_id: number; tpl_id: string; }) => {
  const formData = new FormData()
    data.forEach( (item) => {
      formData.append(`files[]`, item)
    } )
  Object.entries(params).forEach( ( [key, value] ) => {
    formData.append(key, typeof value === 'number' ? `${value}` : value)
  })
  return axiosReq('post', '/api/v1/doc/upload', formData, {
    headers: {
      'Content-Type': 'multipart/form-data'
    }
  })
}

// word文档单个上传
export const wordUpload = (data: { file: Blob; id: number; }) => {
  const formData = new FormData()
  Object.entries(data).forEach( ( [key, value] ) => {
    if (key === 'file') {
      formData.append('file', value as Blob)
    } else {
      formData.append(key, typeof value === 'number' ? `${value}` : value)
    }
  })
  return axiosReq('post', '/api/v1/doc/upload_one_file', formData)
}

// word文档下载
export const wordPreview = (id: number) =>
  axiosReq('get', `/api/v1/doc/preview?id=${id}`, {
    headers: {
      'Content-Type': 'application/octet-stream',
      'responseType': 'blob',
    }
  })

// 获取任务列表
export const getArtImportTaskList = (params: artImportQueryParams) =>
  axiosReq('get', '/api/v1/doc/get_job_list', {
    params,
    paramsSerializer: {
      serialize: (obj: any) => qs.stringify(obj)
    }
  })

// 获取任务详细信息列表
export const getArtImportTaskDetailList = (id: string) =>
  axiosReq('get', `/api/v1/doc/get_job_detail_list?job_id=${id}`)


/* word转html */
