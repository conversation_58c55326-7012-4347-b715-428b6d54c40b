<template>
  <div class="scroll-y" v-loading="loading">
    <el-card shadow="hover">
      <template #header>
        <span style="font-size: 18px; font-weight: 700;">页面复用任务详情</span>
      </template>
      <template #default>
        <el-descriptions :column="1" size="large" class="mb-30px">
          <el-descriptions-item>
            <el-space :size="16">
              <strong>复用来源模板:</strong>
              <el-tag size="large"> {{ task_info?.orgin_tpl_info.channel_code }} </el-tag>
              <el-tag size="large"> {{ task_info?.orgin_tpl_info.tpl_id }} </el-tag>
              <el-tag size="large"> {{ task_info?.orgin_tpl_info.name }} </el-tag>
            </el-space>
          </el-descriptions-item>
          <el-descriptions-item>
            <el-space :size="16">
              <strong>复用目标模板:</strong>
              <el-tag size="large"> {{ task_info?.to_tpl_info.channel_code }} </el-tag>
              <el-tag size="large"> {{ task_info?.to_tpl_info.tpl_id }} </el-tag>
              <el-tag size="large"> {{ task_info?.to_tpl_info.name }} </el-tag>
            </el-space>
          </el-descriptions-item>
          <el-descriptions-item>
            <el-space :size="16">
              <strong>任务创建时间:</strong>
              <el-tag size="large"> {{ task_info?.add_time }} </el-tag>
            </el-space>
          </el-descriptions-item>
          <el-descriptions-item>
            <el-space :size="16">
              <strong>任务完成时间:</strong>
              <el-tag size="large"> {{ task_info?.edit_time }} </el-tag>
            </el-space>
          </el-descriptions-item>
        </el-descriptions>

        <el-table
          border
          :data="tableData"
        >
          <el-table-column label="复用来源页面ID" prop="orgin_page_id" />
          <el-table-column label="对应页面URL" prop="to_page_url" />
          <el-table-column label="复用目标新页面ID">
            <template #default="scope: { row: copyPageItem }">
              <span :style="{ color: scope.row.state === 2 ? 'var(--el-color-error)' : '' }">{{ scope.row.to_page_id || '复用失败' }}</span>
            </template>
          </el-table-column>
          <el-table-column label="备注">
            <template #default="scope: { row: copyPageItem }">
              <span :style="{ color: scope.row.state === 2 ? 'var(--el-color-error)' : '' }">{{ scope.row.remark || '------' }}</span>
            </template>
          </el-table-column>
      </el-table>
      </template>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { useRoute } from 'vue-router'
import { ElMessage } from 'element-plus'
import useTryCatch from '@/hooks/use-try-catch'
import useLoading from '@/hooks/use-loading'
import { copyPageDetail } from '@/api/template-management'
import type { copyPageItem, copyTaskInfo } from '@/api/template-management'

const route = useRoute()

const { id } = route.query as unknown as { id: string }
const { loading, setLoading } = useLoading()
const tableData = ref<copyPageItem[]>([])
const task_info = ref<copyTaskInfo>()

const getData = () => {
  useTryCatch( async () => {
    setLoading(true)

    const res = await copyPageDetail(id)
    if (res.code === 200) {
      tableData.value = res.data.list.data
      task_info.value = res.data.task_info
    } else {
      ElMessage.error(res.msg)
    }

    setLoading(false)
  }, () => setLoading(false) )
}

if (id) {
  getData()
}
</script>