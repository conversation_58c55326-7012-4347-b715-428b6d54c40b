// 全局操作任务记录
import { defineStore } from 'pinia'
import { formatDatetime } from '@/utils/common'

export type recordItem = {
  time?: string;
  title: string;
  site_id: number;
  site_name: string;
  channel_id: number;
  channel_name: string;
  type: string;
  task_id: string;
  task_type?: string;
  file_num?: number;
}

export type typeEmnu = 'page'|'template'|'block'|'upload'|'delete'|'replace'

const sessionName = 'recordList'

const useTaskRecordStore = defineStore('task-record', {
  state: () => {
    return {
      recordList: JSON.parse(window.sessionStorage.getItem(sessionName) || '[]') as recordItem[],
      typeMap: {
        'page': '页面生成任务',
        'template': '模板生成任务',
        'block': '块生成任务',
        'upload': '文件上传任务',
        'delete': '文件删除任务',
        'replace': '文件替换任务',
      }
    }
  },
  actions: {
    appendRecord(data: recordItem) {
      const itemData = {
        ...data,
        time: formatDatetime(new Date())
      }
      this.recordList.unshift(itemData)
      this.setStorage()
    },
    removeRecord(index: number) {
      this.recordList.splice(index, 1)
      this.setStorage()
    },
    setStorage() {
      window.sessionStorage.setItem(sessionName, JSON.stringify(this.recordList))
    }
  }
})

export default useTaskRecordStore