import axiosReq from './axios'
import qs from 'query-string'

/* 公告相关 */
export type announceConfig = {

}
export type queryParams = {
  state: string;
  is_read: string;
}
export type announceListParams = {
  S: queryParams,
  page?: number;
  page_size?: number;
}
export type announceListItem = {
  add_time: string;
  announcement_id: number;
  content: string;
  edit_time: string;
  is_read: number;
  is_top: number;
  is_top_name: string;
  publish_time: string;
  remark: string;
  state: string;
  state_name: string;
  title: string;
  type: string;
  type_name: string;
  user_id_a: number;
  user_id_e: number;
  user_id_p: number;
  user_name: string;
  user_name_p: string;
}
export type announceDetail = {
  add_time: string;
  announcement_id: number;
  content: string;
  edit_time: string;
  is_read: number;
  is_top: number;
  publish_time: string;
  remark: string;
  state: string;
  title: string;
  type: string;
  user_id_a: number;
  user_id_e: number;
  user_id_p: number;
  user_name: string;
}

// 添加公告
export const addAnnounce = (data: announceConfig) => 
  axiosReq('post', `/api/v1/announcement`, data)

// 编辑公告
export const editAnnounce = (announcement_id: number, data: announceConfig) => 
  axiosReq('put', `/api/v1/announcement/${announcement_id}`, data)

// 获取公告列表
export const getAnnounceList = (params: announceListParams) => 
  axiosReq('get', '/api/v1/announcement', {
    params,
    paramsSerializer: {
      serialize: (obj: announceListParams) => qs.stringify({
        S: JSON.stringify(obj.S),
        page: obj.page,
        page_size: obj.page_size
      })
    }
  })

// 获取详情
export const getAnnounceDetail = (announcement_id: number) => 
  axiosReq('get', `/api/v1/announcement/info?id=${announcement_id}`)

// 改变公告状态
export const changeAnnounceState = (data: any) => 
  axiosReq('put', '/api/v1/announcement', data)

// 标记公告为已读
export const setAnnounceReaded = (announcement_id: number) => 
  axiosReq('post', `/api/v1/announcement/read/${announcement_id}`)
/* 公告相关 */