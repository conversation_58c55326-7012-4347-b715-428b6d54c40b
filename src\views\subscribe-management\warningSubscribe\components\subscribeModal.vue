<template>
  <el-dialog
    v-model="show"
    :title="title"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    append-to-body
    align-center
    width="1200"
    @closed="emit('close')"
  >
    <template #default>
      <el-form
        ref="formRef"
        label-suffix=":"
        label-width="150px"
        :model="submitData"
        :rules="formRules"
      >
        <h2 class="title-prepend"><strong>预警内容设置</strong></h2>
        <el-form-item label="预警名称" prop="name">
          <el-input v-model="submitData.name" placeholder="请输入预警名称" style="width: 400px;" :disabled="readonly" />
        </el-form-item>
        <el-form-item label="触发规则" prop="rule" class="is-error_custom">
          <div v-for="(d, index) in submitData.rule" style="margin-bottom: 10px;" :key="index">
            <el-space :size="16">
              <el-select 
                v-model="d.module" 
                placeholder="请选择【模块】" 
                @change="(val: string) => handleModuleChange(val, index)" 
                :disabled="readonly"
                :class="{ error: !d.module, success: d.module  }"
              >
                <el-option v-for="m in moduleList" :label="m.val" :value="m.key" />
              </el-select>
              <el-select 
                v-model="d.scene" 
                placeholder="请选择【场景】" 
                @visible-change="(val: boolean) => handleSceneVisible(val, index)" 
                :disabled="readonly"
                :class="{ error: !d.scene, success: d.scene }"
              >
                <el-option v-for="s in sceneList[index]" :label="s.val" :value="s.key" />
              </el-select>
              <el-select 
                v-model="d.symbol" 
                placeholder="请选择【等式符号】" 
                :disabled="readonly"
                :class="{ error: !d.symbol, success: d.symbol }"
              >
                <el-option v-for="s in symbolList" :label="s.val" :value="s.key" />
              </el-select>
              <el-input 
                v-model="d.rule_value" 
                type="number" 
                placeholder="请输入【数值】" 
                style="width: 180px;" 
                :disabled="readonly"
                :class="{ error: !d.rule_value, success: d.rule_value }"
              />
              <el-select 
                v-model="d.unit" 
                placeholder="请选择【数量单位】" 
                :disabled="readonly"
                :class="{ error: !d.unit, success: d.unit }"
              >
                <el-option v-for="u in unitList" :label="u.val" :value="u.key" />
              </el-select>

              <template v-if="!readonly">
                <Plus v-if="index === submitData.rule.length - 1" width="24" height="24" style="cursor: pointer;" @click="handleAddRule" />
                <Minus v-else width="24" height="24" style="cursor: pointer;" @click="handleRemoveRule(index)" />
              </template>
            </el-space>
          </div>
        </el-form-item>
        <el-form-item label="规则预览">
          <div style="color: var(--el-color-primary);">
            <span v-if="!id" style="color: var(--el-color-error);">当【模块】【场景】【等式符号】【数值】【数量单位】时，进行通知预警</span>
            <div v-for="(d, index) in submitData.rule">
              规则{{ index + 1 }}: 当{{`${resolveRuleKey(d.module, moduleList)}${resolveRuleKey(d.scene, sceneList[index])}${resolveRuleKey(d.symbol, symbolList)}${d.rule_value}${resolveRuleKey(d.unit, unitList)}`}}时, 进行预警通知
            </div>
          </div>
        </el-form-item>
        
        <h2 class="title-prepend"><strong>预警通知设置</strong></h2>
        <el-form-item label="应用位置(站点渠道)" prop="channel_id">
          <el-space :size="16">
            <el-select v-model="submitData.site_id" placeholder="请选择【站点】" filterable @change="handleSiteChange" :disabled="readonly" >
              <el-option v-for="d in webList" :label="d.cms_site_name" :value="d.cms_site_id" />
            </el-select>
            <el-select v-model="submitData.channel_id" placeholder="请选择【渠道】" filterable :disabled="readonly" >
              <el-option v-for="d in channelList" :label="d.channel_code" :value="d.channel_id" />
            </el-select>
          </el-space>
        </el-form-item>
        <el-form-item label="通知方式" prop="notice_type">
          <el-radio-group v-model="submitData.notice_type" :disabled="readonly" >
            <el-radio v-for="d in noticeTypeList" :label="d.key">{{ d.val }}</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="通知频率" prop="cycle">
          <el-select v-model="submitData.cycle" placeholder="请选择【通知频率】" :disabled="readonly" >
            <el-option v-for="c in cycleList" :label="c.val" :value="c.key" />
          </el-select>
        </el-form-item>
        <el-form-item label="通知内容" prop="notice_content">
          <div style="width: 400px; position: relative;">
            <div class="text-right">
              <el-popover placement="right" trigger="click" width="290">
                <template #reference>
                  <el-button type="primary" link :disabled="readonly" > 插入变量 </el-button>
                </template>
                <template #default>
                  <el-button v-for="d in varsList" type="primary" link @click="handleInsertVars(d.value)">{{ d.key }}</el-button>
                </template>
              </el-popover>
            </div>
            <el-input type="textarea" v-model="submitData.notice_content" placeholder="请填写邮件通知内容" :disabled="readonly"  />
          </div>
        </el-form-item>
        <el-form-item label="通知人员" prop="notice_user_id">
          <el-select
            v-model="submitData.notice_user_id"
            placeholder="请选择【通知人员】"
            multiple
            collapse-tags
            collapse-tags-tooltip
            filterable
            :disabled="readonly" 
          >
            <el-option v-for="d in userList" :key="d.user_id" :label="d.real_name" :value="d.wsId"></el-option>
          </el-select>
        </el-form-item>
      </el-form>
    </template>
    <template #footer>
      <div v-if="!readonly" class="text-center">
        <el-button type="primary" plain :loading="loading" @click="submitForm(formRef)">保存设置</el-button>
        <el-button plain @click="show = false">取消</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, reactive } from 'vue'
import { ElMessage } from 'element-plus'
import { Plus, Minus } from '@element-plus/icons-vue'
import { useParamsStore } from '@/store'
import useTryCatch from '@/hooks/use-try-catch'
import useLoading from '@/hooks/use-loading'
import { addSubscribe, editSubscribe, getSubscribeDetail, getSubscribeRule } from '@/api/subscribe-management'
import { getChannelList } from '@/api/params'
import type { subscribeData, detailData, ruleItem } from '@/api/subscribe-management'
import type { channelItem } from '@/store/modules/params/types'
import { formRules } from './rules'
import type { FormInstance } from 'element-plus'

type ruleListItem = { key: string; val: string; }

const varsList = [
  {
    key: '预警名称',
    value: '{name}'
  },
  {
    key: '模块',
    value: '{module}'
  },
  {
    key: '场景',
    value: '{scene}'
  },
  {
    key: '阈值',
    value: '{rule_value}'
  },
  {
    key: '实际值',
    value: '{target_value}'
  }
]

const props = defineProps<{
  id?: number;
  readonly?: boolean;
}>()
const emit = defineEmits(['close', 'save'])

const title = ref('新增预警')
const { webList, userList } = useParamsStore()
const { loading, setLoading } = useLoading()
const channelList = ref<channelItem[]>()

const show = ref(true)
const formRef = ref()
const ruleItem: ruleItem = {
  module: '',
  scene: '',
  symbol: '',
  rule_value: '',
  unit: '',
}
const submitData_raw = {
  name: '',
  site_id: '',
  channel_id: '',
  notice_content: '',
  notice_type: '',
  cycle: '',
  notice_user_id: '',
}
const submitData = reactive<subscribeData>({
  ...submitData_raw,
  rule: [ { ...ruleItem } ],
})

const moduleList = ref<ruleListItem[]>([])
const sceneList = ref<ruleListItem[][]>([])
const unitList = ref<ruleListItem[]>([])
const cycleList = ref<ruleListItem[]>([])
const symbolList = ref<ruleListItem[]>([])
const noticeTypeList = ref<ruleListItem[]>([])

const sceneMap = new Map<string, ruleListItem[]>()

const resolveRuleKey = (val: string, list: ruleListItem[]) => {
  if (!list) {
    return ''
  }
  for ( const item of list ) {
    if (val === item.key) {
      return item.val
    }
  }
  return ''
}

const handleModuleChange = (val: string, index: number) => {
  submitData.rule[index].scene = ''
}

const handleSceneVisible = (val: boolean, index: number) => {
  const m = submitData.rule[index].module
  if (val && m) {
    sceneList.value[index] = sceneMap.get(m) || [] 
  }
}

const handleAddRule = () => {
  submitData.rule.push( { ...ruleItem } )
  sceneList.value.push([])
}

const handleRemoveRule = (index: number) => {
  submitData.rule.splice(index, 1)
  sceneList.value.splice(index, 1)
}

const handleInsertVars = (vars: string) => {
  submitData.notice_content = `${submitData.notice_content}${vars}`
}

const refreshChannelList = () => {
  useTryCatch( async () => {
    const res = await getChannelList(submitData.site_id as number)
    if (res.code === 200) {
      channelList.value = res.data
    }
  } )
}

const handleSiteChange = () => {
  submitData.channel_id = ''
  refreshChannelList()
}

const valueList = (data: any) => {
  moduleList.value = data.module
  unitList.value = data.unit
  cycleList.value = data.cycle
  symbolList.value = data.symbol
  noticeTypeList.value = data.notice_type

  data.module.forEach( (item) => {
    sceneMap.set(item.key, item.scene)
  } )
}

const getRuleList = () => {
  const ruleData = JSON.parse(window.sessionStorage.getItem('ruleData') || 'null')
  if (ruleData) {
    valueList(ruleData)
    return
  }
  useTryCatch( async () => {
    const res = await getSubscribeRule()
    if (res.code === 200) {
      valueList(res.data)
      window.sessionStorage.setItem('ruleData', JSON.stringify(res.data))
    }
  } )
}

const getDetail = () => {
  useTryCatch( async () => {
    const res = await getSubscribeDetail(props.id as number)
    if (res.code === 200) {
      const data = res.data as detailData
      Object.keys(submitData_raw).forEach( (key) => {
        submitData[key] = key === 'notice_user_id' ? data[key].split(',') : data[key]
      } )
      const rule: ruleItem[] = []

      data.rule.forEach( (item) => {
        const obj = {} as ruleItem
        Object.keys(ruleItem).forEach( (key) => {
          obj[key] = item[key]
        } )
        rule.push(obj)
        sceneList.value.push(sceneMap.get(item.module) || [] )
      } )
      submitData.rule = rule
      refreshChannelList()
    }
  } )
}

const initData = () => {
  getRuleList()
  if (props.id) {
    getDetail()
    title.value = '编辑预警'
  }
  if (props.readonly) {
    title.value = '预警详情'
  }
}

const submitForm = (formEl: FormInstance | undefined) => {
  if (!formEl) return
  formEl.validate((valid) => {
    const rule = submitData.rule
    for (const item of rule) {
      for ( const val of Object.values(item) ) {
        if (!val) {
          return ElMessage.warning('请完善规则信息')
        }
      }
    }
    if (valid) {
      handleSave()
    } 
  })
}

const handleSave = () => {
  useTryCatch( async () => {
    setLoading(true)

    const api = props.id ? editSubscribe : addSubscribe

    const params = { 
      ...submitData, 
      notice_user_id: String(submitData.notice_user_id),
      ...props.id ? { id: props.id } : {}
    }
    const res = await api(params)
    if (res.code === 200) {
      ElMessage.success(res.msg)
      emit('save')
      show.value = false
    } else {
      ElMessage.error(res.msg)
    }

    setLoading(false)
  }, () => setLoading(false) )
}

initData()
</script>

<style lang="scss">
.title-prepend {
  display: flex;
  align-items: center;
  margin-bottom: 36px;
  &::before {
    content: '';
    display: inline-block;
    width: 6px;
    height: 24px;
    background: var(--el-color-primary);
    margin-right: 10px;
  }
}

.is-error_custom {
  .error {
    .el-input__wrapper {
      box-shadow: 0 0 0 1px var(--el-color-danger) inset;
    }
  }
  .success {
    .el-input__wrapper {
      box-shadow: 0 0 0 1px var(--el-input-border-color,var(--el-border-color)) inset;
    }
  }
}
</style>