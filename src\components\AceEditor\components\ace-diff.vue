<template>
  <div
    ref="diffRef"
    id="ace-diff-panel" 
    class="ace-diff-panel"
    :style="{ 
      width,
      height, 
      '--fontSize': fontSize + 'px' }"
  >
    
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import AceDiff from 'ace-diff'

const props = withDefaults(defineProps<
  {
    width?: string;
    height?: string;
    leftVal: string;
    rightVal: string;
    fontSize?: number|string;
  }
>(),{
  width: () => '100%',
  height: () => '100%',
  fontSize: () => 16
})

const diffRef = ref()
const diffIntance = ref()

const init = () => {
  diffIntance.value = new AceDiff({
    element: '#ace-diff-panel',
    showDiffs: true,
    left: {
      editable: false,
      copyLinkEnabled: false,
      content: props.leftVal
    },
    right: {
      editable: false,
      copyLinkEnabled: false,
      content: props.rightVal
    }
  })
}

onMounted(() => {
  init()
})
</script>

<style>
#ace-diff-panel .ace_editor {
  font-size: var(--fontSize, 16px);
}
.ace-diff-panel {
  border: 1px solid var(--el-border-color-light);;
}
.acediff__wrap {
  display: flex;
  flex-direction: row;
  position: absolute;
  bottom: 0;
  top: 0px;
  left: 0px;
  height: 100%;
  width: 100%;
  overflow: auto;
}

.acediff__gutter {
  flex: 0 0 60px;
  border-left: 1px solid #999;
  border-right: 1px solid #999;
  background-color: #efefef;
  overflow: hidden;
}
.acediff__gutter svg {
  background-color: #efefef;
}
.acediff__left,
.acediff__right {
  height: 100%;
  flex: 1;
}
.acediff__diffLine {
  background-color: #d8f2ff;
  border-top: 1px solid #a2d7f2;
  border-bottom: 1px solid #a2d7f2;
  position: absolute;
  z-index: 4;
}
.acediff__diffLine.targetOnly {
  height: 0px !important;
  border-top: 1px solid #a2d7f2;
  border-bottom: 0px;
  position: absolute;
}
.acediff__connector {
  fill: #d8f2ff;
  stroke: #a2d7f2;
}
.acediff__copy--right,
.acediff__copy--left {
  position: relative;
}
.acediff__copy--right div,
.acediff__copy--left div {
  color: rgb(0, 0, 0);
  text-shadow: 1px 1px #fff;
  position: absolute;
  margin: 2px 3px;
  cursor: pointer;
}
.acediff__copy--right div:hover {
  color: var(--el-color-error);
}
.acediff__copy--left {
  float: right;
}
.acediff__copy--left div {
  right: 0px;
}
.acediff__copy--left div:hover {
  color: #c98100;
}
</style>