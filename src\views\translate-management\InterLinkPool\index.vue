<template>
  <div class="scroll-y with-flex">
    <QueryForm 
      :form-list="formList"
      :loading="loading"
      @search="handleSearch"
      hide-reset
    />
    <div v-loading="loading" class="sticky-table">
      <CustomTable 
        ref="tableRef"
        :columns="columns"
        :data="tableData"
        :table-method="getList"
        :operate-list="operateList"
        :index-method="indexMethod"
        @selection-change="handleSelectionChange"
      />
    </div> 
  </div>
</template>

<script setup lang="tsx">
import { ref, reactive, toRef } from 'vue'
import { 
  ElButton, 
  ElMessage, ElMessageBox,
  ElSpace,
  ElSelect, ElOption,
  ElInput,
  ElUpload
} from 'element-plus'
import useTryCatch from '@/hooks/use-try-catch'
import useLoading from '@/hooks/use-loading'
import { getHost } from '@/hooks/use-env'
import { poolList, addPool, editPool, deletePool, importPool, poolLangList } from '@/api/translate-management'
import type { poolListItem, poolQueryParams } from '@/api/translate-management'

import QueryForm from '@/components/QueryForm/index.vue'
import CustomTable from '@/components/CustomTable/index.vue'

defineOptions( { name: 'InternalLinkPool' } )

const commonMsgBox = (title: string) => {
  return {
    title,
    customClass: 'dialog-button-center',
    customStyle: {
      maxWidth: 'none',
      width: '400px',
    },
    showCancelButton: false,
    showConfirmButton: false,
    closeOnPressEscape: false,
    closeOnClickModal: false,
    callback: () => {},
  }
}

const { loading, setLoading } = useLoading()
const langList = ref<{ lang_id: number; lang_code: string; name: string; smart_lang_code: string; }[]>([])

const queryParams = reactive<poolQueryParams>({
  source: '',
  target: '',
  source_language: '',
  target_language: '',
})

const formList = ref([
{
    label: '筛选来源语言',
    placeholder: '筛选来源语言',
    value: toRef(queryParams, 'source_language'),
    component: 'select',
    selections: langList,
    labelKey: 'name',
    valueKey: 'lang_code',
  },
  {
    label: '筛选目标语言',
    placeholder: '筛选目标语言',
    value: toRef(queryParams, 'target_language'),
    component: 'select',
    selections: langList,
    labelKey: 'name',
    valueKey: 'lang_code',
  },
  {
    label: '来源链接',
    placeholder: '来源链接',
    value: toRef(queryParams, 'source'),
    component: 'input',
  },
  {
    label: '替换链接',
    placeholder: '替换链接',
    value: toRef(queryParams, 'target'),
    component: 'input',
  }
])
const tableRef = ref()
const tableData = ref<poolListItem[]>([])
const tableSelections = ref<poolListItem[]>([])
const operateList = ref([
  {
    title: '批量导入',
    button: true,
    append: true,
    method: () => {
      const source_language = ref('en')
      const target_language = ref('')
      ElMessageBox({
        ...commonMsgBox('批量导入预设内链'),
        message: () => h('div', {}, [
          h(ElSpace, {
            alignment: 'start',
            size: 16,
            style: { marginBottom: '16px' },
          }, () => [
            h('div', { style: { paddingTop: '6px' } }, '来源语言 : '),
            h(ElSelect, {
              modelValue: source_language.value,
              onChange: (val) => {
                source_language.value = val
              },
            }, () => langList.value.map(( item ) => h(ElOption, { label: item.name, value: item.lang_code })))
          ]),
          h(ElSpace, {
            alignment: 'start',
            size: 16,
            style: { marginBottom: '16px' },
          }, () => [
            h('div', { style: { paddingTop: '6px' } }, '目标语言 : '),
            h(ElSelect, {
              modelValue: target_language.value,
              onChange: (val) => {
                target_language.value = val
              },
            }, () => langList.value.map(( item ) => h(ElOption, { 
              label: item.name,
              value: item.lang_code,
              disabled: item.lang_code === source_language.value
            })))
          ]),
          h(ElSpace, {
            alignment: 'start',
            size: 16,
            style: { marginBottom: '16px' },
          }, () => [
            h('div', '模板下载 : '),
            h(ElButton, {
              type: 'primary',
              link: true,
              onClick: () => {
                location.href = `${getHost()}/api/v1/translate/pool_download_template`
              }
            }, () => '预设内链模板'),
          ]),
          h('br'),
          h(ElSpace, {
            alignment: 'start',
            size: 16,
            style: { marginBottom: '16px' },
          }, () => [
            h('div', { style: { paddingTop: '6px' } }, '上传模板 : '),
            h(ElUpload, {
              action: 'string',
              accept: 'xlsx',
              autoUpload: false,
              showFileList: false,
              limit: 1,
              beforeUpload: () => true,
              onChange: (file) => {
                const params = {
                  source_language: source_language.value,
                  target_language: target_language.value,
                  file: file.raw as Blob
                }
                useTryCatch( async () => {
                  setLoading(true)

                  const res = await importPool(params)
                  if (res.code === 200) {
                    handleSearch()
                    ElMessage.success(res.msg)
                  } else {
                    ElMessage.error(res.msg)
                  }

                  ElMessageBox.close()
                  setLoading(false)
                }, () => setLoading(false) )
              }
            }, () => h(ElButton, { type: 'primary', loading: loading.value, disabled: !target_language.value }, () => '上传'))
          ]),
        ])
      })
    }
  },
  {
    title: '创建',
    button: true,
    append: true,
    method: () => {
      handleAdd()
    }
  },
])
const columns = ref([
  {
    title: '翻译语言',
    dataKey: 'source_language',
    width: 100,
    cellRenderer: ( scope: { row: poolListItem } ) => (
      <>
        { scope.row.source_language.toUpperCase() + ' > ' + scope.row.target_language .toUpperCase()}
      </>
    )
  },
  {
    title: '来源链接',
    dataKey: 'source',
    minWidth: 280,
  },
  {
    title: '替换链接',
    dataKey: 'target',
    minWidth: 280,
  },
  {
    title: '操作人',
    dataKey: 'edit_user',
    width: 100,
  },
  {
    title: '操作',
    dataKey: '',
    fixed: 'right',
    width: 160,
    cellRenderer: ( scope: { row: poolListItem } ) => (
      <>
        <ElButton type='primary' plain onClick={() => handleEdit(scope.row)}> 编辑 </ElButton>
        <ElButton type='danger' plain onClick={() => handleDelete(scope.row)}> 删除 </ElButton>
      </>
    )
  }
])

const handleGetLangList = () => {
  if (langList.value.length === 0) {
    useTryCatch( async () => {
      const res = await poolLangList()
      if (res.code === 200) {
        langList.value = res.data
      }
    } )
  }
}

const refresh = () => {
  const { page, pageSize } = tableRef?.value?.getPagination()
  getList( { page, pageSize } )
}

const handleSearch = () => {
  tableRef.value.setPage(1)
  refresh()
}

const indexMethod = (index: number) => {
  const { page, pageSize } = tableRef.value.getPagination()
  return (page - 1) * pageSize + index + 1
}

const getList = ( { page, pageSize } ) => {
  useTryCatch( async () => {
    setLoading(true)

    const params = {
      ...queryParams,
      page,
      page_size: pageSize
    }

    const res = await poolList( params )
    if (res.code === 200) {
      tableData.value = res.data.list
      tableRef.value?.setTotal(res.data.total)
    } else {
      ElMessage.error(res.msg)
    }

    setLoading(false)
  }, () => setLoading(false) )
}

const handleSelectionChange = (selections: poolListItem[]) => {
  tableSelections.value = selections
}

const handleAdd = () => {
  const params = reactive({
    source_language: 'en',
    target_language: '',
    source: '',
    target: '',
  })
  ElMessageBox({
    ...commonMsgBox('添加预设内链'),
    message: () => h('div', {}, [
      h(ElSpace, {
        alignment: 'start',
        size: 16,
        style: { marginBottom: '16px' },
      }, () => [
        h('div', { style: { paddingTop: '6px' }, class: 'is-required' }, '来源语言 : '),
        h(ElSelect, {
          modelValue: params.source_language,
          onChange: (val) => {
            params.source_language = val
          }
        }, () => langList.value.map(( item ) => h(ElOption, { label: item.name, value: item.lang_code })))
      ]),
      h(ElSpace, {
        alignment: 'start',
        size: 16,
        style: { marginBottom: '16px' },
      }, () => [
        h('div', { style: { paddingTop: '6px' }, class: 'is-required' }, '目标语言 : '),
        h(ElSelect, {
          modelValue: params.target_language,
          onChange: (val) => {
            params.target_language = val
          }
        }, () => langList.value.map(( item ) => h(ElOption, { 
          label: item.name,
          value: item.lang_code,
          disabled: params.source_language === item.lang_code
        })))
      ]),
      h(ElSpace, {
        alignment: 'start',
        size: 16,
        style: { marginBottom: '16px' },
      }, () => [
        h('div', { style: { paddingTop: '6px' }, class: 'is-required' }, '来源链接 : '),
        h(ElInput, {
          style: { width: '220px' },
          modelValue: params.source,
          onInput: (val) => {
            params.source = val
          }
        }),
      ]),
      h(ElSpace, {
        alignment: 'start',
        size: 16,
        style: { marginBottom: '16px' },
      }, () => [
        h('div', { style: { paddingTop: '6px' }, class: 'is-required' }, '替换链接 : '),
        h(ElInput, {
          style: { width: '220px' },
          modelValue: params.target,
          onInput: (val) => {
            params.target = val
          }
        }),
      ]),
      h('div', { style: { marginTop: '16px' }, class: 'text-center' }, [
        h(ElButton, {
          onClick: () => {
            ElMessageBox.close()
          }
        }, () => '取消'),
        h(ElButton, {
          type: 'primary',
          disabled: !params.target_language || !params.source || !params.target,
          loading: loading.value,
          onClick: () => {
            useTryCatch( async () => {
              setLoading(true)

              const res = await addPool(params)
              if (res.code === 200) {
                handleSearch()
                ElMessage.success(res.msg)
              } else {
                ElMessage.error(res.msg)
              }

              ElMessageBox.close()
              setLoading(false)
            }, () => setLoading(false) )
          }
        }, () => '确认')
      ])
    ])
  })
}

const handleEdit = (row: poolListItem) => {
  const params = reactive({
    id: row.id,
    source: row.source,
    target: row.target,
  })
  ElMessageBox({
    ...commonMsgBox('编辑预设内链'),
    message: () => h('div', {}, [
      h(ElSpace, {
        alignment: 'start',
        size: 16,
        style: { marginBottom: '16px' },
      }, () => [
        h('div', { style: { paddingTop: '6px' }, class: 'is-required' }, '来源链接 : '),
        h(ElInput, {
          style: { width: '220px' },
          modelValue: params.source,
          onInput: (val) => {
            params.source = val
          }
        }),
      ]),
      h(ElSpace, {
        alignment: 'start',
        size: 16,
        style: { marginBottom: '16px' },
      }, () => [
        h('div', { style: { paddingTop: '6px' }, class: 'is-required' }, '替换链接 : '),
        h(ElInput, {
          style: { width: '220px' },
          modelValue: params.target,
          onInput: (val) => {
            params.target = val
          }
        }),
      ]),
      h('div', { style: { marginTop: '16px' }, class: 'text-center' }, [
        h(ElButton, {
          onClick: () => {
            ElMessageBox.close()
          }
        }, () => '取消'),
        h(ElButton, {
          type: 'primary',
          disabled: !params.source || !params.target,
          loading: loading.value,
          onClick: () => {
            useTryCatch( async () => {
              setLoading(true)

              const res = await editPool(params)
              if (res.code === 200) {
                refresh()
                ElMessage.success(res.msg)
              } else {
                ElMessage.error(res.msg)
              }

              ElMessageBox.close()
              setLoading(false)
            }, () => setLoading(false) )
          }
        }, () => '确认')
      ])
    ]),
  })
}

const handleDelete = (row: poolListItem) => {
  ElMessageBox.confirm('请确认是否删除此条预设内链', '提示', {
    type: 'warning',
    confirmButtonText: '确认',
    cancelButtonText: '取消',
    callback: (action: string) => {
      if (action === 'confirm') {
        useTryCatch( async () => {
          setLoading(true)

          const res = await deletePool(row.id)
          if (res.code === 200) {
            refresh()
            ElMessage.success(res.msg)
          } else {
            ElMessage.error(res.msg)
          }

          setLoading(false)
        }, () => setLoading(false) )
      }
    }
  })
}

handleGetLangList()
</script>