.tox {
  box-sizing: content-box;
  color: #222f3e;
  font-weight: 400;
  font-size: 16px;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto,
    Oxygen-Sans, Ubuntu, Cantarell, 'Helvetica Neue', sans-serif;
  font-style: normal;
  line-height: normal;
  white-space: normal;
  text-transform: none;
  text-decoration: none;
  text-shadow: none;
  vertical-align: initial;
  box-shadow: none;
  cursor: auto;
  -webkit-tap-highlight-color: transparent;
}

.tox :not(svg):not(rect) {
  box-sizing: inherit;
  color: inherit;
  font-weight: inherit;
  font-size: inherit;
  font-family: inherit;
  font-style: inherit;
  line-height: inherit;
  direction: inherit;
  white-space: inherit;
  text-align: inherit;
  text-transform: inherit;
  text-decoration: inherit;
  text-shadow: inherit;
  vertical-align: inherit;
  cursor: inherit;
  -webkit-tap-highlight-color: inherit;
}

.tox :not(svg):not(rect) {
  position: static;
  float: none;
  width: auto;
  max-width: none;
  height: auto;
  margin: 0;
  padding: 0;
  background: 0 0;
  border: 0;
  outline: 0;
  box-shadow: none;
}

.tox:not([dir='rtl']) {
  direction: ltr;
  text-align: left;
}

.tox[dir='rtl'] {
  direction: rtl;
  text-align: right;
}

.tox-tinymce {
  position: relative;
  display: flex;
  flex-direction: column;
  box-sizing: border-box;
  overflow: hidden;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto,
    Oxygen-Sans, Ubuntu, Cantarell, 'Helvetica Neue', sans-serif;
  border: 2px solid #161f29;
  border-radius: 10px;
  box-shadow: none;
  visibility: inherit !important;
}

.tox.tox-tinymce-inline {
  overflow: initial;
  border: none;
  box-shadow: none;
}

.tox.tox-tinymce-inline .tox-editor-container {
  overflow: initial;
}

.tox.tox-tinymce-inline .tox-editor-header {
  overflow: hidden;
  background-color: #222f3e;
  border: 2px solid #161f29;
  border-radius: 10px;
  box-shadow: none;
}

.tox-tinymce-aux {
  z-index: 1300;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto,
    Oxygen-Sans, Ubuntu, Cantarell, 'Helvetica Neue', sans-serif;
}

.tox-tinymce :focus,
.tox-tinymce-aux :focus {
  outline: 0;
}

button::-moz-focus-inner {
  border: 0;
}

.tox[dir='rtl'] .tox-icon--flip svg {
  transform: rotateY(180deg);
}

.tox .accessibility-issue__header {
  display: flex;
  align-items: center;
  margin-bottom: 4px;
}

.tox .accessibility-issue__description {
  display: flex;
  align-items: stretch;
  justify-content: space-between;
  border: 1px solid #161f29;
  border-radius: 6px;
}

.tox .accessibility-issue__description > div {
  padding-bottom: 4px;
}

.tox .accessibility-issue__description > div > div {
  display: flex;
  align-items: center;
  margin-bottom: 4px;
}

.tox .accessibility-issue__description > :last-child:not(:only-child) {
  border-color: #161f29;
  border-style: solid;
}

.tox .accessibility-issue__repair {
  margin-top: 16px;
}

.tox
  .tox-dialog__body-content
  .accessibility-issue--info
  .accessibility-issue__description {
  color: #fff;
  background-color: rgba(0, 108, 231, 0.5);
  border-color: rgba(0, 108, 231, 0.4);
}

.tox
  .tox-dialog__body-content
  .accessibility-issue--info
  .accessibility-issue__description
  > :last-child {
  border-color: rgba(0, 108, 231, 0.4);
}

.tox .tox-dialog__body-content .accessibility-issue--info .tox-form__group h2 {
  color: #fff;
}

.tox .tox-dialog__body-content .accessibility-issue--info .tox-icon svg {
  fill: #fff;
}

.tox .tox-dialog__body-content .accessibility-issue--info a .tox-icon {
  color: #fff;
}

.tox
  .tox-dialog__body-content
  .accessibility-issue--warn
  .accessibility-issue__description {
  color: #fff;
  background-color: rgba(255, 165, 0, 0.5);
  border-color: rgba(255, 165, 0, 0.8);
}

.tox
  .tox-dialog__body-content
  .accessibility-issue--warn
  .accessibility-issue__description
  > :last-child {
  border-color: rgba(255, 165, 0, 0.8);
}

.tox .tox-dialog__body-content .accessibility-issue--warn .tox-form__group h2 {
  color: #fff;
}

.tox .tox-dialog__body-content .accessibility-issue--warn .tox-icon svg {
  fill: #fff;
}

.tox .tox-dialog__body-content .accessibility-issue--warn a .tox-icon {
  color: #fff;
}

.tox
  .tox-dialog__body-content
  .accessibility-issue--error
  .accessibility-issue__description {
  color: #fff;
  background-color: rgba(204, 0, 0, 0.5);
  border-color: rgba(204, 0, 0, 0.8);
}

.tox
  .tox-dialog__body-content
  .accessibility-issue--error
  .accessibility-issue__description
  > :last-child {
  border-color: rgba(204, 0, 0, 0.8);
}

.tox .tox-dialog__body-content .accessibility-issue--error .tox-form__group h2 {
  color: #fff;
}

.tox .tox-dialog__body-content .accessibility-issue--error .tox-icon svg {
  fill: #fff;
}

.tox .tox-dialog__body-content .accessibility-issue--error a .tox-icon {
  color: #fff;
}

.tox
  .tox-dialog__body-content
  .accessibility-issue--success
  .accessibility-issue__description {
  color: #fff;
  background-color: rgba(120, 171, 70, 0.5);
  border-color: rgba(120, 171, 70, 0.8);
}

.tox
  .tox-dialog__body-content
  .accessibility-issue--success
  .accessibility-issue__description
  > :last-child {
  border-color: rgba(120, 171, 70, 0.8);
}

.tox
  .tox-dialog__body-content
  .accessibility-issue--success
  .tox-form__group
  h2 {
  color: #fff;
}

.tox .tox-dialog__body-content .accessibility-issue--success .tox-icon svg {
  fill: #fff;
}

.tox .tox-dialog__body-content .accessibility-issue--success a .tox-icon {
  color: #fff;
}

.tox .tox-dialog__body-content .accessibility-issue__header h1,
.tox
  .tox-dialog__body-content
  .tox-form__group
  .accessibility-issue__description
  h2 {
  margin-top: 0;
}

.tox:not([dir='rtl'])
  .tox-dialog__body-content
  .accessibility-issue__header
  .tox-button {
  margin-left: 4px;
}

.tox:not([dir='rtl'])
  .tox-dialog__body-content
  .accessibility-issue__header
  > :nth-last-child(2) {
  margin-left: auto;
}

.tox:not([dir='rtl'])
  .tox-dialog__body-content
  .accessibility-issue__description {
  padding: 4px 4px 4px 8px;
}

.tox:not([dir='rtl'])
  .tox-dialog__body-content
  .accessibility-issue__description
  > :last-child {
  padding-left: 4px;
  border-left-width: 1px;
}

.tox[dir='rtl']
  .tox-dialog__body-content
  .accessibility-issue__header
  .tox-button {
  margin-right: 4px;
}

.tox[dir='rtl']
  .tox-dialog__body-content
  .accessibility-issue__header
  > :nth-last-child(2) {
  margin-right: auto;
}

.tox[dir='rtl'] .tox-dialog__body-content .accessibility-issue__description {
  padding: 4px 8px 4px 4px;
}

.tox[dir='rtl']
  .tox-dialog__body-content
  .accessibility-issue__description
  > :last-child {
  padding-right: 4px;
  border-right-width: 1px;
}

.tox .tox-anchorbar {
  display: flex;
  flex: 0 0 auto;
}

.tox .tox-bar {
  display: flex;
  flex: 0 0 auto;
}

.tox .tox-button {
  position: relative;
  display: inline-block;
  box-sizing: border-box;
  margin: 0;
  padding: 4px 16px;
  color: #fff;
  font-weight: 700;
  font-size: 14px;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto,
    Oxygen-Sans, Ubuntu, Cantarell, 'Helvetica Neue', sans-serif;
  font-style: normal;
  line-height: 24px;
  letter-spacing: normal;
  white-space: nowrap;
  text-align: center;
  text-transform: none;
  text-decoration: none;
  background-color: #006ce7;
  background-image: none;
  background-repeat: repeat;
  background-position: 0 0;
  border-color: #006ce7;
  border-style: solid;
  border-width: 1px;
  border-radius: 6px;
  outline: 0;
  box-shadow: none;
  cursor: pointer;
}

.tox .tox-button::before {
  position: absolute;
  top: -1px;
  right: -1px;
  bottom: -1px;
  left: -1px;
  border-radius: 6px;
  box-shadow: inset 0 0 0 2px #fff, 0 0 0 1px #006ce7,
    0 0 0 3px rgba(0, 108, 231, 0.25);
  opacity: 0;
  content: '';
  pointer-events: none;
}

.tox .tox-button[disabled] {
  color: rgba(255, 255, 255, 0.5);
  background-color: #006ce7;
  background-image: none;
  border-color: #006ce7;
  box-shadow: none;
  cursor: not-allowed;
}

.tox .tox-button:focus:not(:disabled) {
  color: #fff;
  background-color: #0060ce;
  background-image: none;
  border-color: #0060ce;
  box-shadow: none;
}

.tox .tox-button:focus-visible:not(:disabled)::before {
  opacity: 1;
}

.tox .tox-button:hover:not(:disabled) {
  color: #fff;
  background-color: #0060ce;
  background-image: none;
  border-color: #0060ce;
  box-shadow: none;
}

.tox .tox-button:active:not(:disabled) {
  color: #fff;
  background-color: #0054b4;
  background-image: none;
  border-color: #0054b4;
  box-shadow: none;
}

.tox .tox-button--secondary {
  padding: 4px 16px;
  color: #fff;
  font-weight: 700;
  font-size: 14px;
  font-style: normal;
  letter-spacing: normal;
  text-transform: none;
  text-decoration: none;
  background-color: #3d546f;
  background-image: none;
  background-repeat: repeat;
  background-position: 0 0;
  border-color: #3d546f;
  border-style: solid;
  border-width: 1px;
  border-radius: 6px;
  outline: 0;
  box-shadow: none;
}

.tox .tox-button--secondary[disabled] {
  color: rgba(255, 255, 255, 0.5);
  background-color: #3d546f;
  background-image: none;
  border-color: #3d546f;
  box-shadow: none;
}

.tox .tox-button--secondary:focus:not(:disabled) {
  color: #fff;
  background-color: #34485f;
  background-image: none;
  border-color: #34485f;
  box-shadow: none;
}

.tox .tox-button--secondary:hover:not(:disabled) {
  color: #fff;
  background-color: #34485f;
  background-image: none;
  border-color: #34485f;
  box-shadow: none;
}

.tox .tox-button--secondary:active:not(:disabled) {
  color: #fff;
  background-color: #2b3b4e;
  background-image: none;
  border-color: #2b3b4e;
  box-shadow: none;
}

.tox .tox-button--icon,
.tox .tox-button.tox-button--icon,
.tox .tox-button.tox-button--secondary.tox-button--icon {
  padding: 4px;
}

.tox .tox-button--icon .tox-icon svg,
.tox .tox-button.tox-button--icon .tox-icon svg,
.tox .tox-button.tox-button--secondary.tox-button--icon .tox-icon svg {
  display: block;
  fill: currentColor;
}

.tox .tox-button-link {
  display: inline-block;
  box-sizing: border-box;
  margin: 0;
  padding: 0;
  font-weight: 400;
  font-size: 16px;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto,
    Oxygen-Sans, Ubuntu, Cantarell, 'Helvetica Neue', sans-serif;
  line-height: 1.3;
  white-space: nowrap;
  background: 0;
  border: none;
  cursor: pointer;
}

.tox .tox-button-link--sm {
  font-size: 14px;
}

.tox .tox-button--naked {
  color: #fff;
  background-color: transparent;
  border-color: transparent;
  box-shadow: unset;
}

.tox .tox-button--naked[disabled] {
  color: rgba(255, 255, 255, 0.5);
  background-color: rgba(255, 255, 255, 0.2);
  border-color: transparent;
  box-shadow: unset;
}

.tox .tox-button--naked:hover:not(:disabled) {
  color: #fff;
  background-color: rgba(255, 255, 255, 0.2);
  border-color: transparent;
  box-shadow: unset;
}

.tox .tox-button--naked:focus:not(:disabled) {
  color: #fff;
  background-color: rgba(255, 255, 255, 0.2);
  border-color: transparent;
  box-shadow: unset;
}

.tox .tox-button--naked:active:not(:disabled) {
  color: #fff;
  background-color: rgba(255, 255, 255, 0.3);
  border-color: transparent;
  box-shadow: unset;
}

.tox .tox-button--naked .tox-icon svg {
  fill: currentColor;
}

.tox .tox-button--naked.tox-button--icon:hover:not(:disabled) {
  color: #fff;
}

.tox .tox-checkbox {
  display: flex;
  align-items: center;
  min-width: 36px;
  height: 36px;
  border-radius: 6px;
  cursor: pointer;
}

.tox .tox-checkbox__input {
  position: absolute;
  top: auto;
  width: 1px;
  height: 1px;
  overflow: hidden;
}

.tox .tox-checkbox__icons {
  display: flex;
  align-items: center;
  justify-content: center;
  box-sizing: content-box;
  width: 24px;
  height: 24px;
  padding: calc(4px - 1px);
  border-radius: 6px;
  box-shadow: 0 0 0 2px transparent;
}

.tox .tox-checkbox__icons .tox-checkbox-icon__unchecked svg {
  display: block;
  fill: rgba(255, 255, 255, 0.2);
}

.tox .tox-checkbox__icons .tox-checkbox-icon__indeterminate svg {
  display: none;
  fill: #006ce7;
}

.tox .tox-checkbox__icons .tox-checkbox-icon__checked svg {
  display: none;
  fill: #006ce7;
}

.tox .tox-checkbox--disabled {
  color: rgba(255, 255, 255, 0.5);
  cursor: not-allowed;
}

.tox
  .tox-checkbox--disabled
  .tox-checkbox__icons
  .tox-checkbox-icon__checked
  svg {
  fill: rgba(255, 255, 255, 0.5);
}

.tox
  .tox-checkbox--disabled
  .tox-checkbox__icons
  .tox-checkbox-icon__unchecked
  svg {
  fill: rgba(255, 255, 255, 0.5);
}

.tox
  .tox-checkbox--disabled
  .tox-checkbox__icons
  .tox-checkbox-icon__indeterminate
  svg {
  fill: rgba(255, 255, 255, 0.5);
}

.tox
  input.tox-checkbox__input:checked
  + .tox-checkbox__icons
  .tox-checkbox-icon__unchecked
  svg {
  display: none;
}

.tox
  input.tox-checkbox__input:checked
  + .tox-checkbox__icons
  .tox-checkbox-icon__checked
  svg {
  display: block;
}

.tox
  input.tox-checkbox__input:indeterminate
  + .tox-checkbox__icons
  .tox-checkbox-icon__unchecked
  svg {
  display: none;
}

.tox
  input.tox-checkbox__input:indeterminate
  + .tox-checkbox__icons
  .tox-checkbox-icon__indeterminate
  svg {
  display: block;
}

.tox input.tox-checkbox__input:focus + .tox-checkbox__icons {
  padding: calc(4px - 1px);
  border-radius: 6px;
  box-shadow: inset 0 0 0 1px #006ce7;
}

.tox:not([dir='rtl']) .tox-checkbox__label {
  margin-left: 4px;
}

.tox:not([dir='rtl']) .tox-checkbox__input {
  left: -10000px;
}

.tox:not([dir='rtl']) .tox-bar .tox-checkbox {
  margin-left: 4px;
}

.tox[dir='rtl'] .tox-checkbox__label {
  margin-right: 4px;
}

.tox[dir='rtl'] .tox-checkbox__input {
  right: -10000px;
}

.tox[dir='rtl'] .tox-bar .tox-checkbox {
  margin-right: 4px;
}

.tox .tox-collection--toolbar .tox-collection__group {
  display: flex;
  padding: 0;
}

.tox .tox-collection--grid .tox-collection__group {
  display: flex;
  flex-wrap: wrap;
  max-height: 208px;
  padding: 0;
  overflow-x: hidden;
  overflow-y: auto;
}

.tox .tox-collection--list .tox-collection__group {
  padding: 4px 0;
  border-color: rgba(255, 255, 255, 0.15);
  border-style: solid;
  border-top-width: 1px;
  border-right-width: 0;
  border-bottom-width: 0;
  border-left-width: 0;
}

.tox .tox-collection--list .tox-collection__group:first-child {
  border-top-width: 0;
}

.tox .tox-collection__group-heading {
  margin-top: -4px;
  margin-bottom: 4px;
  padding: 4px 8px;
  color: rgba(255, 255, 255, 0.5);
  font-weight: 400;
  font-size: 12px;
  font-style: normal;
  text-transform: none;
  background-color: rgba(255, 255, 255, 0.15);
  cursor: default;
  -webkit-user-select: none;
  -moz-user-select: none;
  user-select: none;
  -webkit-touch-callout: none;
}

.tox .tox-collection__item {
  display: flex;
  align-items: center;
  color: #fff;
  border-radius: 3px;
  -webkit-user-select: none;
  -moz-user-select: none;
  user-select: none;
  -webkit-touch-callout: none;
}

.tox .tox-collection--list .tox-collection__item {
  padding: 4px 8px;
}

.tox .tox-collection--toolbar .tox-collection__item {
  padding: 4px;
  border-radius: 3px;
}

.tox .tox-collection--grid .tox-collection__item {
  padding: 4px;
  border-radius: 3px;
}

.tox .tox-collection--list .tox-collection__item--enabled {
  color: #fff;
  background-color: #2b3b4e;
}

.tox .tox-collection--list .tox-collection__item--active {
  background-color: #3389ec;
}

.tox .tox-collection--toolbar .tox-collection__item--enabled {
  color: #fff;
  background-color: #599fef;
}

.tox .tox-collection--toolbar .tox-collection__item--active {
  background-color: #3389ec;
}

.tox .tox-collection--grid .tox-collection__item--enabled {
  color: #fff;
  background-color: #599fef;
}

.tox
  .tox-collection--grid
  .tox-collection__item--active:not(.tox-collection__item--state-disabled) {
  color: #fff;
  background-color: #3389ec;
}

.tox
  .tox-collection--list
  .tox-collection__item--active:not(.tox-collection__item--state-disabled) {
  color: #fff;
}

.tox
  .tox-collection--toolbar
  .tox-collection__item--active:not(.tox-collection__item--state-disabled) {
  color: #fff;
}

.tox .tox-collection__item-checkmark,
.tox .tox-collection__item-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 24px;
  height: 24px;
}

.tox .tox-collection__item-checkmark svg,
.tox .tox-collection__item-icon svg {
  fill: currentColor;
}

.tox .tox-collection--toolbar-lg .tox-collection__item-icon {
  width: 48px;
  height: 48px;
}

.tox .tox-collection__item-label {
  display: inline-block;
  flex: 1;
  color: currentColor;
  font-weight: 400;
  font-size: 14px;
  font-style: normal;
  line-height: 24px;
  text-transform: none;
  word-break: break-all;
}

.tox .tox-collection__item-accessory {
  display: inline-block;
  height: 24px;
  color: rgba(255, 255, 255, 0.5);
  font-size: 14px;
  line-height: 24px;
  text-transform: none;
}

.tox .tox-collection__item-caret {
  display: flex;
  align-items: center;
  min-height: 24px;
}

.tox .tox-collection__item-caret::after {
  min-height: inherit;
  font-size: 0;
  content: '';
}

.tox .tox-collection__item-caret svg {
  fill: #fff;
}

.tox .tox-collection__item--state-disabled {
  color: rgba(255, 255, 255, 0.5);
  background-color: transparent;
  cursor: not-allowed;
}

.tox .tox-collection__item--state-disabled .tox-collection__item-caret svg {
  fill: rgba(255, 255, 255, 0.5);
}

.tox
  .tox-collection--list
  .tox-collection__item:not(.tox-collection__item--enabled)
  .tox-collection__item-checkmark
  svg {
  display: none;
}

.tox
  .tox-collection--list
  .tox-collection__item:not(.tox-collection__item--enabled)
  .tox-collection__item-accessory
  + .tox-collection__item-checkmark {
  display: none;
}

.tox .tox-collection--horizontal {
  display: flex;
  flex: 0 0 auto;
  flex-shrink: 0;
  flex-wrap: nowrap;
  margin-bottom: 0;
  padding: 0;
  overflow-x: auto;
  background-color: #2b3b4e;
  border: 1px solid rgba(255, 255, 255, 0.15);
  border-radius: 6px;
  box-shadow: 0 0 2px 0 rgba(34, 47, 62, 0.2),
    0 4px 8px 0 rgba(34, 47, 62, 0.15);
}

.tox .tox-collection--horizontal .tox-collection__group {
  display: flex;
  flex-wrap: nowrap;
  align-items: center;
  margin: 0;
  padding: 0 4px;
}

.tox .tox-collection--horizontal .tox-collection__item {
  height: 28px;
  margin: 6px 1px 5px 0;
  padding: 0 4px;
}

.tox .tox-collection--horizontal .tox-collection__item-label {
  white-space: nowrap;
}

.tox .tox-collection--horizontal .tox-collection__item-caret {
  margin-left: 4px;
}

.tox .tox-collection__item-container {
  display: flex;
}

.tox .tox-collection__item-container--row {
  flex: 1 1 auto;
  flex-direction: row;
  align-items: center;
}

.tox
  .tox-collection__item-container--row.tox-collection__item-container--align-left {
  margin-right: auto;
}

.tox
  .tox-collection__item-container--row.tox-collection__item-container--align-right {
  justify-content: flex-end;
  margin-left: auto;
}

.tox
  .tox-collection__item-container--row.tox-collection__item-container--valign-top {
  align-items: flex-start;
  margin-bottom: auto;
}

.tox
  .tox-collection__item-container--row.tox-collection__item-container--valign-middle {
  align-items: center;
}

.tox
  .tox-collection__item-container--row.tox-collection__item-container--valign-bottom {
  align-items: flex-end;
  margin-top: auto;
}

.tox .tox-collection__item-container--column {
  flex: 1 1 auto;
  flex-direction: column;
  align-self: center;
}

.tox
  .tox-collection__item-container--column.tox-collection__item-container--align-left {
  align-items: flex-start;
}

.tox
  .tox-collection__item-container--column.tox-collection__item-container--align-right {
  align-items: flex-end;
}

.tox
  .tox-collection__item-container--column.tox-collection__item-container--valign-top {
  align-self: flex-start;
}

.tox
  .tox-collection__item-container--column.tox-collection__item-container--valign-middle {
  align-self: center;
}

.tox
  .tox-collection__item-container--column.tox-collection__item-container--valign-bottom {
  align-self: flex-end;
}

.tox:not([dir='rtl'])
  .tox-collection--horizontal
  .tox-collection__group:not(:last-of-type) {
  border-right: 1px solid transparent;
}

.tox:not([dir='rtl'])
  .tox-collection--list
  .tox-collection__item
  > :not(:first-child) {
  margin-left: 8px;
}

.tox:not([dir='rtl'])
  .tox-collection--list
  .tox-collection__item
  > .tox-collection__item-label:first-child {
  margin-left: 4px;
}

.tox:not([dir='rtl']) .tox-collection__item-accessory {
  margin-left: 16px;
  text-align: right;
}

.tox:not([dir='rtl']) .tox-collection .tox-collection__item-caret {
  margin-left: 16px;
}

.tox[dir='rtl']
  .tox-collection--horizontal
  .tox-collection__group:not(:last-of-type) {
  border-left: 1px solid transparent;
}

.tox[dir='rtl']
  .tox-collection--list
  .tox-collection__item
  > :not(:first-child) {
  margin-right: 8px;
}

.tox[dir='rtl']
  .tox-collection--list
  .tox-collection__item
  > .tox-collection__item-label:first-child {
  margin-right: 4px;
}

.tox[dir='rtl'] .tox-collection__item-accessory {
  margin-right: 16px;
  text-align: left;
}

.tox[dir='rtl'] .tox-collection .tox-collection__item-caret {
  margin-right: 16px;
  transform: rotateY(180deg);
}

.tox[dir='rtl'] .tox-collection--horizontal .tox-collection__item-caret {
  margin-right: 4px;
}

.tox .tox-color-picker-container {
  display: flex;
  flex-direction: row;
  height: 225px;
  margin: 0;
}

.tox .tox-sv-palette {
  display: flex;
  box-sizing: border-box;
  height: 100%;
}

.tox .tox-sv-palette-spectrum {
  height: 100%;
}

.tox .tox-sv-palette,
.tox .tox-sv-palette-spectrum {
  width: 225px;
}

.tox .tox-sv-palette-thumb {
  position: absolute;
  box-sizing: content-box;
  width: 12px;
  height: 12px;
  background: 0 0;
  border: 1px solid #000;
  border-radius: 50%;
}

.tox .tox-sv-palette-inner-thumb {
  position: absolute;
  width: 10px;
  height: 10px;
  border: 1px solid #fff;
  border-radius: 50%;
}

.tox .tox-hue-slider {
  box-sizing: border-box;
  width: 25px;
  height: 100%;
}

.tox .tox-hue-slider-spectrum {
  width: 100%;
  height: 100%;
  background: linear-gradient(
    to bottom,
    red,
    #ff0080,
    #f0f,
    #8000ff,
    #00f,
    #0080ff,
    #0ff,
    #00ff80,
    #0f0,
    #80ff00,
    #ff0,
    #ff8000,
    red
  );
}

.tox .tox-hue-slider,
.tox .tox-hue-slider-spectrum {
  width: 20px;
}

.tox .tox-hue-slider-thumb {
  box-sizing: content-box;
  width: 100%;
  height: 4px;
  background: #fff;
  border: 1px solid #000;
}

.tox .tox-rgb-form {
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

.tox .tox-rgb-form div {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: inherit;
  margin-bottom: 5px;
}

.tox .tox-rgb-form input {
  width: 6em;
}

.tox .tox-rgb-form input.tox-invalid {
  border: 1px solid red !important;
}

.tox .tox-rgb-form .tox-rgba-preview {
  flex-grow: 2;
  margin-bottom: 0;
  border: 1px solid #000;
}

.tox:not([dir='rtl']) .tox-sv-palette {
  margin-right: 15px;
}

.tox:not([dir='rtl']) .tox-hue-slider {
  margin-right: 15px;
}

.tox:not([dir='rtl']) .tox-hue-slider-thumb {
  margin-left: -1px;
}

.tox:not([dir='rtl']) .tox-rgb-form label {
  margin-right: 0.5em;
}

.tox[dir='rtl'] .tox-sv-palette {
  margin-left: 15px;
}

.tox[dir='rtl'] .tox-hue-slider {
  margin-left: 15px;
}

.tox[dir='rtl'] .tox-hue-slider-thumb {
  margin-right: -1px;
}

.tox[dir='rtl'] .tox-rgb-form label {
  margin-left: 0.5em;
}

.tox .tox-toolbar .tox-swatches,
.tox .tox-toolbar__overflow .tox-swatches,
.tox .tox-toolbar__primary .tox-swatches {
  margin: 5px 0 6px 11px;
}

.tox .tox-collection--list .tox-collection__group .tox-swatches-menu {
  margin: -4px -4px;
  border: 0;
}

.tox .tox-swatches__row {
  display: flex;
}

.tox .tox-swatch {
  width: 30px;
  height: 30px;
  transition: transform 0.15s, box-shadow 0.15s;
}

.tox .tox-swatch:focus,
.tox .tox-swatch:hover {
  box-shadow: 0 0 0 1px rgba(127, 127, 127, 0.3) inset;
  transform: scale(0.8);
}

.tox .tox-swatch--remove {
  display: flex;
  align-items: center;
  justify-content: center;
}

.tox .tox-swatch--remove svg path {
  stroke: #e74c3c;
}

.tox .tox-swatches__picker-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 30px;
  height: 30px;
  padding: 0;
  background-color: transparent;
  border: 0;
  outline: 0;
  cursor: pointer;
}

.tox .tox-swatches__picker-btn svg {
  width: 24px;
  height: 24px;
  fill: #fff;
}

.tox .tox-swatches__picker-btn:hover {
  background: #3389ec;
}

.tox:not([dir='rtl']) .tox-swatches__picker-btn {
  margin-left: auto;
}

.tox[dir='rtl'] .tox-swatches__picker-btn {
  margin-right: auto;
}

.tox .tox-comment-thread {
  position: relative;
  background: #2b3b4e;
}

.tox .tox-comment-thread > :not(:first-child) {
  margin-top: 8px;
}

.tox .tox-comment {
  position: relative;
  padding: 8px 8px 16px 8px;
  background: #2b3b4e;
  border: 1px solid #161f29;
  border-radius: 6px;
  box-shadow: 0 4px 8px 0 rgba(34, 47, 62, 0.1);
}

.tox .tox-comment__header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  color: #fff;
}

.tox .tox-comment__date {
  color: #fff;
  font-size: 12px;
  line-height: 18px;
}

.tox .tox-comment__body {
  position: relative;
  margin-top: 8px;
  color: #fff;
  font-weight: 400;
  font-size: 14px;
  font-style: normal;
  line-height: 1.3;
  text-transform: initial;
}

.tox .tox-comment__body textarea {
  width: 100%;
  white-space: normal;
  resize: none;
}

.tox .tox-comment__expander {
  padding-top: 8px;
}

.tox .tox-comment__expander p {
  color: rgba(255, 255, 255, 0.5);
  font-size: 14px;
  font-style: normal;
}

.tox .tox-comment__body p {
  margin: 0;
}

.tox .tox-comment__buttonspacing {
  padding-top: 16px;
  text-align: center;
}

.tox .tox-comment-thread__overlay::after {
  position: absolute;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  z-index: 5;
  display: flex;
  background: #2b3b4e;
  opacity: 0.9;
  content: '';
}

.tox .tox-comment__reply {
  display: flex;
  flex-shrink: 0;
  flex-wrap: wrap;
  justify-content: flex-end;
  margin-top: 8px;
}

.tox .tox-comment__reply > :first-child {
  width: 100%;
  margin-bottom: 8px;
}

.tox .tox-comment__edit {
  display: flex;
  flex-wrap: wrap;
  justify-content: flex-end;
  margin-top: 16px;
}

.tox .tox-comment__gradient::after {
  position: absolute;
  bottom: 0;
  display: block;
  width: 100%;
  height: 5em;
  margin-top: -40px;
  background: linear-gradient(rgba(43, 59, 78, 0), #2b3b4e);
  content: '';
}

.tox .tox-comment__overlay {
  position: absolute;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  z-index: 5;
  display: flex;
  flex-direction: column;
  flex-grow: 1;
  text-align: center;
  background: #2b3b4e;
  opacity: 0.9;
}

.tox .tox-comment__loading-text {
  position: relative;
  display: flex;
  flex-direction: column;
  align-items: center;
  color: #fff;
}

.tox .tox-comment__loading-text > div {
  padding-bottom: 16px;
}

.tox .tox-comment__overlaytext {
  position: absolute;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  z-index: 10;
  flex-direction: column;
  padding: 1em;
  font-size: 14px;
}

.tox .tox-comment__overlaytext p {
  color: #fff;
  text-align: center;
  background-color: #2b3b4e;
  box-shadow: 0 0 8px 8px #2b3b4e;
}

.tox .tox-comment__overlaytext div:nth-of-type(2) {
  font-size: 0.8em;
}

.tox .tox-comment__busy-spinner {
  position: absolute;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  z-index: 20;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #2b3b4e;
}

.tox .tox-comment__scroll {
  display: flex;
  flex-direction: column;
  flex-shrink: 1;
  overflow: auto;
}

.tox .tox-conversations {
  margin: 8px;
}

.tox:not([dir='rtl']) .tox-comment__edit {
  margin-left: 8px;
}

.tox:not([dir='rtl']) .tox-comment__buttonspacing > :last-child,
.tox:not([dir='rtl']) .tox-comment__edit > :last-child,
.tox:not([dir='rtl']) .tox-comment__reply > :last-child {
  margin-left: 8px;
}

.tox[dir='rtl'] .tox-comment__edit {
  margin-right: 8px;
}

.tox[dir='rtl'] .tox-comment__buttonspacing > :last-child,
.tox[dir='rtl'] .tox-comment__edit > :last-child,
.tox[dir='rtl'] .tox-comment__reply > :last-child {
  margin-right: 8px;
}

.tox .tox-user {
  display: flex;
  align-items: center;
}

.tox .tox-user__avatar svg {
  fill: rgba(255, 255, 255, 0.5);
}

.tox .tox-user__avatar img {
  width: 36px;
  height: 36px;
  object-fit: cover;
  vertical-align: middle;
  border-radius: 50%;
}

.tox .tox-user__name {
  color: #fff;
  font-weight: 700;
  font-size: 14px;
  font-style: normal;
  line-height: 18px;
  text-transform: none;
}

.tox:not([dir='rtl']) .tox-user__avatar img,
.tox:not([dir='rtl']) .tox-user__avatar svg {
  margin-right: 8px;
}

.tox:not([dir='rtl']) .tox-user__avatar + .tox-user__name {
  margin-left: 8px;
}

.tox[dir='rtl'] .tox-user__avatar img,
.tox[dir='rtl'] .tox-user__avatar svg {
  margin-left: 8px;
}

.tox[dir='rtl'] .tox-user__avatar + .tox-user__name {
  margin-right: 8px;
}

.tox .tox-dialog-wrap {
  position: fixed;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  z-index: 1100;
  display: flex;
  align-items: center;
  justify-content: center;
}

.tox .tox-dialog-wrap__backdrop {
  position: absolute;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  z-index: 1;
  background-color: rgba(34, 47, 62, 0.75);
}

.tox .tox-dialog-wrap__backdrop--opaque {
  background-color: #222f3e;
}

.tox .tox-dialog {
  position: relative;
  z-index: 2;
  display: flex;
  flex-direction: column;
  width: 95vw;
  max-width: 480px;
  max-height: 100%;
  overflow: hidden;
  background-color: #2b3b4e;
  border-color: #161f29;
  border-style: solid;
  border-width: 0;
  border-radius: 10px;
  box-shadow: 0 16px 16px -10px rgba(34, 47, 62, 0.15),
    0 0 40px 1px rgba(34, 47, 62, 0.15);
}
@media only screen and (max-width: 767px) {
  body:not(.tox-force-desktop) .tox .tox-dialog {
    align-self: flex-start;
    width: calc(100vw - 16px);
    max-height: calc(100vh - 8px * 2);
    margin: 8px auto;
  }
}

.tox .tox-dialog-inline {
  z-index: 1100;
}

.tox .tox-dialog__header {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 8px 16px 0 16px;
  color: #fff;
  font-size: 16px;
  background-color: #2b3b4e;
  border-bottom: none;
}

.tox .tox-dialog__header .tox-button {
  z-index: 1;
}

.tox .tox-dialog__draghandle {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  cursor: grab;
}

.tox .tox-dialog__draghandle:active {
  cursor: grabbing;
}

.tox .tox-dialog__dismiss {
  margin-left: auto;
}

.tox .tox-dialog__title {
  margin: 0;
  font-weight: 400;
  font-size: 20px;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto,
    Oxygen-Sans, Ubuntu, Cantarell, 'Helvetica Neue', sans-serif;
  font-style: normal;
  line-height: 1.3;
  text-transform: none;
}

.tox .tox-dialog__body {
  display: flex;
  flex: 1;
  min-width: 0;
  color: #fff;
  font-weight: 400;
  font-size: 16px;
  font-style: normal;
  line-height: 1.3;
  text-align: left;
  text-transform: none;
}
@media only screen and (max-width: 767px) {
  body:not(.tox-force-desktop) .tox .tox-dialog__body {
    flex-direction: column;
  }
}

.tox .tox-dialog__body-nav {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  padding: 16px 16px;
}
@media only screen and (max-width: 767px) {
  body:not(.tox-force-desktop) .tox .tox-dialog__body-nav {
    flex-direction: row;
    padding-bottom: 0;
    overflow-x: auto;
    -webkit-overflow-scrolling: touch;
  }
}

.tox .tox-dialog__body-nav-item {
  display: inline-block;
  margin-bottom: 8px;
  color: rgba(255, 255, 255, 0.5);
  font-size: 14px;
  line-height: 1.3;
  white-space: nowrap;
  text-decoration: none;
  border-bottom: 2px solid transparent;
}

.tox .tox-dialog__body-nav-item:focus {
  background-color: rgba(0, 108, 231, 0.1);
}

.tox .tox-dialog__body-nav-item--active {
  color: #006ce7;
  border-bottom: 2px solid #006ce7;
}

.tox .tox-dialog__body-content {
  display: flex;
  flex: 1;
  flex-direction: column;
  box-sizing: border-box;
  max-height: 650px;
  padding: 16px 16px;
  overflow: auto;
  -webkit-overflow-scrolling: touch;
}

.tox .tox-dialog__body-content > * {
  margin-top: 16px;
  margin-bottom: 0;
}

.tox .tox-dialog__body-content > :first-child {
  margin-top: 0;
}

.tox .tox-dialog__body-content > :last-child {
  margin-bottom: 0;
}

.tox .tox-dialog__body-content > :only-child {
  margin-top: 0;
  margin-bottom: 0;
}

.tox .tox-dialog__body-content a {
  color: #006ce7;
  text-decoration: none;
  cursor: pointer;
}

.tox .tox-dialog__body-content a:focus,
.tox .tox-dialog__body-content a:hover {
  color: #0054b4;
  text-decoration: none;
}

.tox .tox-dialog__body-content a:active {
  color: #0054b4;
  text-decoration: none;
}

.tox .tox-dialog__body-content svg {
  fill: #fff;
}

.tox .tox-dialog__body-content ul {
  display: block;
  margin-bottom: 16px;
  list-style-type: disc;
  margin-inline-end: 0;
  margin-inline-start: 0;
  padding-inline-start: 2.5rem;
}

.tox .tox-dialog__body-content .tox-form__group h1 {
  margin-top: 2rem;
  margin-bottom: 16px;
  color: #fff;
  font-weight: 700;
  font-size: 20px;
  font-style: normal;
  letter-spacing: normal;
  text-transform: none;
}

.tox .tox-dialog__body-content .tox-form__group h2 {
  margin-top: 2rem;
  margin-bottom: 16px;
  color: #fff;
  font-weight: 700;
  font-size: 16px;
  font-style: normal;
  letter-spacing: normal;
  text-transform: none;
}

.tox .tox-dialog__body-content .tox-form__group p {
  margin-bottom: 16px;
}

.tox .tox-dialog__body-content .tox-form__group h1:first-child,
.tox .tox-dialog__body-content .tox-form__group h2:first-child,
.tox .tox-dialog__body-content .tox-form__group p:first-child {
  margin-top: 0;
}

.tox .tox-dialog__body-content .tox-form__group h1:last-child,
.tox .tox-dialog__body-content .tox-form__group h2:last-child,
.tox .tox-dialog__body-content .tox-form__group p:last-child {
  margin-bottom: 0;
}

.tox .tox-dialog__body-content .tox-form__group h1:only-child,
.tox .tox-dialog__body-content .tox-form__group h2:only-child,
.tox .tox-dialog__body-content .tox-form__group p:only-child {
  margin-top: 0;
  margin-bottom: 0;
}

.tox .tox-dialog--width-lg {
  max-width: 1200px;
  height: 650px;
}

.tox .tox-dialog--width-md {
  max-width: 800px;
}

.tox .tox-dialog--width-md .tox-dialog__body-content {
  overflow: auto;
}

.tox .tox-dialog__body-content--centered {
  text-align: center;
}

.tox .tox-dialog__footer {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 8px 16px;
  background-color: #2b3b4e;
  border-top: none;
}

.tox .tox-dialog__footer-end,
.tox .tox-dialog__footer-start {
  display: flex;
}

.tox .tox-dialog__busy-spinner {
  position: absolute;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  z-index: 3;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: rgba(34, 47, 62, 0.75);
}

.tox .tox-dialog__table {
  width: 100%;
  border-collapse: collapse;
}

.tox .tox-dialog__table thead th {
  padding-bottom: 8px;
  font-weight: 700;
}

.tox .tox-dialog__table tbody tr {
  border-bottom: 1px solid #161f29;
}

.tox .tox-dialog__table tbody tr:last-child {
  border-bottom: none;
}

.tox .tox-dialog__table td {
  padding-top: 8px;
  padding-bottom: 8px;
}

.tox .tox-dialog__iframe.tox-dialog__iframe--opaque {
  background: #fff;
}

.tox .tox-dialog__popups {
  position: absolute;
  z-index: 1100;
  width: 100%;
}

.tox .tox-dialog__body-iframe {
  display: flex;
  flex: 1;
  flex-direction: column;
}

.tox .tox-dialog__body-iframe .tox-navobj {
  display: flex;
  flex: 1;
}

.tox .tox-dialog__body-iframe .tox-navobj :nth-child(2) {
  flex: 1;
  height: 100%;
}

.tox .tox-dialog-dock-fadeout {
  visibility: hidden;
  opacity: 0;
}

.tox .tox-dialog-dock-fadein {
  visibility: visible;
  opacity: 1;
}

.tox .tox-dialog-dock-transition {
  transition: visibility 0s linear 0.3s, opacity 0.3s ease;
}

.tox .tox-dialog-dock-transition.tox-dialog-dock-fadein {
  transition-delay: 0s;
}
@media only screen and (max-width: 767px) {
  body:not(.tox-force-desktop) .tox:not([dir='rtl']) .tox-dialog__body-nav {
    margin-right: 0;
  }
}
@media only screen and (max-width: 767px) {
  body:not(.tox-force-desktop)
    .tox:not([dir='rtl'])
    .tox-dialog__body-nav-item:not(:first-child) {
    margin-left: 8px;
  }
}

.tox:not([dir='rtl']) .tox-dialog__footer .tox-dialog__footer-end > *,
.tox:not([dir='rtl']) .tox-dialog__footer .tox-dialog__footer-start > * {
  margin-left: 8px;
}

.tox[dir='rtl'] .tox-dialog__body {
  text-align: right;
}
@media only screen and (max-width: 767px) {
  body:not(.tox-force-desktop) .tox[dir='rtl'] .tox-dialog__body-nav {
    margin-left: 0;
  }
}
@media only screen and (max-width: 767px) {
  body:not(.tox-force-desktop)
    .tox[dir='rtl']
    .tox-dialog__body-nav-item:not(:first-child) {
    margin-right: 8px;
  }
}

.tox[dir='rtl'] .tox-dialog__footer .tox-dialog__footer-end > *,
.tox[dir='rtl'] .tox-dialog__footer .tox-dialog__footer-start > * {
  margin-right: 8px;
}

body.tox-dialog__disable-scroll {
  overflow: hidden;
}

.tox .tox-dropzone-container {
  display: flex;
  flex: 1;
}

.tox .tox-dropzone {
  display: flex;
  flex-direction: column;
  flex-grow: 1;
  align-items: center;
  justify-content: center;
  box-sizing: border-box;
  min-height: 100px;
  padding: 10px;
  background: #fff;
  border: 2px dashed #161f29;
}

.tox .tox-dropzone p {
  margin: 0 0 16px 0;
  color: rgba(255, 255, 255, 0.5);
}

.tox .tox-edit-area {
  position: relative;
  display: flex;
  flex: 1;
  overflow: hidden;
}

.tox .tox-edit-area__iframe {
  position: absolute;
  flex: 1;
  box-sizing: border-box;
  width: 100%;
  height: 100%;
  background-color: #fff;
  border: 0;
}

.tox.tox-inline-edit-area {
  border: 1px dotted #161f29;
}

.tox .tox-editor-container {
  display: flex;
  flex: 1 1 auto;
  flex-direction: column;
  overflow: hidden;
}

.tox .tox-editor-header {
  z-index: 1;
  display: grid;
  grid-template-columns: 1fr min-content;
}

.tox:not(.tox-tinymce-inline) .tox-editor-header {
  padding: 4px 0;
  background-color: #222f3e;
  border-bottom: 1px solid rgba(255, 255, 255, 0.15);
  box-shadow: none;
  transition: box-shadow 0.5s;
}

.tox:not(.tox-tinymce-inline).tox-tinymce--toolbar-bottom .tox-editor-header {
  border-top: 1px solid rgba(255, 255, 255, 0.15);
  box-shadow: none;
}

.tox:not(.tox-tinymce-inline).tox-tinymce--toolbar-sticky-on
  .tox-editor-header {
  padding: 4px 0;
  background-color: #222f3e;
  box-shadow: none;
}

.tox:not(.tox-tinymce-inline).tox-tinymce--toolbar-sticky-on.tox-tinymce--toolbar-bottom
  .tox-editor-header {
  box-shadow: none;
}

.tox-editor-dock-fadeout {
  visibility: hidden;
  opacity: 0;
}

.tox-editor-dock-fadein {
  visibility: visible;
  opacity: 1;
}

.tox-editor-dock-transition {
  transition: visibility 0s linear 0.25s, opacity 0.25s ease;
}

.tox-editor-dock-transition.tox-editor-dock-fadein {
  transition-delay: 0s;
}

.tox .tox-control-wrap {
  position: relative;
  flex: 1;
}

.tox
  .tox-control-wrap:not(.tox-control-wrap--status-invalid)
  .tox-control-wrap__status-icon-invalid,
.tox
  .tox-control-wrap:not(.tox-control-wrap--status-unknown)
  .tox-control-wrap__status-icon-unknown,
.tox
  .tox-control-wrap:not(.tox-control-wrap--status-valid)
  .tox-control-wrap__status-icon-valid {
  display: none;
}

.tox .tox-control-wrap svg {
  display: block;
}

.tox .tox-control-wrap__status-icon-wrap {
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
}

.tox .tox-control-wrap__status-icon-invalid svg {
  fill: #c00;
}

.tox .tox-control-wrap__status-icon-unknown svg {
  fill: orange;
}

.tox .tox-control-wrap__status-icon-valid svg {
  fill: green;
}

.tox:not([dir='rtl']) .tox-control-wrap--status-invalid .tox-textfield,
.tox:not([dir='rtl']) .tox-control-wrap--status-unknown .tox-textfield,
.tox:not([dir='rtl']) .tox-control-wrap--status-valid .tox-textfield {
  padding-right: 32px;
}

.tox:not([dir='rtl']) .tox-control-wrap__status-icon-wrap {
  right: 4px;
}

.tox[dir='rtl'] .tox-control-wrap--status-invalid .tox-textfield,
.tox[dir='rtl'] .tox-control-wrap--status-unknown .tox-textfield,
.tox[dir='rtl'] .tox-control-wrap--status-valid .tox-textfield {
  padding-left: 32px;
}

.tox[dir='rtl'] .tox-control-wrap__status-icon-wrap {
  left: 4px;
}

.tox .tox-autocompleter {
  max-width: 25em;
}

.tox .tox-autocompleter .tox-menu {
  box-sizing: border-box;
  max-width: 25em;
}

.tox .tox-autocompleter .tox-autocompleter-highlight {
  font-weight: 700;
}

.tox .tox-color-input {
  position: relative;
  z-index: 1;
  display: flex;
}

.tox .tox-color-input .tox-textfield {
  z-index: -1;
}

.tox .tox-color-input span {
  position: absolute;
  top: 6px;
  box-sizing: border-box;
  width: 24px;
  height: 24px;
  border-color: rgba(34, 47, 62, 0.2);
  border-style: solid;
  border-width: 1px;
  border-radius: 6px;
  box-shadow: none;
}

.tox .tox-color-input span:focus:not([aria-disabled='true']),
.tox .tox-color-input span:hover:not([aria-disabled='true']) {
  border-color: #006ce7;
  cursor: pointer;
}

.tox .tox-color-input span::before {
  position: absolute;
  top: -1px;
  left: -1px;
  z-index: -1;
  box-sizing: border-box;
  width: 24px;
  height: 24px;
  background-image: linear-gradient(
      45deg,
      rgba(255, 255, 255, 0.25) 25%,
      transparent 25%
    ),
    linear-gradient(-45deg, rgba(255, 255, 255, 0.25) 25%, transparent 25%),
    linear-gradient(45deg, transparent 75%, rgba(255, 255, 255, 0.25) 75%),
    linear-gradient(-45deg, transparent 75%, rgba(255, 255, 255, 0.25) 75%);
  background-position: 0 0, 0 6px, 6px -6px, -6px 0;
  background-size: 12px 12px;
  border: 1px solid #2b3b4e;
  border-radius: 6px;
  content: '';
}

.tox .tox-color-input span[aria-disabled='true'] {
  cursor: not-allowed;
}

.tox:not([dir='rtl']) .tox-color-input .tox-textfield {
  padding-left: 36px;
}

.tox:not([dir='rtl']) .tox-color-input span {
  left: 6px;
}

.tox[dir='rtl'] .tox-color-input .tox-textfield {
  padding-right: 36px;
}

.tox[dir='rtl'] .tox-color-input span {
  right: 6px;
}

.tox .tox-label,
.tox .tox-toolbar-label {
  display: block;
  padding: 0 8px 0 0;
  color: rgba(255, 255, 255, 0.5);
  font-weight: 400;
  font-size: 14px;
  font-style: normal;
  line-height: 1.3;
  white-space: nowrap;
  text-transform: none;
}

.tox .tox-toolbar-label {
  padding: 0 8px;
}

.tox[dir='rtl'] .tox-label {
  padding: 0 0 0 8px;
}

.tox .tox-form {
  display: flex;
  flex: 1;
  flex-direction: column;
}

.tox .tox-form__group {
  box-sizing: border-box;
  margin-bottom: 4px;
}

.tox .tox-form-group--maximize {
  flex: 1;
}

.tox .tox-form__group--error {
  color: #c00;
}

.tox .tox-form__group--collection {
  display: flex;
}

.tox .tox-form__grid {
  display: flex;
  flex-direction: row;
  flex-wrap: wrap;
  justify-content: space-between;
}

.tox .tox-form__grid--2col > .tox-form__group {
  width: calc(50% - (8px / 2));
}

.tox .tox-form__grid--3col > .tox-form__group {
  width: calc(100% / 3 - (8px / 2));
}

.tox .tox-form__grid--4col > .tox-form__group {
  width: calc(25% - (8px / 2));
}

.tox .tox-form__controls-h-stack {
  display: flex;
  align-items: center;
}

.tox .tox-form__group--inline {
  display: flex;
  align-items: center;
}

.tox .tox-form__group--stretched {
  display: flex;
  flex: 1;
  flex-direction: column;
}

.tox .tox-form__group--stretched .tox-textarea {
  flex: 1;
}

.tox .tox-form__group--stretched .tox-navobj {
  display: flex;
  flex: 1;
}

.tox .tox-form__group--stretched .tox-navobj :nth-child(2) {
  flex: 1;
  height: 100%;
}

.tox:not([dir='rtl']) .tox-form__controls-h-stack > :not(:first-child) {
  margin-left: 4px;
}

.tox[dir='rtl'] .tox-form__controls-h-stack > :not(:first-child) {
  margin-right: 4px;
}

.tox .tox-lock.tox-locked .tox-lock-icon__unlock,
.tox .tox-lock:not(.tox-locked) .tox-lock-icon__lock {
  display: none;
}

.tox .tox-listboxfield .tox-listbox--select,
.tox .tox-textarea,
.tox .tox-textfield,
.tox .tox-toolbar-textfield {
  box-sizing: border-box;
  width: 100%;
  min-height: 34px;
  margin: 0;
  padding: 5px 5.5px;
  color: #fff;
  font-size: 16px;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto,
    Oxygen-Sans, Ubuntu, Cantarell, 'Helvetica Neue', sans-serif;
  line-height: 24px;
  background-color: #2b3b4e;
  border-color: #161f29;
  border-style: solid;
  border-width: 1px;
  border-radius: 6px;
  outline: 0;
  box-shadow: none;
  -webkit-appearance: none;
  -moz-appearance: none;
  appearance: none;
  resize: none;
}

.tox .tox-textarea[disabled],
.tox .tox-textfield[disabled] {
  color: rgba(255, 255, 255, 0.85);
  background-color: #222f3e;
  cursor: not-allowed;
}

.tox .tox-listboxfield .tox-listbox--select:focus,
.tox .tox-textarea:focus,
.tox .tox-textfield:focus {
  background-color: #2b3b4e;
  border-color: #006ce7;
  outline: 0;
  box-shadow: 0 0 0 2px rgba(0, 108, 231, 0.25);
}

.tox .tox-toolbar-textfield {
  max-width: 250px;
  margin-top: 2px;
  margin-bottom: 3px;
  border-width: 0;
}

.tox .tox-naked-btn {
  display: block;
  margin: 0;
  padding: 0;
  color: #006ce7;
  background-color: transparent;
  border: 0;
  border-color: transparent;
  box-shadow: unset;
  cursor: pointer;
}

.tox .tox-naked-btn svg {
  display: block;
  fill: #fff;
}

.tox:not([dir='rtl']) .tox-toolbar-textfield + * {
  margin-left: 4px;
}

.tox[dir='rtl'] .tox-toolbar-textfield + * {
  margin-right: 4px;
}

.tox .tox-listboxfield {
  position: relative;
  cursor: pointer;
}

.tox .tox-listboxfield .tox-listbox--select[disabled] {
  color: rgba(255, 255, 255, 0.85);
  background-color: #19232e;
  cursor: not-allowed;
}

.tox .tox-listbox__select-label {
  flex: 1;
  margin: 0 4px;
  cursor: default;
}

.tox .tox-listbox__select-chevron {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 16px;
}

.tox .tox-listbox__select-chevron svg {
  fill: #fff;
}

.tox .tox-listboxfield .tox-listbox--select {
  display: flex;
  align-items: center;
}

.tox:not([dir='rtl']) .tox-listboxfield svg {
  right: 8px;
}

.tox[dir='rtl'] .tox-listboxfield svg {
  left: 8px;
}

.tox .tox-selectfield {
  position: relative;
  cursor: pointer;
}

.tox .tox-selectfield select {
  box-sizing: border-box;
  width: 100%;
  min-height: 34px;
  margin: 0;
  padding: 5px 5.5px;
  color: #fff;
  font-size: 16px;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto,
    Oxygen-Sans, Ubuntu, Cantarell, 'Helvetica Neue', sans-serif;
  line-height: 24px;
  background-color: #2b3b4e;
  border-color: #161f29;
  border-style: solid;
  border-width: 1px;
  border-radius: 6px;
  outline: 0;
  box-shadow: none;
  -webkit-appearance: none;
  -moz-appearance: none;
  appearance: none;
  resize: none;
}

.tox .tox-selectfield select[disabled] {
  color: rgba(255, 255, 255, 0.85);
  background-color: #19232e;
  cursor: not-allowed;
}

.tox .tox-selectfield select::-ms-expand {
  display: none;
}

.tox .tox-selectfield select:focus {
  background-color: #2b3b4e;
  border-color: #006ce7;
  outline: 0;
  box-shadow: 0 0 0 2px rgba(0, 108, 231, 0.25);
}

.tox .tox-selectfield svg {
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  pointer-events: none;
}

.tox:not([dir='rtl']) .tox-selectfield select[size='0'],
.tox:not([dir='rtl']) .tox-selectfield select[size='1'] {
  padding-right: 24px;
}

.tox:not([dir='rtl']) .tox-selectfield svg {
  right: 8px;
}

.tox[dir='rtl'] .tox-selectfield select[size='0'],
.tox[dir='rtl'] .tox-selectfield select[size='1'] {
  padding-left: 24px;
}

.tox[dir='rtl'] .tox-selectfield svg {
  left: 8px;
}

.tox .tox-textarea {
  white-space: pre-wrap;
  -webkit-appearance: textarea;
  -moz-appearance: textarea;
  appearance: textarea;
}

.tox-fullscreen {
  width: 100%;
  height: 100%;
  margin: 0;
  padding: 0;
  overflow: hidden;
  border: 0;
  touch-action: pinch-zoom;
  overscroll-behavior: none;
}

.tox.tox-tinymce.tox-fullscreen .tox-statusbar__resize-handle {
  display: none;
}

.tox-shadowhost.tox-fullscreen,
.tox.tox-tinymce.tox-fullscreen {
  position: fixed;
  top: 0;
  left: 0;
  z-index: 1200;
}

.tox.tox-tinymce.tox-fullscreen {
  background-color: transparent;
}

.tox-fullscreen .tox.tox-tinymce-aux,
.tox-fullscreen ~ .tox.tox-tinymce-aux {
  z-index: 1201;
}

.tox .tox-help__more-link {
  margin-top: 1em;
  list-style: none;
}

.tox .tox-imagepreview {
  position: relative;
  width: 100%;
  height: 380px;
  overflow: hidden;
  background-color: #666;
}

.tox .tox-imagepreview.tox-imagepreview__loaded {
  overflow: auto;
}

.tox .tox-imagepreview__container {
  position: absolute;
  top: 100vw;
  left: 100vw;
  display: flex;
}

.tox .tox-imagepreview__image {
  background: url(data:image/gif;base64,R0lGODdhDAAMAIABAMzMzP///ywAAAAADAAMAAACFoQfqYeabNyDMkBQb81Uat85nxguUAEAOw==);
}

.tox .tox-image-tools .tox-spacer {
  flex: 1;
}

.tox .tox-image-tools .tox-bar {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 60px;
}

.tox .tox-image-tools .tox-imagepreview,
.tox .tox-image-tools .tox-imagepreview + .tox-bar {
  margin-top: 8px;
}

.tox .tox-image-tools .tox-croprect-block {
  position: absolute;
  background: #000;
  opacity: 0.5;
  zoom: 1;
}

.tox .tox-image-tools .tox-croprect-handle {
  position: absolute;
  top: 0;
  left: 0;
  width: 20px;
  height: 20px;
  border: 2px solid #fff;
}

.tox .tox-image-tools .tox-croprect-handle-move {
  position: absolute;
  border: 0;
  cursor: move;
}

.tox .tox-image-tools .tox-croprect-handle-nw {
  top: 100px;
  left: 100px;
  margin: -2px 0 0 -2px;
  border-width: 2px 0 0 2px;
  cursor: nw-resize;
}

.tox .tox-image-tools .tox-croprect-handle-ne {
  top: 100px;
  left: 200px;
  margin: -2px 0 0 -20px;
  border-width: 2px 2px 0 0;
  cursor: ne-resize;
}

.tox .tox-image-tools .tox-croprect-handle-sw {
  top: 200px;
  left: 100px;
  margin: -20px 2px 0 -2px;
  border-width: 0 0 2px 2px;
  cursor: sw-resize;
}

.tox .tox-image-tools .tox-croprect-handle-se {
  top: 200px;
  left: 200px;
  margin: -20px 0 0 -20px;
  border-width: 0 2px 2px 0;
  cursor: se-resize;
}

.tox .tox-insert-table-picker {
  display: flex;
  flex-wrap: wrap;
  width: 170px;
}

.tox .tox-insert-table-picker > div {
  box-sizing: border-box;
  width: 17px;
  height: 17px;
  border-color: rgba(255, 255, 255, 0.15);
  border-style: solid;
  border-width: 0 1px 1px 0;
}

.tox .tox-collection--list .tox-collection__group .tox-insert-table-picker {
  margin: -4px -4px;
}

.tox .tox-insert-table-picker .tox-insert-table-picker__selected {
  background-color: rgba(0, 108, 231, 0.5);
  border-color: rgba(0, 108, 231, 0.5);
}

.tox .tox-insert-table-picker__label {
  display: block;
  width: 100%;
  padding: 4px;
  color: #fff;
  font-size: 14px;
  text-align: center;
}

.tox:not([dir='rtl']) .tox-insert-table-picker > div:nth-child(10n) {
  border-right: 0;
}

.tox[dir='rtl'] .tox-insert-table-picker > div:nth-child(10n + 1) {
  border-right: 0;
}

.tox .tox-menu {
  z-index: 1150;
  display: inline-block;
  overflow: hidden;
  vertical-align: top;
  background-color: #2b3b4e;
  border: 1px solid rgba(255, 255, 255, 0.15);
  border-radius: 6px;
  box-shadow: none;
}

.tox .tox-menu.tox-collection.tox-collection--list {
  padding: 0 4px;
}

.tox .tox-menu.tox-collection.tox-collection--toolbar {
  padding: 8px;
}

.tox .tox-menu.tox-collection.tox-collection--grid {
  padding: 8px;
}
@media only screen and (min-width: 768px) {
  .tox .tox-menu .tox-collection__item-label {
    word-break: normal;
    overflow-wrap: break-word;
  }
}

.tox .tox-menu__label blockquote,
.tox .tox-menu__label code,
.tox .tox-menu__label h1,
.tox .tox-menu__label h2,
.tox .tox-menu__label h3,
.tox .tox-menu__label h4,
.tox .tox-menu__label h5,
.tox .tox-menu__label h6,
.tox .tox-menu__label p {
  margin: 0;
}

.tox .tox-menubar {
  display: flex;
  flex: 0 0 auto;
  flex-shrink: 0;
  flex-wrap: wrap;
  grid-row: 1;
  grid-column: 1/-1;
  padding: 0 11px 0 12px;
  background: repeating-linear-gradient(transparent 0 1px, transparent 1px 39px)
    center top 39px/100% calc(100% - 39px) no-repeat;
  background-color: #222f3e;
}

.tox .tox-promotion + .tox-menubar {
  grid-column: 1;
}

.tox .tox-promotion {
  grid-row: 1;
  grid-column: 2;
  padding-top: 5px;
  background: repeating-linear-gradient(transparent 0 1px, transparent 1px 39px)
    center top 39px/100% calc(100% - 39px) no-repeat;
  background-color: #222f3e;
  padding-inline-end: 8px;
  padding-inline-start: 4px;
}

.tox .tox-promotion-link {
  display: flex;
  align-items: unsafe center;
  height: 26.6px;
  padding: 4px 8px;
  color: #086be6;
  font-size: 14px;
  white-space: nowrap;
  background-color: #e8f1f8;
  border-radius: 5px;
  cursor: pointer;
}

.tox .tox-promotion-link:hover {
  background-color: #b4d7ff;
}

.tox .tox-promotion-link:focus {
  background-color: #d9edf7;
}

.tox .tox-mbtn {
  display: flex;
  flex: 0 0 auto;
  align-items: center;
  justify-content: center;
  width: auto;
  height: 28px;
  margin: 5px 1px 6px 0;
  padding: 0 4px;
  overflow: hidden;
  color: #fff;
  font-weight: 400;
  font-size: 14px;
  font-style: normal;
  text-transform: none;
  background: 0 0;
  border: 0;
  border-radius: 3px;
  outline: 0;
  box-shadow: none;
}

.tox .tox-mbtn[disabled] {
  color: rgba(255, 255, 255, 0.5);
  background-color: transparent;
  border: 0;
  box-shadow: none;
  cursor: not-allowed;
}

.tox .tox-mbtn:focus:not(:disabled) {
  color: #fff;
  background: #3389ec;
  border: 0;
  box-shadow: none;
}

.tox .tox-mbtn--active {
  color: #fff;
  background: #599fef;
  border: 0;
  box-shadow: none;
}

.tox .tox-mbtn:hover:not(:disabled):not(.tox-mbtn--active) {
  color: #fff;
  background: #3389ec;
  border: 0;
  box-shadow: none;
}

.tox .tox-mbtn__select-label {
  margin: 0 4px;
  font-weight: 400;
  cursor: default;
}

.tox .tox-mbtn[disabled] .tox-mbtn__select-label {
  cursor: not-allowed;
}

.tox .tox-mbtn__select-chevron {
  display: flex;
  display: none;
  align-items: center;
  justify-content: center;
  width: 16px;
}

.tox .tox-notification {
  display: grid;
  grid-template-columns: minmax(40px, 1fr) auto minmax(40px, 1fr);
  box-sizing: border-box;
  margin-top: 4px;
  padding: 4px;
  font-weight: 400;
  font-size: 14px;
  border-style: solid;
  border-width: 1px;
  border-radius: 6px;
  box-shadow: none;
  opacity: 0;
  transition: transform 0.1s ease-in, opacity 150ms ease-in;
}

.tox .tox-notification p {
  font-weight: 400;
  font-size: 14px;
}

.tox .tox-notification a {
  text-decoration: underline;
  cursor: pointer;
}

.tox .tox-notification--in {
  opacity: 1;
}

.tox .tox-notification--success {
  color: #fff;
  background-color: #334840;
  border-color: #3c5440;
}

.tox .tox-notification--success p {
  color: #fff;
}

.tox .tox-notification--success a {
  color: #b5d199;
}

.tox .tox-notification--success svg {
  fill: #fff;
}

.tox .tox-notification--error {
  color: #fff;
  background-color: #442632;
  border-color: #55212b;
}

.tox .tox-notification--error p {
  color: #fff;
}

.tox .tox-notification--error a {
  color: #e68080;
}

.tox .tox-notification--error svg {
  fill: #fff;
}

.tox .tox-notification--warn,
.tox .tox-notification--warning {
  color: #fff0b3;
  background-color: #222f3e;
  border-color: rgba(255, 255, 255, 0.15);
}

.tox .tox-notification--warn p,
.tox .tox-notification--warning p {
  color: #fff0b3;
}

.tox .tox-notification--warn a,
.tox .tox-notification--warning a {
  color: #fc0;
}

.tox .tox-notification--warn svg,
.tox .tox-notification--warning svg {
  fill: #fff0b3;
}

.tox .tox-notification--info {
  color: #fff;
  background-color: #254161;
  border-color: #264972;
}

.tox .tox-notification--info p {
  color: #fff;
}

.tox .tox-notification--info a {
  color: #83b7f3;
}

.tox .tox-notification--info svg {
  fill: #fff;
}

.tox .tox-notification__body {
  grid-row-start: 1;
  grid-row-end: 2;
  grid-column-start: 2;
  grid-column-end: 3;
  align-self: center;
  color: #fff;
  font-size: 14px;
  white-space: normal;
  text-align: center;
  word-break: break-all;
  word-break: break-word;
}

.tox .tox-notification__body > * {
  margin: 0;
}

.tox .tox-notification__body > * + * {
  margin-top: 1rem;
}

.tox .tox-notification__icon {
  grid-row-start: 1;
  grid-row-end: 2;
  grid-column-start: 1;
  grid-column-end: 2;
  align-self: center;
  justify-self: end;
}

.tox .tox-notification__icon svg {
  display: block;
}

.tox .tox-notification__dismiss {
  grid-row-start: 1;
  grid-row-end: 2;
  grid-column-start: 3;
  grid-column-end: 4;
  align-self: start;
  justify-self: end;
}

.tox .tox-notification .tox-progress-bar {
  grid-row-start: 2;
  grid-row-end: 3;
  grid-column-start: 1;
  grid-column-end: 4;
  justify-self: center;
}

.tox .tox-pop {
  position: relative;
  display: inline-block;
}

.tox .tox-pop--resizing {
  transition: width 0.1s ease;
}

.tox .tox-pop--resizing .tox-toolbar,
.tox .tox-pop--resizing .tox-toolbar__group {
  flex-wrap: nowrap;
}

.tox .tox-pop--transition {
  transition: 0.15s ease;
  transition-property: left, right, top, bottom;
}

.tox .tox-pop--transition::after,
.tox .tox-pop--transition::before {
  transition: all 0.15s, visibility 0s, opacity 75ms ease 75ms;
}

.tox .tox-pop__dialog {
  min-width: 0;
  overflow: hidden;
  background-color: #222f3e;
  border: 1px solid #161f29;
  border-radius: 6px;
  box-shadow: 0 0 2px 0 rgba(34, 47, 62, 0.2),
    0 4px 8px 0 rgba(34, 47, 62, 0.15);
}

.tox .tox-pop__dialog > :not(.tox-toolbar) {
  margin: 4px 4px 4px 8px;
}

.tox .tox-pop__dialog .tox-toolbar {
  margin-bottom: -1px;
  background-color: transparent;
}

.tox .tox-pop::after,
.tox .tox-pop::before {
  position: absolute;
  display: block;
  width: 0;
  height: 0;
  border-style: solid;
  opacity: 1;
  content: '';
}

.tox .tox-pop.tox-pop--inset::after,
.tox .tox-pop.tox-pop--inset::before {
  opacity: 0;
  transition: all 0s 0.15s, visibility 0s, opacity 75ms ease;
}

.tox .tox-pop.tox-pop--bottom::after,
.tox .tox-pop.tox-pop--bottom::before {
  top: 100%;
  left: 50%;
}

.tox .tox-pop.tox-pop--bottom::after {
  margin-top: -1px;
  margin-left: -8px;
  border-color: #222f3e transparent transparent transparent;
  border-width: 8px;
}

.tox .tox-pop.tox-pop--bottom::before {
  margin-left: -9px;
  border-color: #161f29 transparent transparent transparent;
  border-width: 9px;
}

.tox .tox-pop.tox-pop--top::after,
.tox .tox-pop.tox-pop--top::before {
  top: 0;
  left: 50%;
  transform: translateY(-100%);
}

.tox .tox-pop.tox-pop--top::after {
  margin-top: 1px;
  margin-left: -8px;
  border-color: transparent transparent #222f3e transparent;
  border-width: 8px;
}

.tox .tox-pop.tox-pop--top::before {
  margin-left: -9px;
  border-color: transparent transparent #161f29 transparent;
  border-width: 9px;
}

.tox .tox-pop.tox-pop--left::after,
.tox .tox-pop.tox-pop--left::before {
  top: calc(50% - 1px);
  left: 0;
  transform: translateY(-50%);
}

.tox .tox-pop.tox-pop--left::after {
  margin-left: -15px;
  border-color: transparent #222f3e transparent transparent;
  border-width: 8px;
}

.tox .tox-pop.tox-pop--left::before {
  margin-left: -19px;
  border-color: transparent #161f29 transparent transparent;
  border-width: 10px;
}

.tox .tox-pop.tox-pop--right::after,
.tox .tox-pop.tox-pop--right::before {
  top: calc(50% + 1px);
  left: 100%;
  transform: translateY(-50%);
}

.tox .tox-pop.tox-pop--right::after {
  margin-left: -1px;
  border-color: transparent transparent transparent #222f3e;
  border-width: 8px;
}

.tox .tox-pop.tox-pop--right::before {
  margin-left: -1px;
  border-color: transparent transparent transparent #161f29;
  border-width: 10px;
}

.tox .tox-pop.tox-pop--align-left::after,
.tox .tox-pop.tox-pop--align-left::before {
  left: 20px;
}

.tox .tox-pop.tox-pop--align-right::after,
.tox .tox-pop.tox-pop--align-right::before {
  left: calc(100% - 20px);
}

.tox .tox-sidebar-wrap {
  display: flex;
  flex-direction: row;
  flex-grow: 1;
  min-height: 0;
}

.tox .tox-sidebar {
  display: flex;
  flex-direction: row;
  justify-content: flex-end;
  background-color: #222f3e;
}

.tox .tox-sidebar__slider {
  display: flex;
  overflow: hidden;
}

.tox .tox-sidebar__pane-container {
  display: flex;
}

.tox .tox-sidebar__pane {
  display: flex;
}

.tox .tox-sidebar--sliding-closed {
  opacity: 0;
}

.tox .tox-sidebar--sliding-open {
  opacity: 1;
}

.tox .tox-sidebar--sliding-growing,
.tox .tox-sidebar--sliding-shrinking {
  transition: width 0.5s ease, opacity 0.5s ease;
}

.tox .tox-selector {
  position: absolute;
  display: inline-block;
  box-sizing: border-box;
  width: 10px;
  height: 10px;
  background-color: #4099ff;
  border-color: #4099ff;
  border-style: solid;
  border-width: 1px;
}

.tox.tox-platform-touch .tox-selector {
  width: 12px;
  height: 12px;
}

.tox .tox-slider {
  position: relative;
  display: flex;
  flex: 1;
  align-items: center;
  justify-content: center;
  height: 24px;
}

.tox .tox-slider__rail {
  width: 100%;
  min-width: 120px;
  height: 10px;
  background-color: transparent;
  border: 1px solid #161f29;
  border-radius: 6px;
}

.tox .tox-slider__handle {
  position: absolute;
  top: 50%;
  left: 50%;
  width: 14px;
  height: 24px;
  background-color: #006ce7;
  border: 2px solid #0054b4;
  border-radius: 6px;
  box-shadow: none;
  transform: translateX(-50%) translateY(-50%);
}

.tox .tox-form__controls-h-stack > .tox-slider:not(:first-of-type) {
  margin-inline-start: 8px;
}

.tox .tox-form__controls-h-stack > .tox-form__group + .tox-slider {
  margin-inline-start: 32px;
}

.tox .tox-form__controls-h-stack > .tox-slider + .tox-form__group {
  margin-inline-start: 32px;
}

.tox .tox-source-code {
  overflow: auto;
}

.tox .tox-spinner {
  display: flex;
}

.tox .tox-spinner > div {
  width: 8px;
  height: 8px;
  background-color: rgba(255, 255, 255, 0.5);
  border-radius: 100%;
  animation: tam-bouncing-dots 1.5s ease-in-out 0s infinite both;
}

.tox .tox-spinner > div:nth-child(1) {
  animation-delay: -0.32s;
}

.tox .tox-spinner > div:nth-child(2) {
  animation-delay: -0.16s;
}
@keyframes tam-bouncing-dots {
  0%,
  100%,
  80% {
    transform: scale(0);
  }

  40% {
    transform: scale(1);
  }
}

.tox:not([dir='rtl']) .tox-spinner > div:not(:first-child) {
  margin-left: 4px;
}

.tox[dir='rtl'] .tox-spinner > div:not(:first-child) {
  margin-right: 4px;
}

.tox .tox-statusbar {
  position: relative;
  display: flex;
  flex: 0 0 auto;
  align-items: center;
  height: 25px;
  padding: 0 8px;
  overflow: hidden;
  color: rgba(255, 255, 255, 0.75);
  font-weight: 400;
  font-size: 14px;
  text-transform: none;
  background-color: #222f3e;
  border-top: 1px solid rgba(255, 255, 255, 0.15);
}

.tox .tox-statusbar__text-container {
  display: flex;
  flex: 1 1 auto;
  justify-content: flex-end;
  overflow: hidden;
}

.tox .tox-statusbar__path {
  display: flex;
  flex: 1 1 auto;
  margin-right: auto;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}

.tox .tox-statusbar__path > * {
  display: inline;
  white-space: nowrap;
}

.tox .tox-statusbar__wordcount {
  flex: 0 0 auto;
  margin-left: 1ch;
}

.tox .tox-statusbar a,
.tox .tox-statusbar__path-item,
.tox .tox-statusbar__wordcount {
  color: rgba(255, 255, 255, 0.75);
  text-decoration: none;
}

.tox .tox-statusbar a:focus:not(:disabled):not([aria-disabled='true']),
.tox .tox-statusbar a:hover:not(:disabled):not([aria-disabled='true']),
.tox .tox-statusbar__path-item:focus:not(:disabled):not([aria-disabled='true']),
.tox .tox-statusbar__path-item:hover:not(:disabled):not([aria-disabled='true']),
.tox .tox-statusbar__wordcount:focus:not(:disabled):not([aria-disabled='true']),
.tox
  .tox-statusbar__wordcount:hover:not(:disabled):not([aria-disabled='true']) {
  color: #fff;
  cursor: pointer;
}

.tox .tox-statusbar__branding svg {
  width: 3.6em;
  height: 1.14em;
  vertical-align: -0.28em;
  fill: rgba(255, 255, 255, 0.8);
}

.tox
  .tox-statusbar__branding
  a:focus:not(:disabled):not([aria-disabled='true'])
  svg,
.tox
  .tox-statusbar__branding
  a:hover:not(:disabled):not([aria-disabled='true'])
  svg {
  fill: #fff;
}

.tox .tox-statusbar__resize-handle {
  display: flex;
  flex: 0 0 auto;
  align-items: flex-end;
  align-self: stretch;
  justify-content: flex-end;
  margin-right: -8px;
  margin-left: auto;
  padding-right: 3px;
  padding-bottom: 3px;
  padding-left: 1ch;
  cursor: nwse-resize;
}

.tox .tox-statusbar__resize-handle svg {
  display: block;
  fill: rgba(255, 255, 255, 0.5);
}

.tox .tox-statusbar__resize-handle:focus svg {
  background-color: #434e5b;
  border-radius: 1px 1px 5px 1px;
  box-shadow: 0 0 0 2px #434e5b;
}

.tox:not([dir='rtl']) .tox-statusbar__path > * {
  margin-right: 4px;
}

.tox:not([dir='rtl']) .tox-statusbar__branding {
  margin-left: 2ch;
}

.tox[dir='rtl'] .tox-statusbar {
  flex-direction: row-reverse;
}

.tox[dir='rtl'] .tox-statusbar__path > * {
  margin-left: 4px;
}

.tox .tox-throbber {
  z-index: 1299;
}

.tox .tox-throbber__busy-spinner {
  position: absolute;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: rgba(34, 47, 62, 0.6);
}

.tox .tox-tbtn {
  display: flex;
  flex: 0 0 auto;
  align-items: center;
  justify-content: center;
  width: 34px;
  height: 28px;
  margin: 6px 1px 5px 0;
  padding: 0;
  overflow: hidden;
  color: #fff;
  font-weight: 400;
  font-size: 14px;
  font-style: normal;
  text-transform: none;
  background: 0 0;
  border: 0;
  border-radius: 3px;
  outline: 0;
  box-shadow: none;
}

.tox .tox-tbtn svg {
  display: block;
  fill: #fff;
}

.tox .tox-tbtn.tox-tbtn-more {
  width: inherit;
  padding-right: 5px;
  padding-left: 5px;
}

.tox .tox-tbtn:focus {
  background: #3389ec;
  border: 0;
  box-shadow: none;
}

.tox .tox-tbtn:hover {
  color: #fff;
  background: #3389ec;
  border: 0;
  box-shadow: none;
}

.tox .tox-tbtn:hover svg {
  fill: #fff;
}

.tox .tox-tbtn:active {
  color: #fff;
  background: #599fef;
  border: 0;
  box-shadow: none;
}

.tox .tox-tbtn:active svg {
  fill: #fff;
}

.tox .tox-tbtn--disabled,
.tox .tox-tbtn--disabled:hover,
.tox .tox-tbtn:disabled,
.tox .tox-tbtn:disabled:hover {
  color: rgba(255, 255, 255, 0.5);
  background: 0 0;
  border: 0;
  box-shadow: none;
  cursor: not-allowed;
}

.tox .tox-tbtn--disabled svg,
.tox .tox-tbtn--disabled:hover svg,
.tox .tox-tbtn:disabled svg,
.tox .tox-tbtn:disabled:hover svg {
  fill: rgba(255, 255, 255, 0.5);
}

.tox .tox-tbtn--enabled,
.tox .tox-tbtn--enabled:hover {
  color: #fff;
  background: #599fef;
  border: 0;
  box-shadow: none;
}

.tox .tox-tbtn--enabled:hover > *,
.tox .tox-tbtn--enabled > * {
  transform: none;
}

.tox .tox-tbtn--enabled svg,
.tox .tox-tbtn--enabled:hover svg {
  fill: #fff;
}

.tox .tox-tbtn:focus:not(.tox-tbtn--disabled) {
  color: #fff;
}

.tox .tox-tbtn:focus:not(.tox-tbtn--disabled) svg {
  fill: #fff;
}

.tox .tox-tbtn:active > * {
  transform: none;
}

.tox .tox-tbtn--md {
  width: 51px;
  height: 42px;
}

.tox .tox-tbtn--lg {
  flex-direction: column;
  width: 68px;
  height: 56px;
}

.tox .tox-tbtn--return {
  align-self: stretch;
  width: 16px;
  height: unset;
}

.tox .tox-tbtn--labeled {
  width: unset;
  padding: 0 4px;
}

.tox .tox-tbtn__vlabel {
  display: block;
  margin-bottom: 4px;
  font-weight: 400;
  font-size: 10px;
  letter-spacing: -0.025em;
  white-space: nowrap;
}

.tox .tox-tbtn--select {
  width: auto;
  margin: 6px 1px 5px 0;
  padding: 0 4px;
}

.tox .tox-tbtn__select-label {
  margin: 0 4px;
  font-weight: 400;
  cursor: default;
}

.tox .tox-tbtn__select-chevron {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 16px;
}

.tox .tox-tbtn__select-chevron svg {
  fill: rgba(255, 255, 255, 0.5);
}

.tox .tox-tbtn--bespoke {
  background: #2f4055;
}

.tox .tox-tbtn--bespoke + .tox-tbtn--bespoke {
  margin-inline-start: 4px;
}

.tox .tox-tbtn--bespoke .tox-tbtn__select-label {
  width: 7em;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}

.tox .tox-split-button {
  display: flex;
  box-sizing: border-box;
  margin: 6px 1px 5px 0;
  overflow: hidden;
  border: 0;
  border-radius: 3px;
}

.tox .tox-split-button:hover {
  box-shadow: 0 0 0 1px #3389ec inset;
}

.tox .tox-split-button:focus {
  color: #fff;
  background: #3389ec;
  box-shadow: none;
}

.tox .tox-split-button > * {
  border-radius: 0;
}

.tox .tox-split-button__chevron {
  width: 16px;
}

.tox .tox-split-button__chevron svg {
  fill: rgba(255, 255, 255, 0.5);
}

.tox .tox-split-button .tox-tbtn {
  margin: 0;
}

.tox .tox-split-button.tox-tbtn--disabled .tox-tbtn:focus,
.tox .tox-split-button.tox-tbtn--disabled .tox-tbtn:hover,
.tox .tox-split-button.tox-tbtn--disabled:focus,
.tox .tox-split-button.tox-tbtn--disabled:hover {
  color: rgba(255, 255, 255, 0.5);
  background: 0 0;
  box-shadow: none;
}

.tox.tox-platform-touch .tox-split-button .tox-tbtn--select {
  padding: 0 0;
}

.tox.tox-platform-touch
  .tox-split-button
  .tox-tbtn:not(.tox-tbtn--select):first-child {
  width: 30px;
}

.tox.tox-platform-touch .tox-split-button__chevron {
  width: 20px;
}

.tox .tox-toolbar-overlord {
  background-color: #222f3e;
}

.tox .tox-toolbar,
.tox .tox-toolbar__overflow,
.tox .tox-toolbar__primary {
  display: flex;
  flex: 0 0 auto;
  flex-shrink: 0;
  flex-wrap: wrap;
  padding: 0 0;
  background-color: #222f3e;
  background-image: repeating-linear-gradient(
    rgba(255, 255, 255, 0.15) 0 1px,
    transparent 1px 39px
  );
  background-repeat: no-repeat;
  background-position: center top 40px;
  background-size: calc(100% - 11px * 2) calc(100% - 41px);
  transform: perspective(1px);
}

.tox .tox-toolbar-overlord > .tox-toolbar,
.tox .tox-toolbar-overlord > .tox-toolbar__overflow,
.tox .tox-toolbar-overlord > .tox-toolbar__primary {
  background-position: center top 0;
  background-size: calc(100% - 11px * 2) calc(100% - 0px);
}

.tox .tox-toolbar__overflow.tox-toolbar__overflow--closed {
  height: 0;
  padding-top: 0;
  padding-bottom: 0;
  visibility: hidden;
  opacity: 0;
}

.tox .tox-toolbar__overflow--growing {
  transition: height 0.3s ease, opacity 0.2s linear 0.1s;
}

.tox .tox-toolbar__overflow--shrinking {
  transition: opacity 0.3s ease, height 0.2s linear 0.1s,
    visibility 0s linear 0.3s;
}

.tox .tox-anchorbar,
.tox .tox-toolbar-overlord {
  grid-column: 1/-1;
}

.tox .tox-menubar + .tox-toolbar,
.tox .tox-menubar + .tox-toolbar-overlord {
  margin-top: -1px;
  padding-top: 1px;
  padding-bottom: 1px;
  border-top: 1px solid transparent;
}

.tox .tox-toolbar--scrolling {
  flex-wrap: nowrap;
  overflow-x: auto;
}

.tox .tox-pop .tox-toolbar {
  border-width: 0;
}

.tox .tox-toolbar--no-divider {
  background-image: none;
}

.tox
  .tox-toolbar-overlord
  .tox-toolbar:not(.tox-toolbar--scrolling):first-child,
.tox .tox-toolbar-overlord .tox-toolbar__primary {
  background-position: center top 39px;
}

.tox .tox-editor-header > .tox-toolbar--scrolling,
.tox .tox-toolbar-overlord .tox-toolbar--scrolling:first-child {
  background-image: none;
}

.tox.tox-tinymce-aux .tox-toolbar__overflow {
  padding: 4px 0;
  background-color: #222f3e;
  background-position: center top 43px;
  background-size: calc(100% - 8px * 2) calc(100% - 51px);
  border: none;
  border-radius: 6px;
  box-shadow: 0 0 2px 0 rgba(34, 47, 62, 0.2),
    0 4px 8px 0 rgba(34, 47, 62, 0.15);
}

.tox-pop .tox-pop__dialog .tox-toolbar {
  padding: 4px 0;
  background-position: center top 43px;
  background-size: calc(100% - 11px * 2) calc(100% - 51px);
}

.tox .tox-toolbar__group {
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  margin: 0 0;
  padding: 0 11px 0 12px;
}

.tox .tox-toolbar__group--pull-right {
  margin-left: auto;
}

.tox .tox-toolbar--scrolling .tox-toolbar__group {
  flex-shrink: 0;
  flex-wrap: nowrap;
}

.tox:not([dir='rtl']) .tox-toolbar__group:not(:last-of-type) {
  border-right: 1px solid transparent;
}

.tox[dir='rtl'] .tox-toolbar__group:not(:last-of-type) {
  border-left: 1px solid transparent;
}

.tox .tox-tooltip {
  position: relative;
  display: inline-block;
  padding: 8px;
}

.tox .tox-tooltip__body {
  padding: 4px 8px;
  color: rgba(255, 255, 255, 0.75);
  font-weight: 400;
  font-size: 14px;
  font-style: normal;
  text-transform: none;
  background-color: #3d546f;
  border-radius: 6px;
  box-shadow: 0 2px 4px rgba(34, 47, 62, 0.3);
}

.tox .tox-tooltip__arrow {
  position: absolute;
}

.tox .tox-tooltip--down .tox-tooltip__arrow {
  position: absolute;
  bottom: 0;
  left: 50%;
  border-top: 8px solid #3d546f;
  border-right: 8px solid transparent;
  border-left: 8px solid transparent;
  transform: translateX(-50%);
}

.tox .tox-tooltip--up .tox-tooltip__arrow {
  position: absolute;
  top: 0;
  left: 50%;
  border-right: 8px solid transparent;
  border-bottom: 8px solid #3d546f;
  border-left: 8px solid transparent;
  transform: translateX(-50%);
}

.tox .tox-tooltip--right .tox-tooltip__arrow {
  position: absolute;
  top: 50%;
  right: 0;
  border-top: 8px solid transparent;
  border-bottom: 8px solid transparent;
  border-left: 8px solid #3d546f;
  transform: translateY(-50%);
}

.tox .tox-tooltip--left .tox-tooltip__arrow {
  position: absolute;
  top: 50%;
  left: 0;
  border-top: 8px solid transparent;
  border-right: 8px solid #3d546f;
  border-bottom: 8px solid transparent;
  transform: translateY(-50%);
}

.tox .tox-well {
  width: 100%;
  padding: 8px;
  border: 1px solid #161f29;
  border-radius: 6px;
}

.tox .tox-well > :first-child {
  margin-top: 0;
}

.tox .tox-well > :last-child {
  margin-bottom: 0;
}

.tox .tox-well > :only-child {
  margin: 0;
}

.tox .tox-custom-editor {
  position: relative;
  display: flex;
  flex: 1;
  border: 1px solid #161f29;
  border-radius: 6px;
}

.tox .tox-dialog-loading::before {
  position: absolute;
  z-index: 1000;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  content: '';
}

.tox .tox-tab {
  cursor: pointer;
}

.tox .tox-dialog__content-js {
  display: flex;
  flex: 1;
}

.tox .tox-dialog__body-content .tox-collection {
  display: flex;
  flex: 1;
}

.tox.tox-tinymce-aux .tox-toolbar__overflow {
  box-shadow: 0 0 0 1px rgba(255, 255, 255, 0.15);
}
