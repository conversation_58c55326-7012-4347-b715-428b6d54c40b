<template>
  <el-dialog
    v-model="show"
    :title="`${title}, 请绑定页面`"
    append-to-body
    width="80%"
    @close="() => emit('close', addPage, backToList)"
  >
    <template #default>
      <QueryForm
        :form-list="formList"
        :loading="loading"
        @search="handleSearch" 
        hide-reset
      />
      <div v-loading="loading" style="min-height: 100px;">
        <CustomTable 
          ref="tableRef"
          :columns="columns"
          :data="tableData"
          :table-method="getList"
          :operate-list="operateList"
          single-selectable
          @current-change="handleSingleSelect"
          height="auto"
          max-height="600px"
        />
      </div>
    </template>
    <template #footer>
      <div class="text-center">
        <el-button plian @click="handleBackToList"> 返回列表 </el-button>
        <el-button type="primary" :loading="loading" :disabled="!pageId" @click="handleConfirm"> 关联此页面 </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, reactive, toRef } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import { storeToRefs } from 'pinia'
import { useParamsStore } from '@/store'
import { useBasicStore } from '@/store/basic'
import useTryCatch from '@/hooks/use-try-catch'
import useLoading from '@/hooks/use-loading'
import { pageBindArt } from '@/api/page-management'
import { getArtTempPage } from '@/api/article-management'
import type { artTempPageQuery, artTempPageItem } from '@/api/article-management'

import QueryForm from '@/components/QueryForm/index.vue'
import CustomTable from '@/components/CustomTable/index.vue'

const props = defineProps<{
  isAdd: boolean;
  art_id: number;
  channel_id?: number;
}>()

const emit = defineEmits(['confirm', 'close'])

const router = useRouter()
const { channel_item_all, userList } = storeToRefs(useParamsStore())
const basicStore = useBasicStore()
const { loading, setLoading } = useLoading()
const show = ref(true)
const title = ref(props.isAdd ? '文章创建成功' : '文章还未绑定页面')

const pageId = ref(0)
const addPage = ref(false)
const backToList = ref(false)

const queryParams = reactive<artTempPageQuery>({
  channel_id: props.channel_id || channel_item_all.value.channel_id as number,
  tpl_page_id: '',
  tpl_id: '',
  user_id: '',
  title: '',
  url: '',
})

const formList = ref([
  {
    label: '页面ID',
    clearable: true,
    placeholder: '页面ID',
    value: toRef(queryParams, 'tpl_page_id'),
    component: 'input',
  },
  {
    label: '页面标题',
    clearable: true,
    placeholder: '页面标题',
    value: toRef(queryParams, 'title'),
    component: 'input',
  },
  {
    label: 'url',
    clearable: true,
    placeholder: 'url',
    value: toRef(queryParams, 'url'),
    component: 'input',
  },
  {
    label: '模板ID',
    clearable: true,
    placeholder: '模板ID',
    value: toRef(queryParams, 'tpl_id'),
    component: 'input',
  },
  {
    label: '页面创建人',
    clearable: true,
    placeholder: '页面创建人',
    value: toRef(queryParams, 'user_id'),
    component: 'select',
    labelKey: 'real_name',
    valueKey: 'user_id',
    selections: userList
  },
])

const columns = ref([
  {
    title: '页面ID',
    dataKey: 'tpl_page_id',
    width: 100,
  },
  {
    title: '页面标题',
    dataKey: 'title',
    minWidth: 200,
  },
  {
    title: 'url',
    dataKey: 'url',
    minWidth: 200,
  },
  {
    title: '页面所属模板ID',
    dataKey: 'tpl_id',
    width: 100,
  },
  {
    title: '页面创建人',
    dataKey: 'user_name',
    minWidth: 100,
  },
])
const tableRef = ref()
const tableData = ref<artTempPageItem[]>([])
const operateList = ref([
  {
    title: '创建页面并绑定文章',
    button: true,
    append: true,
    method: () => {
      addPage.value = true
      handleClose()
      basicStore.delCachedView('PageManagementOperate')
      router.push({
        name: 'PageManagementOperate',
        query: {
          type: 'add',
          art_id: props.art_id,
          channel_id: queryParams.channel_id
        }
      })
    },
    disabled: computed<boolean>(() => queryParams.channel_id ? false : true)
  },
])

const getList = ( { page, pageSize } ) => {
  useTryCatch( async () => {
    setLoading(true)
    const params = {
      ...queryParams,
      page,
      page_size: pageSize
    }

    const res = await getArtTempPage(params)
    if (res.code === 200) {
      tableData.value = res.data.list
      tableRef.value?.setTotal(res.data.total)
    } else {
      ElMessage.error(res.msg)
    }
    setLoading(false)
  }, () => setLoading(false) )
}

const handleSearch = () => {
  pageId.value = 0
  tableRef.value.setPage(1)
  const { page, pageSize } = tableRef?.value?.getPagination()
  getList( { page, pageSize } )
}

const handleClose = () => {
  show.value = false
}
const handleBackToList = () => {
  backToList.value = true
  handleClose()
}

const handleConfirm = () => {
  useTryCatch( async () => {
    setLoading(true)
    const params = {
      tpl_page_id: pageId.value,
      art_id: props.art_id
    }
    const res = await pageBindArt(params)
    if (res.code === 200) {
      ElMessage.success('文章绑定页面成功')
      handleClose()
      emit('confirm')
    } else {
      ElMessage.error(res.msg)
    }
    setLoading(false)
  }, () => setLoading(false) )
}

const handleSingleSelect = (row?: artTempPageItem) => {
  if (row) {
    pageId.value = row.tpl_page_id
  }
}

defineExpose({
  show: () => show.value = true,
  hide: handleClose
})
</script>