{"name": "cms40", "version": "7.0.0", "license": "MIT", "author": "vincent<PERSON>", "packageManager": "pnpm@7.9.0", "scripts": {"dev": "vite --mode serve-dev", "test": "vite --mode serve-test", "build:test": "vite build --mode  build-test", "build": "vite build --mode build", "preview:build": "npm run build && vite preview ", "preview": "vite preview ", "lint": "eslint --ext .js,.jsx,.vue,.ts,.tsx src --fix", "prepare": "husky install", "test:unit": "vue-cli-service test:unit", "test:watchAll": "vue-cli-service test:unit --watchAll", "test:cov": "vue-cli-service test:unit --coverage", "test:majestic": "majestic", "vitest": "vitest --ui", "tsc-check": "tsc", "coverage": "vitest run --coverage"}, "dependencies": {"@element-plus/icons-vue": "^2.1.0", "@tinymce/tinymce-vue": "^5.1.0", "ace-builds": "^1.19.0", "ace-diff": "^3.0.3", "axios": "^1.4.0", "echarts": "^5.4.2", "element-plus": "^2.3.4", "js-beautify": "^1.14.8", "js-cookie": "^2.2.1", "js-error-collection": "^1.0.8", "mitt": "3.0.0", "moment-mini": "2.22.1", "nprogress": "0.2.0", "path-browserify": "^1.0.1", "path-to-regexp": "^6.2.1", "pinia": "^2.0.35", "pinia-plugin-persistedstate": "2.3.0", "pnpm": "^7.32.2", "query-string": "^8.1.0", "screenfull": "^6.0.2", "tinymce": "^6.4.2", "vue": "^3.2.47", "vue-clipboard3": "^2.0.0", "vue-diff": "^1.2.4", "vue-i18n": "9.1.10", "vue-router": "^4.1.6", "xlsx": "^0.18.5"}, "devDependencies": {"@babel/eslint-parser": "7.16.3", "@types/mockjs": "1.0.6", "@types/node": "^17.0.45", "@types/path-browserify": "^1.0.0", "@typescript-eslint/eslint-plugin": "5.30.0", "@typescript-eslint/parser": "5.30.0", "@vitejs/plugin-legacy": "^2.3.1", "@vitejs/plugin-vue": "^2.3.4", "@vitejs/plugin-vue-jsx": "^2.1.1", "@vitest/coverage-c8": "^0.22.1", "@vitest/ui": "^0.22.1", "@vue/cli-plugin-unit-jest": "4.5.17", "@vue/cli-service": "4.5.17", "@vue/test-utils": "^2.3.2", "@vueuse/core": "^8.9.4", "eslint": "8.18.0", "eslint-config-prettier": "8.5.0", "eslint-define-config": "1.5.1", "eslint-plugin-eslint-comments": "3.2.0", "eslint-plugin-import": "2.26.0", "eslint-plugin-jsonc": "^2.7.0", "eslint-plugin-markdown": "^3.0.0", "eslint-plugin-prettier": "4.1.0", "eslint-plugin-unicorn": "^43.0.2", "eslint-plugin-vue": "9.1.1", "husky": "7.0.2", "jsdom": "16.4.0", "jsonc-eslint-parser": "^2.2.0", "majestic": "1.8.1", "mockjs": "1.1.0", "prettier": "2.2.1", "resize-observer-polyfill": "^1.5.1", "rollup-plugin-visualizer": "^5.9.0", "sass": "^1.62.1", "svg-sprite-loader": "6.0.11", "typescript": "^4.9.5", "unocss": "^0.33.5", "unplugin-auto-import": "^0.11.5", "unplugin-vue-components": "^0.22.12", "unplugin-vue-define-options": "1.1.3", "vite": "^3.2.6", "vite-plugin-eslint": "^1.8.1", "vite-plugin-html": "^3.2.0", "vite-plugin-mkcert": "^1.15.0", "vite-plugin-mock": "^2.9.8", "vite-plugin-svg-icons": "^2.0.1", "vitest": "^0.22.1", "vue-tsc": "^0.34.17"}, "pnpm": {"peerDependencyRules": {"ignoreMissing": ["html-webpack-plugin", "vite-plugin-mock", "unplugin-auto-import", "unplugin-vue-components", "vue-template-compiler", "unocss", "unplugin", "vite-plugin-mock", "@vitejs/plugin-legacy", "@vitejs/plugin-vue", "@vitejs/*", "@babel/*", "vite", "vue", "@unocss/vite", "rollup", "vue-jest", "@babel/*"]}}, "browserslist": ["> 1%", "not ie 11", "not op_mini all"], "engines": {"node": ">= 16 <18", "pnpm": ">= 6 <8"}}