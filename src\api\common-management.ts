import axiosReq from './axios'
import type { HttpResponse } from './axios/types'

export type siteConfig = {
  site_id: number;
  name: string;
  host: string;
  html_path: string;
  channel_id: number;
  channel_code: string;
  image_path: string;
  video_path: string;
  secondary_dir: string;
  host_js: string;
  host_css: string;
  host_image: string;
  host_video: string;
  brand: string;
  main_currency: string;
  language: string;
  leads_mail: string;
  syn_mail: string;
  state: string;
  site_file_limits: string;
  can_use: string;
  examiner: string;
}

export const getSiteDetail = (site_id: number) => 
  axiosReq<any, HttpResponse<siteConfig>>('get', `/api/v1/public/site?site_id=${site_id}`)