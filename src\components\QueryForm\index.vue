<template>
  <div class="query-form" @keyup.enter="handleSearch">
    <div ref="scrollRef" style="display: flex; flex-wrap: nowrap; overflow: auto;">
      <FormItems :form-list="constantList"/>
      <div v-if="toggleList.length" class="query-form-item" style="width: 60px">
        <el-tooltip :disabled="true" placement="bottom" :auto-close="0" content="点击展开/收起筛选项">
          <el-button link @click="toggleExpanded" style="height: 32px;">
            {{ expanded ? '收起' : '展开' }}
            <ArrowDownBold :style="{ transform: `rotate(${expanded ? '180' : '0' }deg)`, width: '12px', transition: 'transform .3s', marginLeft: '5px' }" />
          </el-button>
        </el-tooltip>
      </div>
      <div class="query-form-item" style="width: auto">
        <el-space :size="8">
          <el-button type="primary" :icon="Search" @click="handleSearch" :loading="loading">查询</el-button>
          <span v-show="expanded || toggleList.length === 0">
            <el-button v-if="!hideReset" :icon="RefreshLeft" @click="handleReset" :loading="loading">重置</el-button>
          </span>
        </el-space>
      </div>
    </div>
    <CollapseTransition v-if="toggleList.length">
      <div class="toggle-form" v-show="expanded">
        <FormItems :form-list="toggleList"/>
      </div>
    </CollapseTransition>
    <div v-if="toggleList.length" class="query-form-item" :style="{ display: toggleList.length ? 'block' : 'inline-block', width: 'auto' }">
      <el-space :size="8">
        <el-button v-if="showExtraSearch" type="primary" :icon="Search" @click="handleSearch" :loading="loading">查询</el-button>
        <el-button v-if="!hideReset" :icon="RefreshLeft" @click="handleReset" :loading="loading">重置</el-button>
        <!-- <template v-if="hasAppend">
          <el-tooltip v-if="!expanded" placement="bottom" :auto-close="0" content="展示额外筛选项">
            <Plus class="tail-icon" style="width: 24px; cursor: pointer;" @click="setExpanded(true)" />
          </el-tooltip>
          <el-tooltip  v-else placement="bottom" :auto-close="0" content="隐藏额外筛选项">
            <Minus class="tail-icon" style="width: 24px; cursor: pointer;" @click="setExpanded(false)" />
          </el-tooltip>
        </template> -->
      </el-space>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { Search, Plus, Minus, RefreshLeft, ArrowDownBold } from '@element-plus/icons-vue'
import type { listItem } from './types'

import CollapseTransition from '@/components/Transition/collapse.vue'
import FormItems from './formItems.vue'

defineOptions({name: 'QueryForm'})

const props = withDefaults(
  defineProps<{
    formList?: listItem[];
    loading?: boolean;
    hideReset?: boolean;
  }>(), {
    formList: () => [] as listItem[],
    loading: () => false
  })

const emit = defineEmits(['search', 'reset'])

const expanded = ref(false)
const hasAppend = ref(false)
const constantList = ref<listItem[]>([])
const toggleList = ref<listItem[]>([])
const scrollRef = ref<HTMLElement>()
const showExtraSearch = ref(false)

const handleSearch = () => {
  emit('search')
}
const handleReset = () => {
  emit('reset')
}

const setExpanded = (val: boolean) => {
  expanded.value = val
}
const toggleExpanded = () => {
  expanded.value = !expanded.value
}

for(let item of props.formList) {
  if (item.append) {
    toggleList.value.push(item)
  } else {
    constantList.value.push(item)
  }
}

const handleShowExtra = () => {
  const el = scrollRef.value
  if (el) {
    const { width } = el.getBoundingClientRect()
    showExtraSearch.value = el.scrollWidth > width
  }
}

onMounted(() => {
  handleShowExtra()
})

</script>