import { defineStore } from 'pinia'
import { h } from 'vue'
import { ElNotification } from 'element-plus'
import router from '@/router'
import useTryCatch from '@/hooks/use-try-catch'
import { pageQualityCheck } from '@/api/page-management'
// 存放全局异步方法
const useAsyncMethodStroe = defineStore('asyncMethods', {
  state: () => ({
    checkedPage: new Set<number>()
  }),
  actions: {
    handleGlobalPageCheck(tpl_id: number, page_id: number, site_id: number) {
      if (this.checkedPage.has(page_id)) {
        return
      }
      useTryCatch( async () => {
        this.checkedPage.add(page_id)
        const res = await pageQualityCheck(tpl_id, page_id, site_id)
        if (res.data) {
          const instance = ElNotification({
            title: '页面存在质量问题',
            type: 'warning',
            position: 'bottom-right',
            duration: 0,
            showClose: true,
            onClose: () => this.checkedPage.delete(page_id),
            message: h('div', [
              h('div', [
                '页面ID为',
                h('strong', { style: { color: 'var(--el-color-error)' } }, page_id),
                '的页面存在质量问题，点击可查看详情'
              ]),
              h('div', { class: 'pop-footer' }, [
                h('span', {
                  class: 'pop-link',
                  onClick: () => {
                    router.push({
                      name: 'PageWarning',
                      query: {
                        tpl_id,
                        page_id,
                        site_id
                      }
                    })
                    this.checkedPage.delete(page_id)
                    instance.close()
                  }
                }, '查看详情')
              ])
            ])
          })
        }
      } )
    }
  }
})

export default useAsyncMethodStroe