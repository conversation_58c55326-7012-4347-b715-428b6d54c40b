<template>
  <div class="scroll-y">
    <div style="overflow: hidden;" :style="{ minHeight: `calc(100% - ${fixBottomHeight}px)` }">
      <el-form
        ref="formRef"
        label-suffix=":"
        label-width="140px"
        :model="submitData"
      >
        <el-row :gutter="20">
          <el-col :span="12">
            <el-card shadow="never" style="height: 100%;">
              <template #header>
                <strong style="font-size: 1.25rem;">基础信息</strong>
              </template>
              <template #default>
                <el-form-item label="当前站点">
                  {{ site_name_all }}
                </el-form-item>
                <el-form-item label="所属渠道">
                  {{ channel_item_all.channel_code }}
                </el-form-item>
                <template v-if="cat_id">
                  <el-form-item label="分类ID">
                    {{ cat_id }}
                  </el-form-item>
                </template>
                <el-form-item label="分类名称" prop="name" :rules="{ required: true }">
                  <el-input v-model="submitData.name" placeholder="请输入分类名称" />
                </el-form-item>
                <el-form-item label="父级分类" prop="parent_id">
                  <el-tree-select 
                    v-model="submitData.parent_id"
                    placeholder="不选则默认为一级分类"
                    value-key="id"
                    :data="classifyData"
                    :render-after-expand="true"
                    check-strictly
                    show-checkbox
                    check-on-click-node
                  />
                </el-form-item>
                <el-form-item label="分类页面URL" prop="page_url">
                  <el-input v-model="submitData.page_url" placeholder="分类页面URL" />
                </el-form-item>
                <el-form-item label="排序" prop="sort">
                  <el-input-number v-model="submitData.sort" :step="1" step-strictly />
                </el-form-item>
                <el-form-item label="是否展示" prop="is_display">
                  <el-switch 
                    v-model="submitData.is_display"
                    active-value="1"
                    inactive-value="0"
                    active-text="是"
                    inactive-text="否"
                  />
                </el-form-item>
                <el-form-item label="是否热门" prop="is_hot">
                  <el-switch 
                    v-model="submitData.is_hot"
                    active-value="1"
                    inactive-value="0"
                    active-text="是"
                    inactive-text="否"
                  />
                </el-form-item>
                <el-form-item label="是否推荐" prop="is_recom">
                  <el-switch 
                    v-model="submitData.is_recom"
                    active-value="1"
                    inactive-value="0"
                    active-text="是"
                    inactive-text="否"
                  />
                </el-form-item>
                <el-form-item label="文章图片URL" prop="image_url">
                  <el-input v-model="submitData.image_url" placeholder="文章图片URL" />
                </el-form-item>
                <el-form-item label="文章缩略图URL" prop="thumb_url">
                  <el-input v-model="submitData.thumb_url" placeholder="文章缩略图URL" />
                </el-form-item>
                <el-form-item label="产品平台" prop="productOS">
                  <el-radio-group v-model="submitData.productOS">
                    <el-radio label="win">win</el-radio>
                    <el-radio label="mac">mac</el-radio>
                    <el-radio label="android">android</el-radio>
                    <el-radio label="ios">ios</el-radio>
                    <el-radio label="other">其他</el-radio>
                  </el-radio-group>
                </el-form-item>
                <el-form-item label="分类简介" prop="summary">
                  <el-input v-model="submitData.summary" type="textarea" placeholder="分类简介" :rows="3" />
                </el-form-item>
                <el-form-item label="备份说明" prop="remark">
                  <el-input v-model="submitData.remark" type="textarea" placeholder="内部备注专用" :rows="3" />
                </el-form-item>
              </template>
            </el-card>
          </el-col>
          <el-col :span="12">
            
          </el-col>
        </el-row>
      </el-form>
    </div>

    <div class="fixed-bottom text-center">
      <el-button plain :icon="Back" @click="handleBack">返回</el-button>
      <el-button 
        v-if="type !== 'view'"
        type="primary"
        :icon="Finished"
        :loading="loading"
        @click="handleSave()"
      >
        保存
      </el-button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive } from 'vue'
import { ElMessage } from 'element-plus'
import { useRoute, useRouter } from 'vue-router'
import { Back, Finished } from '@element-plus/icons-vue'
import { useParamsStore, useTagsViewStore } from '@/store'
import { useConfigStore } from '@/store/config'
import { useBasicStore } from '@/store/basic'
import useTryCatch from '@/hooks/use-try-catch'
import useLoading from '@/hooks/use-loading'
import { addClassify, editClassify, getClassifyData, getClassifyDetail, getClassifyHistoryDetail } from '@/api/article-management'
import type { classifyData, classifyTreeItem } from '@/api/article-management'

const route = useRoute()
const router = useRouter()
const { site_id_all, site_name_all, channel_item_all } = useParamsStore()
const { delVisitedView, changeViewTitle } = useTagsViewStore()
const { fixBottomHeight } = useConfigStore()
const basicStore = useBasicStore()
const { loading, setLoading } = useLoading()
const { type, cat_id, parent_id, version_id }  = route.query as unknown as {
  type: string;
  cat_id: number;
  parent_id: number;
  version_id: number;
}

const submitData_raw = {
  site_id: site_id_all,
  channel_id: channel_item_all.channel_id as number,
  name: '',
  parent_id: +parent_id || null,
  sort: 0,
  image_url: '',
  thumb_url: '',
  summary: '',
  remark: '',
  productOS: '',
  page_url: '',
  is_display: '1',
  is_hot: '0',
  is_recom: '0'
}
const submitData = reactive<classifyData>({
  ...submitData_raw,
})
const classifyData = ref<classifyTreeItem[]>([])

const getClassifyTreeData = () => {
  useTryCatch( async () => {
    const res = await getClassifyData(site_id_all, channel_item_all.channel_id as number,)
    if (res.code === 200) {
      classifyData.value = res.data
    }
  } )
}
const getDetail = () => {
  useTryCatch( async () => {
    setLoading(true)

    const res = await getClassifyDetail(cat_id)
    if (res.code === 200) {
      const data = res.data
      Object.keys(submitData_raw).forEach(( key ) => {
        data[key] && (submitData[key] = data[key])
      })
    }

    setLoading(false)
  }, () => setLoading(false) )
}
const handleBack = () => {
  delVisitedView(route)
  router.go(-1)
}
const handleSave = () => {
  if (!submitData.name) {
    return ElMessage.warning('分类名称不能为空!')
  }

  useTryCatch( async () => {
    setLoading(true)
    const params = {
      ...submitData
    }
    const api = type === 'add' ? addClassify : editClassify

    const res = await api(params, cat_id)
    if (res.code === 200) {
      ElMessage.success(`${type === 'add' ? '添加' : '编辑'}成功`)
      basicStore.setRefresh(true)
      router.push({
        name: 'ArticleClassifyList'
      })
      delVisitedView(route)
    } else {
      ElMessage.error(res.msg)
    }

    setLoading(false)
  }, () => setLoading(false) )
}

getClassifyTreeData()
if (cat_id) {
  type === 'edit'
  ? getDetail()
  : useTryCatch( async () => {
      const res = await getClassifyHistoryDetail(cat_id, version_id, site_id_all)
      if (res.code === 200) {
        const data = res.data
        Object.keys(submitData_raw).forEach(( key ) => {
          data[key] && (submitData[key] = data[key])
        })
      } else {
        ElMessage.error(res.msg)
      }
  } )
}
changeViewTitle(route, type === 'view' ? '查看分类历史详情' : `${type === 'add' ? '添加' : '编辑'}文章分类`)
</script>