//scss 语法糖包含常用的布局方式 flex column
@import './scss-suger.scss';
//重置 element-plus 样式
@import './reset-elemenet-plus-style.scss';
//动画文件
@import './transition.scss';
// 自定义布局样式
@import './layout.scss';
// 自定义全局组件样式
@import './components.scss';

//reset style
body {
  height: 100%;
  margin: 0;
  padding: 0;
  font-size: 16px;
  overflow: hidden;
}
* {
  box-sizing: border-box;
  &:focus {
    outline: none;
  }
}
*::before,
*::after {
  box-sizing: border-box;
}
a:focus,
a:active {
  outline: none;
}
a,
a:focus,
a:hover {
  cursor: pointer;
  color: inherit;
  text-decoration: none;
}

h1,
h2,
h3,
h4,
h5,
h6 {
  line-height: 1;
  margin: 0;
  padding: 0;
}
span,
output {
  display: inline-block;
}

//scroll
@mixin main-show-wh() {
  /* css 声明 */
  //height: calc(100vh - #{$navBarHeight} - #{$tagViewHeight} - #{$appMainPadding * 2});
  // height: calc(100vh - #{var(--nav-bar-height)} - #{var(--tag-view-height)} - #{calc(var(--app-main-padding) * 2)});
  height: calc(100vh - #{var(--nav-bar-height)}  * 2 - #{var(--tag-view-height)} - #{var(--layout-gap)} * 5 - 2 * #{calc(var(--app-main-padding))});
  width: 100%;
}
.scroll-y {
  @include main-show-wh();
  overflow-y: auto;
  scroll-behavior: smooth;
}
.scroll-x {
  @include main-show-wh();
  overflow-x: auto;
}
.scroll-xy {
  @include main-show-wh();
  overflow: auto;
}

	
* {
  scrollbar-color: var(--el-scrollbar-bg-color) var(--el-fill-color-light)
}
::-webkit-scrollbar {
  width: 6px
}
::-webkit-scrollbar:horizontal {
  height: 6px
}
::-webkit-scrollbar-track {
  border-radius: 10px
}
::-webkit-scrollbar-thumb {
  background-color: #0003;
  border-radius: 10px;
  transition: all .2s ease-in-out
}
::-webkit-scrollbar-thumb:hover {
  cursor: pointer;
  background-color: #0000004d
}
.dark ::-webkit-scrollbar-thumb {
  background-color: #fff3
}
.dark ::-webkit-scrollbar-thumb:hover {
  background-color: #fff6
}
.static-table {
  width: 100%;
  max-width: 800px;
  border-left: var(--el-table-border);
  border-top: var(--el-table-border);
  text-align: center;
  margin-bottom: 20px;
  td {
    border-bottom: var(--el-table-border);
    border-right: var(--el-table-border);
    padding: 12px;
  }
}
.fixed-bottom {
  position: sticky;
  width: 100%;
  padding-top: 10px;
  bottom: 0;
  margin-left: 0;
  background-color: var(--el-bg-color);
  z-index: 9;
  box-shadow: var(--el-box-shadow-lighter);
}
.with-hand {
  cursor: pointer;
}
.dot {
  position: relative;
  &::before{
    content: '';
    transform: translate(0, -50%);
    margin-right: 5px;
    display: inline-block;
    width: 5px;
    height: 5px;
    border-radius: 50%;
    background-color: var(--color);
  }
  
}
/* 首页自定义样式 */
// .home .navbar {
//   position: fixed;
//   width: 100%;
//   left: 0;
//   z-index: 1999;
// }
/* 首页自定义样式 */
.el-dropdown {
  --el-text-color-regular: var(--el-text-color-primary)
}

.fix-left {
  position: absolute;
  left: 0;
  top: 16px;
  width: 24px;
  font-size: 14px;
  line-height: 1.15;
  .tab-item {
    display: inline-block;
    padding: 6px 0;
    padding-left: 8px;
    position: relative;
    transition: color .3s;
    &.active {
      color: var(--el-color-primary);
      &::before {
        content: '';
        position: absolute;
        height: 100%;
        width: 2px;
        left: 0;
        top: 0;
        background-color: var(--el-color-primary);
        z-index: 1;
        border-radius: 1px;
      }
    }
    &:hover {
      color: var(--el-color-primary);
    }
  }
  &::after {
    content: '';
    position: absolute;
    bottom: auto;
    top: 0;
    left: 0;
    background-color: var(--el-border-color-light);
    height: 100%;
    width: 2px;
    border-radius: 1px;
  }
}
.ace-trigger{
  .el-form-item__error,
  &~.el-form-item__error {
    left: auto;
    right: 0;
  }
}

.operate-button-wrapper {
  position: relative;
  &.mask::after{
    content: '';
    position: absolute;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    z-index: 10;
  }
}
// 兼容tinymce
.tox-fullscreen {
  .sidebar-container, .tags-view-container .tags-view-wrapper{
    z-index: -1;
  }
}
// 多选样式不换行
.el-select-tags-wrapper { 
  white-space: nowrap;
  overflow: auto;
}
// 禁用表格label点击
.el-form-item__label {
  pointer-events: none;
}

.bg-lighter {
  background-color: var(--el-fill-color-lighter) !important;
}

.is-required:not(.el-form-item)::before {
  content: "*";
  color: var(--el-color-danger);
  margin-right: 4px;
}
.is-required.absolute::before  {
  position: absolute;
  top: 0;
  left: -10px;
}

// 修饰形状
.dot-triangle-up {
  display: inline-block;
  width: 0;
  height: 0;
  border-left: .5em solid transparent;
  border-right: .5em solid transparent;
  border-bottom: 1em solid var(--color);
}
.dot-square {
  display: inline-block;
  background-color: var(--color);
  width: 1em;
  height: 1em;
}
.dot-round {
  display: inline-block;
  background-color: var(--color);
  width: 1em;
  height: 1em;
  border-radius: 50%;
}
