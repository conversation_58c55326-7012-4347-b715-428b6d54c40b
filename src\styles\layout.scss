// Layout
$layout-box-shadow: none;
$layout-gap: 0px;
$layout-border-radius: 0px;

html{
  --layout-box-shadow: #{$layout-box-shadow};
  --layout-gap: #{$layout-gap};
  --layout-border-radius: #{$layout-border-radius};
  --nav-bar-height: 60px;
  --subnav-bar-height: 50px;
}
.layout-container {
  --layout-border-radius: 12px;
  border-radius: var(--layout-border-radius) 12px 12px 12px;
  background-color: var(--bg-layout);
  border: 1px solid var(--border-color-layout);
  &.layout-radius {
    --layout-border-radius: 0;
  }
}
.layout-gap {
  padding: 16px;
}
