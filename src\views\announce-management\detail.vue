<template>
  <div class="scroll-y">
    <el-form
      v-loading="loading"
      label-position="right"
      label-width="120px"
      style="max-width: 1000px;"
    >
      <el-form-item label="公告标题：">
        <strong>{{ detailInfo?.title }}</strong>
      </el-form-item>
      <el-form-item label="发布时间：">
        <strong>{{ detailInfo?.publish_time }}</strong>
      </el-form-item>
      <el-form-item label="内容：">
        <el-input
          type="textarea"
          :value="detailInfo?.content"
          readonly
          rows="28"
        />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" :icon="Back" @click="handleBack"> 返回 </el-button>
      </el-form-item>
    </el-form>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import { Back } from '@element-plus/icons-vue'
import { useBasicStore } from '@/store/basic'
import useLoading from '@/hooks/use-loading'
import useTryCatch from '@/hooks/use-try-catch'
import { getAnnounceDetail, setAnnounceReaded } from '@/api/announcement'
import type { announceDetail } from '@/api/announcement'

const router = useRouter()
const route = useRoute()
const { announcement_id } = route.query
const basicStore = useBasicStore()
const { loading, setLoading } = useLoading()

const detailInfo = ref<announceDetail>()

const getDetail = async () => {
  
  if (announcement_id) {
    useTryCatch(
      async () => {
        setLoading(true)
        const res = await getAnnounceDetail(+announcement_id)
        if (res.code === 200) {
          detailInfo.value = res.data
          if (detailInfo.value?.is_read !== 1) {
            await setAnnounceReaded(+announcement_id)
            ElMessage.success('已阅')
          }
        } else {
          ElMessage.error(res.msg)
        }
        setLoading(false)
      },
      () => setLoading(false)
    )
  }
}

const handleBack = () => {
  const refresh = (detailInfo.value?.is_read !== 1) ? true : false
  basicStore.setRefresh(refresh)
  router.go(-1)
}

getDetail()
</script>