<template>
  <el-dialog
    title="文章历史对比"
    v-model="show"
    top="3vh"
    width="95%"
    append-to-body
    @closed="emit('close')"
  >
    <template #default>
      <div style="overflow: auto; max-height: calc(100vh - 15vh);">
        <el-form label-suffix=":" label-width="120px" style="overflow: hidden">
          <el-row :gutter="20">
            <el-col :span="12">
              <el-card shadow="never" style="height: 100%;">
                <template #header>
                  文章-{{ art_id }} V{{ prev?.version }} 基础信息
                </template>
                <template #default>
                  <el-form-item
                    v-for="d in commonFields"
                    :label="d.label"
                    :style="{ color: prev?.[d.key] === current?.[d.key] ? '' : 'var(--el-color-success)' }"
                  >
                    <template v-if="d.key === 'is_hot' || d.key === 'is_recom'">
                      {{ prev?.[d.key] === '0' ? '否' : '是' }}
                    </template>
                    <template v-else>
                      {{ prev?.[d.key] }}
                    </template>
                  </el-form-item>
                </template>
              </el-card>
            </el-col>
            <el-col :span="12">
              <el-card shadow="never" style="height: 100%;">
                <template #header>
                  文章-{{ art_id }} V{{ current?.version }} 基础信息
                </template>
                <template #default>
                  <el-form-item
                    v-for="d in commonFields"
                    :label="d.label"
                    :style="{ color: prev?.[d.key] === current?.[d.key] ? '' : 'var(--el-color-danger)' }"
                  >
                  <template v-if="d.key === 'is_hot' || d.key === 'is_recom'">
                      {{ current?.[d.key] === '0' ? '否' : '是' }}
                    </template>
                    <template v-else>
                      {{ current?.[d.key] }}
                    </template>
                  </el-form-item>
                </template>
              </el-card>
            </el-col>
          </el-row>
        </el-form>
        <Diff 
          v-if="showDiff"
          :prev="(prev?.content as string)"
          :current="(current?.content as string)"
        />
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { ElMessage } from 'element-plus'
import useParamsStore from '@/store/modules/params'
import useTryCatch from '@/hooks/use-try-catch'
import { getComparisons } from '@/api/article-management'
import type { comparisonItem } from '@/api/article-management'

import Diff from '@/components/CodeDiff/index.vue'

const props = defineProps<{
  art_id: number|string;
  version_ids: string;
}>()
const emit = defineEmits(['close'])

const commonFields = [
  {
    label: '页面标题',
    key: 'title'
  },
  {
    label: '文章副标题',
    key: 'subtitle'
  },
  {
    label: '文章主分类ID',
    key: 'cat_id'
  },
  {
    label: '文章语言',
    key: 'language'
  },
  {
    label: '文章作者ID',
    key: 'author_id'
  },
  {
    label: '是否热门',
    key: 'is_hot'
  },
  {
    label: '是否推荐',
    key: 'is_recom'
  },
  {
    label: '备份说明',
    key: 'remark'
  }
]

const { site_id_all } = useParamsStore()
const show = ref(true)
const showDiff = ref(false)

const prev = ref<comparisonItem>()
const current = ref<comparisonItem>()

const getDetail = () => {
  useTryCatch( async () => {
    const { art_id, version_ids } = props
    const res = await getComparisons(site_id_all, art_id, version_ids)
    if (res.code === 200) {
      const data = Object.values(res.data)
      prev.value = data[0] as comparisonItem
      current.value = data[1] as comparisonItem
      showDiff.value = true
    } else {
      ElMessage.error(res.msg)
    }
  } )
}

getDetail()
</script>