<template>
  <QueryForm
    :form-list="formList"
    :loading="loading"
     @search="handleSearch" 
     @reset="reset"
   />
   <div v-loading="loading" class="sticky-table">
     <CustomTable
      ref="tableRef"
      :columns="columns"
      :data="tableData"
      :table-method="getList"
      :customize-head="true"
      :selectable="true"
      :operate-list="operateList"
      @selection-change="handleSelectionChange"
      @sort-change="handleSortChange"
    />
  </div>

  <AddPageModal
    v-if="loadAddPageModal"
    @closed="() => (loadAddPageModal = false)"
    @confirm="handleSearch"
    v-bind="{ module: module_name, type: tpl_type, tpl_id: +tpl_id, channel_id }"
  />

  <StartTaskModal 
    v-if="loadStartTaskModal"
    :entity_ids="tpl_page_ids"
    @close="loadStartTaskModal = false"
  />
</template>

<script setup lang="tsx">
import { ref, reactive, toRef, computed, defineAsyncComponent } from 'vue'
import { ElMessage, ElButton, ElDropdown, ElDropdownMenu, ElDropdownItem, ElMessageBox } from 'element-plus'
import { useRoute, useRouter } from 'vue-router'
import { storeToRefs } from 'pinia'
import { useParamsStore, useAsyncMethodStroe, useUserStroe } from '@/store'
import { useBasicStore } from '@/store/basic'
import useTryCatch from '@/hooks/use-try-catch'
import { getHost } from '@/hooks/use-env'
import useLoading from '@/hooks/use-loading'
import { getDownloadLocalToken } from '@/hooks/use-local-auth'
import { usePageGenerate } from '@/hooks/use-generate'
import { getPagesList, changeStatus } from '@/api/page-management'
import { clearCdn } from '@/api/tools-management'
import type { queryParams, listItem } from '@/api/page-management'
import Listener from '@/utils/site-channel-listeners'
import { exportDownload } from './export'

import SvgIcon from '@/icons/SvgIcon.vue'
import QueryForm from '@/components/QueryForm/index.vue'
import CustomTable from '@/components/CustomTable/index.vue'
const AddPageModal = defineAsyncComponent( () => import('./addPageModal.vue') )
const StartTaskModal = defineAsyncComponent( () => import('@/views/translate-management/components/startTaskModal.vue') )

const props = defineProps<{
  isTempList?: boolean;
}>()

const router = useRouter()
const route = useRoute()
const { site_id_all, channel_item_all, userList } = storeToRefs(useParamsStore())
const { moduleList, moduleTypeMap, pageStateList, page_state_map } = useParamsStore()
const { banIds, isBanned } = useUserStroe()
const { handleGlobalPageCheck } = useAsyncMethodStroe()
const basicStore = useBasicStore()
const { loading, setLoading } = useLoading()

const cascaderList = moduleList.map( ( { label, value } ) => {
  return {
    label,
    value,
    children: moduleTypeMap[value] || []
  }
} )


const { module: module_name, tpl_type, tpl_name, state, tpl_id, entity_ids, channel_id, video_type } = route.query as unknown as {
  module: string;
  tpl_type: string;
  tpl_name: string;
  state: string;
  tpl_id: number;
  entity_ids: string;
  channel_id: string;
  video_type: string;
}
const queryParams_raw = {
  module: '',
  type: '',
  user_id_a: '',
  user_id_e: '',
  user_id_c: '',
  tpl_language: '',
  page_state: '',
  url: '',
  tpl_name: '',
  state: '',
  tpl_id: '',
  channel_id: '',
  page_group_id: '',
  tpl_page_ids: '',
  title: '',
  description: '',
  time_field: '',
  start_time: '',
  end_time: '',
  level: '',
  video_type: ''
}
const channelId = ref(channel_id)
const queryParams = reactive<queryParams>({
  ...queryParams_raw,
  module: module_name || '',
  type: tpl_type || '',
  tpl_name: tpl_name || '',
  state: '',
  tpl_id: `${tpl_id || ''}`,
  tpl_page_ids: entity_ids || '',
  video_type: video_type || ''
})
const cascader = ref<string[]>([])
const timeRange = ref('')
const formList = ref([
  {
    label: '页面url',
    clearable: true,
    placeholder: '页面url',
    value: toRef(queryParams, 'url'),
    component: 'textarea',
  },
  {
    label: '页面id',
    clearable: true,
    placeholder: '页面id',
    value: toRef(queryParams, 'tpl_page_ids'),
    component: 'input',
    parse: true
  },
  {
    label: '模板id',
    clearable: true,
    placeholder: '模板id',
    value: toRef(queryParams, 'tpl_id'),
    component: 'input',
    parse: true
  },
  {
    label: '页面等级',
    placeholder: '页面等级',
    value: toRef(queryParams, 'level'),
    component: 'select',
    selections: [
      {
        label: 'A',
        value: 'A'
      },
      {
        label: 'B',
        value: 'B'
      }
    ]
  },
  {
    label: '页面状态',
    clearable: true,
    placeholder: '页面状态',
    value: toRef(queryParams, 'page_state'),
    component: 'select',
    selections: pageStateList,
    multiple: true
  },
  {
    label: '模板名称',
    clearable: true,
    placeholder: '模板名称',
    value: toRef(queryParams, 'tpl_name'),
    component: 'input',
    append: true
  },
  {
    placeholder: '模板类别/类型',
    value: cascader,
    component: 'cascader',
    selections: cascaderList,
    props: { expandTrigger: 'hover' },
    handleChange: (value) => {
      if (value) {
        queryParams.type = value[1]
        queryParams.module = value[0]
      } else {
        queryParams.type = ''
        queryParams.module = ''
      } 
    },
    append: true
  },
  {
    label: '更新人',
    clearable: true,
    placeholder: '更新人',
    value: toRef(queryParams, 'user_id_e'),
    component: 'select',
    labelKey: 'real_name',
    valueKey: 'user_id',
    append: true,
    selections: userList
  },
  {
    label: '创建人',
    clearable: true,
    placeholder: '创建人',
    value: toRef(queryParams, 'user_id_a'),
    component: 'select',
    labelKey: 'real_name',
    valueKey: 'user_id',
    append: true,
    selections: userList
  },
  {
    label: '生成人',
    clearable: true,
    placeholder: '生成人',
    value: toRef(queryParams, 'user_id_c'),
    component: 'select',
    labelKey: 'real_name',
    valueKey: 'user_id',
    append: true,
    selections: userList
  },
  {
    label: '页面标题',
    placeholder: '页面标题',
    value: toRef(queryParams, 'title'),
    component: 'input',
    append: true,
  },
  {
    label: '页面描述',
    placeholder: '页面描述',
    value: toRef(queryParams, 'description'),
    component: 'input',
    append: true,
  },
  {
    component: 'time-range-tab',
    field_key: toRef(queryParams, 'time_field'),
    value: timeRange,
    start_time: toRef(queryParams, 'start_time'),
    end_time: toRef(queryParams, 'end_time'),
    append: true,
    field_list: [
      {
        value: 'add_time',
        label: '创建时间'
      },
      {
        value: 'edit_time',
        label: '更新时间'
      },
      {
        value: 'create_time',
        label: '发布时间'
      },
    ],
  },
])
const columns = ref([
  {
    title: '',
    dataKey: '',
    width: 60,
    cellRenderer: (scope: { row: listItem }) => (
      <div class='rowBC'>
        { 
          scope.row.lighthouse.map(( item ) => 
            <div class='with-hand' title='页面质量预警' onClick={() => handleLighthouseView(item.id)}>
              <SvgIcon iconClass={ item.type === 1 ? 'computer' : 'phone' } />
              <br />
              <span 
                class={ item.performance > 89 ? 'dot-round' : item.performance > 49 ? 'dot-square' : 'dot-triangle-up' }
                style={ { '--color': `var(--el-color-${ item.performance > 89 ? 'success' : item.performance > 49 ? 'warning' : 'danger' })` } }
              ></span>
            </div>
          ) 
        }
      </div>
    )
  },
  {
    title: '页面id',
    dataKey: 'tpl_page_id',
    width: 100,
    sortable: true
  },
  {
    title: '模板id',
    dataKey: 'tpl_id',
    width: 100,
    cellRenderer: (scope: { row: listItem }) => (
      <span class='with-hand' onClick={() => handleToTempList(scope.row.tpl_id)}>
        { scope.row.tpl_id }
      </span>
    )
  },
  {
    title: '页面等级',
    dataKey: 'level',
    width: 90,
    cellRenderer: (scope: { row: listItem }) => (
      <span style={ { 
        color: scope.row.level === 'A' ? 'var(--el-color-error)' : 'inherit', 
        fontWeight: scope.row.level === 'A' ? 700 : 400 
      } }>
        { scope.row.level || '--' }
      </span>
    )
  },
  {
    title: '页面URL',
    dataKey: 'url',
    minWidth: 280,
    cellRenderer: (scope: { row: listItem }) => (
      <a href={scope.row.check_url} target='_blank'>
        { scope.row.url || '--' }
      </a>
    )
  },
  {
    title: '页面标题',
    dataKey: 'title',
    minWidth: 180,
    hideColumn: true
  },
  {
    title: '页面描述',
    dataKey: 'description',
    minWidth: 180,
    hideColumn: true
  },
  {
    title: '所属渠道',
    dataKey: 'channel_name',
    width: 120,
    cellRenderer: (scope: { row: listItem }) => (
      <span class='dot' style={ { '--color': scope.row.color } }>
        { scope.row.channel_name }
      </span>
    )
  },
  {
    title: '页面状态',
    dataKey: 'state',
    width: 90,
    cellRenderer: (scope: { row: listItem }) => (
      <span style={ { 
        color: page_state_map[scope.row.page_state]?.color
      } }>
        { page_state_map[scope.row.page_state]?.text }
      </span>
    )
  },
  {
    title: '创建人',
    dataKey: 'user_name_a',
  },
  {
    title: '创建时间',
    dataKey: 'add_time',
    width: 180,
    sortable: true
  },
  {
    title: '更新人',
    dataKey: 'user_name_e',
  },
  {
    title: '更新时间',
    dataKey: 'edit_time',
    width: 180,
    sortable: true
  },
  {
    title: '生成人',
    dataKey: 'user_name_c',
  },
  {
    title: '生成时间',
    dataKey: 'create_time',
    width: 180,
  },
  {
    title: '操作',
    dataKey: '',
    fixed: 'right',
    width: 300,
    cellRenderer: (scope: { row: listItem }) => (
      <>
        <ElButton type='primary' plain onClick={() => handleEdit(scope.row)}>编辑</ElButton>
        <ElButton type='primary' plain disabled={scope.row.state === 'disable' || !scope.row.url} onClick={() => handlePreview(scope.row)}>预览</ElButton>
        <ElButton 
          type='success' 
          plain 
          disabled={scope.row.state === 'disable' || !scope.row.url} 
          onClick={() => handleGeneratePage(scope.row)}
          v-RoleIdsBanPermission={banIds}
        >
          生成页面
        </ElButton>
        <ElDropdown trigger='click' style={{ marginLeft: '10px' }}>
          {
            {
              default: () => <ElButton type='primary' link> <strong>...</strong> </ElButton>,
              dropdown: () => 
              <ElDropdownMenu>
                <ElDropdownItem>
                  <ElButton type="primary" plain disabled={scope.row.module === 'article'} onClick={() => handleCopyPage(scope.row)}>复用页面</ElButton>
                  {
                    scope.row.url && 
                    <ElButton type={scope.row.state === 'disable' ? 'success' : 'danger'} plain onClick={() => handleOperate({ row: scope.row })}>
                      {scope.row.state === 'disable' ? '启用' : '禁用'}
                    </ElButton>
                  }
                  {
                    (scope.row.url && scope.row.state !== 'disable')
                    ? <>
                        <ElButton type="primary" plain onClick={() => handleOnlineUrl(scope.row.online_url)}>线上地址</ElButton>
                        <ElButton type="primary" plain onClick={() => handleHistory(scope.row)}>历史版本</ElButton>
                      </>
                    : ''
                  }
                </ElDropdownItem>
              </ElDropdownMenu>
            }
          }
        </ElDropdown>
      </>
    )
  }
])
const tableRef = ref()
const tableData = ref<listItem[]>([])
const tableSelections = ref<listItem[]>([])
const disableOperate = computed(() => tableSelections.value.length > 0 ? false : true)
const disableTooltip = computed(() => channel_item_all.value.channel_id ? '' : '请先选择渠道')
const tpl_page_ids = computed(() => tableSelections.value.map(({ tpl_page_id }) => tpl_page_id).join(','))
const operateList = ref([
  {
    title: '创建页面',
    button: true,
    append: true,
    tooltip: disableTooltip,
    disabled: computed<boolean>(() => channel_item_all.value.channel_id ? false : true),
    method: () => {
      basicStore.delCachedView('PageManagementOperate')
      router.push({
        name: 'PageManagementOperate',
        query: {
          type: 'add',
          tpl_id: tpl_id || '',
          module: module_name || '',
          tpl_type: tpl_type || '',
        }
      })
    },
  },
  {
    title: '导出EXCEL',
    icon: 'export',
    method: () => {
      const { page, pageSize, total } = tableRef?.value?.getPagination()
      if ( !tpl_page_ids.value && total > 5000) {
        return ElMessageBox.confirm(
          '当前导出数据超过5000条,超出最大范围限制,请控制到5000条以内后再进行导出',
          '提示',
          {
            showCancelButton: false,
            callback: () => {}
          }
        )
      }
      const params = {
        S: {
          ...queryParams,
          url: queryParams.url.trim().replace(/\s+/g, ','),
          tpl_page_ids: tpl_page_ids.value || queryParams.tpl_page_ids,
          channel_id: channel_item_all.value.channel_id || '',
          page_state: String(queryParams.page_state)
        },
        page: page || 1,
        page_size: pageSize || 10,
        site_id: site_id_all.value
      }
      const exportCSV = () => exportDownload(params)

      if (tpl_page_ids.value) {
        exportCSV()
      } else {
        ElMessageBox.confirm(
          '导出当前查询条件下所有页面',
          '提示',
          {
            confirmButtonText: '确认',
            cancelButtonText: '取消',
            callback: (action: string) => {
              if (action === 'confirm') {
                exportCSV()
              }
            }
          }
        )
      }
    },
  },
  {
    title: '导出HTML',
    icon: 'export',
    method: () => {
      const token = getDownloadLocalToken()
      const ids = tpl_page_ids.value
      const site_id = site_id_all.value
      location.href = `${getHost()}/api/v1/templates/pages/export_html?&ws-login-token=${token}&tpl_page_ids=${ids}&site_id=${site_id}`
    },
    disabled: disableOperate
  },
  ...isBanned() ? [] : [
    {
      title: '生成选中项',
      icon: 'generate',
      method: () => {
        const list = tableSelections.value
        let buy_count = 0
        for ( const item of list ) {
          item.type === 'buy' && buy_count++
        }
        if ( buy_count && buy_count !== list.length ) {
          return ElMessageBox.confirm(`检测到当前批量发布页面中，包含${buy_count}个购买页，购买页请单独发布!`, '提示', {
            type: 'warning',
            callback: () => {}
          })
        }
        usePageGenerate({
          tpl_page_ids: `${tpl_page_ids.value}`,
          site_id: site_id_all.value,
          ...buy_count > 0 ? { store_buy_type: 1 } : {}
        }, refresh)
      },
      disabled: disableOperate
    }
  ],
  {
    title: '禁用选中项',
    method: () => {
      handleOperate({ page_ids: tpl_page_ids.value, state: 'able' })
    },
    disabled: disableOperate
  },
  {
    title: '批量启用',
    method: () => {
      handleOperate({ page_ids: tpl_page_ids.value, state: 'disable' })
    },
    disabled: disableOperate
  },
  {
    title: '刷新CDN',
    tooltip: disableTooltip,
    method: () => {
      ElMessageBox.confirm(
        '请确认是否对已选中页面进行cdn刷新操作',
        '提示',
        {
          confirmButtonText: '确认',
          cancelButtonText: '取消',
          type: 'warning',
          callback: (action: string) => {
            if (action === 'confirm') {
              useTryCatch( async () => {
                setLoading(true)
                const params = {
                  site_id: site_id_all.value,
                  channel_id: channel_item_all.value.channel_id as number,
                  tpl_page_id: tpl_page_ids.value
                }
                const res = await clearCdn(params)
                if (res.code === 200) {
                  refresh()
                  ElMessage.success(res.msg)
                } else {
                  ElMessage.error(res.msg)
                }
                setLoading(false)
              }, () => setLoading(false) )
            }
          }
        }
      )
    },
    disabled: computed(() => !channel_item_all.value.channel_id || tableSelections.value.length === 0)
  },
  {
    title: '发起翻译',
    tooltip: disableTooltip,
    method: () => {
      if (tableSelections.value.length > 100) {
        return ElMessage.warning('单次发起翻译任务, 选择页面不超过100条, 请修改后重新发起')
      }
      loadStartTaskModal.value = true
    },
    disabled: computed(() => !channel_item_all.value.channel_id || tableSelections.value.length === 0)
  }
])
/* 弹窗交互相关 */
const loadAddPageModal = ref(false)
const loadStartTaskModal = ref(false)
/* 弹窗交互相关 */

const reset = () => {
  timeRange.value = ''
  cascader.value = []
  Object.entries(queryParams_raw).forEach(([ key, value ]) => {
    queryParams[key] = value
  })
}
const refresh = () => {
  const { page, pageSize } = tableRef?.value?.getPagination()
  getList( { page, pageSize } )
}
const handleSearch = () => {
  channelId.value = ''
  queryParams.video_type = ''
  tableRef.value.setPage(1)
  refresh()
}
const getList = ( { page, pageSize }, prop?: string, order?: 'asc'|'desc' ) => {
  if (tableRef?.value) {
    const sortObj = tableRef?.value?.getSort()
    if (sortObj.order && !order) {
      prop = sortObj.prop
      order = sortObj.order
    }
  }
  
  useTryCatch( async () => {
    const params = {
      S: {
        ...queryParams,
        channel_id: channelId.value || channel_item_all.value.channel_id as number,
        page_state: String(queryParams.page_state)
      },
      page,
      page_size: pageSize,
      site_id: site_id_all.value,
      ...order ? {
        sort_key: prop,
        sort_type: order
      } : {}
    }
    setLoading(true)
    const res = await getPagesList(params)
    if (res.code === 200) {
      tableRef.value?.setTotal(res.data.total)
      tableData.value = res.data.items
    } else {
      ElMessage(res.msg)
    }
    setLoading(false)
  }, () => setLoading(false))
}
const handleSelectionChange = (selections: listItem[]) => {
  tableSelections.value = selections
}
/* table methods */
const handleEdit = (row: listItem) => {
  basicStore.delCachedView('PageManagementOperate')
  router.push({
    name: 'PageManagementOperate',
    query: {
      type: 'edit',
      page_id: row.tpl_page_id,
      tpl_id: row.tpl_id,
      page_url: row.page_url,
      ...props.isTempList ? { from: 'tempList' } : {}
    }
  })
}
const handlePreview = (row: listItem) => {
  window.open(`${row.page_url}?site_id=${site_id_all.value}`, '_blank')
  handleGlobalPageCheck(row.tpl_id, row.tpl_page_id, site_id_all.value)
}
const handleGeneratePage = (row: listItem) => {
  usePageGenerate({
    tpl_page_ids: `${row.tpl_page_id}`,
    site_id: site_id_all.value,
    ...row.type === 'buy' ? { store_buy_type : 1 } : {}
  }, refresh)
}
const handleCopyPage = (row: listItem) => {
  basicStore.delCachedView('PageManagementOperate')
  router.push({
    name: 'PageManagementOperate',
    query: {
      type: 'copy',
      page_id: row.tpl_page_id
    }
  })
}
const handleOperate = ({ row, state = '', page_ids = '' }: { row?: listItem; state?: string; page_ids?: string; }) => {
  state = row?.state || state
  page_ids = page_ids || `${row?.tpl_page_id}`
  const page_state = row?.state || state
  const isDisable = page_state === 'disable' ? true : false
  ElMessageBox.confirm(
    isDisable 
    ? `请确认是否启用${row ? '该页面' : '选中项'}` 
    : `<p>此操作会将${row ? '该' : '已选中'}页面文件, 实时从线上下架, 请确认清楚, 当前是否要下架线上页面</p>
      <div style="color: var(--el-color-danger)">Tips: 如页面等级为A级, 需要审核通过后, 才会自动发布（下架任务）</div>
    `,
    '提示',
    {
      dangerouslyUseHTMLString: true,
      confirmButtonText: isDisable ? '确认' : '下架页面',
      cancelButtonText: '取消',
      confirmButtonClass: isDisable ? '' : 'el-button--danger',
      callback: (action: string) => {
        if (action === 'confirm') {
          useTryCatch( async () => {
              const res = await changeStatus({ state: isDisable ? 'able' : 'disable', page_ids, site_id: site_id_all.value })
              if (res.code === 200) {
                refresh()
                ElMessage.success('操作成功')
              } else {
                ElMessage.error(res.msg)
              }
          } )
        }
      }
    }
  )
}
const handleOnlineUrl = (online_url: string) => {
  window.open(online_url)
}
const handleHistory = (row: listItem) => {
  router.push({
    name: 'PageHistory',
    query: {
      tpl_id: row.tpl_id,
      page_id: row.tpl_page_id
    }
  })
}
const handleToTempList = (tpl_id: number) => {
  basicStore.delCachedView('TemplateManage')
  router.push(
    {
      name: 'TemplateManage',
      query: {
        tpl_id
      }
    }
  )
}
const handleSortChange = ({ prop, order }: { prop: string; order: 'ascending'|'descending'|null }) => {
  const { page, pageSize } = tableRef?.value?.getPagination()
  getList( { page, pageSize }, prop, order ? order.replace('ending', '') as 'asc'|'desc' : undefined )
}
const handleLighthouseView = (id: number) => {
  window.open(`${getHost()}/api/v1/lighthouse/page?id=${id}`, '_blank')
}
/* table methods */
onActivated(() => {
  if (basicStore.refresh) {
    basicStore.setRefresh(false)
    refresh()
  }
})

Listener(route, () => {
  reset()
  handleSearch()
})
</script>