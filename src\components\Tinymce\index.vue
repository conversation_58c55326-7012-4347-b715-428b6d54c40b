<template>
  <div class="tinymce-editor-container" style="height: 100%">
    <Editor
      :id="tinymceId"
      :init="init"
      :model-value="value"
      @update:modelValue="handleChange"
    />
  </div>

  <el-dialog
    v-model="resourceDialog"
    :title="`选择您需要插入的${fileType === 1 ? '图片' : '视频'}`"
    width="90%"
    top="5vh"
    class="resource-dialog"
    :append-to-body="true"
  >
    <ResourceCenter
      v-if="loadResourceCenter"
      :file-type="fileType"
      :show-bottom="true"
      @confirm="handleConfirm"
      @cancel="() => resourceDialog = false"
    />
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, defineAsyncComponent, watch, getCurrentInstance, nextTick } from 'vue'
import { useRoute } from 'vue-router'
import 'tinymce/tinymce'
import Editor from '@tinymce/tinymce-vue'
import 'tinymce/themes/silver'
import 'tinymce/models/dom/model'
import 'tinymce/icons/default/icons'
import 'tinymce/plugins/image'
import 'tinymce/plugins/wordcount'
import 'tinymce/plugins/code'
import 'tinymce/plugins/link'
import 'tinymce/plugins/table'
import 'tinymce/plugins/searchreplace'
import 'tinymce/plugins/fullscreen'
import 'tinymce/plugins/lists'
import 'tinymce/plugins/advlist'
import { useConfigStore } from '@/store/config'
import Listener from '@/utils/site-channel-listeners'
import type { ComponentInternalInstance } from 'vue'

const ResourceCenter = defineAsyncComponent(() => import('@/components/ResourceCenter/index.vue'))

defineOptions({ name: 'TinymceEditor' })

const { appContext } = getCurrentInstance() as ComponentInternalInstance
const { $Ace } = appContext.config.globalProperties
const props = withDefaults(
  defineProps<{
    height?: number;
    modelValue: string;
    resize?: boolean;
  }>(), {
    height: () => 600,
  }
)
const emit = defineEmits(['update:modelValue', 'change'])

// 兼容异步状态下的赋值
watch(() => props.modelValue, (val) => {
  value.value = val
})
// 运营编辑操作特殊bug修复
const bisSizeReg = /\s*bis_size="\{.*?\}"/ig

const route = useRoute()
const { tinymceSkin } = useConfigStore()

const value = ref(props.modelValue)
const resourceDialog = ref(false)
const loadResourceCenter = ref(false)
const fileType = ref(1)

const tinymceId = `vue-tinymce-${`${+new Date()}${( Math.random() * 1000).toFixed(0)}`}`
let mediaCallback = (...rest: any[]) => {}
const init = {
  resize: props.resize,
  base_url: '/tinymce',
  height: props.height,
  skin_url: `/tinymce/skins/ui/${tinymceSkin}`,
  content_css: `/tinymce/skins/ui/${tinymceSkin}/content.min.css`,
  language: 'zh-Hans',
  language_url: "/tinymce/language/zh-Hans.js",
  toolbar: `ace undo redo blocks fontsize casechange lineheight image media bold italic link forecolor backcolor | alignleft aligncenter alignright alignjustify | bullist numlist outdent indent table | searchreplace fullscreen`,
  font_size_formats: '12px 14px 16px 18px 20px 22px 24px 28px 30px 32px 36px',
  branding: false, // 去水印
  elementpath: true, // 编辑器底部的状态栏
  statusbar: true, // 编辑器底部的状态栏
  paste_data_images: false, // 允许粘贴图像
  menubar: false, // 隐藏最上方menu
  placeholder: '请输入您的编辑内容......',
  plugins: 'image wordcount code link table searchreplace fullscreen lists advlist media powerpaste casechange', // 插件
  link_rel_list: [
    { title: 'follow', value: '' },
    { title: 'nofollow', value: 'nofollow' },
  ],
  allow_unsafe_link_target: true,
  content_style: 'body { font-family:Helvetica,Arial,sans-serif; font-size:16px }',
  powerpaste_word_import: 'clean',
  entity_encoding: 'raw', // 特殊字符多语言不转义
  valid_elements : '*[*]',
  file_picker_callback: (callback: (arg_0: string, arg_1: { alt: string }) => void, value: string, meta: { filetype: string, fieldname: string }) => {
    const selectType = (meta.filetype === 'image' || meta.fieldname === 'poster') ? 1 : 2
    if (meta.filetype === 'image' || meta.filetype === 'media') {
      if (fileType.value !== selectType) {
        fileType.value = selectType
        loadResourceCenter.value = false
      }
      nextTick( () => {
        resourceDialog.value = true
        loadResourceCenter.value = true
      } )
      mediaCallback = callback
    }
  },
  init_instance_callback: (editor: any) => {
    emit('change', {
      count: getWordCount(),
      value: getValue()
    })
  },
  setup: (editor: any) => {
    editor.ui.registry.addButton('ace', {
      icon: 'sourcecode',
      tooltip: '编辑器源码模式',
      onAction: (_: any) => {
        $Ace({
          code: getValue(),
          showModal: true,
          save: (val: string) => {
            value.value = val
            emit('update:modelValue', val)
          }
        })
      }
    })
  }
}

const getWordCount = (): number => {
    const tinymce = window.tinymce.get(tinymceId)
    if (tinymce) {
      const wordcount = tinymce.plugins.wordcount 
      return wordcount.body.getWordCount()
    }
    return 0
}
const getValue = (): string => {
  const tinymce = window.tinymce.get(tinymceId)
  if (tinymce) {
    return tinymce.getContent().replace(bisSizeReg, '')
  }
  return ''
}

const handleConfirm = ( { url, name } ) => {
  mediaCallback(url, { alt: name })
  resourceDialog.value = false
}

const handleChange = (val: string) => {
  emit('update:modelValue', val)
  emit('change', {
    count: getWordCount(),
    value: val
  })
}

defineExpose({
  getWordCount,
  getValue
})

Listener(route, () => {
  loadResourceCenter.value = false
})
</script>