<script lang="tsx">
import { defineComponent, ref, onMounted, nextTick } from 'vue'
import type { PropType } from 'vue'
import usePagination from '@/hooks/use-pagination'
import { useConfigStore } from '@/store/config'
import { handleCachedTableHead, handleTableHeadModal, getTableHeight } from './tableHeads'
import PublicOperate from '@/components/PublicOperate/index.vue'
import type { operateItem } from '@/components/PublicOperate/type'

type column = {
  key?: string;
  title: string;
  dataKey: string;
  minWidth?: string|number;
  width?: string|number;
  fixed?: boolean|string;
  align?: string;
  sortable?: boolean;
  hideColumn?: boolean;
  emptyText?: string;
  showOverflowTooltip?: boolean;
  hideOverflowTooltip?: boolean;
  headerRenderer?: (scope: any) => any;
  cellRenderer?: (scope: any) => any;
}

export default defineComponent({
  name: 'CustomTable',
  props: {
    data: {
      type: Array as PropType<any[]>,
      required: true,
      default: () => []
    },
    columns: {
      type: Array as PropType<column[]>,
      required: true,
      default: () => []
    },
    singleSelectable: {
      type: Boolean,
      default: () => false
    },
    selectable: {
      type: Boolean,
      default: () => false
    },
    selectMethod: {
      type: Function as PropType<(row: any, index: number) => boolean>,
      default: () => true
    },
    indexMethod: {
      type: Function as PropType<(index: number) => number>
    },
    border: {
      type: Boolean,
      default: () => false
    },
    pageObj: {
      type: Object as PropType<{
        page: number;
        pageSize: number;
        total: number;
      }>,
    },
    tableMethod: {
      type: Function
    },
    customizeHead: {
      type: Boolean
    },
    hidePagination: {
      type: Boolean,
      default: () => false
    },
    pagePosition: {
      type: String,
      default: () => 'right'
    },
    operateList: {
      type: Array as PropType<operateItem[]>,
      default: () => []
    },
    emptyText: {
      type: String,
      default: () => '/'
    },
    rowClassName: {
      type: (Function || String) as PropType<string|Function>,
      default: () => ''
    },
    extraHeight: {
      type: Number,
      default: 0
    },
    height: {
      type: String,
    },
    maxHeight: {
      type: String || Number,
      default: () => 'auto'
    },
    skipInit: {
      type: Boolean,
      default: () => false
    },
    rowKey: {
      type: String,
    },
    containerClass: {
      type: String,
    },
    defaultExpandAll: {
      type: Boolean,
      default: () => false
    }
  },
  emits: ['select', 'selectionChange', 'currentChange', 'sizeChange', 'pageChange', 'sortChange', 'expandChange', 'rowClick'],
  setup(props, { emit, expose }) {
    const configStore = useConfigStore()
    const { page, pageSize, total, setPage, setPageSize, setTotal } = usePagination()
    const sortObj: {
      prop: string;
      order: 'asc'|'desc'|null
    } = {
      prop: '',
      order: null
    }

    expose({
      page: page.value,
      pageSize: pageSize.value,
      total: total.value,
      getTotal: () => total.value,
      getPagination: () => ({
        page: page.value,
        pageSize: pageSize.value,
        total: total.value
      }),
      setTotal,
      setPage,
      setPageSize,
      getSort: () => sortObj,
    })

    const getList = () => {
      props.tableMethod && props.tableMethod({
        page: page.value,
        pageSize: pageSize.value
      }, sortObj.prop, sortObj.order ? sortObj.order: undefined)
    }
    !props.skipInit && getList()
    if(props.customizeHead) {
      handleCachedTableHead(props.columns)
    }

    const height = ref('auto')
    const paginationHeight = props.hidePagination ? 0 : 52
    const extraHeight = ( props.operateList.length > 0 ? (44 + paginationHeight) : paginationHeight) + props.extraHeight
    const handleHeight = () => {
      nextTick(() => {
        setTimeout(() => {
          height.value = getTableHeight( extraHeight, props.containerClass || 'sticky-table' )
        }, 350)
      })
    }
    onMounted(() => {
      handleHeight()
    })

    if (props.customizeHead) {
      props.operateList.push({
        title: '',
        icon: 'setting',
        append: true,
        tooltip: '自定义表头',
        method: () => {
          handleTableHeadModal(props.columns)
        }
      })
    }

    return () => (
      <>
        {
          props.operateList.length > 0 && <PublicOperate operateList={props.operateList} />
        }
        <div class='custom-table-container'>
          <el-table
            ref="customTableRef"
            data={props.data}
            border={true}
            default-expand-all={props.defaultExpandAll}
            rowClassName={props.rowClassName}
            rowKey={props.rowKey || ''}
            highlightCurrentRow={props.singleSelectable}
            onSelect={(selection: any[], row: any) => {
              emit('select', selection, row)
            }}
            onSelectionChange={(selection: any[]) => {
              emit('selectionChange', selection)
            }}
            onCurrentChange={(row: any, oldRow: any) => {
              emit('currentChange', row)
            }}
            height={ props.height || height.value}
            max-height={ props.maxHeight }
            onSortChange={(column: { prop: string; order: 'ascending'|'descending'|null }) => {
              sortObj.prop = column.prop
              sortObj.order = column.order?.replace('ending', '') as 'asc'|'desc'
              emit('sortChange', column)
            }}
            onExpandChange={(expandedRows: any[], expanded: boolean) => {
              emit('expandChange', expandedRows, expanded)
            }}
            onRowClick={(row: any, column: any, event: any) => {
              emit('rowClick', row, column, event)
            }}
          >
            {
              props.singleSelectable &&
              <el-table-column width={50}>
                {
                  {
                    header: (scope: { row: any, $index: number }) => '',
                    default: (scope: { row: any, $index: number }) =>
                    <label class="el-radio el-radio--default">
                      <span class="el-radio__input">
                        <span class="el-radio__inner"></span>
                      </span>
                      <span class="el-radio__label"></span>
                    </label>
                  }
                }
              </el-table-column>
            }
            {
              (props.selectable && !props.singleSelectable) && <el-table-column type='selection' selectable={props.selectMethod} width={55} />
            }
            {
              props.indexMethod && <el-table-column type='index' label='序号' width={100} index={props.indexMethod} />
            }
            {
              props.columns.map((column: column) => {
                return (
                  !column.hideColumn &&
                  <el-table-column
                    label={column.title}
                    prop={column.dataKey}
                    width={column.width || 'auto'}
                    minWidth={column.minWidth || 'auto'}
                    fixed={column.fixed}
                    align={column.align}
                    showOverflowTooltip={column.hideOverflowTooltip ? false : true}
                    sortable={column.sortable ? 'custom': false}
                    resizable={true}
                  >
                    {
                      {
                        header: (scope: any) => column.headerRenderer ? column.headerRenderer(scope) : column.title,
                        default: (scope: any) => column.cellRenderer ? column.cellRenderer(scope) : (scope.row[column.dataKey] || column.emptyText || props.emptyText)
                      }
                    }
                  </el-table-column>
                )
              })
            }
          </el-table>
        </div>
        {
          !props.hidePagination &&
          <el-pagination
            class={props.pagePosition}
            small={configStore.size === 'small' ? true : false}
            background
            total={props.pageObj ? props.pageObj.total : total.value}
            currentPage={props.pageObj ? props.pageObj.page : page.value}
            pageSize={props.pageObj ? props.pageObj.pageSize : pageSize.value}
            pageSizes={[5,10,15,20,30,50,100,200,500,1000]}
            layout='total, sizes, prev, pager, next'
            onSizeChange={(size: number) => {
              window.localStorage.setItem('page_size', `${size}`)
              setPageSize(size)
              emit('sizeChange', size)
              getList()
            }}
            onCurrentChange={(page: number) => {
              setPage(page)
              emit('pageChange', page)
              getList()
            }}
          />
        }
      </>
    )
  }
})

</script>