<template>
  <div class="scroll-y">
    <div style="overflow: hidden;" :style="{ minHeight: `calc(100% - ${fixBottomHeight}px)` }">
      <el-form
        ref="formRef"
        label-suffix=":"
        label-width="150px"
        :model="submitData"
        :rules="formRules"
      >
        <el-row :gutter="20">
          <el-col :span="12">
            <el-card shadow="never" style="height: 100%;">
              <template #header>
                <strong style="font-size: 1.25rem; padding-right: 24px;">CMS系统设置</strong>
                <el-checkbox 
                  :disabled="submitData.is_check_task === 0"
                  v-model="submitData.is_check_cms"
                  :true-label="1"
                  :false-label="0"
                />
              </template>
              <template #default v-if="submitData.is_check_cms === 1">
                <el-form-item label="站点名称" prop="name">
                  <el-input v-model="submitData.name" placeholder="请输入站点名称" maxlength="100" show-word-limit />
                </el-form-item>
                <el-form-item label="主机域名" prop="host">
                  <el-input v-model="submitData.host" placeholder="请输入主机域名" maxlength="150" show-word-limit />
                </el-form-item>
                <el-form-item label="所属渠道" prop="channel_code">
                  <el-select v-model="submitData.channel_code" filterable placeholder="请选择渠道" @focus="handleGetChannelList">
                    <el-option 
                      v-for="d in channelList"
                      :key="d.id"
                      :label="d.channel_name"
                      :value="d.channel_code"
                    />
                  </el-select>
                </el-form-item>
                <el-form-item label="站点语言" prop="language">
                  <el-select v-model="submitData.language" filterable clearable placeholder="请选择语言" @focus="handleGetLangList">
                    <el-option 
                      v-for="d in langList"
                      :key="d.lang_id"
                      :label="d.name"
                      :value="d.lang_code"
                    />
                  </el-select>
                </el-form-item>
                <el-form-item label="网站状态" prop="state">
                  <el-switch 
                    v-model="submitData.state" 
                    active-value="able"
                    inactive-value="disable" 
                    active-text="启用" 
                    inactive-text="禁用"
                  />
                </el-form-item>
                <el-form-item label="静态文件路径" prop="html_path">
                  <el-input v-model="submitData.html_path" placeholder="请输入静态文件路径" maxlength="100" show-word-limit />
                </el-form-item>
                <el-form-item label="图片主机域名" prop="host_image">
                  <el-input v-model="submitData.host_image" placeholder="请输入图片主机域名" maxlength="100" show-word-limit />
                </el-form-item>
                <el-form-item label="图片文件存放路径" prop="image_path">
                  <el-input v-model="submitData.image_path" placeholder="请输入图片文件存放路径" maxlength="100" show-word-limit />
                </el-form-item>
                <el-form-item label="视频主机域名" prop="host_video">
                  <el-input v-model="submitData.host_video" placeholder="请输入图片主机域名" maxlength="100" show-word-limit />
                </el-form-item>
                <el-form-item label="视频文件存放路径" prop="video_path">
                  <el-input v-model="submitData.video_path" placeholder="请输入图片文件存放路径" maxlength="100" show-word-limit />
                </el-form-item>
                <el-form-item label="js主机路径" prop="host_js">
                  <el-input v-model="submitData.host_js" placeholder="请输入js主机路径" maxlength="100" show-word-limit />
                </el-form-item>
                <el-form-item label="css主机路径" prop="host_css">
                  <el-input v-model="submitData.host_css" placeholder="请输入css主机路径" maxlength="100" show-word-limit />
                </el-form-item>
                <el-form-item label="站点文件限制" prop="site_file_limits">
                  <div style="display: flex;width: 100%;">
                    <div style="width: 84px; margin-right: 16px;">
                      <el-switch 
                        v-model="limitType" 
                        :active-value="1"
                        :inactive-value="0" 
                        inline-prompt
                        active-text="自定义限制"
                        inactive-text="不限制" 
                        @change="handleLimitChange"
                      />
                    </div>
                    <div v-show="limitType === 1" style="flex-grow: 1;">
                      <el-input v-model="submitData.site_file_limits" placeholder="请输入可上传文件后缀, 多个用英文逗号隔开" />
                    </div>
                  </div>
                </el-form-item>
              </template>
            </el-card>
          </el-col>
          <el-col :span="12">
            <el-card shadow="never" style="height: 100%;">
              <template #header>
                <strong style="font-size: 1.25rem; padding-right: 24px;">发布系统设置</strong>
                <el-checkbox 
                  :disabled="submitData.is_check_cms === 0"
                  v-model="submitData.is_check_task"
                  :true-label="1"
                  :false-label="0"
                  @change="handleCheckTaskChange"
                />
              </template>
              <template #default v-if="submitData.is_check_task === 1">
                <el-form-item label="站点名称" prop="task_site_name">
                  <el-input v-model="submitData.task_site_name" placeholder="请输入站点名称" maxlength="100" show-word-limit />
                </el-form-item>
                <el-form-item label="站点域名" prop="task_site_host">
                  <el-input v-model="submitData.task_site_host" placeholder="请输入站点域名" maxlength="100" show-word-limit />
                </el-form-item>
                <el-form-item label="站点路径" prop="task_site_path">
                  <el-input v-model="submitData.task_site_path" placeholder="请输入站点路径" maxlength="100" show-word-limit />
                </el-form-item>
                <el-form-item v-if="type !== 'edit'" label="创建审核流程模板" prop="is_create_audit">
                  <el-switch 
                    v-model="submitData.is_create_audit" 
                    :active-value="1"
                    :inactive-value="0" 
                    active-text="是" 
                    inactive-text="否"
                  />
                </el-form-item>
              </template>
            </el-card>
          </el-col>
        </el-row>
      </el-form>
    </div>
    <div class="fixed-bottom text-center">
      <el-button plain @click="handleCancel"> 取消 </el-button>
      <el-button type="primary" :loading="loading" @click="handleSave(formRef)"> 保存 </el-button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive } from 'vue'
import { ElMessage } from 'element-plus'
import { useRoute, useRouter } from 'vue-router'
import { useTagsViewStore } from '@/store'
import { useConfigStore } from '@/store/config'
import { useBasicStore } from '@/store/basic'
import useTryCatch from '@/hooks/use-try-catch'
import useLoading from '@/hooks/use-loading'
import { addCmsSite, editCmsSite, siteDetail, getChannelList, getLangList } from '@/api/site-config'
import type { siteData, channelListItem, langListItem } from '@/api/site-config'
import type { FormInstance } from 'element-plus'
import { formRules } from './rules'

defineOptions( { name: 'SiteOperate' } )

const route = useRoute()
const router = useRouter()
const basicStore = useBasicStore()
const { fixBottomHeight } = useConfigStore()
const { delVisitedView, changeViewTitle } = useTagsViewStore()
const { loading, setLoading } = useLoading()

const { type, id, cms_id, task_id } = route.query as unknown as { type: string; id: number; cms_id: number; task_id: number; }

const submitData_raw = {
  name: '',
  html_path: '',
  image_path: '',
  video_path: '',
  host: '',
  host_js: '',
  host_css: '',
  host_image: '',
  host_video: '',
  brand: '',
  main_currency: '',
  language: '',
  state: 'able',
  channel_code: '',
  secondary_dir: '',
  site_file_limits: '',
  leads_mail: '',
  syn_mail: '',
  examiner: '',
  is_check_cms: 1,
  is_check_task: 1,
  task_site_name: '',
  task_site_host: '',
  task_site_path: '',
  task_site_state: 'able',
  is_create_audit: 1,
}
const formRef = ref<FormInstance>()
const submitData = reactive<siteData>({
  ...submitData_raw
})

const limitType = ref(0)
const channelList = ref<channelListItem[]>([])
const langList = ref<langListItem[]>([])

const handleGetChannelList = () => {
  if (channelList.value.length === 0) {
    useTryCatch(async () => {
      const params = {
        page: 1,
        page_size: 100
      }
      const res = await getChannelList(params)
      if (res.code === 200) {
        const list = res.data.items as channelListItem[]
        channelList.value = list.reverse()
      }
    })
  }
}
const handleGetLangList = () => {
  if (langList.value.length === 0) {
    useTryCatch(async () => {
      const res = await getLangList()
      if (res.code === 200) {
        const list = res.data
        langList.value = list
      }
    })
  }
}
const handleLimitChange = () => {
  submitData.site_file_limits = ''
  if (limitType.value === 0) {
    submitData.site_file_limits = 'all'
  }
}
const handleCheckTaskChange = (val: number) => {
  submitData.is_create_audit = val
}

const handleCancel = () => {
  delVisitedView(route)
  router.push({ name: 'SiteConfiguration' })
}
const handleSave = async (formEl: FormInstance | undefined) => {
  if (!formEl) return
  await formEl.validate(( valid ) => {
    if (valid) {
      const isAdd = type === 'add'
      const api = isAdd ? addCmsSite : editCmsSite
      useTryCatch( async () => {
        setLoading(true)
        const params = {
          ...submitData
        }
        const res = await api(params, id)
        if (res.code === 200) {
          basicStore.setRefresh(true)
          ElMessage.success(res.msg)
          if (isAdd) {
            handleCancel()
          }
        } else {
          ElMessage.error(res.msg)
        }
        setLoading(false)
      }, () => setLoading(false) )
    }
  })
}

const getDetail = () => {
  useTryCatch( async () => {
    setLoading(true)
    const res = await siteDetail(id)
    if (res.code === 200) {
      const data = res.data
      Object.keys(submitData).forEach((key) => {
        if (data[key] !== undefined) {
          submitData[key] = data[key]
        }
      })
      submitData.cms_site_id = data.cms_site_id
      submitData.task_site_id = data.task_site_id
      if (submitData.site_file_limits) {
        limitType.value = 1
      }
    } else {
      ElMessage.error(res.msg)
    }
    setLoading(false)
  }, () => setLoading(false) )
}

changeViewTitle(route, type === 'add' ? '添加站点' : '编辑站点')
if (id) {
  getDetail()
  handleGetChannelList()
  handleGetLangList()
}
</script>