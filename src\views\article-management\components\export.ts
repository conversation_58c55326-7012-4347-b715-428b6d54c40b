import { h, ref } from 'vue'
import { ElMessageBox, ElCheckbox } from 'element-plus'
import { getHost } from '@/hooks/use-env'
import { stringify } from '@/utils/common'
import type { queryParams } from '@/api/article-management'

const exportList = [
  {
    key: 'art_id',
    label: '文章ID',
    selected: true
  },
  {
    key: 'page_title',
    label: '页面标题',
    selected: true
  },
  {
    key: 'page_keyword',
    label: '关联页面keyword',
    selected: true
  },
  {
    key: 'page_desc',
    label: '关联页面description',
    selected: true
  },
  {
    key: 'title',
    label: '文章标题',
    selected: true
  },
  {
    key: 'author_id',
    label: '作者id',
    selected: true
  },
  {
    key: 'cat_id',
    label: '文章分类ID',
    selected: true
  },
  {
    key: 'cat_id_name',
    label: '主分类名称',
    selected: true
  },
  {
    key: 'cat_id_v',
    label: '副分类',
    selected: true
  },
  {
    key: 'cat_id_v_name',
    label: '副分类名称',
    selected: true
  },
  {
    key: 'rel_article_ids',
    label: '相关文章',
    selected: true
  },
  {
    key: 'rel_pro_ids',
    label: '相关产品',
    selected: true
  },
  {
    key: 'tpl_id',
    label: '关联页面所属模板ID',
    selected: true
  },
  {
    key: 'page_id',
    label: '页面ID',
    selected: true
  },
  {
    key: 'page_url',
    label: '关联页面URL',
    selected: true
  },
  {
    key: 'online_url',
    label: '完整页面URL',
    selected: true
  },
  {
    key: 'content',
    label: '正文内容',
    selected: true
  },
  {
    key: 'side_block',
    label: '侧边栏ID',
    selected: true
  },
  {
    key: 'user_name_e',
    label: '更新人',
    selected: true
  },
  {
    key: 'edit_time',
    label: '更新时间',
    selected: true
  },
  {
    key: 'page_state_name',
    label: '页面状态',
    selected: true
  },
  {
    key: 'user_name_a',
    label: '创建人',
    selected: true
  },
  {
    key: 'add_time',
    label: '创建时间',
    selected: true
  },
  {
    key: 'content_word_num',
    label: '单词个数',
    selected: true
  },
]
const selectAll = ref(true)

export const exportDownload = (type: string, ids: string, queryParams: queryParams) => {
  const checkList = ref(exportList)
  ElMessageBox({
    title: '请确认需要导出的字段',
    showConfirmButton: true,
    showCancelButton: true,
    confirmButtonText: '确认',
    cancelButtonText: '取消',
    customStyle: {
      '--el-messagebox-width': '600px'
    },
    callback: (action: string) => {
      if (action === 'confirm') {
        const fileds = checkList.value.filter(( { selected } ) => selected).map(( { key } ) => key).join(',')
        const params = {
          export_type: type,
          export_fields: fileds,
          export_art_ids: ids,
          ...queryParams,
        }
        const url = `${getHost()}/api/v1/articles?${stringify(params)}`
        location.href = url
      }
    },
    message: () => {
      return h('div', checkList.value.map(( item ) => h(ElCheckbox, {
        key: item.key,
        modelValue: item.selected,
        label: item.label,
        onChange: (value: any) => {
          item.selected = value
        }
      })).concat([
        h('div', { style: { padding: '24px 0 5px', textAlign: 'center' } }, h(ElCheckbox, {
          label: '全选',
          modelValue: selectAll.value,
          onChange: (value: any) => {
            selectAll.value = value
            checkList.value.forEach(( item ) => item.selected = value)
          }
        }))
      ]))
    }
  })
}