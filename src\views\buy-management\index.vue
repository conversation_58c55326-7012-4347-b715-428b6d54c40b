<template>
  <div class="scroll-y with-flex">
    <QueryForm
      :form-list="formList"
      :loading="loading"
      @search="handleSearch" 
      @reset="reset"
    />
    <div v-loading="loading" class="sticky-table">
      <CustomTable 
        ref="tableRef"
        :columns="columns"
        :data="tableData"
        :table-method="getList"
      />
    </div>
  </div>
</template>

<script setup lang="tsx">
import { ref, reactive, toRef } from 'vue'
import { useRoute } from 'vue-router'
import { ElMessage } from 'element-plus'
import { storeToRefs } from 'pinia'
import { useParamsStore } from '@/store'
import useTryCatch from '@/hooks/use-try-catch'
import useLoading from '@/hooks/use-loading'
import { getBuyUrlList } from '@/api/buy-management'
import type { urlListItem, urlListQueryParams } from '@/api/buy-management'
import Listener from '@/utils/site-channel-listeners'

import QueryForm from '@/components/QueryForm/index.vue'
import CustomTable from '@/components/CustomTable/index.vue'

defineOptions( { name: 'BuyUrls' } )

const route = useRoute()
const { site_id_all, channel_item_all } = storeToRefs(useParamsStore())
const { loading, setLoading } = useLoading()

const queryParams_raw = {
  page_id: '',
  sku_id: '',
}

const queryParams = reactive<urlListQueryParams>({
  ...queryParams_raw,
  site_id: site_id_all.value,
})

const formList = ref([
  {
    placeholder: 'sku id',
    value: toRef(queryParams, 'sku_id'),
    component: 'input',
  },
  {
    placeholder: '页面ID',
    value: toRef(queryParams, 'page_id'),
    component: 'input',
  }
])
const tableRef = ref()
const tableData = ref<urlListItem[]>([])
const columns = ref([
  {
    title: '主Sku id',
    dataKey: 'sku_id',
    width: 150,
  },
  {
    title: '购买链接',
    dataKey: 'buy_url',
    minWidth: 300,
    hideOverflowTooltip: true
  },
  {
    title: '活动ID',
    dataKey: 'activity_id',
    width: 200,
  },
  {
    title: '所属渠道',
    dataKey: 'channel_code',
    width: 100,
  },
  {
    title: '部署页面ID',
    dataKey: 'page_id',
    width: 100,
  },
  {
    title: '部署位置',
    dataKey: 'position_name',
    width: 200,
  },
])

const reset = () => {
  Object.entries(queryParams_raw).forEach(([ key, value ]) => {
    queryParams[key] = value
  })
}

const handleSearch = () => {
  tableRef.value.setPage(1)
  const { page, pageSize } = tableRef?.value?.getPagination()
  getList( { page, pageSize } )
}

const getList = ( { page, pageSize } ) => {
  useTryCatch( async () => {
    setLoading(true)

    const params = {
      ...queryParams,
      site_id: site_id_all.value,
      channel_id: channel_item_all.value.channel_id as number,
      page,
      page_size: pageSize,
    }

    const res = await getBuyUrlList( params )
    if (res.code === 200) {
      tableData.value = res.data.list
      tableRef.value.setTotal( res.data.total )
    } else {
      ElMessage.error(res.msg)
    }

    setLoading(false)
  }, () => setLoading(false) )
}

Listener( route, () => {
  reset()
  handleSearch()
} )
</script>