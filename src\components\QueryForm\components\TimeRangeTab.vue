<template>
  <el-popover
    :visible="visible"
    placement="bottom"
    trigger="click"
    :width="760"
    :disabled="item.disabled"
    @after-enter="handleDateShow"
  >
    <template #reference>
      <div class="time-range-tab" @click="handlePopShow">
        <el-date-picker
          v-model="item.value"
          type="datetimerange"
          value-format="YYYY-MM-DD HH:mm:ss"
          :range-separator="item.range_separator || '至'"
          :start-placeholder="item.start_placeholder || '开始日期'"
          :end-placeholder="item.end_placeholder || '结束日期'"
          :clearable="item.hideClearable ? false : true"
          :disabled="item.disabled"
          readonly
        />
      </div>
    </template>
    <div class="time-range-tab-content" style="position: relative;">
      <el-tabs 
        v-model="item.field_key"
        @tab-change="handleTabChange"
        tab-position="left"
        :style="{ height: item.field_list ? item.field_list.length * 40 + 'px' : '80px', minHeight: '80px'}"
      >
        <el-tab-pane v-for="d in item.field_list" :label="d.label" :name="d.value" />
        <el-date-picker
          ref="datePickerRef"
          v-model="item.value"
          type="datetimerange"
          unlink-panels
          value-format="YYYY-MM-DD HH:mm:ss"
          :range-separator="item.range_separator || '至'"
          :start-placeholder="item.start_placeholder || '开始日期'"
          :end-placeholder="item.end_placeholder || '结束日期'"
          :disabled="!item.field_key"
          style="width: 100%;"
          @change="handleDateChange"
        />
      </el-tabs>
      <span style="position: absolute; right: 0; bottom: 0">
        <el-button @click="visible = false"> 取消 </el-button>
        <el-button type="primary" @click="visible = false"> 确定 </el-button>
      </span>
    </div>
  </el-popover>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import type { listItem } from '../types'

defineOptions( { name: 'TimeRangeTab' } )

const props = defineProps<{
  item: listItem
}>()

const visible = ref(false)
const datePickerRef = ref()

const handlePopShow = () => {
  visible.value = true
}

const handleDateChange = (value) => {
  if (value) {
    visible.value = false
    props.item.start_time = value[0]
    props.item.end_time = value[1]
  } else {
    props.item.start_time = ''
    props.item.end_time = ''
  }
  props.item.dateChange?.(props.item.value)
}

const handleDateShow = () => {
  if (props.item.field_key) {
    datePickerRef.value?.handleOpen()
  }
}

const handleTabChange = () => {
  props.item.value = ''
  props.item.start_time = ''
  props.item.end_time = ''
  handleDateShow()
  props.item.handleChange?.(props.item.field_key)
}
</script>