import axios from 'axios'
import type { InternalAxiosRequestConfig, AxiosResponse } from 'axios';
import { getHost, getAppName, getCodeAuthLoginUrl } from '@/hooks/use-env'
import { useCodeStore } from '@/store'
import { getLocalToken } from '@/hooks/use-local-auth'
import { ElMessage } from 'element-plus'

const APP_NAME = getAppName()

axios.defaults.withCredentials = true
axios.defaults.baseURL = getHost()

const whiteList = [
  'users/channel_read_update',
  'tool/tpl_transfer',
  'register/',
  'translate/',
  'audit/',
  'page/cdn_clear',
  'templates/copy_template',
  'templates/copy_template_page',
  'oauth/token'
]

//请求前拦截
axios.interceptors.request.use(
  (config: InternalAxiosRequestConfig) => {
    // 所有接口都请求子系统
    // 设置授权token
    if (config.headers) {
      config.headers.Authorization = getLocalToken()
      // 设置校验token
      config.headers['Id-Token'] = getLocalToken(`${APP_NAME}IdToken`)
    }
    if (config.method === 'post' || config.method === 'put') {
      if (sessionStorage.getItem('channel_read')) {

        for ( const path of whiteList ) {
          if (config.url?.includes(path)) {
            return config
          }
        }

        const channelSet = new Set(sessionStorage.getItem('channel_read')?.split(','))
        if (localStorage.getItem('channel_item_all')) {
          const channel_item_all = JSON.parse(localStorage.getItem('channel_item_all') || '{}')
          if (channelSet.has(String(channel_item_all.channel_id))) {
            const msg = '当前渠道已被设置为仅查看，无法提交数据。'
            ElMessage.warning(msg)
            return Promise.reject(msg)
          }
        }
      }
    }

    return config
  },
  (error) => {
    return Promise.reject(error)
  }
)

//请求后拦截
// 登录过期处理函数
const expireCallback = () => {
  // pinia调用时机，按需取用
  const { codeToTokenSuccess } = useCodeStore()
  if (codeToTokenSuccess) {
    const fullPath = window.location.pathname + window.location.search
    window.location.href = getCodeAuthLoginUrl(fullPath.split(`/${APP_NAME}`)[1])
  }
}

axios.interceptors.response.use(
  (response: AxiosResponse) => {
    const res = response.data
    if (res.code === 401) {
      return expireCallback()
    }
    return res
  },
  (error) => {
    if (error.message && error.message === 'Request failed with status code 401') {
      expireCallback()
    }
    return Promise.reject(error)
  }
)