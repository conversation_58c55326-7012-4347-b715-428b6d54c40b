import type { folderItem } from '@/api/resource-center'
import type Node from 'element-plus/es/components/tree/src/model/node'

const filterNodeTree = (value: string, data: folderItem, node: Node) => {
  if (!value) {
    data.pid !== 0 && (node.expanded = false)
    return true
  }

  return chooseNode(value, data, node)
}

const chooseNode  = (value: string, data: folderItem, node: Node) => {
  if (data.name.indexOf(value) !== -1) return true

  const level = node.level
  // 如果传入的节点本身就是一级节点就不用校验了
  if (level === 1) {
    return false
  }
  // 先取当前节点的父节点
  let parentData = node.parent
  // 遍历当前节点的父节点
  let index = 0
  while (index < level - 1) {
    // 如果匹配到直接返回，此处name值是中文字符，enName是英文字符。判断匹配中英文过滤
    if (parentData.data.name.indexOf(value) !== -1) {
      return true
    }
    // 否则的话再往上一层做匹配
    parentData = parentData.parent
    index++
  }
  // 没匹配到返回false
  return false
}

export default filterNodeTree