<template>
  <div class="scroll-y with-flex">

    <el-result
      v-show="!channel_item_all.channel_id"
      icon="warning"
      title="请先选择具体渠道!" 
      sub-title="您还未选择渠道"
      style="position: absolute; width: 100%; height: 100%; z-index: 10; background: var(--bg-layout);"
    />

    <el-tabs v-model="activeName" @tab-change="handleTabChange">
      <el-tab-pane label="评论" name="comment" />
      <el-tab-pane label="回复" name="reply" />
    </el-tabs>

    <QueryForm
      :form-list="formList"
      :loading="loading"
      @search="handleSearch"
      @reset="reset"
    />

    <div v-loading="loading" class="sticky-table">
      <CustomTable 
        ref="tableRef"
        :columns="columns"
        :operate-list="operateList"
        :data="tableData"
        :table-method="getList"
        selectable
        @selection-change="handleSelectionChange"
      />
    </div>

  </div>
</template>

<script setup lang="tsx">
import { ref, reactive, toRef } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { ElMessage, ElMessageBox, ElButton, ElInput, ElForm, ElFormItem } from 'element-plus'
import { storeToRefs } from 'pinia'
import { useParamsStore } from '@/store'
import useTryCatch from '@/hooks/use-try-catch'
import useLoading from '@/hooks/use-loading'
import { getCommentList, getReplyList, commentCheck, commetReply } from '@/api/web-config'
import Listener from '@/utils/site-channel-listeners'

import QueryForm from '@/components/QueryForm/index.vue'
import CustomTable from '@/components/CustomTable/index.vue'

defineOptions( { name: 'CommentManagement' } )

const route = useRoute()
const router = useRouter()
const { channel_item_all } = storeToRefs(useParamsStore())
const { loading, setLoading } = useLoading()

const activeName = ref('comment')
const queryParams_raw = {
  url: '',
  uid: '',
  country: '',
  check_state: '',
  begin_time: '',
  end_time: '',
}
const page_id = ref('')

const queryParams = reactive( {
  ...queryParams_raw
} )

const formList = ref([
{
    placeholder: '页面URL',
    value: toRef(queryParams, 'url'),
    component: 'textarea',
  },
  {
    placeholder: '页面ID',
    value: page_id,
    component: 'input',
  },
  {
    placeholder: '用户wsid',
    value: toRef(queryParams, 'uid'),
    component: 'input',
  },
  {
    placeholder: '审核状态',
    value: toRef(queryParams, 'check_state'),
    component: 'select',
    selections: [
      {
        label: '待审核',
        value: 'pending'
      },
      {
        label: '审核通过',
        value: 'able'
      },
      {
        label: '审核不通过',
        value: 'disable'
      }
    ]
  },
  {
    placeholder: '国家/区域',
    value: toRef(queryParams, 'country'),
    component: 'input',
  },
  {
    start_placeholder: '开始日期',
    end_placeholder: '结束日期',
    value: '',
    component: 'datetimerange',
    append: true,
    dateChange: (value: [string, string] | null) => {
      if (value) {
        queryParams.begin_time = value[0]
        queryParams.end_time = value[1]
      } else {
        queryParams.begin_time = ''
        queryParams.end_time = ''
      }
    }
  },
])

const tableRef = ref()
const tableData = ref()
const tableSelections = ref<number[]>([])
const hideComment = computed(() => activeName.value === 'reply')
const hideReply = computed(() => activeName.value === 'comment')
const operateList = ref([
  {
    title: '审核通过',
    method: () => {
      handleCheck(1)
    },
    disabled: computed( () => tableSelections.value.length === 0 )
  },
  {
    title: '审核不通过',
    method: () => {
      handleCheck(2)
    },
    disabled: computed( () =>  tableSelections.value.length === 0 )
  }
])
const columns = ref([
  {
    title: '评论ID',
    dataKey: 'id',
    width: 100,
    hideColumn: hideComment
  },
  {
    title: '页面ID',
    dataKey: 'page_id',
    width: 100,
    hideColumn: hideComment
  },
  {
    title: '页面URL',
    dataKey: 'url',
    minWidth: 200,
    hideColumn: hideComment
  },
  {
    title: '评论内容',
    dataKey: 'message',
    minWidth: 200,
    hideColumn: hideComment
  },
  {
    title: '评论用户',
    dataKey: 'uid',
    width: 100,
    hideColumn: hideComment
  },
  {
    title: '评论时间',
    dataKey: 'time',
    width: 180,
    hideColumn: hideComment
  },
  {
    title: '回复数',
    dataKey: 'reply_total',
    width: 100,
    hideColumn: hideComment
  },
  {
    title: '拓展信息',
    dataKey: 'extend',
    minWidth: 180,
    hideColumn: hideComment
  },
  {
    title: '归属评论ID',
    dataKey: 'review_id',
    width: 100,
    hideColumn: hideReply
  },
  {
    title: '回复ID',
    dataKey: 'reply_id',
    width: 100,
    hideColumn: hideReply
  },
  {
    title: '回复内容',
    dataKey: 'message',
    minWidth: 200,
    hideColumn: hideReply
  },
  {
    title: '回复对象',
    dataKey: 'reply_to_uid',
    width: 100,
    hideColumn: hideReply
  },
  {
    title: '回复时间',
    dataKey: 'time',
    width: 180,
    hideColumn: hideReply
  },
  {
    title: '国家/区域',
    dataKey: 'country',
    width: 120
  },
  {
    title: '审核状态',
    dataKey: 'check_state',
    width: 150,
    cellRenderer: (scope: { row: any }) => (
      <span style={ { color: scope.row.check_state === 'able' ? 'var(--el-color-success)' : scope.row.check_state === 'disable' ? 'var(--el-color-error)' : ''} }>
        { scope.row.check_state === 'pending' ? '待审核' : scope.row.check_state === 'able' ? '审核通过' : '审核不通过' }
      </span>
    )
  },
  {
    title: '操作人',
    dataKey: 'user_name',
    width: 100
  },
  {
    title: '操作时间',
    dataKey: 'update_time',
    width: 180
  },
  {
    title: '操作',
    dataKey: '',
    width: 300,
    fixed: 'right',
    cellRenderer: (scope: { row: any }) => (
      <>
        <ElButton type='primary' plain disabled={scope.row.check_state !== 'able'} onClick={ () => handleView(scope.row.url) }>查看页面</ElButton>
        <ElButton type='primary' plain disabled={scope.row.check_state !== 'able'} onClick={ () => handleDetail(scope.row.id, scope.row.review_id) }>评论详情</ElButton>
        <ElButton type='primary' plain disabled={scope.row.check_state !== 'able'} onClick={ () => handleReply(scope.row) }>回复</ElButton>
      </>
    )
  }
])

const reset = () => {
  Object.entries(queryParams_raw).forEach(([ key, value ]) => {
    queryParams[key] = value
  })
}

const refresh = () => {
  const { page, pageSize } = tableRef?.value?.getPagination()
  getList( { page, pageSize } )
}

const handleTabChange = () => {
  handleSearch()
}

const handleSearch = () => {
  tableRef.value?.setPage(1)
  refresh()
}

const getList = ( { page, pageSize } ) => {
  if (!channel_item_all.value.channel_id) {
    return
  }
  useTryCatch( async () => {
    setLoading(true)

    const api = activeName.value === 'comment' ? getCommentList : getReplyList
    const params = {
      q: { 
        ...queryParams,
        ...page_id.value ? { page_id: +page_id.value } : {},
        channel_id: channel_item_all.value.channel_id as number
      },
      page,
      limit: pageSize
    }
    const res = await api(params)
    if (res.code === 200) {
      tableData.value = res.data.list || []
      tableRef.value.setTotal(res.data.total)
    } else {
      ElMessage.error(res.msg)
    }

    setLoading(false)
  }, () => setLoading(false) )
}

const handleSelectionChange = (selections: any[]) => {
  tableSelections.value = selections.map(item => item.id)
}

const handleCheck = ( type: 1|2 ) => {
  ElMessageBox.confirm(`审核${type === 1 ? '通过后' : '不通过'}，选中的评论/回复，将${type === 1 ? '' : '不'}会展示在对应页面上`, `审核${type === 1?'':'不'}通过`, {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning',
    callback: ( action: string ) => {
      if (action === 'confirm') {
        const params = {
          ids: tableSelections.value.join(','),
          type: activeName.value === 'comment' ? 1 : 2,
          state: type === 1 ? 'able' : 'disable'
        }


        useTryCatch( async () => {
          const res = await commentCheck(params)

          if (res.code === 200) {
            refresh()
            ElMessage.success('操作成功')
          } else {
            ElMessage.error(res.msg)
          }
        } )
      }
    }
  })
}

const handleReply = (row: any) => {
  const replyMsg = ref(row.official_reply ? row.official_reply.message : '')
  ElMessageBox({
    title: '回复内容',
    customClass: 'dialog-button-center',
    customStyle: {
      maxWidth: 'none',
      width: '500px',
    },
    showCancelButton: true,
    showConfirmButton: true,
    cancelButtonText: '取消',
    confirmButtonText: '确认',
    callback: (action: string) => {
      if (action === 'confirm') {
        useTryCatch( async () => {
          const params = {
            id: row.id,
            message: replyMsg.value,
            type: activeName.value === 'comment' ? 1 : 2
          }
          const res = await commetReply(params)

          if (res.code === 200) {
            refresh()
            ElMessage.success('回复成功')
          } else {
            ElMessage.error(res.msg)
          }
        } )
      }
    },
    message: () => h(ElForm, { labelWidth: '120px', labelSuffix: ':' }, () => [
      h(ElFormItem, { label: '用户评论/回复' }, () => row.message),
      h(ElFormItem, { label: '回复内容' }, () => h(ElInput, {
        type: 'textarea',
        modelValue: replyMsg.value,
        onInput: (value) => replyMsg.value = value
      }))
    ])
  })
}

const handleView = (url: string) => {
  window.open(url)
}

const handleDetail = (id: number, review_id?: number) => {
  const comment_id = review_id || id
  router.push({
    name: 'CommentDetail',
    query: {
      comment_id,
      channel_id: channel_item_all.value.channel_id
    }
  })
}

Listener(route, () => {
  if (channel_item_all.value.channel_id) {
    reset()
    handleSearch()
  }
})
</script>