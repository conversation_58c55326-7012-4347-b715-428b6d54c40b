import axiosReq from './axios'
import qs from 'query-string'
import type { fieldItem } from './template-management'
import type { buyInfo } from './buy-management'
import type { HttpResponse } from './axios/types'

export type queryParams = {
  module: string;
  type: string;
  user_id_a?: string|number;
  user_id_e?: string|number;
  user_id_c?: string|number;
  tpl_language?: string;
  page_state?: string|string[];
  url: string;
  tpl_name?: string;
  state?: string;
  tpl_id?: string;
  channel_id?: string|number;
  page_group_id?: string;
  tpl_page_ids?: string;
  title?: string;
  description?: string;
  time_field?: string;
  start_time?: string;
  end_time?: string;
  level?: string;
  video_type?: string;
}

export type listQueryParams = {
  S: queryParams;
  page: number;
  page_size: number;
  site_id?: number;
  sort_key?: string;
  sort_type?: string;
}

export type listItem = {
  tpl_page_id: number;
  tpl_id: number;
  title: string;
  keyword: string;
  description: string;
  url: string;
  entity_id: number;
  entity_name: string;
  is_default: string;
  html_time: string;
  action_name: string;
  user_id_a: number
  user_id_e: number
  add_time: string;
  edit_time: string;
  user_name: string;
  state: string;
  page_state: string;
  src_id: number
  page_group_id: number
  channel_id: number;
  cbs_ids: string;
  src_version: string;
  user_id_c: number;
  create_time: string;
  channel_code: string;
  channel_name: string;
  color: string;
  count_block: number;
  count_internal_links: number;
  count_external_links: number;
  language: string;
  online_url: string;
  check_url: string;
  module: string;
  type: string;
  page_url: string;
  main_tpl_id: number;
  user_name_a: string;
  user_name_e: string;
  user_name_c: string;
  level: string;
  lighthouse: {
    cls: number;
    fcp: number;
    id: number;
    lcp: number;
    page_id: number;
    performance: number;
    si: number;
    tbt: number;
    type: number;  // 1: pc; 2: mobile
  }[]
}

export type addPageData = {
  site_id: number;
  module: string;
  type: string;
  tpl_id: number|string;
  entity_id: string;  // 实体ID：包括文章集ID，module=article且type=content时候必传
  channel_ids: string; // 所属渠道，多个用,隔开
  child_channel?: string;
  child_module?: string;
}

export type pageData = {
  site_id: number;
  channel_id: number;
  cbs_ids: string;
  template_id: number|string;
  module: string;
  tpl_type: string;
  title: string;
  keyword: string;
  description: string;
  entity_id: string|number;
  entity_name: string;
  url: string;
  remark: string;
  tpl_fields: fieldItem[];
  state: string;
}

export interface pageDetailCommon {
  tpl_page_id: number;
  tpl_id: number;
  title: string;
  keyword: string;
  description: string;
  url: string;
  entity_id: number;
  entity_name: string;
  is_default: string;
  html_time: string;
  action_name: string;
  user_id_a: number;
  user_id_e: number;
  add_time: string;
  edit_time: string;
  user_name: string;
  state: string;
  page_state: string;
  src_id: number;
  page_group_id: number;
  channel_id: number;
  cbs_ids: string;
  src_version: string;
  user_id_c: number;
  create_time: string;
  main_tpl_id: number;
  domain_host: string;
  channel_code: string;
  art_id: number;
  tpl_type_name: string;
  tpl_module_name: string;
  tpl_name: string;
  tpl_module: string;
  tpl_type: string;
  buy_info: buyInfo|null;
}

export interface pageDetail extends pageDetailCommon {
  template_info: templateInfo;
  page_field: fieldItem[];
}

export type templateInfo = {
  tpl_id: number;
  name: string;
  content: string;
  module: string;
  type: string;
  level: string;
  is_list: string;
  is_default: string;
  site_id: number;
  action_name: string;
  user_id_a: number;
  user_id_e: number;
  add_time: string;
  edit_time: string;
  user_name: string;
  state: string;
  pub_switch: string;
  src_id: string;
  language: string;
  is_check: number;
  remark: string;
  use_remark: string;
  channel_id: number;
  parent_id: number;
  draft_version_id: number;
  src_version: string;
}

export type errorItem = {
  error: number;
  error_detail: string;
  faq: string;
  level: string;
  num: number;
  title: string;
  type: string;
}

/* 新版页面管理 */

export type addPageData_new = {
  channel_id: number;
  site_id: number;
  tpl_id: number|string;
  title: string;
  keyword: string;
  description: string;
  url: string;
  remark: string;
  art_id?: number;
  tpl_fields: fieldItem[];
  page_extend: {val: string; key: string}[];
  buy_info?: buyInfo|null;
}

export type editPageData = {
  tpl_page_id: number;
  title: string;
  keyword: string;
  description: string;
  url: string;
  remark: string;
  tpl_fields: fieldItem[];
  page_extend: {val: string; key: string}[];
  buy_info?: buyInfo|null;
}

export type pageInfoData = {
  is_article_page: boolean;
  page_info: pageDetailCommon;
  page_field: fieldItem[];
}

export type listByType = {
  tpl_id: number;
  name: string;
}[]

export type artTempPageQuery = {
  site_id: number;
  channel_id: number;
  page?: number;
  page_size?: number;
  cat_id?: number|string;
  user_id?: number|string;
  title: string|string;
  art_id?: number|string;
}

export type artTempPageItem = {
  art_id: number;
  title: string;
  cat_id: number;
  user_id_a: number;
  user_name: string;
  cat_name: string;
}
/* 新版页面管理 */

export type historyListItem = {
  id: number;
  site_id: number;
  entity_id: number;
  type: number;
  version: number;
  es_id: string;
  add_time: string;
  edit_time: string;
  user_name: string;
  user_id_a: number;
  user_id_e: number;
  remark: string;
  state: string;
  name: string;
  is_current_version?: boolean;
}

export type pageQualityItem = {
  error: number;
  error_detail: string;
  faq: string;
  level: string;
  num: number;
  title: string;
  type: string;
}

export type comparisonItem = {
  action_name: string;
  add_time: string;
  cbs_ids: string;
  channel_id: number;
  create_time: string;
  description: string;
  edit_time: string;
  entity_id: number;
  entity_name: string;
  field_value: { field_name: string; field_value: string; input_label: string; }[];
  html_time: string;
  is_default: string;
  keyword: string;
  page_extend: string;
  page_group_id: number;
  page_state: string;
  remark: string;
  src_id: number;
  src_version: string;
  state: string;
  title: string;
  tpl_id: number;
  tpl_page_id: number;
  url: string;
  user_name: string;
  version: number;
}

// 获取页面管理列表
export const getPagesList = (params: listQueryParams) => 
  axiosReq('get', '/api/v1/templates/pages', {
    params,
    paramsSerializer: {
      serialize: (obj: listQueryParams) => qs.stringify({
        S: JSON.stringify(obj.S),
        page: obj.page,
        page_size: obj.page_size,
        site_id: obj.site_id,
        ...obj.sort_type ? {
          sort_key: obj.sort_key,
          sort_type: obj.sort_type
        } : {}
      })
    }
  })

// 启用&禁用页面
export const changeStatus = (data: { state: 'disable'|'able'; page_ids: string; site_id: number; }) => 
  axiosReq('put', '/api/v1/templates/pages/state', data)

// 添加页面（集）
export const addPage = (data: addPageData) =>
  axiosReq('post', '/api/v1/templates/page_groups', { data })

// 查询子渠道下面的模板列表
export const getChildModuleList = (channel_id: number|string, params: { module: string; type: string; }) => 
  axiosReq('get', `/api/v1/templates/channels/${channel_id}`, {
    params,
    paramsSerializer: {
      serialize: (obj) => qs.stringify(obj)
    }
  })

// 获取页面详情
export const getPageDetail = (page_id: number, site_id: number) => 
  axiosReq('get', `/api/v1/templates/pages/${page_id}?site_id=${site_id}`)

// 编辑页面
export const editPage = (page_id: number, data: pageData) =>
  axiosReq('post', `/api/v1/templates/${data.template_id}/pages/${page_id}`, data)

// 页面质量检测&获取列表
export const pageQualityCheck = (tpl_id: number, page_id: number, site_id: number) => 
  axiosReq<any, HttpResponse<errorItem[]|null>>('get', `/api/v1/templates/${tpl_id}/pages/${page_id}/checkpreview?site_id=${site_id}&page_check=1`)


/* 新版页面管理 */
// 根据模板ID查询页面字段
export const getFieldInfo = (tpl_id: number) => 
  axiosReq('get', `/api/v1/templates/field_info?tpl_id=${tpl_id}`)

// 页面添加
export const addPage_new = (data: addPageData_new) =>  
  axiosReq('post', '/api/v1/page/page_add', data)

// 获取详情
export const getPageDetail_new = (tpl_page_id: number) => 
  axiosReq('get', `/api/v1/page/page_info?tpl_page_id=${tpl_page_id}`)

//  页面编辑
export const editPage_new = (data: editPageData) => 
  axiosReq('post', '/api/v1/page/page_edit', data)

// 查询模板下拉列表数据
export const getListByType = (params: { channel_id: number; type: string; module: string }) => 
  axiosReq('get', '/api/v1/templates/list_by_type', {
    params,
    paramsSerializer: {
      serialize: (obj) => qs.stringify(obj)
    }
  }
)

// 渠道下未绑定文章列表
export const getArtTempPage = (params: artTempPageQuery) => 
  axiosReq('get', '/api/v1/page/art_no_page_list', {
    params,
    paramsSerializer: {
      serialize: (obj) => qs.stringify(obj)
    }
  })

// 页面绑定文章
export const pageBindArt = (data: { tpl_page_id: number; art_id: number; }) => 
  axiosReq('post', '/api/v1/page/page_band_art', data)

/* 新版页面管理 */

// 模板历史记录列表
export const getHistoryList = (tpl_id: number, page_id: number, params: { site_id: number; page: number; page_size: number }) => 
  axiosReq('get', `/api/v1/templates/${tpl_id}/pages/${page_id}/versions`, {
    params,
    paramsSerializer: {
      serialize: (obj) => qs.stringify(obj)
    }
  })

// 回退至指定版本 
export const restoreVersion = (tpl_id: number, page_id: number, data: { site_id: number; version_id: number; }) => 
  axiosReq('post', `/api/v1/templates/${tpl_id}/pages/${page_id}/versions/restoration`, data)

// 查看历史版本详情
export const getHistoryDetail = (page_id: number|string, version_id: number, site_id: number ) => 
  axiosReq('get', `/api/v1/templates/pages/${page_id}/versions/${version_id}?site_id=${site_id}`)

// 版本对比
export const getComparisons = (site_id: number, tpl_id: string|number, page_id: string|number, version_ids: string) => 
  axiosReq('get', `/api/v1/templates/${tpl_id}/pages/${page_id}/versions/comparisons?site_id=${site_id}&version_ids=${version_ids}`)