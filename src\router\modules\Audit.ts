import type { moduleMap } from '../aysncRoutes'
export default {
  'ReviewCenter': {
    component: () => import('@/views/audit-management/index.vue'),
    path: 'audit-center',
    title: '审核中心',
    cachePage: true,
  },
  'ReviewApply': {
    component: () => import('@/views/audit-management/apply/index.vue'),
    path: 'audit-apply',
    title: '我的申请',
    hidden: true,
  },
  'ReviewDetail': {
    component: () => import('@/views/audit-management/detail/index.vue'),
    path: 'audit-detail',
    title: '审核详情',
    hidden: true,
    queryRequired: true
  },
  'ReviewList': {
    component: () => import('@/views/audit-management/list/index.vue'),
    path: 'audit-list',
    title: '我的待办/已办',
    cachePage: true,
    hidden: true,
  },
} as {
  [propName: string]: moduleMap
}