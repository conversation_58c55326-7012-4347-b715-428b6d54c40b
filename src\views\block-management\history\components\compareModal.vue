<template>
  <el-dialog
    title="块历史对比"
    v-model="show"
    top="3vh"
    width="95%"
    append-to-body
    @closed="emit('close')"
  >
    <template #default>
      <div style="overflow: auto; max-height: calc(100vh - 15vh);">
        <el-form label-suffix=":" label-width="120px" style="overflow: hidden">
          <el-row :gutter="20">
            <el-col :span="12">
              <el-card shadow="never" style="height: 100%;">
                <template #header>
                  块-{{ blk_id }} V{{ prev?.version }} 基础信息
                </template>
                <template #default>
                  <el-form-item
                    v-for="d in commonFields"
                    :label="d.label"
                    :style="{ color: prev?.[d.key] === current?.[d.key] ? '' : 'var(--el-color-success)' }"
                  >
                    {{ prev?.[d.key] }}
                  </el-form-item>
                </template>
              </el-card>
            </el-col>
            <el-col :span="12">
              <el-card shadow="never" style="height: 100%;">
                <template #header>
                  块-{{ blk_id }} V{{ current?.version }} 基础信息
                </template>
                <template #default>
                  <el-form-item
                    v-for="d in commonFields"
                    :label="d.label"
                    :style="{ color: prev?.[d.key] === current?.[d.key] ? '' : 'var(--el-color-danger)' }"
                  >
                    {{ current?.[d.key] }}
                  </el-form-item>
                </template>
              </el-card>
            </el-col>
          </el-row>
        </el-form>
        <Diff 
          v-if="showDiff"
          :prev="(prev?.content as string)"
          :current="(current?.content as string)"
        />
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { ElMessage } from 'element-plus'
import useParamsStore from '@/store/modules/params'
import useTryCatch from '@/hooks/use-try-catch'
import { getComparisons } from '@/api/block-management'
import type { comparisonItem } from '@/api/block-management'

import Diff from '@/components/CodeDiff/index.vue'

const props = defineProps<{
  blk_id: number|string;
  version_ids: string;
}>()
const emit = defineEmits(['close'])

const commonFields = [
  {
    label: '块名称',
    key: 'blk_name'
  },
  {
    label: '文件路径',
    key: 'file_path'
  },
  {
    label: '备份说明',
    key: 'remark'
  }
]

const { site_id_all } = useParamsStore()
const show = ref(true)
const showDiff = ref(false)

const prev = ref<comparisonItem>()
const current = ref<comparisonItem>()

const getDetail = () => {
  useTryCatch( async () => {
    const { blk_id, version_ids } = props
    const res = await getComparisons(site_id_all, blk_id, version_ids)
    if (res.code === 200) {
      prev.value = res.data[1]
      current.value = res.data[0]
      showDiff.value = true
    } else {
      ElMessage.error(res.msg)
    }
  } )
}

getDetail()
</script>