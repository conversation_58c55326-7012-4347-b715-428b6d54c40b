<template>
  <div class="scroll-y">
    <el-card shadow="never">
      <template #header>
        <div class="rowBC">
          <div><strong style="font-size: 1.25em;">编辑模板关系</strong></div>
          <div>
            <el-button
              type="primary"
              plain
              @click="handleEditGroup(null)"
              :disabled="tableData.length === 0"
            >
              复用模板
            </el-button>
            <el-button
              type="primary"
              plain
              @click="handleCopy(null)"
              :disabled="tableData.length === 0"
            >
              复用模板下页面
            </el-button>
          </div>
        </div>
      </template>
      <template #default>
        <el-table
          border
          max-height="800"
          :data="tableData"
        >
          <el-table-column
            prop="tpl_id"
            label="模板ID"
            min-width="100"
            show-overflow-tooltip
          />
          <el-table-column
            prop="name"
            label="模板名称"
            min-width="200"
            show-overflow-tooltip
          />
          <el-table-column label="模板渠道" width="120" show-overflow-tooltip>
            <template #default="{ row }: { row: groupListItem }">
              <span>{{ row.channel_name }}</span>
            </template>
          </el-table-column>
          <el-table-column
            prop="user_name_e"
            label="更新人"
            show-overflow-tooltip
          />
          <el-table-column
            prop="edit_time"
            label="更新时间"
            show-overflow-tooltip
          />
          <el-table-column label="操作" width="290">
            <template #default="{ row }: { row: groupListItem }">
              <el-button type="primary" plain @click="handleEdit(row)">编辑</el-button>
              <el-button type="primary" plain @click="handleEditGroup(row)">复用模板</el-button>
              <el-button type="primary" plain @click="handleCopy(row)">复用页面</el-button>
            </template>
          </el-table-column>
        </el-table>
      </template>
    </el-card>

    <Teleport to="body">
      <TempGroup 
        ref="tempGroupRef"
        v-if="loadTempGroup"
        :channel-list="channelList"
        :table-data="tableData"
        :current-row="currentRow"
        @success="handleSuccess"
      />
    </Teleport>

    <Teleport to="body">
      <CopyTempPage 
        v-if="loadTempPage"
        ref="copyTempPageRef"
        :channel-list="channelList"
        :table-data="tableData"
        :current-row="currentRow"
        @success="handleCopyPageSuccess"
      />
    </Teleport>
  </div>
</template>

<script setup lang="ts">
import { ref, defineAsyncComponent } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { ElMessage, ElTable, ElTableColumn } from 'element-plus'
import { useBasicStore } from '@/store/basic'
import useTryCatch from '@/hooks/use-try-catch'
import { getTempGroupList } from '@/api/template-management'
import type { groupListItem } from '@/api/template-management'

const TempGroup = defineAsyncComponent(() => import('../components/editGroupTemplate.vue'))
const CopyTempPage = defineAsyncComponent(() => import('./components/copyTempPage.vue'))

const router = useRouter()
const route = useRoute()
const tpl_id = route.query.tpl_id as string || ''
const basicStore = useBasicStore()

const tableData = ref<groupListItem[]>([])
const channelList = ref<{channel_id: number; channel_code: string}[]>([])
const tempGroupRef = ref()
const loadTempGroup = ref(false)
const copyTempPageRef = ref()
const loadTempPage = ref(false)
const currentRow = ref()

const handleDeleteCache = () => {
  const name = 'TemplateManageOperate'
  if (basicStore.cachedViews.indexOf(name) > -1) {
    basicStore.delCachedView(name)
  }
}

const getList = () => {
  useTryCatch( async () => {
    const res = await getTempGroupList(tpl_id)
    if (res.code === 200) {
      tableData.value = res.data.list
      channelList.value = res.data.channel_list
    } else {
      ElMessage.error(res.msg)
    }
  })
}
const handleEdit = (row: groupListItem) => {
  handleDeleteCache()
  router.push({
    name: 'TemplateManageOperate',
    query: {
      type: 'edit',
      tpl_id: row.tpl_id,
      channel_id: row.channel_id,
    }
  })
}
const handleCopy = (row: groupListItem|null) => {
  currentRow.value = row
  loadTempPage.value = true
  if (!loadTempPage.value) {
    loadTempPage.value = true
  } else {
    copyTempPageRef.value?.show()
    copyTempPageRef.value?.handleSourceChannel(row?.channel_id)
  }
}

const handleEditGroup = (row: groupListItem|null) => {
  currentRow.value = row
  if (!loadTempGroup.value) {
    loadTempGroup.value = true
  } else {
    tempGroupRef.value?.show()
    tempGroupRef.value?.handleSourceChannel(row?.channel_id)
  }
}
const handleSuccess = () => {
  loadTempGroup.value = false
  loadTempPage.value = false
  getList()
}
const handleCopyPageSuccess = () => {
  loadTempPage.value = false
}

getList()
</script>