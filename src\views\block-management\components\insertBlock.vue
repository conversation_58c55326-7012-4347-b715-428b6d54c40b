<template>
  <el-dialog
   v-model="show"
   title="插入代码块"
   width="1000px"
   align-center
   append-to-body
   @close="emit('close')"
  >
    <template #default>
      <QueryForm 
       :form-list="formList"
       :loading="loading"
       @search="handleSearch"
       @reset="reset"
      />

      <div v-loading="loading" height="800px">
        <CustomTable 
          ref="tableRef"
          :data="tableData"
          :columns="columns"
          :table-method="getList"
          selectable
          height="800"
          @selection-change="handleSelectionChange"
        />
      </div>
    </template>
    <template #footer>
      <div class="text-center" style="position: relative;">
        插入至第
        <el-input v-model="row" type="number" placeholder="只能输入正整数" style="width: 80px" />
        行
        <el-button type="primary" @click="handleInsert" :disabled="disabled"> 插入块 </el-button>

        <template v-if="tableSelections.length > 0">
          <span v-show="disabled" style="position: absolute; color: var(--el-color-error);font-size: 12px;left: 50%;bottom: -16px;transform: translateX(-68%);">
            只能输入正整数
          </span>
        </template>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="tsx">
import { ref, reactive, toRef, computed } from 'vue'
import { ElMessage } from 'element-plus'
import { useParamsStore } from '@/store'
import useTryCatch from '@/hooks/use-try-catch'
import useLoading from '@/hooks/use-loading'
import { getBlockList } from '@/api/block-management'
import type { queryParams, listItem } from '@/api/block-management'

import QueryForm from '@/components/QueryForm/index.vue'
import CustomTable from '@/components/CustomTable/index.vue'

const emit = defineEmits(['insert', 'close'])

const { site_id_all, channel_item_all } = useParamsStore()
const { loading, setLoading  } = useLoading()

const show = ref(true)
const row = ref(1)
const disabled = computed( () => {
  if (tableSelections.value.length === 0) {
    return true
  }
  if (!/^[1-9]\d*$/.test(`${row.value}`)) {
    return true
  }

  return false
} )

const queryParams_raw = {
  site_id: site_id_all,
  blk_name: '',
  blk_type: '',
  language: '',
  user_id_a: '',
  user_id_e: '',
  user_id_c: '',
  created_state: '',
  tpl_ids: '',
  art_ids: '',
  blk_ids: '',
  file_path: '',
  channel_id: '',
  is_main: '',
  time_field: '',
  start_time: '',
  end_time: ''
}
const queryParams = reactive<queryParams>({
  ...queryParams_raw
})
const formList = ref([
  {
    placeholder: '输入块id',
    value: toRef(queryParams, 'blk_ids'),
    component: 'input',
  },
  {
    placeholder: '输入块名称',
    value: toRef(queryParams, 'blk_name'),
    component: 'input',
  },
  {
    placeholder: '输入块路径',
    value: toRef(queryParams, 'file_path'),
    component: 'input',
  }
])

const tableRef = ref()
const tableData = ref<listItem[]>([])
const tableSelections = ref<listItem[]>([])
const columns = ref([
  {
    title: '块id',
    dataKey: 'blk_id',
    width: 100,
  },
  {
    title: '块名称',
    dataKey: 'blk_name',
    minWidth: 200,
  },
  {
    title: '块路径',
    dataKey: 'file_path',
    minWidth: 180,
  }
])

const reset = () => {
  Object.entries(queryParams_raw).forEach(([ key, value ]) => {
    queryParams[key] = value
  })
}

const handleSearch = () => {
  tableRef.value.setPage(1)
  const { page, pageSize } = tableRef?.value?.getPagination()
  getList( { page, pageSize } )
}

const getList = ( { page, pageSize } ) => {
  useTryCatch( async () => {
    setLoading(true)

    const params = {
      S:{
        ...queryParams,
        channel_id: channel_item_all.channel_id,
      },
      page,
      page_size: pageSize
    }
    const res = await getBlockList(params)
    if (res.code === 200) {
      tableRef.value?.setTotal(res.data.total)
      tableData.value = res.data.items
    } else {
      ElMessage(res.msg)
    }
    setLoading(false)
  }, () => setLoading(false) )
}

const handleSelectionChange = (selections: listItem[]) => {
  tableSelections.value = selections
}

const getBlockTemp = (blk_id: number, file_path: string) => {
  return `<BLOCKSTART object_id='${blk_id}'>{% model block::block_detail file='${file_path}' %}</BLOCKSTART>`
}

const handleInsert = () => {
  const blockList: string[] = []
  tableSelections.value.forEach( (item) => {
    blockList.push( getBlockTemp(item.blk_id, item.file_path) )
  } )
  emit('insert', `${blockList.join('\n')}\n`, row.value - 1)
}
</script>