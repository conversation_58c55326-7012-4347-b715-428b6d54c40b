export default {
  // 错误类
  '114': {
    title: '页面源代码中的 Hreflang 冲突',
    description: '1、运营多语言网站时，需要帮助其他国家/地区的用户以最适合他们的语言找到您的内容。这就是 hreflang (rel="alternate" hreflang="x") 属性派上用场的地方。<br>2、此属性可帮助搜索引擎了解应根据访问者的位置向其显示哪个页面。',
    solution: '1、hreflang 和 rel=canonical URL 冲突<br>2、hreflang URL 冲突<br>3、无自引用 hreflang URL'
  },
  '1': {
    title: '返回 5XX 状态代码的页面',
    description: '1、5xx错误是指服务器无法执行用户或爬虫请求的问题。<br>2、它们会阻止用户和搜索引擎机器人访问网页，并可能对用户体验和搜索引擎的抓取能力产生负面影响，导致网站的流量下降。',
    solution: '排查这些服务器产生错误的原因并修复'
  },
  '3': {
    title: '页面没有标题标签',
    description: '1、&lt;title&gt;; 标签是页面上关键的 SEO 元素。它出现在浏览器和搜索结果中，帮助搜索引擎和用户了解网页内容。<br>2、如果页面缺少标题，或者 &lt;title&gt;; 标记为空，Google 可能会认为该页面质量较低。如果在搜索结果中宣传此页面，容易错过获得高排名和获得更高点击率的机会。',
    solution: '确保网站上的每个页面都有一个独特且简洁的标题，其中包含最重要的关键字。'
  },
  '6': {
    title: '重复标题标签的问题',
    description: '1、仅当标题标签完全匹配时，谷歌爬虫才会报告具有重复标题标签的页面。<br>2、重复的 &lt;title&gt;; 标记使搜索引擎难以确定网站的哪些页面与特定搜索查询相关，以及哪个页面应在搜索结果中优先显示。<br>3、具有重复标题的页面获得良好排名的机会较低，并且有被禁止的风险。此外，相同的 &lt;title&gt;; 标签会让用户感到困惑，不知道他们应该关注哪个网页。',
    solution: '为包含最重要关键字的每个页面提供独特而简洁的标题'
  },
  '8': {
    title: '损坏的内部链接',
    description: '1、损坏的内部链接会将用户从一个网站引导到另一个网站，并将他们带到不存在的网页。<br>2、多个损坏的链接会对用户体验产生负面影响，并可能降低搜索引擎排名，因为爬虫可能会认为您的网站维护或编码不善。<br>3、抓取工具可能会检测到工作链接已损坏。一般来说，如果网站的服务器阻止爬虫访问该网站，就会发生这种情况。',
    solution: '1、请检查所有报告为损坏的链接。如果目标网页返回错误，请删除指向错误页面的链接或将其替换为其他资源。<br>2、如果报告为损坏的链接在使用浏览器访问时仍然有效，应该联系网站运营人员并告知该问题。'
  },
  '9': {
    title: '页面无法抓取',
    description: '1、网站的服务器响应时间超过 5 秒<br>2、服务器拒绝访问网页',
    solution: '联系网站管理员或者运维人员'
  },
  '10': {
    title: '无法抓取页面（DNS 解析问题）',
    description: '谷歌抓取工具在尝试访问网页时无法解析主机名，则会报告 DNS 解析错误',
    solution: '联系网站管理员或者运维人员'
  },
  '11': {
    title: '无法抓取页面（URL 格式不正确）',
    description: '1、网站的服务器响应时间超过 5<br>2、服务器拒绝访问网页',
    solution: '确保页面的 URL 符合标准，并且没有任何不必要的字符或拼写错误。'
  },
  '13': {
    title: '内部图像损坏',
    description: '1、内部损坏的图像是指由于不再存在、URL 拼写错误或文件路径无效而无法显示的图像。<br>2、损坏的图像可能会影响搜索排名，因为它们会提供较差的用户体验，并向搜索引擎发出信号，表明网页页面质量较低。',
    solution: '1、如果图像不再位于同一位置，请更改其 URL<br>2、如果图像被删除或损坏，请用新图像替换<br>3、如果图像不存在不再需要，只需将其从页面代码中删除即可'
  },
  '15': {
    title: '具有重复元描述的页面',
    description: '1、仅当它们完全匹配时，谷歌爬虫才会报告具有重复元描述的页面。<br>2、<meta description> 标签是网页内容的简短摘要，可帮助搜索引擎了解页面的内容，并可以在搜索结果中向用户显示。<br>3、不同页面上的重复元描述意味着失去使用更相关关键字的机会。此外，重复的元描述使搜索引擎和用户难以区分不同的网页。<br>4、没有元描述比有重复的元描述要好。',
    solution: '为每个网页提供独特、相关的元描述。'
  },
  '16': {
    title: 'Robots.txt 文件中的格式错误',
    description: '1、robots.txt 文件配置不当，可能导致搜索结果中推广的网页不会被搜索引擎索引，而私人信息可能会暴露给用户。<br>2、一个配置错误可能会损害搜索排名，毁掉所有的SEO优化工作。',
    solution: '1、检查 robots.txt 文件并修复所有错误（如果有）。<br>2、可以使用Google 的 robots.txt Tester检查文件。'
  },
  '17': {
    title: 'sitemap.xml 文件中的格式错误',
    description: '1、如果 sitemap.xml 包含引导至具有相同内容的网页、重定向到不同的网页、 返回非 200 状态代码的URL，则会触发此错误。<br>2、使用此类 URL 填充文件会迷惑搜索引擎，导致不必要的抓取或可能甚至导致站点地图被拒绝。',
    solution: '1、检查 sitemap.xml 中是否有任何重定向、非规范或非 200 URL。<br>2、提供规范的最终目标 URL 并返回 200 状态代码。'
  },
  '21': {
    title: '页面上的 HTML 大小太大',
    description: '1、网页的 HTML 大小是其中包含的所有 HTML 代码的大小。<br>2、页面大小太大（即超过2MB）会导致页面加载时间变慢，从而导致用户体验不佳和搜索引擎排名较低。',
    solution: '检查页面的 HTML 代码并考虑优化其结构和/或删除内联脚本和样式。'
  },
  '2': {
    title: '返回 4XX 状态代码的页面',
    description: '1、4xx错误意味着无法访问网页，通常是链接损坏的结果。<br>2、DDoS 防护系统。<br>3、服务器过载或配置错误。',
    solution: '1、如果网页返回错误，请删除所有指向错误页面的链接或将其替换为其他资源。<br>2、如果使用浏览器访问时报告为 4xx 的链接确实有效，可以尝试联系网络托管支持团队、或者通过指定“crawl-delay”指令，指示搜索引擎机器人不要过于频繁地抓取网站在的 robots.txt '
  },
  '33': {
    title: '重定向链和循环',
    description: '1、重定向使用不当的两个常见示例是重定向链和循环。<br>2、使搜索引擎难以抓取网站，从而影响网站抓取预算使用情况以及网页的索引程度，降低网站的加载速度，导致对网站的排名和用户体验产生负面影响。',
    solution: '1、避免这类问题的最佳方法是遵循一个一般规则：不要在链中使用超过三个重定向。<br>2、如果网站已经遇到长重定向链或循环的问题，建议您将链中的每个 URL 重定向到最终目标页面。<br>3、不建议简单地删除中间页面的重定向，因为可能有其他链接指向已删除的 URL，因此可能会遇到 404 错误。'
  },
  '41': {
    title: '内部 JavaScript 和 CSS 文件损坏的问题',
    description: '1、损坏的 JavaScript 或 CSS 文件是网站上应该注意的问题。<br>2、网站上停止运行的任何脚本都可能会危及您的排名，因为搜索引擎将无法正确呈现您的网页并为其建立索引。<br>3、损坏的 JS 和 CSS 文件可能会导致网站错误，破坏用户体验。',
    solution: '检查网站运维对所有损坏的 JavaScript 和 CSS 文件进行修复。'
  },
  '111': {
    title: '加载速度慢的页面',
    description: '1、页面（HTML）加载速度是最重要的排名因素之一。页面加载速度越快，获得的排名就越高。<br>2、快速加载的页面会对用户体验产生积极影响，有利于提高网站转化率。<br>3、“页面加载速度”通常是指浏览器完全呈现网页所需的时间。<br>4、爬虫仅测量加载网页 HTML 代码所需的时间 - 不考虑图像、JavaScript 和 CSS 的加载时间。',
    solution: '1、对 HTML 页面生成时间产生负面影响的主要因素是服务器的性能和网页 HTML 代码的密度。<br>2、尝试清理网页的 HTML 代码。<br>3、如果问题出在网络服务器上，应该考虑迁移到具有更多资源的更好的托管服务。'
  },
  '45': {
    title: '无效的结构化数据项',
    description: '1、如果结构化数据项包含不符合Google 准则的字段，则会触发此问题。<br>2、如果网站标记有错误，爬虫将无法正确理解它，您可能会面临失去获得丰富摘要和获得更有利排名机会的风险。',
    solution: '1、使用验证工具检查网页上的结构化数据，不同的标记测试工具可能会显示不同的结果。'
  },
  // 警告类
  '101': {
    title: '标题标签内没有足够文本的页面',
    description: '1、一般来说建议在网页上使用短标题。<br>2、包含 10 个或更少字符的标题无法提供有关网页内容的足够信息，并且会限制网页在不同关键字的搜索结果中显示的可能性。',
    solution: '在页面的 &lt;title&gt;; 标记内添加更多描述性文本。'
  },
  '112': {
    title: '文本与 HTML 比例较低的页面',
    description: '1、文本与 HTML 的比率表示网页上的实际文本量与代码量之比。<br>2、当网页 HTML 文本比例为 10% 或更少时，就会触发此问题。<br>3、较高的文本与 HTML 比率意味着网站页面更有可能在搜索结果中获得良好的位置。<br>4、更少的代码可以提高页面的加载速度，可以帮助搜索引擎机器人更快地抓取网站，也有助于网站排名。',
    solution: '1、将网页的文本内容和代码拆分为单独的文件并比较它们的大小。<br>2、如果代码文件的大小超过文本文件的大小，请检查页面的 HTML 代码并考虑优化其结构并删除嵌入的脚本和样式。'
  },
  '106': {
    title: '没有元描述的页面',
    description: '1、元描述对排名没有直接影响，但搜索引擎使用它们在搜索结果中显示页面的描述。<br>2、良好的描述可以帮助用户了解网站页面的内容并鼓励用户点击。如果页面的元描述标签丢失，搜索引擎通常会显示其第一句话。',
    solution: '为了获得更高的点击率，应该确保所有网页都有包含相关关键字的元描述。'
  },
  '103': {
    title: '没有 h1 标题的页面  ',
    description: '1、虽然 h1 标题不如 &lt;title&gt;; 标签重要，但它仍然有助于为搜索引擎和用户定义页面的主题。<br>2、如果 &lt;h1&gt;; 标记为空或丢失，搜索引擎可能会将您的页面放置在比其他标记更低的位置。<br>3、缺少 &lt;h1&gt;; 标签会破坏页面的标题层次结构，这对 SEO 不友好。',
    solution: '为每个页面提供简洁、相关的 h1 标题。'
  },
  '122': {
    title: 'URL 中带有下划线的页面  ',
    description: '1、涉及到 URL 结构时，不建议使用下划线作为单词分隔符，因为搜索引擎可能无法正确解释它们并可能认为它们是单词的一部分。<br>2、使用下划线不会对网页可见性产生巨大影响，但与使用连字符相比，它会降低页面出现在搜索结果中的机会。',
    solution: '将下划线替换为连字符。但是，如果网站页面排名良好，则不建议这样做。'
  },
  '116': {
    title: '字数较少的页面',
    description: '1、网页上的字数少于 200，则会触发此问题。网页上的文字数量对搜索引擎来说是一个质量信号。<br>2、搜索引擎更愿意向用户提供尽可能多的信息，因此内容较长的页面往往会在搜索结果中排名靠前，而不是字数较少的页面。',
    solution: '改进页面内容并确保包含 200 个以上有意义的单词。'
  },
  '109': {
    title: '具有临时重定向的页面  ',
    description: '1、临时重定向（即 302 和 307 重定向）意味着页面已暂时移动到新位置。<br>2、搜索引擎将继续索引重定向的页面，并且不会将链接汁或流量传递到新页面，这就是为什么临时重定向如果错误使用可能会损害网站搜索排名。',
    solution: '1、检查所有 URL 以确保使用 302 和 307 重定向是合理的。<br>2、不再需要它们时将其删除。<br>3、如果需要永久移动任何页面，请将 302/307 重定向替换为 301/308 重定向。'
  }
}