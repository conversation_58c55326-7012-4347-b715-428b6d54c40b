<template>
  <div class="scroll-y with-flex">
    <QueryForm
      :form-list="formList"
      :loading="loading"
      @search="handleSearch" 
      @reset="reset"
    />
    <div v-loading="loading" class="sticky-table">
      <CustomTable
        ref="tableRef"
        customize-head
        :columns="columns"
        :data="tableData"
        :table-method="getList"
        :operate-list="operateList"
      />
    </div>
  </div>
</template>

<script setup lang="tsx">
import { ref, reactive, toRef, onActivated } from 'vue'
import { ElMessage, ElButton, ElMessageBox } from 'element-plus'
import { useRouter, useRoute } from 'vue-router'
import { storeToRefs } from 'pinia'
import { useParamsStore } from '@/store'
import { useBasicStore } from '@/store/basic'
import useTryCatch from '@/hooks/use-try-catch'
import useLoading from '@/hooks/use-loading'
import { getList as getSiteList, siteStatus } from '@/api/site-config'
import type { queryParams, listItem } from '@/api/site-config'
import Listener from '@/utils/site-channel-listeners'

import QueryForm from '@/components/QueryForm/index.vue'
import CustomTable from '@/components/CustomTable/index.vue'

defineOptions( { name: 'SiteConfiguration' } )

const state_map = {
  'able': '启用',
  'disable': '禁用'
}

const route = useRoute()
const router = useRouter()
const { channel_item_all } = storeToRefs(useParamsStore())
const basicStore = useBasicStore()
const { loading, setLoading } = useLoading()
const queryParams_raw = {
  id: '',
  cms_id: '',
  site_name: '',
  html_path: '',
  channel_code: '',
  language: '',
  cms_state: '',
}
const queryParams = reactive<queryParams>({
  ...queryParams_raw,
  channel_code: channel_item_all.value.channel_code || ''
})
const formList = ref([
  {
    label: '站点映射ID',
    placeholder: '站点映射ID',
    value: toRef(queryParams, 'id'),
    component: 'input',
  },
  {
    label: 'CMS站点ID',
    placeholder: 'CMS站点ID',
    value: toRef(queryParams, 'cms_id'),
    component: 'input',
  },
  {
    label: '站点名称',
    placeholder: '站点名称',
    value: toRef(queryParams, 'site_name'),
    component: 'input',
  },
  {
    label: '静态根路径',
    placeholder: '静态根路径',
    value: toRef(queryParams, 'html_path'),
    component: 'input',
  },
  {
    label: '语言',
    placeholder: '语言',
    value: toRef(queryParams, 'language'),
    component: 'input',
  },
  {
    label: '状态',
    placeholder: '状态',
    value: toRef(queryParams, 'cms_state'),
    component: 'select',
    selections: [
      {
        label: '启用',
        value: 'able'
      },
      {
        label: '禁用',
        value: 'disable'
      }
    ]
  },
])
const tableRef = ref()
const columns = ref([
  {
    title: '站点映射ID',
    dataKey: 'id',
    width: 100,
  },
  {
    title: '站点名称',
    dataKey: 'site_name',
    minWidth: 180,
  },
  {
    title: 'cms站点ID',
    dataKey: 'cms_id',
    width: 100,
  },
  {
    title: '发布系统站点ID',
    dataKey: 'task_id',
    width: 134,
  },
  {
    title: '静态根路径',
    dataKey: 'html_path',
    minWidth: 240,
  },
  {
    title: '站点语言',
    dataKey: 'language',
    width: 100,
  },
  {
    title: '所属渠道',
    dataKey: 'channel_code',
    width: 100,
  },
  {
    title: '包含子渠道',
    dataKey: 'channels',
    minWidth: 100,
  },
  {
    title: '状态',
    dataKey: 'cms_state',
    width: 100,
    cellRenderer: (scope: { row: listItem }) => (
      <span style={ { color: scope.row.cms_state === 'able' ? 'var(--el-color-success)' : 'var(--el-color-danger)' } }>{ state_map[scope.row.cms_state] }</span>
    )
  },
  {
    title: '更新人',
    dataKey: 'user_name',
    width: 100,
  },
  {
    title: '更新时间',
    dataKey: 'edit_time',
    width: 180,
  },
  {
    title: '操作',
    dataKey: '',
    fixed: 'right',
    width: 260,
    cellRenderer: (scope: { row: listItem }) => (
      <>
        <ElButton type='primary' plain onClick={() => handleEdit(scope.row)}>编辑</ElButton>
        <ElButton type='primary' plain disabled={ !scope.row.cms_id } onClick={() => handleEditChannel(scope.row)}>编辑渠道</ElButton>
        <ElButton 
          type={ scope.row.cms_state === 'able' ? 'danger' : 'success' } 
          plain 
          onClick={() => handleOperate(scope.row)}
        >
          { state_map[scope.row.cms_state === 'able' ? 'disable' : 'able'] }
        </ElButton>
      </>
    )
  },
])
const operateList = ref([
  {
    title: '创建站点',
    button: true,
    append: true,
    method: () => {
      const name = 'SiteOperate'
      basicStore.delCachedView(name)
      router.push({
        name,
        query: {
          type: 'add'
        }
      })
    }
  }
])
const tableData = ref<listItem[]>([])

const reset = () => {
  Object.entries(queryParams_raw).forEach(([ key, value ]) => {
    queryParams[key] = value
  })
}
const handleSearch = () => {
  tableRef.value?.setPage(1)
  const { page, pageSize } = tableRef.value?.getPagination()
  getList({ page, pageSize })
}
const getList = ( { page, pageSize } ) => {
  useTryCatch( async () => {
    setLoading(true)
    const params = {
      S: {
        ...queryParams,
        channel_code: channel_item_all.value.channel_code || ''
      },
      page,
      page_size: pageSize
    }
    const res = await getSiteList(params)
    if (res.code === 200) {
      tableData.value = res.data.items
      tableRef.value?.setTotal(res.data.total)
    } else {
      ElMessage.error(res.msg)
    }

    setLoading(false)
  }, () => setLoading(false) )
}

const handleEdit = (row: listItem) => {
  const name = 'SiteOperate'
  basicStore.delCachedView(name)
  router.push({
    name,
    query: {
      type: 'edit',
      id: row.id,
      cms_id: row.cms_id,
      task_id: row.task_id
    }
  })
}

const handleEditChannel = (row: listItem) => {
  router.push({
    name: 'SiteConfigChannel',
    query: {
      id: row.cms_id
    }
  })
}

const handleOperate = (row: listItem) => {
  const isDisable = row.cms_state === 'able' ? false : true
  ElMessageBox.confirm(
    `请确认是否${isDisable ? '启用' : '禁用'}站点`,
    '提示',
    {
      dangerouslyUseHTMLString: true,
      confirmButtonText: '确认',
      cancelButtonText: '取消',
      callback: (action: string) => {
        if (action === 'confirm') {
          useTryCatch( async () => {
            setLoading(true)

            const res = await siteStatus(row.id, { state: isDisable ? 'able' : 'disable'})
            if (res.code === 200) {
              handleSearch()
              ElMessage.success(res.msg)
            } else {
              ElMessage.error(res.msg)
            }
            setLoading(false)
          }, () => setLoading(false) )
        }
      }
    }
  )
}

onActivated(() => {
  if (basicStore.refresh) {
    basicStore.setRefresh(false)
    handleSearch()
  }
})

Listener(route, () => {
  reset()
  handleSearch()
})
</script>