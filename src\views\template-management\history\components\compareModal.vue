<template>
  <el-dialog
    title="模板历史对比"
    v-model="show"
    top="3vh"
    width="95%"
    append-to-body
    @closed="emit('close')"
  >
    <template #default>
      <div style="overflow: auto; max-height: calc(100vh - 15vh);">
        <el-form label-suffix=":" label-width="120px" style="overflow: hidden">
          <el-row :gutter="20">
            <el-col :span="12">
              <el-card shadow="never" style="height: 100%;">
                <template #header>
                  模板-{{ tpl_id }} V{{ prev?.version }} 基础信息
                </template>
                <template #default>
                  <el-form-item
                    v-for="d in commonFields"
                    :label="d.label"
                    :style="{ color: prev?.[d.key] === current?.[d.key] ? '' : 'var(--el-color-success)' }"
                  >
                    {{ prev?.[d.key] }}
                  </el-form-item>
                  <el-form-item label="自定义字段">
                    <el-table :data="prev?.custom_fields">
                      <el-table-column prop="field_id" label="字段ID" show-overflow-tooltip />
                      <el-table-column prop="field_name" label="表字段名称" show-overflow-tooltip />
                      <el-table-column prop="input_label" label="字段名称" show-overflow-tooltip />
                      <el-table-column prop="input_type" label="输入框类型" show-overflow-tooltip />
                      <el-table-column prop="field_type" label="字段类型" show-overflow-tooltip />
                    </el-table>
                  </el-form-item>
                </template>
              </el-card>
            </el-col>
            <el-col :span="12">
              <el-card shadow="never" style="height: 100%;">
                <template #header>
                  模板-{{ tpl_id }} V{{ current?.version }} 基础信息
                </template>
                <template #default>
                  <el-form-item
                    v-for="d in commonFields"
                    :label="d.label"
                    :style="{ color: prev?.[d.key] === current?.[d.key] ? '' : 'var(--el-color-danger)' }"
                  >
                    {{ current?.[d.key] }}
                  </el-form-item>
                  <el-form-item label="自定义字段">
                    <el-table :data="current?.custom_fields">
                      <el-table-column prop="field_id" label="字段ID" show-overflow-tooltip />
                      <el-table-column prop="field_name" label="表字段名称" show-overflow-tooltip />
                      <el-table-column prop="input_label" label="字段名称" show-overflow-tooltip />
                      <el-table-column prop="input_type" label="输入框类型" show-overflow-tooltip />
                      <el-table-column prop="field_type" label="字段类型" show-overflow-tooltip />
                    </el-table>
                  </el-form-item>
                </template>
              </el-card>
            </el-col>
          </el-row>
        </el-form>
        <Diff 
          v-if="showDiff"
          :prev="(prev?.content as string)"
          :current="(current?.content as string)"
        />
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { ElMessage } from 'element-plus'
import useParamsStore from '@/store/modules/params'
import useTryCatch from '@/hooks/use-try-catch'
import { getComparisons } from '@/api/template-management'
import type { comparisonItem } from '@/api/template-management'

import Diff from '@/components/CodeDiff/index.vue'

const props = defineProps<{
  tpl_id: string|number;
  version_ids: string;
}>()
const emit = defineEmits(['close'])

const commonFields = [
  {
    label: '模版名称',
    key: 'name'
  },
  {
    label: '编辑人',
    key: 'action_name'
  },
  {
    label: '备份说明',
    key: 'remark'
  }
]

const { site_id_all } = useParamsStore()
const show = ref(true)
const showDiff = ref(false)

const prev = ref<comparisonItem>()
const current = ref<comparisonItem>()

const getDetail = () => {
  useTryCatch( async () => {
    const { tpl_id, version_ids } = props
    const res = await getComparisons(site_id_all, tpl_id, version_ids)
    if (res.code === 200) {
      prev.value = res.data[1]
      current.value = res.data[0]
      showDiff.value = true
    } else {
      ElMessage.error(res.msg)
    }
  } )
}

getDetail()
</script>