/*custom  variables*/
$--colors: (
  'primary': (
    'base': #589ef8
  ),
  'success': (
    'base': #21ba45
  ),
  'warning': (
    'base': #f2711c
  ),
  'danger': (
    'base': #db2828
  ),
  'error': (
    'base': #db2828
  ),
  'info': (
    'base': #42b8dd
  )
);

@forward "./var.scss" with (
  $colors: $--colors
);

/*import theme 起作用*/
@use "./css-vars.scss" as *;
@use '../vars' as *;
html.lighting-theme {
  /*element-plus section */
  //--el-menu-active-color: #409eff;
  //--el-menu-text-color: #bfcbd9;
  //--el-menu-hover-text-color: var(--el-color-primary);
  //--el-menu-bg-color: #304156;
  //--el-menu-hover-bg-color: #263445;
  //--el-menu-item-height: 56px;
  --el-menu-border-color: none;
  /*layout section*/
  //layout
  --layout-border-left-color: #ddd;
  //Breadcrumb
  --breadcrumb-no-redirect: #97a8be;
  //Hamburger
  --hamburger-width: 20px;
  --hamburger-height: 20px;
  //Sidebar
  --sidebar-el-icon-size: 20px;
  --sidebar-logo-background: #fff;
  --sidebar-logo-color: #ff9901;
  --sidebar-logo-width: 32px;
  --sidebar-logo-height: 32px;
  --sidebar-logo-title-color: #2b2f3a;
  --side-bar-width: 210px;
  --side-bar-border-right-color: '#ddd';
  --sidebar-icon-color: #989ba6;
  //TagsView
  --tags-view-background: #{$tags-view-background};
  --tags-view-border-bottom: #{$tags-view-border-bottom};
  --tags-view-box-shadow: #{$tags-view-box-shadow};
  --tags-view-item-background: #{$tags-view-item-background};
  --tags-view-item-border-color: #{$tags-view-item-border-color};
  --tags-view-item-color: #{$tags-view-item-color};
  --tag-view-height: #{$tag-view-height};
  --tags-view-item-active-background: #{$tags-view-item-active-background};
  --tags-view-item-active-color: #{$tags-view-item-active-color};
  --tags-view-item-active-border-color: #{$tags-view-item-active-border-color};
  --tags-view-contextmenu-background: #{$tags-view-contextmenu-background};
  --tags-view-contextmenu-color: #{$tags-view-contextmenu-color};
  --tags-view-contextmenu-box-shadow: #{$tags-view-contextmenu-box-shadow};
  --tags-view-contextmenu-hover-background: #{$tags-view-contextmenu-hover-background};
  --tags-view-contextmenu-hover-color: #{$tags-view-contextmenu-hover-color};
  //close-icon
  --tags-view-close-icon-hover-background: #{$tags-view-close-icon-hover-background};
  --tags-view-close-icon-hover-color: #{$tags-view-close-icon-hover-color};
  //AppMain.vue
  --app-main-padding: 16px;
  --app-main-background: #fff;
  //Navbar.vue
  --nav-bar-height: #{$nav-bar-height};
  --nav-bar-background: #fff;
  --nav-bar-box-shadow: 0 1px 4px rgba(0, 21, 41, 0.08);
  --nav-bar-right-menu-background: #fff;

  //transition 动画
  //侧边栏切换动画时长
  --sideBar-switch-duration: 0.2s;
  //logo切换动画时长
  --logo-switch-duration: 1.5s;
  //页面动画时长
  --page-transform-duration: 0.2s;
  //面包屑导航动画时长
  --breadcrumb-change-duration: 0.2s;

  //进度条颜色
  --pregress-bar-color: #409eff;

  background-color: #{$layout-background};

  // 组件样式override
  .el-table {
    --el-table-header-bg-color: #fafafa;
    --el-table-header-text-color: #17181f;
  }
  .el-dropdown__popper {
    --el-dropdown-menuItem-hover-fill: transparent;
  }
  --el-menu-bg-color: ${$layout-background};
  --bg-layout: #fff;
  --border-color-layout: transparent;
  --layout-border-radius: 0;
  --bg-file-item-color: #f6f8fc;
}
