<template>
  <input ref="inputRef" type="file" @change="handleFileAsync" style="display: none">
</template>

<script setup lang="ts">
import { ref } from 'vue'
import * as XLSX from 'xlsx'
import { ElMessage } from 'element-plus'

defineOptions( { name: 'CodeSplitImport' } )

const emit = defineEmits([ 'change' ])

const inputRef = ref()

const handleFileAsync = async (e) => {
  try {
    const file = e.target.files[0]
    const data = await file.arrayBuffer()
    const workbook = XLSX.read(data, { type: 'buffer' })

    const { split_text_sheet, split_link_sheet, split_img_sheet, origin_code_sheet } = workbook.Sheets

    const splitText: any[]|null = split_text_sheet ? XLSX.utils.sheet_to_json(split_text_sheet, {  header: 1  }) : null
    const splitLink: any[]|null = split_link_sheet ? XLSX.utils.sheet_to_json(split_link_sheet, {  header: 1  }) : null
    const splitImg: any[]|null = split_img_sheet ? XLSX.utils.sheet_to_json(split_img_sheet, {  header: 1  }) : null
    const originCode = origin_code_sheet ? XLSX.utils.sheet_to_json(origin_code_sheet, {  header: 1  })?.[0]?.[1] : ''

    if (!originCode) {
      return ElMessage.warning('此Excel文件暂不支持导入模式,请尝试其他模式')
    }

    splitText && splitText.shift()
    splitLink && splitLink.shift()
    splitImg && splitImg.shift()

    const targetTexts = splitText ? splitText.map(([ origin, target ]) => target) : null
    const targetLinks = splitLink ? splitLink.map(([ origin, target ]) => target) : null
    const targetImgs = splitImg ? splitImg.map(([ origin, target ]) => target) : null

    emit('change', originCode, targetTexts, targetLinks, targetImgs)
  }catch(e){
    emit('change', '', '')
  }
}

const handleExport = (
  sheets = { split_text_sheet: [], split_link_sheet: [], split_img_sheet: [] },
  rowHtmlString = '', 
  fileName = '代码文本分离 ' + new Date().toLocaleDateString() + ' ' + new Date().toLocaleTimeString(),
  sheetTitle = { split_text_sheet: [ '原文本', '目标文本' ], split_link_sheet: [ '原超链接', '目标超链接' ], split_img_sheet: [ '原图片url', '目标图片url' ] }
) => {
  const workbook = XLSX.utils.book_new()

  Object.entries(sheets).forEach(([sheetName, data]) => {
    if (data && data.length > 0) {
      const sheet = XLSX.utils.json_to_sheet(data)
      XLSX.utils.book_append_sheet(workbook, sheet, sheetName)
      XLSX.utils.sheet_add_aoa(sheet, [sheetTitle[sheetName]], { origin: "A1" })
    }
  })
  
  if (rowHtmlString.length < 32000) {
    const codeSheet = XLSX.utils.aoa_to_sheet([ [ '源code', rowHtmlString ] ])

    XLSX.utils.book_append_sheet(workbook, codeSheet, 'origin_code_sheet')
  } else {
    XLSX.utils.book_append_sheet(workbook, XLSX.utils.aoa_to_sheet([ [ '导入模式已禁用', '源代码超出Excel最大限制,请勿使用导入模式' ] ]), '提示')
  }
  
  try {
    XLSX.writeFile(workbook, fileName + '.xlsx')
  } catch (error: any) {
    ElMessage.error('导出失败, 请联系管理员处理')
    throw new Error(error)
  }
}

defineExpose({
  handleImport: () => {
    inputRef.value.click()
  },
  handleExport
})
</script>