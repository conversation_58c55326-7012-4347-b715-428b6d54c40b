<template>
  <div class="scroll-y with-flex">
    <el-tabs v-model="activeName" @tab-change="handleSearch">
      <el-tab-pane label="视频覆盖率" name="ratio" />
      <el-tab-pane label="youtube视频引用数" name="count" />
    </el-tabs>
    <div v-loading="loading" class="sticky-table">
      <CustomTable 
        ref="tableRef"
        :columns="columns"
        :data="tableData"
        :operate-list="operateList"
        :table-method="getList"
      />
    </div>
  </div>
</template>

<script setup lang="tsx">
import { ref, computed } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { ElMessage, ElLink, ElTooltip } from 'element-plus'
import { storeToRefs } from 'pinia'
import { useParamsStore } from '@/store'
import { useBasicStore } from '@/store/basic'
import useTryCatch from '@/hooks/use-try-catch'
import useLoading from '@/hooks/use-loading'
import { getHost } from '@/hooks/use-env'
import { videoOverview, getVideoCodeList } from '@/api/tools-management'
import Listener from '@/utils/site-channel-listeners'

import CustomTable from '@/components/CustomTable/index.vue'
import SvgIcon from '@/icons/SvgIcon.vue'

defineOptions( { name: 'VideoStatic' } )

type listItem = {
  all_nums: number;
  channel_id: number;
  channel_code: string;
  youtobe_page: number;
  other_video_page: number;
  video_page: number;
  youtobe_page_ratio: number;
  other_video_page_ratio: number;
  video_page_ratio: number;
}

const route = useRoute()
const router = useRouter()
const basicStore = useBasicStore()
const { site_id_all, channel_item_all } = storeToRefs(useParamsStore())
const { loading, setLoading } = useLoading()

const activeName = ref( 'ratio' )
const tableRef = ref()
const tableData = ref<listItem[]>([])
const operateList = ref([
  {
    title: '',
    append: true,
    icon: 'question',
    tooltip: '如果存在部署在块中的视频，块单独生成发布时，视频监控的相关表格的数据不会发生更新；只有对应的页面生成发布时，数据才会更新',
    method: () => {
      
    }
  },
  {
    title: '导出数据',
    append: true,
    method: () => {
      location.href = `${getHost()}/api/v1/page/video_overview?site_id=${site_id_all.value}&is_export=${1}`
    },
    disabled: computed( () => activeName.value !== 'ratio' )
  }
])
const columns = ref([
  {
    title: '渠道',
    dataKey: 'channel_code',
    width: 180,
    hideColumn: computed( () => activeName.value === 'count')
  },
  {
    title: 'youtube视频页面数',
    dataKey: 'youtobe_page',
    minWidth: 200,
    headerRenderer: () => (
      <>
        youtube视频页面数
        <ElTooltip content='指页面中部署了youtube类等外部视频平台的视频，并且页面状态处于发布上线的页面总数'>
          <SvgIcon iconClass='question' class='ml-2' />
        </ElTooltip>
      </>
    ),
    cellRenderer: ( scope: { row: listItem } ) => (
      <>
        <ElLink type='primary' onClick={() => handleToPage(scope.row.channel_id, 'youtube')}>{ scope.row.youtobe_page }</ElLink>
      </>
    ),
    hideColumn: computed( () => activeName.value === 'count')
  },
  {
    title: '视频文件页面数',
    dataKey: 'other_video_page',
    minWidth: 180,
    headerRenderer: () => (
      <>
        视频文件页面数
        <ElTooltip content='指页面中部署了直接的视频文件，例如mp4视频等，并且页面状态处于发布上线的页面总数。当前包含(mp4、avi、mvasf、asx、rmvb、m4v、avidat、mkv、flv、vob、3gp、mov)'>
          <SvgIcon iconClass='question' class='ml-2' />
        </ElTooltip>
      </>
    ),
    cellRenderer: ( scope: { row: listItem } ) => (
      <>
        <ElLink type='primary' onClick={() => handleToPage(scope.row.channel_id, 'other')}>{ scope.row.other_video_page }</ElLink>
      </>
    ),
    hideColumn: computed( () => activeName.value === 'count')
  },
  {
    title: '包含视频页面数',
    dataKey: 'video_page',
    minWidth: 200,
    headerRenderer: () => (
      <>
        包含视频页面数
        <ElTooltip content='页面中包含了各种类型视频(youtube视频/视频文件)，会有部分页面同时部署了两种类型的视频，已进行去重统计'>
          <SvgIcon iconClass='question' class='ml-2' />
        </ElTooltip>
      </>
    ),
    cellRenderer: ( scope: { row: listItem } ) => (
      <>
        <ElLink type='primary' onClick={() => handleToPage(scope.row.channel_id, 'all')}>{ scope.row.video_page }</ElLink>
      </>
    ),
    hideColumn: computed( () => activeName.value === 'count')
  },
  {
    title: 'youtube视频覆盖率',
    dataKey: 'youtobe_page_ratio',
    minWidth: 200,
    hideColumn: computed( () => activeName.value === 'count')
  },
  {
    title: '其他类型视频覆盖率',
    dataKey: 'other_video_page_ratio',
    minWidth: 200,
    hideColumn: computed( () => activeName.value === 'count')
  },
  {
    title: '视频覆盖率',
    dataKey: 'video_page_ratio',
    minWidth: 180,
    hideColumn: computed( () => activeName.value === 'count')
  },

  {
    title: 'youtube视频参数',
    dataKey: 'video_code',
    minWidth: 200,
    hideColumn: computed( () => activeName.value === 'ratio')
  },
  {
    title: 'youtube内嵌视频链接',
    dataKey: '',
    minWidth: 300,
    cellRenderer: (scope: { row: { video_code: string; page_ids: string; page_num: number } }) => (
      <>
        <ElLink type='primary'>
          <a href={`https://www.youtube.com/embed/${scope.row.video_code}`} target='_blank'>
            https://www.youtube.com/embed/{ scope.row.video_code }
          </a>
        </ElLink>
      </>
    ),
    hideColumn: computed( () => activeName.value === 'ratio')
  },
  {
    title: '被引用的页面数',
    dataKey: 'page_num',
    width: 150,
    cellRenderer: (scope: { row: { video_code: string; page_ids: string; page_num: number } }) => (
      <>
        <ElLink type='primary' onClick={() => handleJumpPage(scope.row.page_ids)}>
          <u>{scope.row.page_num}</u>
        </ElLink>
      </>
    ),
    hideColumn: computed( () => activeName.value === 'ratio')
  }
])

const handleSearch = () => {
  tableRef.value?.setPage(1)
  const { page, pageSize } = tableRef?.value?.getPagination()
  getList( { page, pageSize } )
}

const getList = ( { page, pageSize } ) => {
  if (activeName.value === 'ratio') {
    getVideoList()
  } else {
    getCodeList( { page, pageSize } )
  }
}

const getVideoList = () => {
  useTryCatch( async () => {
    setLoading(true)

    const res = await videoOverview(site_id_all.value, 0)
    if (res.code === 200) {
      tableData.value = res.data
      tableRef.value?.setTotal(tableData.value.length)
    } else {
      ElMessage.error(res.msg)
    }

    setLoading(false)
  }, () => setLoading(false) )
}

const getCodeList = ( { page, pageSize } ) => {
  useTryCatch( async () => {
    setLoading(true)

    const params = {
      site_id: site_id_all.value,
      channel_id: channel_item_all.value.channel_id || 0,
      page,
      page_size: pageSize
    }
    const res = await getVideoCodeList(params)
    if (res.code === 200) {
      tableData.value = res.data.list
      tableRef.value?.setTotal(res.data.total)
    } else {
      ElMessage.error(res.msg)
    }

    setLoading(false)
  }, () => setLoading(false) )
}

const handleToPage = (channel_id: number, video_type: 'youtube'|'other'|'all') => {
  basicStore.delCachedView('PageManagement')
  router.push({
    name: 'PageManagement',
    query: {
      channel_id,
      video_type
    }
  })
}

const handleJumpPage = (page_ids: string) => {
  basicStore.delCachedView('PageManagement')
  router.push({
    name: 'PageManagement',
    query: {
      entity_ids: page_ids
    }
  })
}

Listener( route, () => {
  handleSearch()
} )
</script>