<template>
  <div class="scroll-y">
    <div style="padding: 50px;">
      <h2 class="title-prepend"><strong>消息设置</strong></h2>
      <div class="mb-36px pl-24px">
        <div class="rowSS">
          <strong class="pt-6px">弹窗提醒: </strong>
          <div class="pl-24px">
            <div class="mb-20px">
              <el-radio-group v-model="popConfig.globalPop" @change="handlePopChange">
                <el-radio label="1">开启</el-radio>
                <el-radio label="0">关闭</el-radio>
              </el-radio-group>
            </div>
          </div>
        </div>
      </div>

      <h2 class="title-prepend"><strong>主题设置</strong></h2>
      <div class="mb-36px pl-24px">
        <div class="rowSS">
          <strong class="pt-6px">主题选择: </strong>
          <div class="pl-24px">
            <div class="mb-20px">
              <el-radio-group v-model="theme" @change="handleThemeChange">
                <el-radio label="lighting-theme">白色主题</el-radio>
                <el-radio label="dark">黑色主题</el-radio>
              </el-radio-group>
            </div>
          </div>
        </div>
      </div>

      <h2 class="title-prepend"><strong>渠道设置</strong></h2>
      <div class="mb-36px pl-24px">
        <div class="rowSS">
          <strong class="pt-6px">渠道设置: </strong>
          <div class="pl-24px">
            <div class="mb-20px">
              <el-button type="primary" @click="loadChannelSetting = true"> 渠道只读设置 </el-button>
              <p style="color: var(--el-color-danger); font-size: 12px;"> 进行此设置后, 在对应渠道操作时, 除部分特殊场景外, 只能查看, 无法编辑保存 </p>
            </div>
          </div>
        </div>
      </div>
    </div>

    <ChannelSetting v-if="loadChannelSetting" @close="loadChannelSetting = false" />
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, defineAsyncComponent } from 'vue'
import { storeToRefs } from 'pinia/dist/pinia'
import { useConfigStore } from '@/store/config'

const ChannelSetting = defineAsyncComponent( () => import('./components/channelSetting.vue') )

const configStore = useConfigStore()

const popConfig_raw = localStorage.getItem('noticePop')

const popConfig_obj = popConfig_raw ? JSON.parse(popConfig_raw) : {
  globalPop: '0',
  sysPop: '0',
  businessPop: '0'
}

const popConfig = reactive(popConfig_obj)
const { theme } = storeToRefs(configStore)

const loadChannelSetting = ref(false)

const handlePopChange = () => {
  localStorage.setItem('noticePop', JSON.stringify(popConfig))
}

const handleThemeChange = () => {
  configStore.setTheme(theme.value)
}
</script>

<style lang="scss">
.title-prepend {
  display: flex;
  align-items: center;
  margin-bottom: 36px;
  &::before {
    content: '';
    display: inline-block;
    width: 6px;
    height: 24px;
    background: var(--el-color-primary);
    margin-right: 10px;
  }
}
</style>