<template>
  <div ref="listContainerRef" class="list-mode" v-loading="loading">
    <CustomTable
      ref="tableRef"
      :columns="columns"
      :data="tableData"
      :table-method="getList"
      :border="false"
      :height="height"
      selectable
      @selection-change="handleSelectionChange"
    />
  </div>
</template>

<script setup lang="tsx">
import { ref, onMounted } from 'vue'
import { ElMessage, ElButton, ElDropdown, ElDropdownMenu, ElDropdownItem } from 'element-plus'
import CustomTable from '@/components/CustomTable/index.vue'
import useLoading from '@/hooks/use-loading'
import useTryCatch from '@/hooks/use-try-catch'
import { getFileList } from '@/api/resource-center'
import { statusMap, getSelectFileItem, fileSuffixReg, imgReg } from './types'
import type { fileItem, fileQueryParams } from '@/api/resource-center'

import SvgIcon from '@/icons/SvgIcon.vue'

const props = defineProps<{
  fileListParams: fileQueryParams;
  formatSize: any;
}>()
const emit = defineEmits([
  'selectionChange',
  'copy',
  'preview',
  'rename',
  'download',
  'delete',
  'compress',
  'filelog'
])

const { loading, setLoading } = useLoading()
const listContainerRef = ref()
const height = ref('0')
const tableRef = ref()
const tableData = ref<fileItem[]>([])
const columns = ref([
  {
    title: '状态',
    dataKey: 'publish',
    width: 100,
    cellRenderer: (scope: { row: fileItem }) => (
      <span 
        style={ { '--bg-color': scope.row.publish == 3 ? 'var(--el-color-success)' : scope.row.publish == 4 ? 'var(--el-color-danger)' : 'var(--el-color-primary)' } }
        class='tag'
      >
        { statusMap.get(scope.row.publish) }
      </span>
    )
  },
  {
    title: '',
    dataKey: '',
    width: 54,
    cellRenderer: (scope: { row: fileItem }) => (
      <SvgIcon 
        iconClass={fileSuffixReg.test(scope.row.suffix) ? scope.row.suffix : 'file'}
        style='width: 24px; height: 24px'
      />
    )
  },
  {
    title: '文件名',
    dataKey: 'file_name',
    minWidth: 150,
  },
  { 
    title: '线上URL',
    dataKey: 'file_url',
    minWidth: 200,
    cellRenderer: (scope: { row: fileItem }) => (
      <a href={scope.row.file_url}  target='_blank' > { scope.row.file_url } </a>
    )
  },
  {
    title: '添加时间',
    dataKey: 'add_time',
    width: 180,
  },
  {
    title: '添加人',
    dataKey: 'add_name',
    width: 80,
  },
  {
    title: '更新时间',
    dataKey: 'edit_time',
    width: 180,
  },
  {
    title: '更新人',
    dataKey: 'edit_name',
    width: 80,
  },
  {
    title: '文件大小',
    dataKey: 'file_size',
    width: 120,
    cellRenderer: (scope: { row: fileItem }) => (
      <>
        { props.formatSize(scope.row.file_size) }
      </>
    )
  },
  {
    title: '操作',
    dataKey: '',
    fixed: 'right',
    width: 300,
    cellRenderer: (scope: { row: fileItem }) => (
      <>
      <ElButton type='primary' plain onClick={() => handleCopy(scope.row)}> 复制链接 </ElButton>
        <ElButton type='primary' plain onClick={() => handlePreview(scope.row)}> 查看 </ElButton>
        <ElButton type='primary' plain onClick={() => handleRename(scope.row)}> 重命名 </ElButton>
        <ElDropdown trigger='click' style={{ marginLeft: '10px' }}>
          {
            {
              default: () => <ElButton type='primary' link> <strong>...</strong> </ElButton>,
              dropdown: () => 
              <ElDropdownMenu>
                <ElDropdownItem>
                  <ElButton type='primary' plain onClick={() => handleDownload(scope.row)}> 下载 </ElButton>
                  {
                    imgReg.test(scope.row.suffix) && <ElButton type='primary' plain onClick={() => handleCompress(scope.row)}> 压缩图片 </ElButton>
                  }
                  <ElButton  type='primary' plain onClick={() => handleFileLog(scope.row)}> 文件历史 </ElButton>
                  <ElButton type='danger' plain onClick={() => handleDelete(scope.row)}> 删除 </ElButton>
                </ElDropdownItem>  
              </ElDropdownMenu>
            }
          }
        </ElDropdown>
      </>
    )
  }
])

const getList = ( {page, pageSize} ) => {
  useTryCatch( async () => {
    setLoading(true)
    const params = {
      ...props.fileListParams,
      page,
      page_size: pageSize
    }

    const res = await getFileList(params)
    if (res.code === 200) {
      tableData.value = res.data.list
      tableRef.value?.setTotal(res.data.total)
    } else {
      ElMessage.error(res.msg)
    }
    setLoading(false)
  }, () => setLoading(false) )
}

const refresh = () => {
  const { pageSize } = tableRef.value?.getPagination()
  getList( { page: 1, pageSize } )
}

const handleSelectionChange = (selections: fileItem[]) => {
  emit('selectionChange', selections)
}

const handleCopy = (item: fileItem) => {
  emit('copy', getSelectFileItem(item))
}
const handlePreview = (item: fileItem) => {
  emit('preview', getSelectFileItem(item))
}
const handleRename = (item: fileItem) => {
  emit('rename', getSelectFileItem(item))
}
const handleDownload = (item: fileItem) => {
  emit('download', getSelectFileItem(item))
}
const handleDelete = (item: fileItem) => {
  emit('delete', getSelectFileItem(item))
}
const handleCompress = (item: fileItem) => {
  emit('compress', getSelectFileItem(item))
}
const handleFileLog = (item: fileItem) => {
  emit('filelog', getSelectFileItem(item))
}

defineExpose({
  refresh
})

onMounted(() => {
  height.value = listContainerRef.value.getBoundingClientRect().height - 60 + 'px'
})
</script>

<style lang="scss" scoped>
.list-mode {
  width: 100%;
  height: var(--scroll-height, 100%);
  --radius: 2px;
}
</style>