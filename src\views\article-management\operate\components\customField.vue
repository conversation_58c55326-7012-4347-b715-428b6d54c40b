<template>
  <el-form-item label="拓展字段">
    <div v-for="(d, index) in extend" class="extend-item">
      <strong>拓展字段{{ index + 1 }}</strong> <br>
      <Close class="close" @click="handleDelete(index)" />
      <el-space :size="10" nowrap style="padding-bottom: 12px;">
        <span class="label">字段名称：</span>
        <div class="form" :class="{ 'is-error': isError(d.desc) }">
          <el-input v-model="d.desc" placeholder="字段名称" />
          <span class="error-info"> 只能输入字母、数字以及下划线 </span>
        </div>
      </el-space>
      <el-space :size="10" nowrap>
        <span class="label">字段值：</span>
        <el-input v-model="d.value" placeholder="字段值"/>
      </el-space>
    </div>
    <el-button v-if="extend.length < limit" type="primary" :icon="Plus" @click="handleAdd"> 添加字段 </el-button>
  </el-form-item>
</template>

<script setup lang="ts">
import { ElMessage } from 'element-plus'
import { Plus, Close } from '@element-plus/icons-vue'

const keyReg = /^[a-zA-Z\d\_]{0,}$/
const isError = (key: string) => !keyReg.test(key)

const props = withDefaults(defineProps<{
  limit?: number;
  extend: { value: string; desc: string; }[]
}>(), {
  limit: 5
})

const handleAdd = () => {
  const { extend, limit } = props
  if ( extend.length === limit ) {
    return ElMessage.warning(`最多添加${limit}个`)
  }
  const item = {
    value: '',
    desc: ''
  }
  extend.push(item)
}
const handleDelete = (index: number) => {
  const { extend } = props
  extend.splice(index, 1)
}
</script>

<style lang="scss">
.extend-item {
  border: 1px dashed var(--el-border-color-light); 
  margin-bottom: 10px;
  padding: 10px; 
  border-radius: 6px;
  position: relative;
  .label {
    display: inline-block;
    width: 70px;
    text-align: right;
  }
  .close {
    width: 16px;
    height: 16px;
    position: absolute;
    right: 0px;
    top: 5px;
    cursor: pointer;
  }
  .form {
    position: relative;
    .error-info {
      color: var(--el-color-danger);
      font-size: 12px;
      line-height: 1;
      position: absolute;
      width: 100%;
      left: 0;
      bottom: 0;
      transform: translateY(100%);
      opacity: 0;
    }
    &.is-error {
      .el-input__wrapper {
        box-shadow: 0 0 0 1px var(--el-color-danger) inset;
      }
      .error-info {
        opacity: 1;
      }
    }
  }
}
.hide .el-form-item__label {
    opacity: 0;
  }
</style>