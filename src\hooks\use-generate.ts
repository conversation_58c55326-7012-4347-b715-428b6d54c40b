// 全局业务生成处理逻辑
import { ref, h } from 'vue'
import { ElMessage, ElMessageBox, ElRadioGroup, ElRadio, ElButton, ElInput } from 'element-plus'
import useTryCatch from './use-try-catch'
import useLoading from './use-loading'
import { pageGenerate, tempGenerate, blockGenerate } from '@/api/generate-manegement'
import type { pageGenerateData, tempGenerateData, blockGenerateData } from '@/api/generate-manegement'
import { useTaskRecordStore, useParamsStore } from '@/store'
import emitter from '@/utils/bus'
import type { typeEmnu } from '@/store/modules/task-record'

const api = {
  'page': pageGenerate,
  'template': tempGenerate,
  'block': blockGenerate
}
const loadingSet = new Set<typeEmnu>()
const { appendRecord, typeMap } = useTaskRecordStore()
const { loading, setLoading } = useLoading()

const getSiteChannelInfo = () => {
  const { site_id_all, site_name_all, channel_item_all } = useParamsStore()
  return {
    site_id: site_id_all,
    site_name: site_name_all,
    channel_id: channel_item_all?.channel_id || 0,
    channel_name: channel_item_all?.channel_code || ''
  }
}

const confirm = ( type: typeEmnu, data: any, callback?: () => any ) => {
  if (loadingSet.has(type)) {
    return ElMessage.warning('正在进行生成操作, 请稍后再试')
  }
  const store_buy_type = ref(data.store_buy_type || 1)
  const remark = ref('')
  ElMessageBox(
    {
      title: `${data.store_buy_type ? '[购买页]' : ''}${typeMap[type].replace('任务', '发布确认')}`,
      showCancelButton: false,
      showConfirmButton: false,
      callback: () => {},
      message: () => h('div',  null, [
        h('span', null, 
          !data.store_buy_type 
          ? [
            '请确认是否进行生成操作, 生成操作将触发页面/块文件发布至线上(A级页面/块需审核)',
            type === 'block' ? h('strong', null, '，生成发布块后, 自动会将引用了此块的线上页面, 进行CDN的刷新') : ''
          ] : '请确认购买页生成发布内容，生成操作将触发购买页发布上线'
        ),
        ...data.store_buy_type ? [
          h('div', { style: { textAlign: 'center', marginTop: '20px'} }, h(ElRadioGroup, {
            modelValue: store_buy_type.value,
            onChange: (val) => {
              store_buy_type.value = val
            }
          }, () => [
            h(ElRadio, { label: 1, disabled: type === 'template' }, () => '仅修改文案'),
            h(ElRadio, { label: 2 }, () => '新增/修改其他内容')
          ]))
        ] : [],
        h(ElInput, {
          style: { margin: '20px 0' },
          class: { 'is-required absolute': store_buy_type.value === 2 },
          type: 'textarea',
          placeholder: `请输入生成备注信息${store_buy_type.value === 2 ? '（必填）' : '（选填）'}`,
          modelValue: remark.value,
          'onUpdate:modelValue': (val) => remark.value = val
        }),
        h('div', { style: { textAlign: 'right', marginBottom: '-10px' } }, h(ElButton, {
          loading: loading.value,
          disabled: store_buy_type.value === 2 && !remark.value,
          type: 'primary',
          onClick: () => {
            if (remark.value) {
              data.remark = remark.value
            }
            if (data.store_buy_type) {
              data.store_buy_type = store_buy_type.value
            }
            useTryCatch( async () => {
              loadingSet.add(type)
              setLoading(true)
              const res = await api[type](data)
              if (res.code === 200) {
                appendRecord({
                  title: typeMap[type],
                  type: '1,2',
                  task_id: res.data.task_id || '',
                  ...getSiteChannelInfo()
                })
                ElMessageBox.close()
                ElMessage.success('操作成功')
                emitter.emit('noticeUpdate')
                callback && callback()
              } else {
                ElMessage.error(res.msg)
              }
          
              loadingSet.delete(type)
              setTimeout(() => {
                setLoading(false)
              }, 500)
            }, () => {
              loadingSet.delete(type)
              setLoading(false)
            } )      
          }
        }, () => '确认'))
      ])
    })
}

export const usePageGenerate = (data: pageGenerateData, callback?: () => any) => {
  confirm('page', data, callback)
}

export const useBlockGenerate = (data: blockGenerateData, callback?: () => any) => {
  confirm('block', { data }, callback)
}

export const useTempGenerate = (data: tempGenerateData, callback?: () => any) => {
  confirm('template', data, callback)
}