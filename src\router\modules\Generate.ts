import type { moduleMap } from '../aysncRoutes'
export default {
  'GeneratePublishRecord': {
    component: () => import('@/views/generate-management/record/index.vue'),
    path: 'generate-publish-record',
    title: '发布记录',
    cachePage: true,
  },
  'GeneratePublishDetail': {
    component: () => import('@/views/generate-management/record/detail.vue'),
    path: 'generate-publish-detail',
    title: '发布详情',
    hidden: true,
  },
} as {
  [propName: string]: moduleMap
}