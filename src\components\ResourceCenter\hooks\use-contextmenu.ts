import { nextTick } from 'vue'
import { handleIsRootNode, getSelectFileItem } from '../types'
import type { Ref } from "vue"
import type Node from 'element-plus/es/components/tree/src/model/node'
import type { fileItem } from '@/api/resource-center'
import type { selectFileItem } from '../types'

export default 
( contextMenuRef: Ref<HTMLInputElement|null>,
  contextMenuVisible: Ref<boolean>,
  menuType: Ref<number>,
  menuNode: Ref<Node|null>,
  menuFile: Ref<selectFileItem|null>,
  menuLeft: Ref<number|string>,
  menuTop: Ref<number|string>,
  isContextRootNode: Ref<boolean>,
  showBottom: boolean|undefined,
) => {
  // 处理菜单展示位置
  const handleContextmenuPosition = (e: MouseEvent) => {
    const { clientX, clientY } = e
    menuLeft.value = `${clientX}px`
    menuTop.value = `${clientY}px`
    contextMenuVisible.value = true

    nextTick(() => {
      const contextMenu = contextMenuRef.value
      if (contextMenu) {
        const { top, height, left, width } = contextMenu.getBoundingClientRect()
        const overY = window.innerHeight - top - height
        const overX = window.innerWidth - left - width
        contextMenu.style.transform = `translate(${overX > 0 ? 0 : overX}px, ${overY > 0 ? 0 : overY}px)`
      }
    })
  }
  // 重置菜单展示位置
  const handleContextmenuPositionReset = () => {
    const contextMenu = contextMenuRef.value
    if (contextMenu) {
      contextMenu.style.transform = `translate(0, 0)`
    }
  }
  return {
    // 右键点击树形组件
    handleContextmenu: (e: MouseEvent, data: any, node: Node) => {
      if (showBottom) return
      menuType.value = 1
      menuNode.value = node
      isContextRootNode.value = handleIsRootNode(node)
      handleContextmenuPositionReset()
      handleContextmenuPosition(e)
    },
    // 右键点击文件区域
    handleFileContextmenu: (e: MouseEvent, fileItem: fileItem) => {
      if (showBottom) return
      menuType.value = 2
      handleContextmenuPositionReset()
      handleContextmenuPosition(e)

      // 此处处理成统一数据格式，便于共享操作方法
      menuFile.value = getSelectFileItem(fileItem)
    },
    handCloseContextMenu() {
      menuFile.value = null
      contextMenuVisible.value = false
    }
  }
}