

# node版本要求 >= 16.0.0。

```bash
# 使用pnpm进行包管理
npm install pnpm@7.9.0

# 安装依赖
pnpm i

# 打包构建
pnpm run build
```

# git管理

## 分支管理规范
```
创建本地开发分支
git checkout -b cmsnew/brach-name origin 主分支

推送本地开发分支至远程
git push origin cmsnew/brach-name

远程进行将开发分支合并至主分支
```

## step1:
```
首先在开发分支同步拉取远程主分支
git pull origin 主分支
```

## step2:
```
完成本地commit后推送同名开发分支至远程
git commit
git push 
```

## step3:
```
git仓库进行合并
不删除开发分支
多个commit进行squash
```

## 注意事项
```
非特殊情况不要在本地合并后推送至主分支
主分支仅进行merge操作
开发分支进行迭代前一定进行一次pull远程主分支操作
```

