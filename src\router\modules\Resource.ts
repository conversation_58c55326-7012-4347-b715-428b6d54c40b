import type { moduleMap } from '../aysncRoutes'
export default {
  'ResourceManagemnt': {
    component: () => import('@/views/resource-center/index.vue'),
    path: 'resource-management',
    title: '资源管理',
    cachePage: true,
    icon: 'resource',
  },
  // 'FileManagement': {
  //   component: () => import('@/views/resource-center/file.vue'),
  //   path: 'file-management',
  //   title: '文件管理',
  //   cachePage: true,
  // },
  // 'ImageManagement': {
  //   component: () => import('@/views/resource-center/image.vue'),
  //   path: 'image-management',
  //   title: '图片管理',
  //   cachePage: true,
  // },
  // 'VideoManagement': {
  //   component: () => import('@/views/resource-center/video.vue'),
  //   path: 'video-management',
  //   title: '视频管理',
  //   cachePage: true,
  // }
} as {
  [propName: string]: moduleMap
}