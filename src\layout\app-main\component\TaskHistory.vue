<template>
  <div 
    ref="dragRef" 
    class="global-task-record"
    :style="{ top: `${top}px` }"
  >
    <div 
      ref="targetRef" 
      v-show="recordList.length > 0"
      title="生成发布任务历史"
      :class="{ 'drag-transiton': dragEnd }"
    >
      <el-badge 
        class="record-badge"
        :value="recordList.length" 
        :max="99" 
        :hidden="!showNum"
      >
        <svg-icon 
          icon-class="history" 
          class="record-trigger"
          @click="handleOpen"
        />
      </el-badge>
    </div>
  </div>

  <el-drawer v-model="drawer" direction="rtl" :size="400">
    <template #header>
      <h4> 生成发布任务历史 </h4>
    </template>
    <template #default>
      <el-space direction="vertical" fill style="width: 100%;" :size="20">
        <el-card 
          v-for="(d, index) in recordList" 
          :key="d.time" 
          class="record-item"
          shadow="never"
        >
          <template #header>
            <div class="rowBC">
              <strong>{{ d.title }}</strong>
              <Close class="close" @click="handleDelete(index)" />
            </div>
          </template>
          <template #default>
            您在<span class="highlight">{{ d.time }}</span>有发布任务生成,
            <template v-if="d.task_type === 'upload'">共包含<strong class="highlight">{{ d.file_num }}</strong>个文件,</template>
            任务ID为<strong class="highlight">{{ d.task_id }}</strong>,
            此任务在<span class="highlight">{{d.site_name}}</span>站点<template v-if="d.channel_name">, <span class="highlight">{{ d.channel_name }}</span>渠道</template>下操作
            <div class="footer text-right">
              <el-link type="primary" @click="handleDetail(d, index)"> 查看详情 </el-link>
            </div>
          </template>
        </el-card>
      </el-space>
    </template>
  </el-drawer>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { ElMessageBox } from 'element-plus'
import { Close } from '@element-plus/icons-vue'
import { storeToRefs } from 'pinia'
import { useTaskRecordStore, useParamsStore } from '@/store'
import { useBasicStore } from '@/store/basic'
import { useDraggable } from '@/hooks/use-draggable'
import emitter from '@/utils/bus'
import type { recordItem } from '@/store/modules/task-record'

import SvgIcon from '@/icons/SvgIcon.vue'

const route = useRoute()
const router = useRouter()
const { site_id_all, channel_item_all } = storeToRefs(useParamsStore())
const { recordList } = storeToRefs(useTaskRecordStore())
const { removeRecord } = useTaskRecordStore()
const basicStore = useBasicStore()

const dragRef = ref<HTMLElement|undefined>()
const targetRef = ref<HTMLElement|undefined>()
const dragging = ref(false)
const drawer = ref(false)
const showNum = ref(true)
const dragEnd = ref(true)

const top = window.localStorage.getItem('task_container_top') || 500

const handleOpen = () => {
  if (dragging.value) {
    return
  }
  drawer.value = true
}
const handleDelete = (index: number) => {
  removeRecord(index)
}
const handleDetail = async (item: recordItem, index: number) => {
  const { site_id, channel_id } = item
  let diff_count = 0
  if (site_id && site_id !== site_id_all.value) {
    diff_count++
  }
  if (channel_id && channel_item_all.value.channel_id && ( channel_id !== channel_item_all.value.channel_id ) ) {
    diff_count++
  }
  if (diff_count > 0) {
    ElMessageBox.confirm(
      `检测到当前系统${diff_count === 1 ? '站点' : '站点及渠道'}与任务不一致, 建议先切换至相同站点及渠道, 否则无法查看数据, 请确认是否仍要进行操作?`, 
      '提示',
      {
        type: 'warning',
        callback: (action: string) => {
          if (action === 'confirm') {
            handleJump(item)
          }
        }
      }
    )
    return
  }
  handleJump(item)
}
const handleJump = async (item: recordItem) => {
  drawer.value = false
  basicStore.delCachedView('GeneratePublishRecord')
  await router.push({
    name: 'GeneratePublishRecord',
    query: {
      task_id: item.task_id,
      type: item.type
    }
  })
  if (route.name === 'GeneratePublishRecord') {
    emitter.emit(route.name)
  }
}

useDraggable(targetRef, dragRef, computed( () => ref(true).value ), {
  down: () => {
    dragging.value = true
    dragEnd.value = false
  },
  up: (dragTrans) => {
    dragEnd.value = true
    setTimeout(() => {
      const transform = targetRef.value!.style.transform
      const top = targetRef.value!.getBoundingClientRect().top
      targetRef.value!.style.transform = transform.replace(/-?\d+px/, '0px')
      dragTrans.offsetX = 0
      dragging.value = false
      window.localStorage.setItem('task_container_top', `${top}`)
    }, 0)
  }
})
</script>

<style lang="scss">
.global-task-record {
  position: fixed;
  right: 0;
  top: 500px;
  transition: transform .3s;
  z-index: 1099;
}
.drag-transiton {
  transition: transform 1s;
}
.record-trigger {
  cursor: pointer;
  font-size: 1.5em;
}
.el-card.record-item {
  --el-card-padding: 10px;
  font-size: 14px;
  .highlight {
    color: var(--el-color-primary);
  }
}
.close {
  width: 1em;
  cursor: pointer;
  &:hover {
    color: var(--el-color-primary);
  }
}
.record-badge {
  transition: transform .3s;
  transform: translateX(60%);
  &:hover {
    transform: translateX(0);
  }
  .el-badge__content.is-fixed  {
    right: auto;
    left: -30px;
  }
}
</style>