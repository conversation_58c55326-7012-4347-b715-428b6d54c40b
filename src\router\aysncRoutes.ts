import Layout from '@/layout/index.vue'

import type { Component } from 'vue'
import Audit from './modules/Audit'
import Article from './modules/Article'
import Site from './modules/Site'
import Generate from './modules/Generate'
import Resource from './modules/Resource'
import Page from './modules/Page'
import Business from './modules/Business'
import Tools from './modules/Tools'
import Translte from './modules/Translate'
import Web from './modules/Web'
import User from './modules/User'
import Quality from './modules/Quality'
import Subscribe from './modules/Subscribe'
import Buy from './modules/Buy'
import Task from './modules/Task'

import type { cmsUserInfo } from '@/store/modules/user/types'

// 后台配置路由名与vue-router配置的映射关系
export type childModule = {
  component: Component;
  path: string;
  name?: string;
  hidden?: boolean;
  meta: {
    title: string;
    cachePage?: boolean;
    icon?: string;
    plainLayout?: boolean;
    leaveRmCachePage?: boolean;
    forbidden?: boolean;
  }
}
export type moduleMap = {
  component: Component;
  path: string;
  title: string; // 覆盖后台配置title
  cachePage?: boolean; // 此处缓存优先级最高， 非菜单页面按需配置
  icon?: string;
  hidden?: boolean;
  children?: childModule[];
  queryRequired?: boolean;  // 必须携带query才能访问的路由，兼容一些缓存场景
  hasDropdown?: boolean;  // 是否存在下拉菜单，相同tag历史记录回溯
  validate?: (userInfo: cmsUserInfo) => boolean;  // 校验通过方可访问及渲染，未通过需动态添加hidden
}

const getChildren = ( RouteModule: { [propName: string]: moduleMap }, userInfo?: cmsUserInfo ) => {
  return Object.entries(RouteModule).map(( [ key, value ] ) => {
    const { validate } = value
    return {
      name: key,
      path: value.path,
      component: value.component,
      hidden: value.hidden || ((validate && userInfo) ? !validate(userInfo) : false),
      meta: {
        title: value.title,
        cachePage: value.cachePage,
        icon: value.icon,
        closeTabRmCache: true,
        queryRequired: value.queryRequired ? true : false,
        hasDropdown: value.hasDropdown ? true : false,
        forbidden: (validate && userInfo) ? !validate(userInfo) : false
      },
      ...value.children ? {
        children: value.children
      } : {}
    }
  })
}


export const routeMap = new Map([
  ['/user-center', { name: 'user-center', title: '工作台', icon: 'workbench', children: [...getChildren(User)] }],
  ['/site-config', { name: 'site-config', title: '站点配置', icon: 'system-setting', children: [...getChildren(Site)] }],
  ['/page-management', { name: 'page-management', title: '页面管理', icon: 'content', children: [...getChildren(Page)] }],
  ['/article-setting', { name: 'article-setting', title: '文章管理', icon: 'article', children: [...getChildren(Article)] }],
  ['/resource', { name: 'resource', title: '资源管理', icon: 'resource', children: [...getChildren(Resource)] }],
  ['/task-center', { name: 'task', title: '任务中心', icon: 'task', children: [...getChildren(Task)] }],
  ['/buy-management', { name: 'buy-management', title: '购买管理', icon: 'buy', children: [...getChildren(Buy)] }],
  ['/business-setting', { name: 'business-setting', title: '业务配置', icon: 'audit', children: [...getChildren(Business)] }],
  ['/publish-center', { name: 'publish-center', title: '发布中心', icon: 'publish', children: [...getChildren(Generate), ...getChildren(Audit)] }],
  ['/translate-management', { name: 'translate-management', title: '翻译管理', icon: 'translate', children: [...getChildren(Translte)] }],
  ['/cms-tools', { name: 'cms-tools', title: '站点工具', icon: 'tools', children: [...getChildren(Tools)] }],
  ['/web-quality-management', { name: 'web-quality-management', title: '网站质量管理', icon: 'web-quality', children: [...getChildren(Quality)] }],
  ['/subscribe-management', { name: 'subscribe-management', title: '订阅管理', icon: 'subscribe', children: [...getChildren(Subscribe)] }],
  ['/web-setting', { name: 'web-setting', title: '网页配置', icon: 'web-setting', children: [...getChildren(Web)] }],
])

export const addRoutes = [...routeMap.entries()].map( ( [ path, route ] ) => ({
  path,
  name: route.name,
  component: Layout,
  meta: {
    title: route.title,
    icon: route.icon
  },
  hidden: true
}) )