<template>
  <div class="scroll-y with-flex">
    <el-tabs v-model="queryParams.task_type" @tab-change="handleSearch">
      <el-tab-pane label="页面/块文件" name="1,2"></el-tab-pane>
      <el-tab-pane label="图片/其他文件" name="3,4"></el-tab-pane>
    </el-tabs>
    <QueryForm 
      :loading="loading"
      :form-list="formList"
      @search="handleSearch"
      @reset="reset"
    />
    <div v-loading="loading" class="sticky-table">
      <CustomTable
        ref="tableRef"
        :columns="columns"
        :data="tableData"
        :table-method="getList"  
      />
    </div>
  </div>
</template>

<script setup lang="tsx">
import { ref, reactive, computed, toRef, onActivated } from 'vue'
import { ElMessage, ElButton } from 'element-plus'
import { useRoute, useRouter } from 'vue-router'
import { storeToRefs } from 'pinia'
import { useParamsStore, useUserStroe } from '@/store'
import { useBasicStore } from '@/store/basic'
import useTryCatch from '@/hooks/use-try-catch'
import useLoading from '@/hooks/use-loading'
import { getChannelList } from '@/api/params'
import { getRecordList, taskPush } from '@/api/generate-manegement'
import { actionTypeMap, taskTypeMap, taskStatusMap, checkStatusMap, statusMap } from '@/views/generate-management/types'
import type { recordQueryParams, recordListItem } from '@/api/generate-manegement'

import QueryForm from '@/components/QueryForm/index.vue'
import CustomTable from '@/components/CustomTable/index.vue'

defineOptions( { name: 'MyPublish' } )

const route = useRoute()
const router = useRouter()
const { userInfo } = storeToRefs(useUserStroe())
const { webList } = storeToRefs(useParamsStore())
const basicStore = useBasicStore()
const { loading, setLoading } = useLoading()

const { task_id, task_status, user_id } = route.query as unknown as {
  task_id: string;
  task_status?: number;
  user_id?: number;
}

const queryParams_raw = {
  action_type: '',
  task_status: '',
  check_status: '',
  status: '',
  user_id_a: '',
  task_id: '',
  url: '',
}
const queryParams = reactive<recordQueryParams>({
  ...queryParams_raw,
  task_status: task_status ? +task_status : '',
  user_id_a: user_id ? +user_id : '',
  task_type: '1,2',
  task_id: task_id || ''
})
const site_id = ref<any>('')
const channel_id = ref('')
const channelList = ref([])
const formList = ref([
  {
    label: '任务ID',
    placeholder: '任务ID',
    value: toRef(queryParams, 'task_id'),
    component: 'input',
  },
  {
    label: '任务类型',
    placeholder: '任务类型',
    value: toRef(queryParams, 'action_type'),
    component: 'select',
    selections: [...actionTypeMap].map(( [ key, value ] ) => ({ label: value, value: key }))
  },
  {
    label: '所属站点',
    placeholder: '所属站点',
    value: site_id,
    component: 'select',
    labelKey: 'cms_site_name',
    valueKey: 'cms_site_id',
    selections: webList,
    handleChange: () => {
      channel_id.value = ''
      if (site_id.value) {
        useTryCatch( async () => {
          const res = await getChannelList(site_id.value as number)
          if (res.code === 200) {
            channelList.value = res.data
          }
        } )
      } else {
        channelList.value = []
      }
    }
  },
  {
    label: '所属渠道',
    placeholder: '所属渠道',
    value: channel_id,
    component: 'select',
    labelKey: 'channel_code',
    valueKey: 'channel_id',
    selections: channelList
  },
  {
    label: '审核状态',
    placeholder: '审核状态',
    value: toRef(queryParams, 'check_status'),
    component: 'select',
    append: true,
    selections: [...checkStatusMap].map(( [ key, value ] ) => ({ label: value, value: key }))
  },
  {
    label: '文件状态',
    placeholder: '文件状态',
    value: toRef(queryParams, 'status'),
    component: 'select',
    append: true,
    selections: [...statusMap].map(( [ key, value ] ) => ({ label: value, value: key }))
  },
  {
    label: '发布状态',
    placeholder: '发布状态',
    value: toRef(queryParams, 'task_status'),
    component: 'select',
    append: true,
    selections: [...taskStatusMap].map(( [ key, value ] ) => ({ label: value, value: key }))
  },
  {
    label: computed(() => queryParams.task_type === '1,2' ? '页面url/块url' : '文件夹路径'),
    placeholder: computed(() => queryParams.task_type === '1,2' ? '页面url/块url' : '文件夹路径'),
    value: toRef(queryParams, 'url'),
    component: 'textarea',
    append: true,
  },
])
const tableRef = ref()
const tableData = ref<recordListItem[]>([])
const columns = ref([
  {
    title: '任务ID',
    dataKey: 'task_id',
  },
  {
    title: '任务类型',
    dataKey: 'action_type',
    cellRenderer: ( scope: { row: recordListItem } ) => (
      <span style={ { color: scope.row.action_type === 'delete' ? 'var(--el-color-error)' : '' } }>
        { actionTypeMap.get(scope.row.action_type) || '/' }
      </span>
    )
  },
  {
    title: '任务等级',
    dataKey: 'tpl_level',
    cellRenderer: ( scope: { row: recordListItem } ) => (
      scope.row.tpl_level === 'A' 
      ? <span style={ { color: 'var(--el-color-error)', fontWeight: 700 } }>
        { scope.row.tpl_level }
      </span>
      : scope.row.tpl_level
    )
  },
  {
    title: '文件类型',
    dataKey: 'task_type',
    hideColumn: computed(() => queryParams.task_type !== '1,2'),
    cellRenderer: ( scope: { row: recordListItem } ) => (taskTypeMap.get(scope.row.task_type) || '/')
  },
  {
    title: '所属渠道',
    dataKey: 'channel_code',
  },
  {
    title: '发起人',
    dataKey: 'user_name_a',
  },
  {
    title: '文件数量',
    dataKey: 'total',
  },
  {
    title: '任务开始时间',
    dataKey: 'add_time',
    width: 180
  },
  {
    title: '当前状态更新时间',
    dataKey: 'edit_time',
    width: 180
  },
  {
    title: '审核状态',
    dataKey: 'check_status',
    width: 120,
    cellRenderer: ( scope: { row: recordListItem } ) => (
      <span style={ { color: scope.row.check_status === 2 ? 'var(--el-color-success)' : scope.row.check_status === 3 ? 'var(--el-color-error)' : '' } }>
        { checkStatusMap.get(scope.row.check_status) || '--' }
      </span>
    )
  },
  {
    title: '文件状态',
    dataKey: 'status',
    width: 120,
    hideColumn: computed(() => queryParams.task_type !== '1,2'),
    cellRenderer: ( scope: { row: recordListItem } ) => (
      <>
        {
          scope.row.check_status === 2 && scope.row.action_type !== 'delete' ? 
          <span style={ { color: scope.row.status === 2 ? 'var(--el-color-success)' : scope.row.status === 3 ? 'var(--el-color-error)' : '' } }>
            { statusMap.get(scope.row.status) || '--' }
          </span>
          : '--'
        }
      </>
    )
  },
  {
    title: '发布状态',
    dataKey: 'task_status',
    cellRenderer: ( scope: { row: recordListItem } ) => (
      <>
        {
          scope.row.check_status === 2 && ( scope.row.action_type === 'delete' || scope.row.status === 2) ? 
          <span style={ { color: scope.row.task_status === 3 ? 'var(--el-color-success)' : scope.row.task_status === 4 ? 'var(--el-color-error)' : '' } }>
            { taskStatusMap.get(scope.row.task_status) || '--' }
          </span>
          : '--'
        }
      </>
    )
  },
  {
    title: '操作',
    dataKey: '',
    fixed: 'right',
    width: 320,
    cellRenderer: ( scope: { row: recordListItem } ) => (
      <>
        <ElButton 
          type='primary' 
          plain
          onClick={() => handleDetail(scope.row)}
        >
          查看详情
        </ElButton>
        <ElButton 
          type='primary' 
          plain
          disabled={ scope.row.check_status !== 1 }
          onClick={() => handleAudit(scope.row)}
        >
          查看审核
        </ElButton>
        <ElButton 
          type='success'
          plain
          disabled={ scope.row.task_status !== 4 || scope.row.check_status !== 2 || scope.row.status === 3 }
          onClick={() => handleRePublish(scope.row.task_id)}
         >
          重新发布
        </ElButton>
      </>
    )
  }
])

const getList = ( { page, pageSize } ) => {
  useTryCatch( async () => {
    setLoading(true)
    const params = {
      S: {
        ...queryParams,
        user_id_a: userInfo.value.user_id as number,
      },
      site_id: site_id.value,
      channel_id: channel_id.value,
      page,
      page_size: pageSize
    }

    const res = await getRecordList(params)
    if (res.code === 200) {
      tableData.value = res.data.items
      tableRef.value?.setTotal(res.data.total)
    } else {
      ElMessage.error(res.msg)
    }
    setLoading(false)
  }, () => setLoading(false) )
}

const reset = () => {
  Object.entries(queryParams_raw).forEach(( [ key, value ] ) => {
    queryParams[key] = value
  })
}
const handleSearch = () => {
  tableRef.value?.setPage(1)
  const { page, pageSize } = tableRef.value ? tableRef.value : { page: 1, pageSize: 10 }
  getList( { page, pageSize } )
}
/* table methods */
const handleDetail = (row: recordListItem) => {
  router.push({
    name: 'GeneratePublishDetail',
    query: {
      task_id: row.task_id,
      task_type: row.task_type
    }
  })
}
const handleAudit = (row: recordListItem) => {
  router.push({
    name: 'ReviewDetail',
    query: {
      id: row.audit_task_id
    }
  })
}
const handleRePublish = (task_id: number) => {
  useTryCatch( async () => {
    setLoading(true)
    const res = await taskPush(task_id)
    if (res.code === 200) {
      const { page, pageSize } = tableRef?.value.getPagination?.() || { page: 1, pageSize: 10 }
      getList( { page, pageSize } )
      ElMessage.success(res.msg)
    } else {
      ElMessage.error(res.msg)
    }

    setLoading(false)
  }, () => setLoading(false) )
}
/* table methods */
const handleRouteQuery = () => {
  if(route.query.task_id) {
    queryParams.task_id = route.query.task_id as string
    queryParams.task_type = route.query.type as string
  }
}

onActivated(() => {
  if (basicStore.refresh) {
    basicStore.setRefresh(false)
    handleRouteQuery()
    handleSearch()
  }
})
</script>