<template>
  <div :class="{...classObj, dashboard: route.name === 'Dashboard'}" class="layout-wrapper">
    <div class="navbar-outer-container">
      <Navbar />
    </div>
    <!--left side-->
    <Sidebar class="sidebar-container custom-layout-box" />
    <!--right container-->
    <div class="main-container">
      <CollapseTransition>
        <div v-show="route.name !== 'Dashboard'">
          <div class="custom-container">
            <SubNav />
          </div>
    
          <div v-if="settings.showTagsView" class="tags-outer-container">
            <TagsView />
          </div>
        </div>
      </CollapseTransition>
      <AppMain />
    </div>

    <div class="global-task-history-container">
      <TaskHistory />
    </div>
  </div>
</template>
<script setup lang="ts">
import { computed } from 'vue'
import { useRoute } from 'vue-router'
import Sidebar from './sidebar/index.vue'
import AppMain from './app-main/index.vue'
import Navbar from './app-main/Navbar.vue'
import SubNav from './app-main/SubNav.vue'
import TagsView from './app-main/TagsView.vue'
import TaskHistory from './app-main/component/TaskHistory.vue'
import { useBasicStore } from '@/store/basic'
import { resizeHandler } from '@/hooks/use-layout'
import CollapseTransition from '@/components/Transition/collapse.vue'
const route = useRoute()
const { sidebar, settings, setSidebarOpen } = useBasicStore()
const classObj = computed(() => {
  return {
    openSidebar: sidebar.opened,
    closeSidebar: !sidebar.opened,
    hideSidebar: !settings.showLeftMenu
  }
})
resizeHandler().then(() => {
  if (route.name === 'Dashboard') {
    setSidebarOpen(false)
  }
})
</script>

<style lang="scss" scoped>
.layout-wrapper {
  --side-bar-width: 270px;
  &.dashboard {
    --layout-gap: 0px;
    &.closeSidebar {
      --width: 0px;
      .sidebar-container {
        transform: translateX(-10px);
      }
    }
  }
}
.main-container {
  height: 100%;
  transition: margin-left var(--sideBar-switch-duration);
  margin-left: calc( #{var(--side-bar-width)} + #{var(--layout-gap)} );
  position: relative;
  padding: #{var(--layout-gap)};
}
.navbar-outer-container, .custom-container, .tags-outer-container {
  margin-bottom: var(--layout-gap);
}
.sidebar-container {
  transition: width var(--sideBar-switch-duration);
  width: var(--side-bar-width) !important;
  background-color: var(--el-menu-bg-color);
  height: calc(100vh - #{var(--layout-gap)} * 2 - #{var(--nav-bar-height)});
  position: fixed;
  font-size: 0;
  top: var(--layout-gap, 0);
  bottom: 0;
  left: var(--layout-gap, 0);
  z-index: 1001;
  overflow: hidden;
  margin-top: var(--nav-bar-height);
}
.closeSidebar {
  --width: 54px;
  .sidebar-container {
    width: calc(#{var(--width)}) !important;
  }
  .main-container {
    margin-left: calc(#{var(--width)} + #{var(--layout-gap)}) !important;
  }
}
.hideSidebar {
  .sidebar-container {
    width: 0 !important;
  }
  .main-container {
    margin-left: 0;
  }
}
</style>

<style lang="scss">
.openSidebar {
  .el-svg-icon {
    margin-right: 13px !important;
  }
  .nav-icon {
    margin-right: 20px !important;
  }
  .nav-icon-empty {
    display: inline-block;
    padding-right: 20px;
  }
}
.closeSidebar {
  .nav-icon {
    margin-right: 4px;
  }
}
</style>
