import loadAce from "./hooks/ues-ace"
import type { DirectiveBinding, App } from 'vue'

let currentTimeStamp = new Date().getTime()

const noCode = (val: string) => val && val.indexOf('<') === -1 && (val.indexOf('/>') === -1 || val.indexOf('</') === -1)

const aceDirective = (app: App) => (
  app.directive('ace', {
    mounted(el: HTMLElement, binding: DirectiveBinding) {
      const { getVal, save, showTips } = binding.value
      el.classList.add('ace-trigger')
      el.addEventListener('click', () => {
        const codeValue = getVal()

        const now = new Date().getTime()
        if (now - currentTimeStamp <= 300) {
          loadAce({
            code: codeValue,
            showModal: true,
            save: (val: string) => {
              save && save(val)
            }
          }, app._context)
        } else {
          currentTimeStamp = now
        }
      })

      let addTips = false
      el.addEventListener('mouseover', () => {
        if (addTips || !showTips || noCode(getVal())) {
          return
        }
        addTips = true
        const span = document.createElement('span')
        span.innerText = '双击唤醒在线编辑器'
        span.className = 'ace-trigger-tip'
        el.appendChild(span)
      })
    },
  })
)

export default aceDirective