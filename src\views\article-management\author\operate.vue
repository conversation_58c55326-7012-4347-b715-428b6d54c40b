<template>
  <div class="scroll-y">
    <div style="overflow: hidden;" :style="{ minHeight: `calc(100% - ${fixBottomHeight}px)` }">
      <el-form
        ref="formRef"
        label-suffix=":"
        label-width="140px"
        :model="submitData"
      >
        <el-row :gutter="20">
          <el-col :span="12">
            <el-card shadow="never" style="height: 100%;">
              <template #header>
                <strong style="font-size: 1.25rem;">基础信息</strong>
              </template>
              <template #default>
                <el-form-item v-if="type === 'edit'" label="作者ID">
                  {{ author_id }}
                </el-form-item>
                <el-form-item label="名称" prop="name" :rules="{ required: true }">
                  <el-input v-model="submitData.name" placeholder="请输入作者名称" />
                </el-form-item>
                <el-form-item label="作者等级" prop="level" :rules="{ required: true }">
                  <el-select v-model="submitData.level" placeholder="请选择作者等级">
                    <el-option 
                      v-for="(d, index) in levelList"
                      :key="d.value"
                      :label="d.label"
                      :value="d.value"
                    />
                  </el-select>
                </el-form-item>
                <el-form-item label="作者语言" prop="language" :rules="{ required: true }">
                  <el-select v-model="submitData.language" placeholder="请选择语言">
                    <el-option 
                      v-for="(d, index) in langList"
                      :key="index"
                      :label="d.name"
                      :value="d.lang_code"
                    />
                  </el-select>
                </el-form-item>
                <el-form-item label="邮箱" prop="email" :rules="{ required: true }">
                  <el-input v-model="submitData.email" placeholder="请输入邮箱" />
                </el-form-item>
                <!-- <el-form-item label="邮箱密码" prop="password" >
                  <el-input v-model="submitData.password" placeholder="请输入邮箱密码" />
                </el-form-item> -->
                <el-form-item label="头像地址" prop="avatar">
                  <el-input v-model="submitData.avatar" placeholder="头像地址" />
                </el-form-item>
                <el-form-item label="作者简介页配置" prop="channel_urls_json">
                  <el-button type="primary" @click="handleSetChannel"> 编辑 </el-button>
                  <template v-if="selectChannel.length > 0">
                    <el-table :data="selectChannel">
                      <el-table-column label="渠道" prop="channel_code" show-overflow-tooltip  />
                      <el-table-column label="URL" prop="url" show-overflow-tooltip />
                    </el-table>
                  </template>
                </el-form-item>
                <el-form-item label="作者简介" prop="summary">
                  <el-input v-model="submitData.summary" placeholder="作者简介" />
                </el-form-item>
                <el-form-item label="Facebook账号" prop="facebook_account">
                  <el-input v-model="submitData.facebook_account" placeholder="Facebook账号" />
                </el-form-item>
                <el-form-item label="Twitter账号" prop="twitter_account">
                  <el-input v-model="submitData.twitter_account" placeholder="Twitter账号" />
                </el-form-item>
                <el-form-item label="Google+账号" prop="google_plus">
                  <el-input v-model="submitData.google_plus" placeholder="Google+账号" />
                </el-form-item>
                <el-form-item label="Linkedin账号" prop="linkedin_account">
                  <el-input v-model="submitData.linkedin_account" placeholder="Linkedin账号" />
                </el-form-item>
                <el-form-item label="排序" prop="sort">
                  <el-input-number v-model="submitData.sort" :step="1" :min="0" step-strictly />
                </el-form-item>
              </template>
            </el-card>
          </el-col>
        </el-row>
      </el-form>
    </div>

    <div class="fixed-bottom text-center">
      <el-button plain :icon="Back" @click="handleBack">返回</el-button>
      <el-button 
        type="primary"
        :icon="Finished"
        :loading="loading"
        @click="handleSave()"
      >
        保存
      </el-button>
    </div>
  </div>
</template>

<script setup lang="tsx">
import { ref, reactive, h } from 'vue'
import { ElMessage, ElMessageBox, ElForm, ElFormItem, ElInput } from 'element-plus'
import { useRoute, useRouter } from 'vue-router'
import { Back, Finished } from '@element-plus/icons-vue'
import { useParamsStore, useTagsViewStore } from '@/store'
import { useConfigStore } from '@/store/config'
import { useBasicStore } from '@/store/basic'
import useLoading from '@/hooks/use-loading'
import useTryCatch from '@/hooks/use-try-catch'
import { addAuthor, editAuthor, getAuthorDetail } from '@/api/article-management'
import { getLangList } from '@/api/site-config'
import type { langListItem } from '@/api/site-config'
import type { authorData } from '@/api/article-management'

defineOptions( { name: 'ArticleAuthorOperate' } )

type channelUrl = {
  channel_id: number;
  channel_code: string;
  url: string;
}

const route = useRoute()
const router = useRouter()
const { site_id_all, channelList } = useParamsStore()
const { delVisitedView, changeViewTitle } = useTagsViewStore()
const { fixBottomHeight } = useConfigStore()
const basicStore = useBasicStore()
const { loading, setLoading } = useLoading()
const { type, author_id } = route.query as unknown as { type: string; author_id: number }

const levelList = [
  {
    label: '主编',
    value: 'chief'
  },
  {
    label: '普通编辑',
    value: 'staff'
  },
  {
    label: '站外编辑',
    value: 'contributor'
  }
]
const langList = ref<langListItem[]>([])
const channelUrls = ref<channelUrl[]>([])
const channelIdMap = new Map<number,channelUrl>()
if (channelList) {
  channelUrls.value = channelList.map((item) => {
    const channel_id = item.channel_id as number
    const channel_code = item.channel_code as string
    const channel_url = {
      channel_id,
      channel_code,
      url: ''
    }
    channelIdMap.set(channel_id, channel_url)
    return channel_url
  })
}

const submitData = reactive<authorData>({
  site_id: site_id_all,
  name: '',
  level: '',
  language: '',
  email: '',
  password: '',
  avatar: '',
  facebook_account: '',
  twitter_account: '',
  google_plus: '',
  linkedin_account: '',
  sort: 0,
  summary: '',
  channel_urls_json: '[]',
})

const selectChannel = computed(() => {
  const urls = JSON.parse(submitData.channel_urls_json as string) as channelUrl[]
  urls.forEach((item) => {
    if (!item.channel_code) {
      const channel_url = channelIdMap.get(item.channel_id) as channelUrl
      item.channel_code = channel_url.channel_code
      channel_url.url = item.url
    }
  })
  return urls
})

const getDetail = () => {
  useTryCatch( async () => {
    setLoading(true)
    const res = await getAuthorDetail(author_id)
    if (res.code === 200) {
      const data = res.data
      Object.keys(submitData).forEach((key) => {
        if (data[key]) {
          submitData[key] = data[key]
        }
      })
    } else {
      ElMessage.error(res.msg)
    }
    setLoading(false)
  }, () => setLoading(false) )
}

const handleBack = () => {
  delVisitedView(route)
  router.push({
    name: 'ArticleAuthorList'
  })
}

const handleSave = () => {
  if (type === 'add') {
    handleAdd()
  } else {
    handleEdit()
  }
}

const handleAdd = () => {
  useTryCatch( async () => {
    const params = {
      ...submitData
    }
    const res = await addAuthor(params)
    if (res.code === 200) {
      basicStore.setRefresh(true)
      handleBack()
      ElMessage.success(res.msg)
    } else {
      ElMessage.error(res.msg)
    }
  })
}
const handleEdit = () => {
  useTryCatch( async () => {
    const params = {
      ...submitData
    }
    const res = await editAuthor(author_id, params)
    if (res.code === 200) {
      ElMessage.success(res.msg)
    } else {
      ElMessage.error(res.msg)
    }
  })
}
const handleGetLangList = () => {
  useTryCatch( async () => {
    const res = await getLangList()
    if (res.code === 200) {
      langList.value = res.data
    } else {
      ElMessage.error(res.msg)
    }
  })
}
const handleSetChannel = () => {
  ElMessageBox(
    {
      title: '作者简介页配置',
      customClass: 'dialog-button-center',
      customStyle: {
        maxWidth: 'none',
        width: '800px',
      },
      showCancelButton: true,
      showConfirmButton: true,
      cancelButtonText: '取消',
      confirmButtonText: '确认',
      callback: (action: string) => {
        if (action === 'confirm') {
          submitData.channel_urls_json = JSON.stringify(channelUrls.value.filter(( item ) => !!item.url))
        }
      },
      message: () => h(ElForm, {
        labelWidth: '130',
        labelSuffix: ':'
      }, {
        default: () => channelUrls.value.map((item) => h(ElFormItem, {
          label: item.channel_code
        }, { default: () => h(ElInput, {
          modelValue: item.url,
          onInput: (value) => (item.url = value)
        }) }))
      })
    }
  )
}

if (author_id) {
  getDetail()
}
changeViewTitle(route, type === 'add' ? '添加作者' : '编辑作者')
handleGetLangList()
</script>