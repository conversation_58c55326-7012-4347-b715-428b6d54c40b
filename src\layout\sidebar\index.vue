<template>
  <div id="Sidebar" class="reset-menu-style">
    <!--router menu-->
    <el-scrollbar ref="sidebarRef" class="side-bar-scroll">
      <el-menu
        class="el-menu-vertical"
        :collapse="!sidebar.opened"
        :default-active="activeMenu"
        :collapse-transition="false"
        mode="vertical"
      >
        <sidebar-item v-for="route in allRoutes" :key="route.path" :item="route" :base-path="route.path" />
      </el-menu>
    </el-scrollbar>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { storeToRefs } from 'pinia/dist/pinia'
import { useRoute } from 'vue-router'
import SidebarItem from './SidebarItem.vue'
import { useBasicStore } from '@/store/basic'
const { allRoutes, sidebar } = storeToRefs(useBasicStore())
const _route = useRoute()
const activeMenu = computed(() => {
  const { meta, path } = _route
  // if set path, the sidebar will highlight the path you set
  if (meta.activeMenu) {
    return meta.activeMenu
  }
  return path
})
</script>
<style lang="scss">
$bar-gap-width: 0x;
//fix open the item style issue
.el-menu-vertical {
  width: calc(#{var(--side-bar-width)} - #{$bar-gap-width} * 2);
}
.openSidebar {
  .reset-menu-style {
    --bar-gap: 16px;
  }
}
.reset-menu-style {
  --bar-gap: 5px;
  --padding-top: 24px;
  --el-menu-base-level-padding: 16px;
  padding: 0 $bar-gap-width;
  .el-menu--collapse {
    margin-left: -5px;
  }
}
</style>

<style lang="scss" scoped>
:deep(.el-scrollbar) {
  height: calc(100% - var(--padding-top));
  padding: 0 var(--bar-gap);
  margin-top: var(--padding-top);
  --el-menu-item-font-size: 16px;
  .el-menu--collapse {
    .el-sub-menu__title {
      padding: 0 var(--el-menu-base-level-padding) !important;
    }
  }
  .el-sub-menu__title {
    margin-bottom: 12px;
    &>span {
      font-weight: 700;
    }
  }
  .submenu-title-noDropdown {
    font-weight: 700;
  }
}
:deep(.el-sub-menu) {
  &.is-active {
    &>.el-sub-menu__title {
      color: var(--tags-view-item-active-color);
    }
  }
}
</style>
