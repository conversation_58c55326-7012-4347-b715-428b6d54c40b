<template>
  <div class="scroll-y with-flex">
    <el-tabs v-model="queryParams.module" @tab-change="handleSearch">
      <el-tab-pane label="CMS生成文件" name="1"></el-tab-pane>
      <el-tab-pane label="其他文件" name="2"></el-tab-pane>
    </el-tabs>
    <QueryForm
      :form-list="formList"
      :loading="loading"
      @search="handleSearch"
      hide-reset
    />
    <div v-loading="loading" class="sticky-table">
      <CustomTable
        ref="tableRef"
        :columns="columns"
        :data="tableData"
        :table-method="getList"
        :operate-list="operateList"
      />
    </div>
    <el-result
      v-show="!channel_item_all.channel_id"
      icon="warning"
      title="请先选择具体渠道!" 
      sub-title="您还未选择渠道"
      style="position: absolute; bottom: 0; width: 100%; height: 100%; background-color: var(--bg-layout); z-index: 19"
    />
  </div>
</template>

<script setup lang="tsx">
import { reactive, ref, toRef, computed } from 'vue'
import { useRoute } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import { storeToRefs } from 'pinia'
import { useParamsStore } from '@/store'
import useTryCatch from '@/hooks/use-try-catch'
import { getHost } from '@/hooks/use-env'
import useLoading from '@/hooks/use-loading'
import { siteFileList } from '@/api/tools-management'
import type { siteFileQuery, siteFileListItem } from '@/api/tools-management'
import Listener from '@/utils/site-channel-listeners'
import { stringify } from '@/utils/common'

import QueryForm from '@/components/QueryForm/index.vue'
import CustomTable from '@/components/CustomTable/index.vue'

defineOptions( { name: 'SiteFileManagement' } )

const route = useRoute()
const { channel_item_all } = storeToRefs(useParamsStore())
const { loading, setLoading } = useLoading()

const lastModifyTime = ref('')

const queryParams = reactive<siteFileQuery>({
  module: '1',
  type: '',
  redundant_type: '',
})

const states = {
  'new': '新添加',
  'created': '已生成',
  'modified': '已修改',
  'disable': '禁用',
  'uncreated': '未生成',
  '/': '/'
}

const formList = ref([
  {
    label: '文件类型',
    placeholder: '文件类型',
    value: toRef(queryParams, 'type'),
    component: 'select',
    selections: [
      {
        value: '0',
        label: '全部'
      },
      {
        value: '1',
        label: '页面文件'
      },
      {
        value: '2',
        label: '块文件'
      },
      {
        value: '3',
        label: '其他文件'
      }
    ],
    disabled: computed( () => queryParams.module === '2' )
  },
  {
    label: '冗余类型',
    placeholder: '冗余类型',
    value: toRef(queryParams, 'redundant_type'),
    component: 'select',
    selections: [
      {
        value: '0',
        label: '全部'
      },
      {
        value: '1',
        label: '本地冗余'
      },
      {
        value: '2',
        label: '线上冗余'
      },
      {
        value: '3',
        label: '本地&线上冗余'
      }
    ],
  }
])

const tableRef = ref()
const tableData = ref<siteFileListItem[]>([])
const columns = ref([
  {
    title: 'CMS文件类型',
    dataKey: 'type',
    width: 120,
    cellRenderer: ( scope: { row: siteFileListItem } ) => (
      <>
        { scope.row.type === 1 ? '页面文件' : scope.row.type === 2 ? '块文件' : '其他文件' }
      </>
    ),
    hideColumn: computed( () => queryParams.module === '2' )
  },
  {
    title: 'CMS文件ID',
    dataKey: 'file_id',
    width: 120,
    hideColumn: computed( () => queryParams.module === '2' )
  },
  {
    title: '文件状态',
    dataKey: 'create_state',
    width: 100,
    cellRenderer: ( scope: { row: siteFileListItem } ) => (
      <>
        { states[scope.row.create_state] }
      </>
    ),
    hideColumn: computed( () => queryParams.module === '2' )
  },
  {
    title: '本地服务器路径',
    dataKey: 'path',
    minWidth: 180,
    cellRenderer: ( scope: { row: siteFileListItem } ) => (
      <>
        {
          scope.row.path && <a href={`http://check.wondershare.com.wx/${scope.row.path}`} target='blank'> {scope.row.path} </a>
        }
      </>
    )
  },
  {
    title: '线上服务器路径',
    dataKey: 'url',
    minWidth: 180,
    cellRenderer: ( scope: { row: siteFileListItem } ) => (
      <>
        {
          scope.row.url && <a href={`${channel_item_all.value.host}${scope.row.url}`} target='blank'> {scope.row.path} </a>
        }
      </>
    )
  },
  {
    title: '异常类型',
    dataKey: 'except_type',
    width: 150,
    cellRenderer: ( scope: { row: siteFileListItem } ) => (
      <>
        <u style="color: var(--el-color-primary); cursor: pointer;" onClick={() => handleTypeModal(scope.row.except_type)}>异常场景{ scope.row.except_type }</u>
      </>
    )
  },
  {
    title: '冗余类型',
    dataKey: 'redundant_type',
    width: 120,
    cellRenderer: ( scope: { row: siteFileListItem } ) => (
      <>
        { scope.row.redundant_type === 1 ? '本地冗余' : scope.row.redundant_type === 2 ? '线上冗余' : '本地&线上冗余' }
      </>
    )
  },
])
const operateList = ref([
  {
    title: '导出Excel',
    method: () => {
      const params = {
        ...queryParams,
        channel_id: channel_item_all.value.channel_id as number,
        export: 1
      }
      const qsParams = stringify(params)
      location.href = `${getHost()}/api/v1/tool/file_management?${qsParams}`
    },
    disabled: computed( () => queryParams.module === '2' )
  },
  {
    title: 'CMS文件说明',
    method: () => {
      handleDesModal()
    }
  }
])

const getList = ( { page, pageSize } ) => {
  if (!channel_item_all.value.channel_id) {
    return
  }
  useTryCatch( async () => {
    setLoading(true)
    const params = {
      ...queryParams,
      type: queryParams.module === '2' ? '': queryParams.type,
      channel_id: channel_item_all.value.channel_id,
      page,
      page_size: pageSize,
    }
    const res = await siteFileList(params)
    if (res.code === 200) {
      tableData.value = res.data.list
      tableRef.value?.setTotal(res.data.total)
      lastModifyTime.value = res.data.last_check_time
    } else {
      ElMessage.error(res.msg)
    }

    setLoading(false)
  }, () => setLoading(false) )
}

const handleSearch = () => {
  tableRef.value?.setPage(1)
  const { page, pageSize } = tableRef?.value?.getPagination()
  getList( { page, pageSize } )
}

const handleDesModal = () => {
  ElMessageBox({
    title: 'CMS文件说明',
    customClass: 'dialog-button-center',
    customStyle: {
      maxWidth: 'none',
      width: '600px',
    },
    showCancelButton: true,
    showConfirmButton: true,
    cancelButtonText: '取消',
    confirmButtonText: '确认',
    dangerouslyUseHTMLString: true,
    callback: () => {},
    message: `<p>CMS生成文件: 指通过CMS生成的文件, 主要为页面和块, 文件类型为html。</p>
    <p>当前共有5种异常场景, 基于不同的业务场景导致; 异常场景会导致不同的类型的冗余文件</p>
    <p>因扫描服务器文件需要时间，所以此模块数据为定期更新；最近一次更新为：<strong style="color: var(--el-color-primary)">${ lastModifyTime.value || ''}</strong></p>
    `,
  })
}

const handleTypeModal = (type: number) => {
  ElMessageBox({
    title: `异常场景${type}`,
    customClass: 'dialog-button-center',
    customStyle: {
      maxWidth: 'none',
      width: '800px',
    },
    showCancelButton: true,
    showConfirmButton: true,
    cancelButtonText: '取消',
    confirmButtonText: '确认',
    dangerouslyUseHTMLString: true,
    callback: () => {},
    message: type === 1 ? `<strong>cms和本地服务器存在, 线上无文件</strong><br> 具体原因： 生成页面后，没有在发布系统，进行&lt;上线页面&gt;的发布任务` 
    : type === 2 ? `<strong>cms和本地服务器皆无, 但线上存在文件</strong><br> 在旧页面进行了路径更改，但是没有发布&lt;下线页面&gt;的发布任务` 
    : type === 3 ? `<strong>cms存在页面, 本地服务器无文件, 线上存在文件</strong><br> 在旧页面进行了禁用，但是没有发布&lt;下线页面&gt;的发布任务` 
    : type === 4 ? `<strong>cms不存在页面, 本地服务器有文件, 线上有文件</strong><br> 
    (1)网站变更域名: 导致CMS配置的域名变化, 后续会直接生成新的域名的文件; 但旧域名的文件, 因为从网站层面变更域名, 不会触发删除旧域名文件的&lt;下线页面&gt;的发布任务,所以导致并存新旧域名两套文件; 但CMS中无旧域名的页面 <br>
    (2)批量变更url的需求: 偶尔会存在批量变更url文件夹的需求, 提出需求由平台开发组在数据库层面进行批量修改; 因为是从数据库修改, 不会触发删除旧域名文件的下线页面的发布任务, 所以会存在新旧两个url文件, 但是CMS中无旧url的页面
    ` 
    : `<strong>cms不存在页面, 本地服务器有文件, 线上无文件</strong><br> 具体原因:
    (1)直接通过文件管理, 放入本地编辑的页面文件(当前营销站点此操作场景较少,功能站点常规操作)<br>
    (2)基于上述中, 因为变更存在新旧两套文件; 然后一般仅会针对线上服务器的旧文件, 提301重定向或批量下架需求, 导致仅本地服务器剩余文件, 造成冗余。<br>
    (3)因为大部分同时部署在CMS2.5和CMs4.0的站点, 都是共用同一套本地服务器和线上服务器的路径; 存在2.5进行操作后, 在4.0无法找到对应页面, 但本地存在页面文件的情况
    `,
  })
}

Listener( route, () => {
  if (channel_item_all.value.channel_id) {
    handleSearch()
  }
} )
</script>