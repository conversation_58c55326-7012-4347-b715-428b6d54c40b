<template>
  <div 
    v-for="(d, index) in formList" 
    :key="index"
    class="query-form-item"
    :class="{ range: wideItems.has(d.component) }"
    :style="{ width: d.component === 'textarea' ? '350px' : '220px' }">
    <template v-if="d.component === 'input'">
      <el-input 
        v-model="d.value" 
        :placeholder="d.placeholder" 
        :clearable="d.hideClearable ? false : true" 
        :disabled="d.disabled"
        :formatter="(val: string) => d.parse ? parseEmptyChar(val, d.parseSymbol || ',') : val "
        :parser="(val: string) => d.parse ? parseEmptyChar(val, d.parseSymbol || ',') : val "
      />
    </template>
    <template v-if="d.component === 'textarea'">
      <el-input @keyup.stop type="textarea" v-model="d.value" :placeholder="d.placeholder" :rows="1" :disabled="d.disabled" />
    </template>
    <template v-if="d.component === 'select'">
      <el-select 
        v-model="d.value"
        :placeholder="d.placeholder"
        :clearable="d.hideClearable ? false : true"
        :multiple="d.multiple ? true : false"
        :disabled="d.disabled"
        collapse-tags
        collapse-tags-tooltip
        filterable
        @change="(d.handleChange ? d.handleChange : () => {})"
        @visible-change="(d.visibleChange ? d.visibleChange : () => {})"
      >
        <el-option 
          v-for="(selection, idx) in d.selections" 
          :key="idx" 
          :label="selection[d.labelKey ? d.labelKey : 'label']" 
          :value="selection[d.valueKey ? d.valueKey : 'value']" 
        />
      </el-select>
    </template>
    <template v-if="d.component === 'cascader'">
      <el-cascader 
        v-model="d.value"
        :placeholder="d.placeholder"
        :options="d.selections"
        :props="d.props ? d.props : {}"
        :clearable="d.hideClearable ? false : true"
        :disabled="d.disabled"
        @change="(d.handleChange ? d.handleChange : () => {})"
      />
    </template>
    <template v-if="d.component === 'tree-select'">
      <el-tree-select 
        v-model="d.value"
        :data="d.data"
        :placeholder="d.placeholder"
        :clearable="d.hideClearable ? false : true"
        :value-key="d.valueKey"
        :props="d.props ? d.props : {}"
        :disabled="d.disabled"
        show-checkbox
        check-strictly
        check-on-click-node
        render-after-expand
        multiple
        collapse-tags
        collapse-tags-tooltip
        filterable
        @change="(d.handleChange ? d.handleChange : () => {})"
        @visible-change="(d.visibleChange ? d.visibleChange : () => {})"
      />
    </template>
    <template v-if="d.component === 'datetimerange'">
      <el-date-picker
        v-model="d.value"
        type="datetimerange"
        unlink-panels
        value-format="YYYY-MM-DD HH:mm:ss"
        :range-separator="d.range_separator || '至'"
        :start-placeholder="d.start_placeholder || '开始日期'"
        :end-placeholder="d.end_placeholder || '结束日期'"
        :clearable="d.hideClearable ? false : true"
        :disabled="d.disabled"
        @change="d.dateChange"
      />
    </template>
    <template v-if="d.component === 'datetime'">
      <el-date-picker 
        v-model="d.value"
        type="datetime"
        value-format="YYYY-MM-DD HH:mm:ss"
        :placeholder="d.placeholder"
        :clearable="d.hideClearable ? false : true"
        :disabled="d.disabled"
        @change="d.dateChange"
      />
    </template>
    <template v-if="d.component === 'datetime'">
      <el-date-picker 
        v-model="d.value"
        type="datetime"
        value-format="YYYY-MM-DD HH:mm:ss"
        :placeholder="d.placeholder"
        :clearable="d.hideClearable ? false : true"
      />
    </template>
    <template v-if="d.component === 'time-range-tab'">
      <TimeRangeTab :item="d" />
    </template>
  </div>
</template>

<script setup lang="ts">
import type { listItem } from './types'
import TimeRangeTab from './components/TimeRangeTab.vue'
const props = defineProps<{
  formList: listItem[]
}>()

const wideItems = new Set(['datetimerange', 'time-range-tab'])

const parseEmptyChar = (str: string, symbol: string = ',') => {
  return str.replace(/[\n\r\t\0\s]+/g, symbol)
}
</script>