<template>
  <div class="scroll-y with-flex">
    <div v-loading="loading" class="sticky-table">
      <CustomTable
        ref="tableRef"
        :columns="columns"
        :data="tableData"
        :table-method="getList"
        :operate-list="operateList"
        selectable
        @selection-change="(selections: site404Item[]) => selectIds = selections.map((item) => item.link_id).join(',')"
      />
    </div>
    <LinkModal 
      v-if="loadModal"
      :link_id="linkId"
      @close="loadModal = false"
    />
  </div>
</template>

<script setup lang="tsx">
import { ref, computed, defineAsyncComponent } from 'vue'
import { useRoute } from 'vue-router'
import { ElMessage, ElButton } from 'element-plus'
import { storeToRefs } from 'pinia'
import { useParamsStore } from '@/store'
import useTryCatch from '@/hooks/use-try-catch'
import useLoading from '@/hooks/use-loading'
import { getHost } from '@/hooks/use-env'
import { site404List } from '@/api/tools-management'
import type { site404Item } from '@/api/tools-management'

import CustomTable from '@/components/CustomTable/index.vue'
import Listener from '@/utils/site-channel-listeners'
const LinkModal = defineAsyncComponent(() => import('./components/linkModal.vue'))

defineOptions( { name: 'NotFindManagement' } )

const route = useRoute()
const { site_id_all, channel_item_all } = storeToRefs(useParamsStore())
const { loading, setLoading } = useLoading()

const loadModal = ref(false)
const linkId = ref(0)
const tableRef = ref()
const tableData = ref<site404Item[]>([])
const selectIds = ref('')
const columns = ref([
  {
    title: '线上404页面URL',
    dataKey: 'link_url',
    minWidth: 280,
  },
  {
    title: '所属渠道',
    dataKey: 'channel_code',
    width: 120,
  },
  {
    title: '404状态监测时间',
    dataKey: 'edit_time',
    width: 180,
  },
  {
    title: '链接到404url的页面数',
    dataKey: 'count',
    width: 180,
    cellRenderer: ( scope: { row: site404Item } ) => (
      <>
        <u style='color: var(--el-color-primary); cursor: pointer;' onClick={() => handleLinkModal(scope.row)}> { scope.row.count } </u>
      </>
    )
  },
  {
    title: '操作',
    dataKey: '',
    fixed: 'right',
    width: 150,
    cellRenderer: ( scope: { row: site404Item } ) => (
      <>
        <ElButton type='primary' plain onClick={() => handleExport(scope.row.link_id)}>导出链接页面</ElButton>
      </>
    )
  }
])
const operateList = ref([
  {
    title: '导出选中页面Excel',
    method: () => {
      handleExport()
    },
    disabled: computed( () => !selectIds.value)
  }
])

const getList = ( { page, pageSize } ) => {
  if (!channel_item_all.value.channel_id) {
    return
  }
  useTryCatch( async () => {
    setLoading(true)
    const params = {
      site_id: site_id_all.value,
      channel_id: channel_item_all.value.channel_id as number,
      page,
      page_size: pageSize,
    }
    const res = await site404List(params)
    if (res.code === 200) {
      tableData.value = res.data.items
      tableRef.value?.setTotal(res.data.total)
    } else {
      ElMessage.error(res.msg)
    }

    setLoading(false)
  }, () => setLoading(false) )
}

const handleExport = (ids?: string|number) => {
  location.href = `${getHost()}/api/v1/tool/error_links/export?site_id=${site_id_all.value}&link_ids=${ids || selectIds.value}`
}

const handleLinkModal = ( row: site404Item ) => {
  loadModal.value = true
  linkId.value = row.link_id
}

const reset = () => {
  tableRef.value?.setPage(1)
  const { page, pageSize } = tableRef?.value?.getPagination()
  getList( { page, pageSize } )
}

Listener( route, () => {
  if (channel_item_all.value.channel_id) {
    reset()
  }
} )
</script>