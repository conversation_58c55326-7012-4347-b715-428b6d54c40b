<template>
  <div ref="scrollyRef" class="scroll-y">
    <div style="font-size: 24px; margin-bottom: 20px;">
      <el-space>
        <span>任务ID: <strong>{{ task_id }}</strong>;</span>
        <span>创建时间: <strong>{{ add_time }}</strong></span>
        <el-tag :type=" status === 2 ? 'success' : status === 3 ? 'danger' : '' "> {{ statusMap.get(status) }} </el-tag>
      </el-space>
    </div>
    <div :style="{ height, position: 'relative' }">
      <CustomTable
        ref="tableRef"
        :table-method="getList"
        :columns="columns"
        :data="tableData"
        :index-method="(index: number) => index + 1"
        hide-pagination
        height="100%"
      />
      <div class="fixed-bottom text-center" style="position: absolute; padding-bottom: 10px;">
        <template v-if="(status === 2 || status === 3)">
          <template v-if="page_ids">
            <el-button type="success" plain :loading="loading" @click="handleGenerate(page_ids, 'page')" v-RoleIdsBanPermission="banIds"> 一键发布页面 </el-button>
            <el-button type="primary" plain :loading="loading" @click="handleJump(page_ids, 'page')"> 跳转发布页面 </el-button>
          </template>
          <template v-if="blk_ids">
            <el-button type="success" plain :loading="loading" @click="handleGenerate(blk_ids, 'block')" v-RoleIdsBanPermission="banIds"> 一键发布块 </el-button>
            <el-button type="primary" plain :loading="loading" @click="handleJump(blk_ids, 'block')"> 跳转发布块 </el-button>
          </template>
          <el-button type="primary" :loading="loading" @click="handleReset" :disabled="status === 3"> 还原此任务 </el-button>
        </template>
        <div v-if="hasDisabled" style="color: var(--el-color-error); font-size: 12px; position: absolute; width: 100%; bottom: -2px;">
          当前任务存在禁用状态文章/页面/模板/块，禁用状态的数据将在发布时过滤。
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="tsx">
import { ref, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { useRoute, useRouter } from 'vue-router'
import { useParamsStore, useUserStroe } from '@/store'
import { useBasicStore } from '@/store/basic'
import useLoading from '@/hooks/use-loading'
import useTryCatch from '@/hooks/use-try-catch'
import { usePageGenerate, useBlockGenerate } from '@/hooks/use-generate'
import { replaceReset, replaceDetail } from '@/api/tools-management'
import type { replaceDetailItem } from '@/api/tools-management'

import CustomTable from '@/components/CustomTable/index.vue'

defineOptions ( { name: 'replaceDetail' } )

const statusMap = new Map(
  [
    [1, '待处理'],
    [2, '已处理'],
    [3, '已回退'],
    [4, '执行中'],
    [5, '回退中']
  ]
)

const route = useRoute()
const router = useRouter()
const { site_id_all } = useParamsStore()
const { banIds } = useUserStroe()
const basicStore = useBasicStore()
const { loading, setLoading } = useLoading()
const { task_id } = route.query as unknown as { task_id: number; }

const hasDisabled = ref(false)
const add_time = ref('')
const page_ids = ref('')
const blk_ids = ref('')
const status = ref(1)
const scrollyRef = ref()
const height = ref('0px')
const tableRef = ref()
const tableData = ref<replaceDetailItem[]>([])
const columns = ref([
  {
    title: '来源类型',
    dataKey: 'type',
    width: 100,
  },
  {
    title: '类型ID',
    dataKey: 'type_id',
    width: 120,
  },
  {
    title: '来源字段',
    dataKey: 'type_field',
    width: 160,
  },
  {
    title: '自定义字段名称',
    dataKey: 'type_field_custom',
    width: 160,
  },
  {
    title: '状态',
    dataKey: 'entity_status',
    width: 100,
    cellRenderer: ( scope: { row: replaceDetailItem } ) => (
      <div style={ { color: scope.row.entity_status === 'able' ? 'var(--el-color-success)' : 'var(--el-color-error)' } }>
        { scope.row.entity_status === 'able' ? '启用' : scope.row.entity_status === 'disable' ? '禁用' : '' }
      </div>
    )
  },
  {
    title: '来源内容',
    dataKey: 'source',
    minWidth: 150,
  },
  {
    title: '替换内容',
    dataKey: 'target',
    minWidth: 150,
  },
])

const initHeight = () => {
  const scrollyEl = scrollyRef.value as HTMLElement
  if (scrollyEl) {
    const h = scrollyEl.getBoundingClientRect().height
    height.value = `${h - 55}px`
  }
}

const getList = () => {
  useTryCatch( async () => {
    setLoading(true)

    const res = await replaceDetail(task_id)
    if (res.code === 200) {
      const list = res.data.detailList
      tableData.value = list
      add_time.value = tableData.value[0].add_time
      blk_ids.value = res.data.blk_id_str
      page_ids.value = res.data.page_id_str
      status.value = res.data.status

      for ( const item of list) {
        if (item.entity_status === 'disable') {
          hasDisabled.value = true
          break
        }
      }
    } else {
      ElMessage.error(res.msg)
    }

    setLoading(false)
  }, () => setLoading(false) )
}

const handleReset = () => {
  ElMessageBox.confirm(
    '请确认是否进行还原操作',
    '提示',
    {
      type: 'warning',
      confirmButtonText: '确认',
      cancelButtonText: '取消',
      callback: (action: string) => {
        if (action === 'confirm') {
          useTryCatch( async () => {
            const res = await replaceReset(task_id)
            if (res.code === 200) {
              ElMessage.success(res.msg)
              router.push({
                name: 'BatchReplacement',
                query: {
                  name: 'second'
                }
              })
            } else {
              ElMessage.error(res.msg)
            }
          } )
        }
      }
    }
  )
}
const handleGenerate = (ids: string, type: 'page'|'block') => {
  if (type === 'page') {
    usePageGenerate({
      tpl_page_ids: ids,
      site_id: site_id_all
    })
  }
  if (type === 'block') {
    useBlockGenerate({
      blk_ids: ids,
      site_id: site_id_all
    })
  }
}
const handleJump = (ids: string, type: 'page'|'block') => {
  const name = type === 'page' ? 'PageManagement' : 'BlockManagement'
  basicStore.delCachedView(name)
  router.push({
    name,
    query: {
      entity_ids: ids
    }
  })
}

onMounted(() => {
  initHeight()
})
</script>

<style lang="scss" scoped>
:deep(.custom-table-container) {
  height: calc(100% - 60px);
}
</style>