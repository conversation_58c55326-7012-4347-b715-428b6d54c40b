import { ref, h } from 'vue'
import { ElCheckboxGroup, ElCheckbox, ElMessageBox } from 'element-plus'

const pageStateList = [
  {
    label: '未生成',
    value: 'new'
  },
  {
    label: '已修改',
    value: 'modified'
  },
  {
    label: '已生成',
    value: 'created'
  }
]
const selection = ref<any[]>(pageStateList.map(( { value } ) => value))

export default (callback: Function) => {
  ElMessageBox({
    title: '提示',
    customStyle: {
      maxWidth: 'none',
      width: 'auto',
    },
    showCancelButton: true,
    showConfirmButton: true,
    cancelButtonText: '取消',
    confirmButtonText: '生成发布',
    confirmButtonClass: 'el-button el-button--success el-button--default is-plain',
    callback: (action: string) => {
      if (action === 'confirm') {
        callback(selection.value)
      }
    },
    message: () => h('div', {
      style: {
        width: '600px',
      }
    }, [
      h('div', null, [
        h('span', { innerText: '此操作会将选中模板下的页面, 批量生成并实时发布到线上；请确认清楚, 当前是否要更新线上页面；如确认, 请选择需要生成的页面状态（可多选）' }),
        h('p', null, [
          'Tips1: ',
          h('span', { 
            style: { color: 'var(--el-color-error)' }, 
            innerText: '如模板中无此状态的页面, 则不会触发对应的生成发布任务' 
          }),
        ]),
        h('p', null, [
          'Tips2: ',
          h('span', { 
            style: { color: 'var(--el-color-error)' },
            innerText: '若模板等级为A级, 则对应页面需要通过审核后, 才会自动发布（上线任务）'
           }),
        ]),
      ]),
      h('div', {
        style: { padding: '20px 0' },
      },[
        h(ElCheckboxGroup, {
          modelValue: selection.value,
          onChange: (value) => {
            selection.value = value
          }
        }, () => pageStateList.map(({ label, value }) => h(ElCheckbox, {
          label: value
        }, () => label)))
      ]),
    ]),
  })
}