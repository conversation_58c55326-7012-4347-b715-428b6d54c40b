import { reactive } from 'vue'
import type { FormRules } from 'element-plus'

export const maxMap = {
  title: 200,
  subTitle: 150,
  summary: 300,
}

export const titleMap = {
  add: '添加文章',
  edit: '编辑文章',
  copy: '复制文章',
}

export const formRules = reactive<FormRules>({
  title: [
    { required: true, message: `文章标题不能为空`, trigger: 'blur' },
    { max: maxMap.title, message: `长度不能超过200字符`, trigger: 'blur' },
  ],
  cat_id: [
    { required: true, message: `文章分类不能为空`, trigger: 'change' },
  ],
  author_id: [
    { required: true, message: `文章作者不能为空`, trigger: 'change' }
  ],
  remark: [
    { required: true, message: `备份说明不能为空`, trigger: 'blur' },
  ],
  content: [
    { required: true, message: `文章正文不能为空`, trigger: 'blur' },
  ]
})