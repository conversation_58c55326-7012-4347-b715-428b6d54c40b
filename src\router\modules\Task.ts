import type { moduleMap } from '../aysncRoutes'
export default {
  'ArticleImportRecord': {
    component: () => import('@/views/task-center/artImportRecord/index.vue'),
    path: 'article-import-record',
    title: '文章导入记录',
    cachePage: true,
    icon: 'task'
  },
  'ArticleImportDetail': {
    component: () => import('@/views/task-center/artImportRecord/detail.vue'),
    path: 'article-import-detail',
    title: '文章导入详情',
    cachePage: true,
    queryRequired: true,
    hidden: true,
  },
} as {
  [propName: string]: moduleMap
}