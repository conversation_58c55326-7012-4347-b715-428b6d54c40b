<template>
  <el-dialog
    v-model="show"
    :title="`NPS弹窗配置${formData.id ? '编辑' : '添加'}`"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    append-to-body
    align-center
    width="1000px"
    @closed="emit('close')"
  >
    <template #default>
      <el-form
        ref="formRef"
        :model="formData"
        :rules="formRules"
        label-suffix=":"
        label-width="200"
      >
        <el-form-item label="策略名称" prop="name">
          <el-input v-model="formData.name" placeholder="请输入策略名称" />
        </el-form-item>
        <el-form-item label="弹出样式版本" prop="version">
          <el-select v-model="formData.version" placeholder="请选择版本号">
            <el-option
              v-for="d in versionList"
              :key="d.value"
              :label="`${d.label}(${d.value})`"
              :value="d.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="弹出概率" prop="pop_percent">
          <el-select v-model="formData.pop_percent" placeholder="请选择弹出概率">
            <el-option
              v-for="d in proList"
              :key="d.value"
              :label="d.label"
              :value="d.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="对应站点" prop="site_ids">
          <el-select 
            v-model="formData.site_ids" 
            placeholder="请选择对应站点"
            multiple
            collapse-tags
            collapse-tags-tooltip
            filterable
            clearable
          >
            <el-option
              v-for="d in webList"
              :key="d.cms_site_id"
              :label="d.cms_site_name"
              :value="d.cms_site_id"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="弹出等待时长(未登录用户)" prop="pop_time_logout">
          <el-select v-model="formData.pop_time_logout">
            <el-option 
              v-for="d in timeList"
              :key="d.value"
              :label="d.label"
              :value="d.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="弹出等待时长(已登录用户)" prop="pop_time_login">
          <el-select v-model="formData.pop_time_login">
            <el-option 
              v-for="d in timeList"
              :key="d.value"
              :label="d.label"
              :value="d.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="限制模板类型" prop="limit_template">
          <el-select
            v-model="formData.limit_template" 
            placeholder="不限制"
            multiple
            collapse-tags
            collapse-tags-tooltip
            filterable
            clearable
          >
            <el-option 
              v-for="d in tempList"
              :key="d.value"
              :label="d.label"
              :value="d.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="url关键词过滤" prop="url_keyword">
          <el-input type="textarea" v-model="formData.url_keyword" placeholder="请输入url关键词,逗号隔开" />
        </el-form-item>
      </el-form>
    </template>
    <template #footer>
      <div class="text-center">
        <el-button @click="show = false"> 取消 </el-button>
        <el-button type="primary" :loading="loading" @click="handleSave(formRef)"> 确定 </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { ElMessage } from 'element-plus'
import { useParamsStore } from '@/store'
import useTryCatch from '@/hooks/use-try-catch'
import useLoading from '@/hooks/use-loading'
import { addNps, editNps, getVersion } from '@/api/web-config'
import type { npsData, npsListItem } from '@/api/web-config'
import type { FormInstance } from 'element-plus'
import { formRules, proList, timeList } from './rules'

const { webList, moduleTypeMap } = useParamsStore()
const { loading, setLoading } = useLoading()

const emit = defineEmits(['close', 'save'])

const props = defineProps<{
  row: npsListItem | null;
}>()

const versionList = ref<{label: string; value: string}[]>([])
const tempList = ref<{label: string; value: string}[]>([])

const show = ref(true)
const formRef = ref<FormInstance>()

const formData_raw:npsData = {
  name: '',
  version: '',
  pop_percent: 100,
  pop_time_login: 60,
  pop_time_logout: 30,
  limit_template: '',
  url_keyword: '',
  site_ids: '',
}

if (props.row) {
  const row = props.row as npsListItem
  Object.keys(formData_raw).forEach((key) => {
    if (key === 'limit_template') {
      formData_raw[key] = row[key] ? row[key].split(',') : ''
    } else {
      formData_raw[key] = row[key]
    }
  })

  formData_raw.site_ids = row.site
}

const formData = reactive<npsData>({
  ...formData_raw,
  ...props.row ? { id: props.row.id } : {}
})

const handleSave = async ( formEl: FormInstance | undefined ) => {
  if (!formEl) {
    return
  }
  await formEl.validate( ( valid ) => {
    if (valid) {
      useTryCatch( async () => {
        setLoading(true)

        const params = {
          ...formData,
          site_ids: String(formData.site_ids),
          limit_template: String(formData.limit_template),
        }
        
        const api = props.row ? editNps : addNps
        const res = await api( params )
        if (res.code === 200) {
          show.value = false
          emit('save')
          ElMessage.success(res.msg || `${props.row?'添加':'编辑'}成功`)
        } else {
          ElMessage.error(res.msg)
        }

        setLoading(false)
      }, () => setLoading(false) )
    }
  } )
}

const handleGetVersion = () => {
  const versions = sessionStorage.getItem('nps_versions')
  if (versions) {
    versionList.value = JSON.parse(versions)
    return
  }
  useTryCatch( async () => {
    const res = await getVersion()
    if (res.code === 200) {
      versionList.value = res.data
      sessionStorage.setItem('nps_versions', JSON.stringify(res.data))
    }
  } )
}

const handleTempList = () => {
  const keyMap = {
    'article': '文章模板',
    'product': '产品模板',
    'store': '购买页模板'
  }

  const _list: {label: string, value: string}[] = []
  Object.entries(moduleTypeMap).forEach( ([key, list]) => {
    const label = keyMap[key] || '其他模板'
    list.forEach( (item) => {
      _list.push({
        label: `${label}|${item.label}`,
        value: `${key}|${item.value}`
      })
    } )
  } )

  tempList.value = _list
}

handleGetVersion()
handleTempList()
</script>