<template>
  <div class="scroll-y with-flex">
    <QueryForm 
      :form-list="formList"
      :loading="loading"
      @search="handleSearch"
      @reset="reset"
    />
    <div v-loading="loading" class="sticky-table">
      <CustomTable
        ref="tableRef"
        :columns="columns"
        :data="tableData"
        :table-method="getList"
        :operate-list="operateList"
        :select-method="(row: listItem) => selectMethod(row)"
        row-key="c_id"
        selectable
        :row-class-name="( { row } ) => row.isChildren ? 'bg-lighter' : ''"
        @selection-change="handleSelectionChange"
      />
    </div>

    <SubmitModal 
      v-if="loadModal"
      :data="currentSelections"
      @close="loadModal = false"
      @success="refresh"
    />
    <BillModal 
      v-if="loadBillModal"
      @close="loadBillModal = false"
    />
    <RecordModal 
      v-if="loadRecordModal"
      @close="loadRecordModal = false"
    />
  </div>
</template>

<script setup lang="tsx">
import { ref, reactive, computed, toRef, onActivated, h, defineAsyncComponent } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { 
  ElMessage, 
  ElButton, 
  ElMessageBox, 
  ElTooltip, 
  ElRadioGroup, ElRadio, 
  ElUpload, 
  ElSpace, 
  ElCheckboxGroup, ElCheckbox, 
  ElDropdown, ElDropdownMenu, ElDropdownItem
} from 'element-plus'
import { storeToRefs } from 'pinia'
import { useParamsStore, useUserStroe } from '@/store'
import { useBasicStore } from '@/store/basic'
import useTryCatch from '@/hooks/use-try-catch'
import useLoading from '@/hooks/use-loading'
import { getHost } from '@/hooks/use-env'
import { getChannelList } from '@/api/params'
import { getTaskList, rejectJob, createArticle, retryAITranslate } from '@/api/translate-management'
import type { queryParams, listItem } from '@/api/translate-management'
import type { channelItem } from '@/store/modules/params/types'
import { typeList } from './data'

import QueryForm from '@/components/QueryForm/index.vue'
import CustomTable from '@/components/CustomTable/index.vue'
import SvgIcon from '@/icons/SvgIcon.vue'
const SubmitModal = defineAsyncComponent( () => import('./components/submitTaskModal.vue') )
const BillModal = defineAsyncComponent( () => import('./components/billAccountModal.vue') )
const RecordModal = defineAsyncComponent( () => import('./components/billRecordModal.vue') )

defineOptions( { name: 'TranslateManagement' } )

const router = useRouter()
const route = useRoute()
const { webList, userList } = storeToRefs(useParamsStore())
const { userInfo } = useUserStroe()
const basicStore = useBasicStore()
const { loading, setLoading } = useLoading()
const channelList = ref<channelItem[]>([])

const { job_id } = route.query as unknown as { job_id?: string; }

const queryParams_raw = {
  site_id: '',
  channel_id: '',
  job_id: '',
  status: '',
  start_time: '',
  end_time: '',
  time_field: '',
  add_user: '',
  type: ''
}
const hasPermission = ref(new Set(userInfo.user_parent_id?.split(',')).has('1') ? true : false)
const queryParams = reactive<queryParams>({
  ...queryParams_raw,
  job_id: job_id || '',
})
const timeRange = ref('')
const downloading = ref(false)
const formList = ref([
  {
    label: '站点名称',
    placeholder: '站点名称',
    value: toRef(queryParams, 'site_id'),
    component: 'select',
    selections: webList,
    labelKey: 'cms_site_name',
    valueKey: 'cms_site_id',
    handleChange: () => {
      queryParams.channel_id = ''
      if (queryParams.site_id) {
        useTryCatch( async () => {
          const res = await getChannelList(queryParams.site_id as number)
          if (res.code === 200) {
            channelList.value = res.data
          }
        } )
      } else {
        channelList.value = []
      }
    }
  },
  {
    label: '渠道名称',
    placeholder: '渠道名称',
    value: toRef(queryParams, 'channel_id'),
    component: 'select',
    selections: channelList,
    labelKey: 'channel_code',
    valueKey: 'channel_id',
  },
  {
    label: '翻译方式',
    placeholder: '翻译方式',
    value: toRef(queryParams, 'type'),
    component: 'select',
    selections: typeList,
  },
  {
    label: '任务ID',
    placeholder: '任务ID',
    value: toRef(queryParams, 'job_id'),
    component: 'input',
  },
  {
    label: '任务状态',
    placeholder: '任务状态',
    value: toRef(queryParams, 'status'),
    component: 'select',
    selections: [
      {
        value: 1,
        label: '待提交'
      },
      {
        value: 2,
        label: '提交中'
      },
      {
        value: 3,
        label: '翻译中'
      },
      {
        value: 4,
        label: '校验中'
      },
      {
        value: 5,
        label: '已完成'
      },
      {
        value: 6,
        label: '翻译失败'
      },
      {
        value: 7,
        label: '已驳回'
      }
    ]
  },
  {
    label: '创建人',
    placeholder: '创建人',
    value: toRef(queryParams, 'add_user'),
    component: 'select',
    labelKey: 'real_name',
    valueKey: 'real_name',
    selections: userList,
    append: true,
  },
  {
    component: 'time-range-tab',
    field_key: toRef(queryParams, 'time_field'),
    value: timeRange,
    start_time: toRef(queryParams, 'start_time'),
    end_time: toRef(queryParams, 'end_time'),
    append: true,
    field_list: [
      {
        value: 'add_time',
        label: '创建时间'
      },
      {
        value: 'dead_line',
        label: '期望完成时间'
      },
      {
        value: 'actual_finish',
        label: '实际完成时间'
      }
    ],
  },
])
const tableRef = ref()
const tableData = ref<listItem[]>([])
const tableSelections = ref<listItem[]>([])
const loadModal = ref(false)
const loadBillModal = ref(false)
const loadRecordModal = ref(false)
const currentSelections = ref<listItem[]>([])
const operateList = ref([
  {
    title: '生成确认单',
    button: true,
    append: true,
    method: () => {
      loadBillModal.value = true
    },
    disabled: !hasPermission.value
  },
  {
    title: '确认单生成记录',
    button: true,
    append: true,
    method: () => {
      loadRecordModal.value = true
    },
    disabled: !hasPermission.value
  },
  {
    title: '提交任务',
    button: true,
    append: true,
    disabled: computed(() => tableSelections.value.length === 0),
    method: () => {
      handleSubmit()
    }
  }
])
const columns = ref([
  {
    title: '任务ID',
    dataKey: 'id',
    width: 120,
    cellRenderer: (scope: { row: listItem }) => (
      <>
        {
          scope.row.isChildren ? '' : scope.row.id
        }
      </>
    )
  },
  {
    title: '需求来源',
    dataKey: 'channel_name',
    minWidth: 100,
    cellRenderer: (scope: { row: listItem }) => (
      <>
        {
          scope.row.isChildren ? '' : scope.row.channel_name
        }
      </>
    )
  },
  {
    title: '任务状态',
    dataKey: 'status_name',
    width: 100,
    cellRenderer: (scope: { row: listItem }) => (
      <>
        <span style={ { 
          color: (scope.row.status === 6 || scope.row.status === 7) ? 'var(--el-color-error)' 
          : (scope.row.status === 4 || scope.row.status === 5) ? 'var(--el-color-success)' 
          : scope.row.status === 1 ? 'var(--el-color-warning)' 
          : (scope.row.status === 3) ? 'var(--el-color-primary)' : ''
          } }
        >
          { scope.row.status_name }
        </span>
        {
          (scope.row.reason && !scope.row.isChildren) && <ElTooltip content={scope.row.reason}><SvgIcon iconClass='question' /></ElTooltip>
        }
      </>
    )
  },
  {
    title: '创建人',
    dataKey: 'add_user',
    width: 100,
  },
  {
    title: '翻译语言',
    dataKey: 'source_language',
    minWidth: 100,
    cellRenderer: (scope: { row: listItem }) => (
      <>
        {
          scope.row.isChildren
          ? <strong style='padding-left: 30px'>{ scope.row.target_language.toUpperCase() }</strong>
          : <>
            <strong style='width: 20px; display: inline-block'>{ scope.row.source_language.toUpperCase() }</strong> 
              &gt; 
            <strong>{ scope.row.target_language.toUpperCase() }</strong>
          </>
        }
      </>
    )
  },
  {
    title: '翻译方式',
    dataKey: 'type_name',
    width: 100,
  },
  {
    title: '任务名称',
    dataKey: 'name',
    minWidth: 150,
    cellRenderer: (scope: { row: listItem }) => (
      <>
        { !scope.row.isChildren && scope.row.name }
      </>
    )
  },
  {
    title: '页面总数',
    dataKey: 'page_count',
    width: 100,
    cellRenderer: (scope: { row: listItem }) => (
      <>
        { scope.row.page_count }
      </>
    )
  },
  {
    title: '总字数',
    dataKey: 'words_count',
    width: 100,
    cellRenderer: (scope: { row: listItem }) => (
      <>
        { scope.row.words_count }
      </>
    )
  },
  {
    title: '预估价',
    dataKey: 'pre_price',
    width: 120,
    cellRenderer: (scope: { row: listItem }) => (
      <>
        { scope.row.type === 2 || scope.row.type === 3 ? '免费' : (scope.row.pre_price || '-') }
      </>
    )
  },
  {
    title: '供应商',
    dataKey: 'my_team_name',
    width: 150,
  },
  {
    title: '创建时间',
    dataKey: 'add_time',
    width: 180,
    cellRenderer: (scope: { row: listItem }) => (
      <>
        { !scope.row.isChildren && scope.row.add_time }
      </>
    )
  },
  {
    title: '期望完成时间',
    dataKey: 'dead_line',
    width: 180,
    cellRenderer: (scope: { row: listItem }) => (
      <>
        { !scope.row.isChildren && scope.row.dead_line }
      </>
    )
  },
  {
    title: '实际完成时间',
    dataKey: 'actual_finish',
    width: 180,
    cellRenderer: (scope: { row: listItem }) => (
      <>
        { !scope.row.isChildren && scope.row.actual_finish }
      </>
    )
  },
  {
    title: '操作',
    dataKey: '',
    fixed: 'right',
    width: 310,
    cellRenderer: (scope: { row: listItem }) => (
      <>
        { !scope.row.isChildren && 
          <ElTooltip content='跳转smart cat'>
            <ElButton type='primary' plain disabled={!scope.row.smartcat_url} onClick={() => handleSmartcatJump(scope.row)}> 跳转SC </ElButton>
          </ElTooltip>
        }
        <ElTooltip content='查看详情'>
          <ElButton type='primary' plain onClick={() => handleDetail(scope.row)}> 查看 </ElButton>
        </ElTooltip>
        {
          scope.row.isChildren
          ? <>
            <ElTooltip content='创建页面/文章'>
              <ElButton 
                type='primary' 
                plain 
                disabled={(scope.row.status !== 4 && scope.row.status !== 5) || scope.row.is_create !== 0} 
                onClick={() => handleCreateArtilce(scope.row)}> 创建 </ElButton>
            </ElTooltip>
            <ElTooltip content='下载译文'>
              <ElButton 
                type='primary' 
                plain 
                disabled={scope.row.status !== 4 && scope.row.status !== 5} 
                onClick={() => handleDownload(scope.row)}> 下载 
              </ElButton>
            </ElTooltip>
          </>
          : <>
            <ElButton 
              type='primary' 
              plain 
              disabled={ scope.row.type === 1 || scope.row.status !== 6 }
              onClick={() => handleRetry(scope.row)}> 重新翻译 
            </ElButton>
            <ElDropdown trigger='click' style={{ marginLeft: '10px' }}>
              {
                {
                  default: () => <ElButton type='primary' link> <strong>...</strong> </ElButton>,
                  dropdown: () => 
                  <ElDropdownMenu>
                    <ElDropdownItem>
                      {
                        hasPermission.value
                        && <ElButton 
                          type='primary' 
                          plain 
                          disabled={scope.row.status !== 1} 
                          onClick={() => handleSubmit(scope.row)}
                          > 提交任务 
                        </ElButton>
                      }
                      <ElButton 
                        type='danger' 
                        plain 
                        disabled={ scope.row.status !== 1 }
                        onClick={() => handleReject(scope.row)}
                        > 驳回 
                      </ElButton>
                    </ElDropdownItem>
                  </ElDropdownMenu>
                }
              }
            </ElDropdown>
          </>
        }
      </>
    )
  }
])

const getList = ( { page, pageSize } ) => {
  useTryCatch( async () => {
    setLoading(true)

    const params = {
      ...queryParams,
      page,
      page_size: pageSize,
    }
    const res = await getTaskList(params)
    if (res.code === 200) {
      const list = res.data.list.map((item: listItem) => {
        const newItem = { ...item, c_id: item.id }
        if (newItem.documentList) {
          newItem.children = newItem.documentList.map((lang, index) => {
            return {
              ...newItem,
              c_id: `${item.id}${index}`,
              ...lang,
              status: lang.status || item.status,
              status_name: lang.status_name || item.status_name,
              isChildren: true
            }
          })
        }
        return newItem
      })
      tableRef.value?.setTotal(res.data.total)
      tableData.value = list
    } else {
      ElMessage.error(res.msg)
    }

    setLoading(false)
  }, () => setLoading(false) )
}

const handleSelectionChange = (selections: listItem[]) => {
  tableSelections.value = selections
}

/**
 * 判断表格行是否可被选择
 * @param row 表格行数据
 * @returns {boolean} 是否可选择
 *
 * 条件说明:
 * 1. 状态为"待提交"(status === 1)且非子行(!isChildren)的情况下:
 *    - 如果是类型2(type === 2), 则可选择
 *    - 如果用户有权限(hasPermission), 则可选择
 * 2. 其他情况均不可选择
 */
const selectMethod = (row: listItem) => {
  if (row.status === 1 && !row.isChildren) {
    if (row.type === 2) {
      return true
    }
    if (hasPermission.value) {
      return true
    }
  }
  return false
}

const reset = () => {
  channelList.value = []
  timeRange.value = ''
  Object.entries(queryParams_raw).forEach(( [ key, value ] ) => {
    queryParams[key] = value
  })
}
const refresh = () => {
  const { page, pageSize } = tableRef?.value?.getPagination()
  getList( { page, pageSize } )
}

const handleSearch = () => {
  tableRef.value.setPage(1)
  refresh()
}

const handleSmartcatJump = (row: listItem) => {
  window.open(row.smartcat_url)
}
const handleDetail = (row: listItem, is_cross = false) => {
  router.push({ 
    name: 'TranslateDetail',
    query: {
      job_id: row.id,
      channel_name: row.channel_name,
      channel_code: row.channel_code,
      ...row.isChildren ? { target_language: row.target_language } : {},
      ...is_cross ? { is_cross: 1 } : {}
    }
  })
}

const handleSubmit = (row?: listItem) => {
  currentSelections.value = row ? [row] : tableSelections.value.filter( ( item ) => !item.isChildren )
  loadModal.value = true
}

const handleCreateArtilce  = (row: listItem) => {
  const type = ref(1)
  const crossOnly = ref(false)
  const target_channel = ref<number[]>([])
  const target_multilingual = [...row.target_multilingual]
  const is_cross = ref<number>(row.cross_or_not || 0)
  if (target_multilingual.length === 1) {
    target_channel.value.push(target_multilingual[0].channel_id)
  }
  if ( target_multilingual.length === 0 ) {
    crossOnly.value = true
    is_cross.value = 1
  }
  ElMessageBox({
    title: '创建页面/文章',
    customClass: 'dialog-button-center',
    customStyle: {
      maxWidth: 'none',
      width: '420px',
    },
    showCancelButton: false,
    showConfirmButton: false,
    closeOnPressEscape: false,
    closeOnClickModal: false,
    callback: ( action: string ) => {
      
    },
    message: () => h('div', { }, [
      h(ElSpace, {
        alignment: 'start',
        size: 16,
        style: { marginBottom: '16px', paddingTop: '6px' },
      }, () => [
        h('div', '此任务的内容来源 : '),
        h('div', [
          row.channel_name,
          ' ',
          row.channel_code
        ])
      ]),
      h(ElSpace, {
        alignment: 'start',
        size: 16,
        style: { marginBottom: '16px' },
      }, () => [
        h('div', { style: { paddingTop: '6px' } }, '自动创建目标站点 : '),
        h(ElRadioGroup, {
          modelValue: is_cross.value,
          onChange: (val) => {
            is_cross.value = val as 0|1
          },
        }, () => [
          h(ElRadio, { label: 0, disabled: row.cross_or_not === 1 || crossOnly.value }, () => '同站点'),
          h(ElRadio, { label: 1 }, () => '跨站点')
        ]),
      ]),
      ...is_cross.value === 0
      ? [
        h(ElSpace, {
          alignment: 'start',
          size: 16,
          style: { marginBottom: '16px' }
        }, () => [
          h('div', { style: { paddingTop: '6px' } }, '自动创建目标渠道 : '),
          h(ElCheckboxGroup, {
            modelValue: target_channel.value,
            onChange: (value) => target_channel.value = value as number[]
          }, () => target_multilingual.map((item) => h(ElCheckbox, { label: item.channel_id }, () => item.channel_code))),
        ]),
        h(ElSpace, {
          alignment: 'start',
          size: 16,
          style: { marginBottom: '16px' }
        }, () => [
          h('div', { style: { paddingTop: '6px' } }, [
            '是否将内链同时本地化 : ',
            h('div', { style: { color: 'var(--el-color-info)' } }, '(将内链进行批量替换)')
          ] ),
          h(ElRadioGroup, {
            style: { width: '200px'},
            modelValue: type.value,
            onChange: (value) => type.value = value as number
          }, () => [
            h(ElRadio, { label: 1 }, () => '批量替换同时创建文章'),
            h(ElRadio, { label: 2 }, () => '直接创建文章')
          ]),
        ]),
        h(ElSpace, {
          wrap: false,
          size: 16
        }, {
          default: () => type.value === 1 ? [
            h(ElButton, {
              type: 'primary',
              link: true,
              style: { marginLeft: '56px' },
              onClick: () => {
                location.href = `${getHost()}/api/v1/translate/download_replace_template?job_id=${row.id}&target_language=${row.target_language}`
              }
            }, () => '点击下载批量导入模板.xslx'),
            h(ElUpload, {
              action: 'string',
              accept: 'xlsx',
              autoUpload: false,
              showFileList: false,
              limit: 1,
              beforeUpload: () => true,
              onChange: (file) => {
                const params = {
                  job_id: `${row.id}`,
                  target_language: row.target_language,
                  type: 1,
                  target_channel: String(target_channel.value),
                  file: file.raw,
                  is_cross: is_cross.value
                }
                createArticleMethod(params)
              }
            }, { default: () => h(ElButton, { type: 'primary', loading: loading.value, disabled: target_channel.value.length === 0 }, () => '上传') })
          ] : []
        }),
        h('div', { style: { marginTop: '30px' }, class: 'text-center' }, [
          h(ElButton, { plain: true, onClick: () => ElMessageBox.close() }, () => '取消'),
          type.value === 2 ? h(ElButton, { 
            type: 'primary', 
            disabled: target_channel.value.length === 0, 
            onClick: () => {
              const params = {
                job_id: `${row.id}`,
                target_language: row.target_language,
                type: 2,
                target_channel: String(target_channel.value),
                is_cross: is_cross.value
              }
              createArticleMethod(params)
            } 
          }, () => '确认') : ''
        ])
      ] 
      : [
        h( 'div', { style: { marginTop: '30px' }, class: 'text-center' }, h(ElButton, {
          type: 'primary',
          onClick: () => {
            handleDetail(row, true)
            ElMessageBox.close()
          }
        }, () => '下一步') )
      ]
    ])
  })
}
const handleDownload = (row: listItem) => {
  if (!downloading.value) {
    downloading.value = true
    location.href = `${getHost()}/api/v1/translate/smartcat/document_export?job_id=${row.id}&target_language=${row.target_language}`
    setTimeout(() => {
      downloading.value = false
    }, 3500)
  } else {
    ElMessage.warning('操作频繁，稍后操作')
  }
}

const handleRetry = (row: listItem) => {
  ElMessageBox.confirm('请确认是否重新发起翻译?', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    callback: (action: string) => {
      if (action === 'confirm') {
        useTryCatch( async () => {
          const res = await retryAITranslate(row.id)
          if (res.code === 200) {
            refresh()
            ElMessage.success(res.msg)
          } else {
            ElMessage.error(res.msg)
          }
        } )
      }
    }
  })
}

const createArticleMethod = (data: { job_id: string; target_language: string; type: number; target_channel: string; file?: Blob; is_cross: number }) => {
  useTryCatch( async () => {
    setLoading(true)
    const res = await createArticle(data)
    if (res.code === 200) {
      handleSearch()
      ElMessage.success(res.msg)
    } else {
      ElMessage.error(res.msg)
    }
    ElMessageBox.close()
    setLoading(false)
  }, () => setLoading(false))
}

const handleReject = (row: listItem) => {
  ElMessageBox.prompt(
    '',
    '驳回理由',
    {
      confirmButtonText: '确认',
      cancelButtonText: '取消',
      inputPattern: /.+/,
      inputPlaceholder: '请输入驳回理由',
      inputErrorMessage: '驳回理由不能为空',
      callback: (action: any) => {
        if (action.action === 'confirm') { 
          useTryCatch( async () => {
            setLoading(true)

            const res = await rejectJob({ id: row.id, reason: action.value})
            if (res.code === 200) {
              refresh()
              ElMessage.success(res.msg)
            } else {
              ElMessage.error(res.msg)
            }
            setLoading(false)
          }, () => setLoading(false) )

        }
      }
    }
  )
}

onActivated( () => {
  if (basicStore.refresh) {
    basicStore.setRefresh(false)
    refresh()
  }
  if (route.query.job_id) {
    queryParams.job_id = route.query.job_id as string
    handleSearch()
  }
} )
</script>