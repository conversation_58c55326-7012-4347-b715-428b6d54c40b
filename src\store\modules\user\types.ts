export type userInfo = {
  account?: string;
  accounts?: string;
  client_id?: number;
  created_at: string;
  dept_id: number;
  wsid?: number|string;
  dept_name?: string;
  effective_time: string;
  email: string;
  err_login_count: number;
  id?: number;
  is_administrator?: boolean;
  name?: string;
  operator_id?: string;
  password?: string;
  phone?: string;
  remark?: string;
  role_ids?: number[];
  site_ids?: string;
  status?: number;
  type?: number;
  updated_at?: string;
  channel_read?: string;
}

export type cmsUserInfo = {
  user_id?: number;
  user_name?: string;
  real_name?: string;
  password?: string;
  email?: string;
  user_parent_id?: string;
  group_ids?: string;
  user_prev?: number;
  add_time?: string;
  last_login_time?: string;
  state?: string;
  site_ids?: string;
  menu_ids?: string;
  wsId?: string;
  wsid?: string|number;
  type?: number;
  phone?: string;
  remarks?: string;
  user_collection_site_id?: string;
  role_ids?: string;
  channel_read?: string;
}