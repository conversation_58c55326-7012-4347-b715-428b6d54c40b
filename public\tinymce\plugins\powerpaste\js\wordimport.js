/* !
 * Tiny PowerPaste plugin
 *
 * Copyright (c) 2023 Ephox Corporation DBA Tiny Technologies, Inc.
 * Licensed under the Tiny commercial license. See https://www.tiny.cloud/legal/
 *
 * Version: 6.2.5-16
 */

// Generated by js_of_ocaml 4.0.0
(function(a){typeof
  globalThis!=="object"&&(this?b():(a.defineProperty(a.prototype,"_T_",{configurable:true,get:b}),_T_));function
  b(){var
  b=this||self;b.globalThis=b;delete
  a.prototype._T_}}(Object));(function(x){"use strict";var
  jI="q",f8="i",aU="img",i3="Invalid_argument",d7="Map.bal",i2="@[",i1="%ni",jH="data-text-indent-alt",c0="th",bb="!",i0="align",jG="applewebkit",b5="label",bt="col",ag=0xff,cc="title",fI="del",l=-579472809,cZ="font",iZ="strike",X="contents",cK="height",iY="abbr",af="0",f7="samp",c=-841728391,R=698996132,cY=128,iX="Sys_blocked_io",aT="p",cX="start",jF="fd ",iW="normal",ba="form",A=248,f6="DeltaViewInsertion",f5="var",f4=" {\n",cW=">",jE=1027,f3="em",aC=0xFF,ei="caption",ao=246,bI="td",bs="object",iV="%u",fH="[endif]",cV="noscript",jD="error",a$="table",cH="tbody",cI=127,cJ=1024,ap="script",iU="@{",f1="1",f2="e",fG=" : flags Open_rdonly and Open_wronly are not compatible",jC="mso-list",eh="button",cU="h1",br="-",f0=": Not a directory",fF=" : file already exists",fZ="b",a7=0xffffff,iT="startfragment",fE="strong",jB="Out_of_memory",eg="big",$="ul",jA=": closedir failed",fY="index out of bounds",d6="select",at="tr",ef="_bigarr02",ee="@",iR=", characters ",iS=0x7F,cb="isindex",aV=0xffff,d5="basefont",iP="wordimport.js",d4="none",iQ=12520,d3="small",jz="infinity",aa="li",d2="menu",cG=1000,e="",cT="sub",iO="^",ca=749117977,bq=0x3f,bC="link",iN="src",ed="frame",iM="Match_failure",jy="mso-element",cS="html",ec="iframe",eb=252,an=0x00,iL=" : is a directory",bu=".",b4="+",jw="safari",fX="tt",jx=0xf0,jv="<![endif]",b3="param",bH="width",iK="%li",ju="map",bG="fieldset",bB="a",iJ=65536,fW=-32,fD=")",d1="legend",bA=-810056052,fC="nan",jt="list-style-type",js="closedir",d0="applet",iI=0xe0,iH=0xdfff,b2="div",aS="/",jr="Assert_failure",dZ="s",bp="meta",fB="ENOTDIR",fA=1073741823,j=870530776,b1="dl",bo=250,b$="frameset",cR="blockquote",ea=",",bF='"',iG="function",fz=1255,fV="<",jq="Fatal error: exception %s\n",b0=255,jp=0x800,fy="jsError",aR=0x8000,aK=256,aE="style",fx="\n}",iF="End_of_file",jo="text-align",dY="center",jn="data-main",fw="Failure",iE="data-converted-paragraph",v=50834029,fv="code",fU="ENOENT",iD=".5pt",fu="dfn",jm="([^/]+)",fT=0xf,iB=-48,iC=0xdc00,jl="ENOTEMPTY",ft="EBADF",iA="data-list-level",N="camlinternalFormat.ml",jk="Division_by_zero",jj="<\/",iz="Sys_error",fs="cite",b_="noframes",W="ol",ji=": ",iy="EEXIST",fS="%d",ix="Printexc.handle_uncaught_exception",d$="optgroup",fr="kbd",jg="'",jh="buffer.ml",cQ="h5",dX="int_of_string",dW="dt",iw="display",iu=120,iv=103,d_="colgroup",cF="h6",a_="head",cP="h3",it=512,jf="br",is=0x7ff0,b9="data-list-id",a9="body",dV="u",ir="0x",dU="\n",cE="h2",d9=254,iq="bdo",fR=100,cO="pre",io="%Li",ip=": file descriptor already closed",fQ=" : flags Open_text and Open_binary are not compatible",dT="area",je="Safari",cN="tfoot",dS="input",bz="span",fq=3257036,a8=-804212868,cD="thead",fP=17731,im="lexing: empty token",fO="Unix.Unix_error",by="--",jd="&nbsp;",fp="mkdir",jc="Stack_overflow",fo="v:shape",cC="address",dR=": No such file or directory",ja="border",jb="/static/",i$=-97,i9="tab-interval",i_="Not_found",i8="chrome",cM="dd",fn="ins",cB=", ",i=781665294,il=0xFE,fN="rmdir",bE="dir",fm="data-list-type",bn="class",i7=1026,i6="Chromium",cL="sup",fM="?",dQ="list-style",ik="Pervasives.do_at_exit",ae=" ",fL="Fatal error: exception ",aD=0x80,ij="Undefined_recursive_module",b8="base",bD=":",i5="\xc2\xa0",b7="option",b6="hr",ii="cleanDocument",cA="h4",o=-936778451,dP="Set.bal",i4="_",fK="compare: functional value",ih="%i",d8="true",dO="textarea",fJ="acronym";function
  zB(d,b,e,c,f){if(c<=b)for(var
  a=1;a<=f;a++)e[c+a]=d[b+a];else
  for(var
  a=f;a>=1;a--)e[c+a]=d[b+a];return 0}function
  zC(e,f,d){var
  a=new
  Array(d+1);a[0]=0;for(var
  b=1,c=f+1;b<=d;b++,c++)a[b]=e[c];return a}function
  c_(c,b,a){var
  d=String.fromCharCode;if(b==0&&a<=4096&&a==c.length)return d.apply(null,c);var
  f=e;for(;0<a;b+=cJ,a-=cJ)f+=d.apply(null,c.slice(b,b+Math.min(a,cJ)));return f}function
  ek(b){var
  c=new
  Uint8Array(b.l),e=b.c,d=e.length,a=0;for(;a<d;a++)c[a]=e.charCodeAt(a);for(d=b.l;a<d;a++)c[a]=0;b.c=c;b.t=4;return c}function
  bc(d,e,b,f,c){if(c==0)return 0;if(f==0&&(c>=b.l||b.t==2&&c>=b.c.length)){b.c=d.t==4?c_(d.c,e,c):e==0&&d.c.length==c?d.c:d.c.substr(e,c);b.t=b.c.length==b.l?0:2}else
  if(b.t==2&&f==b.c.length){b.c+=d.t==4?c_(d.c,e,c):e==0&&d.c.length==c?d.c:d.c.substr(e,c);b.t=b.c.length==b.l?0:2}else{if(b.t!=4)ek(b);var
  g=d.c,h=b.c;if(d.t==4)if(f<=e)for(var
  a=0;a<c;a++)h[f+a]=g[e+a];else
  for(var
  a=c-1;a>=0;a--)h[f+a]=g[e+a];else{var
  i=Math.min(c,g.length-e);for(var
  a=0;a<i;a++)h[f+a]=g.charCodeAt(e+a);for(;a<c;a++)h[f+a]=0}}return 0}function
  au(a){return a}function
  bd(a,b,c,d,e){bc(au(a),b,c,d,e);return 0}function
  cf(b,a){if(b==0)return e;if(a.repeat)return a.repeat(b);var
  d=e,c=0;for(;;){if(b&1)d+=a;b>>=1;if(b==0)return d;a+=a;c++;if(c==9)a.slice(0,1)}}function
  bM(a){if(a.t==2)a.c+=cf(a.l-a.c.length,"\0");else
  a.c=c_(a.c,0,a.c.length);a.t=0}function
  jS(a,b){if(a===b)return 1;a.t&6&&bM(a);b.t&6&&bM(b);return a.c==b.c?1:0}function
  Ao(b,a){throw[0,b,a]}function
  kd(a){if(a.length<24){for(var
  b=0;b<a.length;b++)if(a.charCodeAt(b)>cI)return false;return true}else
  return!/[^\x00-\x7f]/.test(a)}function
  Ax(f){for(var
  k=e,c=e,h,g,i,a,b=0,j=f.length;b<j;b++){g=f.charCodeAt(b);if(g<aD){for(var
  d=b+1;d<j&&(g=f.charCodeAt(d))<aD;d++);if(d-b>it){c.substr(0,1);k+=c;c=e;k+=f.slice(b,d)}else
  c+=f.slice(b,d);if(d==j)break;b=d}a=1;if(++b<j&&((i=f.charCodeAt(b))&-64)==cY){h=i+(g<<6);if(g<iI){a=h-0x3080;if(a<aD)a=1}else{a=2;if(++b<j&&((i=f.charCodeAt(b))&-64)==cY){h=i+(h<<6);if(g<jx){a=h-0xe2080;if(a<jp||a>=0xd7ff&&a<0xe000)a=2}else{a=3;if(++b<j&&((i=f.charCodeAt(b))&-64)==cY&&g<0xf5){a=i-0x3c82080+(h<<6);if(a<0x10000||a>0x10ffff)a=3}}}}}if(a<4){b-=a;c+="\ufffd"}else
  if(a>aV)c+=String.fromCharCode(0xd7c0+(a>>10),iC+(a&0x3FF));else
  c+=String.fromCharCode(a);if(c.length>cJ){c.substr(0,1);k+=c;c=e}}return k+c}function
  aW(c,a,b){this.t=c;this.c=a;this.l=b}aW.prototype.toString=function(){switch(this.t){case
  9:return this.c;default:bM(this);case
  0:if(kd(this.c)){this.t=9;return this.c}this.t=8;case
  8:return this.c}};aW.prototype.toUtf16=function(){var
  a=this.toString();if(this.t==9)return a;return Ax(a)};aW.prototype.slice=function(){var
  a=this.t==4?this.c.slice():this.c;return new
  aW(this.t,a,this.l)};function
  jT(a){return new
  aW(0,a,a.length)}function
  a(a){return jT(a)}function
  gn(c,b){Ao(c,a(b))}var
  aG=[0];function
  L(a){gn(aG.Invalid_argument,a)}function
  jQ(){L(fY)}function
  bL(a,b){switch(a.t&6){default:if(b>=a.c.length)return 0;case
  0:return a.c.charCodeAt(b);case
  4:return a.c[b]}}function
  bK(b,a){if(a>>>0>=b.l)jQ();return bL(b,a)}function
  H(a,c,b){b&=ag;if(a.t!=4){if(c==a.c.length){a.c+=String.fromCharCode(b);if(c+1==a.l)a.t=0;return 0}ek(a)}a.c[c]=b;return 0}function
  aL(b,a,c){if(a>>>0>=b.l)jQ();return H(b,a,c)}function
  aF(c,a){if(c.fun)return aF(c.fun,a);if(typeof
  c!=="function")return c;var
  b=c.length|0;if(b===0)return c.apply(null,a);var
  e=a.length|0,d=b-e|0;if(d==0)return c.apply(null,a);else
  if(d<0)return aF(c.apply(null,a.slice(0,b)),a.slice(b));else
  return function(){var
  e=arguments.length==0?1:arguments.length,d=new
  Array(a.length+e);for(var
  b=0;b<a.length;b++)d[b]=a[b];for(var
  b=0;b<arguments.length;b++)d[a.length+b]=arguments[b];return aF(c,d)}}function
  c2(){L(fY)}function
  w(a,b){if(b>>>0>=a.length-1)c2();return a}function
  zG(a){if(isFinite(a)){if(Math.abs(a)>=2.2250738585072014e-308)return 0;if(a!=0)return 1;return 2}return isNaN(a)?4:3}function
  aw(a){a.t&6&&bM(a);return a.c}var
  AA=Math.log2&&Math.log2(1.1235582092889474E+307)==1020;function
  Az(a){if(AA)return Math.floor(Math.log2(a));var
  b=0;if(a==0)return-Infinity;if(a>=1)while(a>=2){a/=2;b++}else
  while(a<1){a*=2;b--}return b}function
  gf(c){var
  a=new
  Float32Array(1);a[0]=c;var
  b=new
  Int32Array(a.buffer);return b[0]|0}var
  jZ=Math.pow(2,-24);function
  j9(a){throw a}function
  go(){j9(aG.Division_by_zero)}function
  m(b,c,a){this.lo=b&a7;this.mi=c&a7;this.hi=a&aV}m.prototype.caml_custom="_j";m.prototype.copy=function(){return new
  m(this.lo,this.mi,this.hi)};m.prototype.ucompare=function(a){if(this.hi>a.hi)return 1;if(this.hi<a.hi)return-1;if(this.mi>a.mi)return 1;if(this.mi<a.mi)return-1;if(this.lo>a.lo)return 1;if(this.lo<a.lo)return-1;return 0};m.prototype.compare=function(a){var
  b=this.hi<<16,c=a.hi<<16;if(b>c)return 1;if(b<c)return-1;if(this.mi>a.mi)return 1;if(this.mi<a.mi)return-1;if(this.lo>a.lo)return 1;if(this.lo<a.lo)return-1;return 0};m.prototype.neg=function(){var
  a=-this.lo,b=-this.mi+(a>>24),c=-this.hi+(b>>24);return new
  m(a,b,c)};m.prototype.add=function(a){var
  b=this.lo+a.lo,c=this.mi+a.mi+(b>>24),d=this.hi+a.hi+(c>>24);return new
  m(b,c,d)};m.prototype.sub=function(a){var
  b=this.lo-a.lo,c=this.mi-a.mi+(b>>24),d=this.hi-a.hi+(c>>24);return new
  m(b,c,d)};m.prototype.mul=function(a){var
  b=this.lo*a.lo,c=(b*jZ|0)+this.mi*a.lo+this.lo*a.mi,d=(c*jZ|0)+this.hi*a.lo+this.mi*a.mi+this.lo*a.hi;return new
  m(b,c,d)};m.prototype.isZero=function(){return(this.lo|this.mi|this.hi)==0};m.prototype.isNeg=function(){return this.hi<<16<0};m.prototype.and=function(a){return new
  m(this.lo&a.lo,this.mi&a.mi,this.hi&a.hi)};m.prototype.or=function(a){return new
  m(this.lo|a.lo,this.mi|a.mi,this.hi|a.hi)};m.prototype.xor=function(a){return new
  m(this.lo^a.lo,this.mi^a.mi,this.hi^a.hi)};m.prototype.shift_left=function(a){a=a&63;if(a==0)return this;if(a<24)return new
  m(this.lo<<a,this.mi<<a|this.lo>>24-a,this.hi<<a|this.mi>>24-a);if(a<48)return new
  m(0,this.lo<<a-24,this.mi<<a-24|this.lo>>48-a);return new
  m(0,0,this.lo<<a-48)};m.prototype.shift_right_unsigned=function(a){a=a&63;if(a==0)return this;if(a<24)return new
  m(this.lo>>a|this.mi<<24-a,this.mi>>a|this.hi<<24-a,this.hi>>a);if(a<48)return new
  m(this.mi>>a-24|this.hi<<48-a,this.hi>>a-24,0);return new
  m(this.hi>>a-48,0,0)};m.prototype.shift_right=function(a){a=a&63;if(a==0)return this;var
  c=this.hi<<16>>16;if(a<24)return new
  m(this.lo>>a|this.mi<<24-a,this.mi>>a|c<<24-a,this.hi<<16>>a>>>16);var
  b=this.hi<<16>>31;if(a<48)return new
  m(this.mi>>a-24|this.hi<<48-a,this.hi<<16>>a-24>>16,b&aV);return new
  m(this.hi<<16>>a-32,b,b)};m.prototype.lsl1=function(){this.hi=this.hi<<1|this.mi>>23;this.mi=(this.mi<<1|this.lo>>23)&a7;this.lo=this.lo<<1&a7};m.prototype.lsr1=function(){this.lo=(this.lo>>>1|this.mi<<23)&a7;this.mi=(this.mi>>>1|this.hi<<23)&a7;this.hi=this.hi>>>1};m.prototype.udivmod=function(e){var
  c=0,b=this.copy(),a=e.copy(),d=new
  m(0,0,0);while(b.ucompare(a)>0){c++;a.lsl1()}while(c>=0){c--;d.lsl1();if(b.ucompare(a)>=0){d.lo++;b=b.sub(a)}a.lsr1()}return{quotient:d,modulus:b}};m.prototype.div=function(a){var
  b=this;if(a.isZero())go();var
  d=b.hi^a.hi;if(b.hi&aR)b=b.neg();if(a.hi&aR)a=a.neg();var
  c=b.udivmod(a).quotient;if(d&aR)c=c.neg();return c};m.prototype.mod=function(b){var
  a=this;if(b.isZero())go();var
  d=a.hi;if(a.hi&aR)a=a.neg();if(b.hi&aR)b=b.neg();var
  c=a.udivmod(b).modulus;if(d&aR)c=c.neg();return c};m.prototype.toInt=function(){return this.lo|this.mi<<24};m.prototype.toFloat=function(){return(this.hi<<16)*Math.pow(2,32)+this.mi*Math.pow(2,24)+this.lo};m.prototype.toArray=function(){return[this.hi>>8,this.hi&ag,this.mi>>16,this.mi>>8&ag,this.mi&ag,this.lo>>16,this.lo>>8&ag,this.lo&ag]};m.prototype.lo32=function(){return this.lo|(this.mi&ag)<<24};m.prototype.hi32=function(){return this.mi>>>8&aV|this.hi<<16};function
  en(b,c,a){return new
  m(b,c,a)}function
  em(a){if(!isFinite(a)){if(isNaN(a))return en(1,0,is);return a>0?en(0,0,is):en(0,0,0xfff0)}var
  f=a==0&&1/a==-Infinity?aR:a>=0?0:aR;if(f)a=-a;var
  b=Az(a)+1023;if(b<=0){b=0;a/=Math.pow(2,-i7)}else{a/=Math.pow(2,b-jE);if(a<16){a*=2;b-=1}if(b==0)a/=2}var
  d=Math.pow(2,24),c=a|0;a=(a-c)*d;var
  e=a|0;a=(a-e)*d;var
  g=a|0;c=c&fT|f|b<<4;return en(g,e,c)}function
  c6(a){return a.toArray()}function
  jP(c,b,g){c.write(32,b.dims.length);c.write(32,b.kind|b.layout<<8);if(b.caml_custom==ef)for(var
  a=0;a<b.dims.length;a++)if(b.dims[a]<aV)c.write(16,b.dims[a]);else{c.write(16,aV);c.write(32,0);c.write(32,b.dims[a])}else
  for(var
  a=0;a<b.dims.length;a++)c.write(32,b.dims[a]);switch(b.kind){case
  2:case
  3:case
  12:for(var
  a=0;a<b.data.length;a++)c.write(8,b.data[a]);break;case
  4:case
  5:for(var
  a=0;a<b.data.length;a++)c.write(16,b.data[a]);break;case
  6:for(var
  a=0;a<b.data.length;a++)c.write(32,b.data[a]);break;case
  8:case
  9:c.write(8,0);for(var
  a=0;a<b.data.length;a++)c.write(32,b.data[a]);break;case
  7:for(var
  a=0;a<b.data.length/2;a++){var
  e=c6(b.get(a));for(var
  d=0;d<8;d++)c.write(8,e[d])}break;case
  1:for(var
  a=0;a<b.data.length;a++){var
  e=c6(em(b.get(a)));for(var
  d=0;d<8;d++)c.write(8,e[d])}break;case
  0:for(var
  a=0;a<b.data.length;a++){var
  e=gf(b.get(a));c.write(32,e)}break;case
  10:for(var
  a=0;a<b.data.length/2;a++){var
  d=b.get(a);c.write(32,gf(d[1]));c.write(32,gf(d[2]))}break;case
  11:for(var
  a=0;a<b.data.length/2;a++){var
  f=b.get(a),e=c6(em(f[1]));for(var
  d=0;d<8;d++)c.write(8,e[d]);var
  e=c6(em(f[2]));for(var
  d=0;d<8;d++)c.write(8,e[d])}break}g[0]=(4+b.dims.length)*4;g[1]=(4+b.dims.length)*8}function
  jN(a){switch(a){case
  7:case
  10:case
  11:return 2;default:return 1}}function
  zD(b,d){var
  a;switch(b){case
  0:a=Float32Array;break;case
  1:a=Float64Array;break;case
  2:a=Int8Array;break;case
  3:a=Uint8Array;break;case
  4:a=Int16Array;break;case
  5:a=Uint16Array;break;case
  6:a=Int32Array;break;case
  7:a=Int32Array;break;case
  8:a=Int32Array;break;case
  9:a=Int32Array;break;case
  10:a=Float32Array;break;case
  11:a=Float64Array;break;case
  12:a=Uint8Array;break}if(!a)L("Bigarray.create: unsupported kind");var
  c=new
  a(d*jN(b));return c}function
  gg(c){var
  a=new
  Int32Array(1);a[0]=c;var
  b=new
  Float32Array(a.buffer);return b[0]}function
  c5(a){return new
  m(a[7]<<0|a[6]<<8|a[5]<<16,a[4]<<0|a[3]<<8|a[2]<<16,a[1]<<0|a[0]<<8)}function
  gh(d){var
  f=d.lo,g=d.mi,b=d.hi,c=(b&0x7fff)>>4;if(c==2047)return(f|g|b&fT)==0?b&aR?-Infinity:Infinity:NaN;var
  e=Math.pow(2,-24),a=(f*e+g)*e+(b&fT);if(c>0){a+=16;a*=Math.pow(2,c-jE)}else
  a*=Math.pow(2,-i7);if(b&aR)a=-a;return a}function
  f9(b){var
  d=b.length,c=1;for(var
  a=0;a<d;a++){if(b[a]<0)L("Bigarray.create: negative dimension");c=c*b[a]}return c}function
  zV(b,a){return new
  m(b&a7,b>>>24&ag|(a&aV)<<8,a>>>16&aV)}function
  gi(a){return a.hi32()}function
  gj(a){return a.lo32()}var
  zE=ef;function
  bv(c,d,b,a){this.kind=c;this.layout=d;this.dims=b;this.data=a}bv.prototype.caml_custom=zE;bv.prototype.offset=function(b){var
  c=0;if(typeof
  b==="number")b=[b];if(!(b
  instanceof
  Array))L("bigarray.js: invalid offset");if(this.dims.length!=b.length)L("Bigarray.get/set: bad number of dimensions");if(this.layout==0)for(var
  a=0;a<this.dims.length;a++){if(b[a]<0||b[a]>=this.dims[a])c2();c=c*this.dims[a]+b[a]}else
  for(var
  a=this.dims.length-1;a>=0;a--){if(b[a]<1||b[a]>this.dims[a])c2();c=c*this.dims[a]+(b[a]-1)}return c};bv.prototype.get=function(a){switch(this.kind){case
  7:var
  d=this.data[a*2+0],b=this.data[a*2+1];return zV(d,b);case
  10:case
  11:var
  e=this.data[a*2+0],c=this.data[a*2+1];return[d9,e,c];default:return this.data[a]}};bv.prototype.set=function(a,b){switch(this.kind){case
  7:this.data[a*2+0]=gj(b);this.data[a*2+1]=gi(b);break;case
  10:case
  11:this.data[a*2+0]=b[1];this.data[a*2+1]=b[2];break;default:this.data[a]=b;break}return 0};bv.prototype.fill=function(b){switch(this.kind){case
  7:var
  c=gj(b),e=gi(b);if(c==e)this.data.fill(c);else
  for(var
  a=0;a<this.data.length;a++)this.data[a]=a%2==0?c:e;break;case
  10:case
  11:var
  d=b[1],f=b[2];if(d==f)this.data.fill(d);else
  for(var
  a=0;a<this.data.length;a++)this.data[a]=a%2==0?d:f;break;default:this.data.fill(b);break}};bv.prototype.compare=function(b,g){if(this.layout!=b.layout||this.kind!=b.kind){var
  e=this.kind|this.layout<<8,f=b.kind|b.layout<<8;return f-e}if(this.dims.length!=b.dims.length)return b.dims.length-this.dims.length;for(var
  a=0;a<this.dims.length;a++)if(this.dims[a]!=b.dims[a])return this.dims[a]<b.dims[a]?-1:1;switch(this.kind){case
  0:case
  1:case
  10:case
  11:var
  c,d;for(var
  a=0;a<this.data.length;a++){c=this.data[a];d=b.data[a];if(c<d)return-1;if(c>d)return 1;if(c!=d){if(!g)return NaN;if(c==c)return 1;if(d==d)return-1}}break;case
  7:for(var
  a=0;a<this.data.length;a+=2){if(this.data[a+1]<b.data[a+1])return-1;if(this.data[a+1]>b.data[a+1])return 1;if(this.data[a]>>>0<b.data[a]>>>0)return-1;if(this.data[a]>>>0>b.data[a]>>>0)return 1}break;case
  2:case
  3:case
  4:case
  5:case
  6:case
  8:case
  9:case
  12:for(var
  a=0;a<this.data.length;a++){if(this.data[a]<b.data[a])return-1;if(this.data[a]>b.data[a])return 1}break}return 0};function
  cd(c,d,b,a){this.kind=c;this.layout=d;this.dims=b;this.data=a}cd.prototype=new
  bv();cd.prototype.offset=function(a){if(typeof
  a!=="number")if(a
  instanceof
  Array&&a.length==1)a=a[0];else
  L("Ml_Bigarray_c_1_1.offset");if(a<0||a>=this.dims[0])c2();return a};cd.prototype.get=function(a){return this.data[a]};cd.prototype.set=function(a,b){this.data[a]=b;return 0};cd.prototype.fill=function(a){this.data.fill(a);return 0};function
  jL(c,d,a,b){var
  e=jN(c);if(f9(a)*e!=b.length)L("length doesn't match dims");if(d==0&&a.length==1&&e==1)return new
  cd(c,d,a,b);return new
  bv(c,d,a,b)}function
  av(b){if(!aG.Failure)aG.Failure=[A,a(fw),-3];gn(aG.Failure,b)}function
  jM(b,v,r){var
  i=b.read32s();if(i<0||i>16)av("input_value: wrong number of bigarray dimensions");var
  p=b.read32s(),j=p&ag,o=p>>8&1,h=[];if(r==ef)for(var
  a=0;a<i;a++){var
  n=b.read16u();if(n==aV){var
  t=b.read32u(),u=b.read32u();if(t!=0)av("input_value: bigarray dimension overflow in 32bit");n=u}h.push(n)}else
  for(var
  a=0;a<i;a++)h.push(b.read32u());var
  d=f9(h),f=zD(j,d),g=jL(j,o,h,f);switch(j){case
  2:for(var
  a=0;a<d;a++)f[a]=b.read8s();break;case
  3:case
  12:for(var
  a=0;a<d;a++)f[a]=b.read8u();break;case
  4:for(var
  a=0;a<d;a++)f[a]=b.read16s();break;case
  5:for(var
  a=0;a<d;a++)f[a]=b.read16u();break;case
  6:for(var
  a=0;a<d;a++)f[a]=b.read32s();break;case
  8:case
  9:var
  s=b.read8u();if(s)av("input_value: cannot read bigarray with 64-bit OCaml ints");for(var
  a=0;a<d;a++)f[a]=b.read32s();break;case
  7:var
  e=new
  Array(8);for(var
  a=0;a<d;a++){for(var
  c=0;c<8;c++)e[c]=b.read8u();var
  q=c5(e);g.set(a,q)}break;case
  1:var
  e=new
  Array(8);for(var
  a=0;a<d;a++){for(var
  c=0;c<8;c++)e[c]=b.read8u();var
  k=gh(c5(e));g.set(a,k)}break;case
  0:for(var
  a=0;a<d;a++){var
  k=gg(b.read32s());g.set(a,k)}break;case
  10:for(var
  a=0;a<d;a++){var
  m=gg(b.read32s()),l=gg(b.read32s());g.set(a,[d9,m,l])}break;case
  11:var
  e=new
  Array(8);for(var
  a=0;a<d;a++){for(var
  c=0;c<8;c++)e[c]=b.read8u();var
  m=gh(c5(e));for(var
  c=0;c<8;c++)e[c]=b.read8u();var
  l=gh(c5(e));g.set(a,[d9,m,l])}break}v[0]=(4+i)*4;return jL(j,o,h,f)}function
  jK(a,b,c){return a.compare(b,c)}function
  ep(a,b){return Math.imul(a,b)}function
  Y(b,a){a=ep(a,0xcc9e2d51|0);a=a<<15|a>>>32-15;a=ep(a,0x1b873593);b^=a;b=b<<13|b>>>32-13;return(b+(b<<2)|0)+(0xe6546b64|0)|0}function
  zQ(a,b){a=Y(a,gj(b));a=Y(a,gi(b));return a}function
  gd(a,b){return zQ(a,em(b))}function
  jO(c){var
  b=f9(c.dims),d=0;switch(c.kind){case
  2:case
  3:case
  12:if(b>aK)b=aK;var
  e=0,a=0;for(a=0;a+4<=c.data.length;a+=4){e=c.data[a+0]|c.data[a+1]<<8|c.data[a+2]<<16|c.data[a+3]<<24;d=Y(d,e)}e=0;switch(b&3){case
  3:e=c.data[a+2]<<16;case
  2:e|=c.data[a+1]<<8;case
  1:e|=c.data[a+0];d=Y(d,e)}break;case
  4:case
  5:if(b>cY)b=cY;var
  e=0,a=0;for(a=0;a+2<=c.data.length;a+=2){e=c.data[a+0]|c.data[a+1]<<16;d=Y(d,e)}if((b&1)!=0)d=Y(d,c.data[a]);break;case
  6:if(b>64)b=64;for(var
  a=0;a<b;a++)d=Y(d,c.data[a]);break;case
  8:case
  9:if(b>64)b=64;for(var
  a=0;a<b;a++)d=Y(d,c.data[a]);break;case
  7:if(b>32)b=32;b*=2;for(var
  a=0;a<b;a++)d=Y(d,c.data[a]);break;case
  10:b*=2;case
  0:if(b>64)b=64;for(var
  a=0;a<b;a++)d=gd(d,c.data[a]);break;case
  11:b*=2;case
  1:if(b>32)b=32;for(var
  a=0;a<b;a++)d=gd(d,c.data[a]);break}return d}function
  zT(a,b){b[0]=4;return a.read32s()}function
  Aj(a,b){switch(a.read8u()){case
  1:b[0]=4;return a.read32s();case
  2:av("input_value: native integer value too large");default:av("input_value: ill-formed native integer")}}function
  z4(c,d){var
  b=new
  Array(8);for(var
  a=0;a<8;a++)b[a]=c.read8u();d[0]=8;return c5(b)}function
  z0(e,d,b){var
  c=c6(d);for(var
  a=0;a<8;a++)e.write(8,c[a]);b[0]=8;b[1]=8}function
  zU(a,b,c){return a.compare(b)}function
  zX(a){return a.lo32()^a.hi32()}var
  c4={"_j":{deserialize:z4,serialize:z0,fixed_length:8,compare:zU,hash:zX},"_i":{deserialize:zT,fixed_length:4},"_n":{deserialize:Aj,fixed_length:4},"_bigarray":{deserialize:function(a,b){return jM(a,b,"_bigarray")},serialize:jP,compare:jK,hash:jO},"_bigarr02":{deserialize:function(a,b){return jM(a,b,ef)},serialize:jP,compare:jK,hash:jO}};function
  ga(a){return c4[a.caml_custom]&&c4[a.caml_custom].compare}function
  jU(f,c,d,e){var
  b=ga(c);if(b){var
  a=d>0?b(c,f,e):b(f,c,e);if(e&&a!=a)return d;if(+a!=+a)return+a;if((a|0)!=0)return a|0}return d}function
  c8(a){return a
  instanceof
  aW}function
  eo(a){return c8(a)}function
  jV(a){if(typeof
  a==="number")return cG;else
  if(c8(a))return eb;else
  if(eo(a))return 1252;else
  if(a
  instanceof
  Array&&a[0]===a[0]>>>0&&a[0]<=b0){var
  b=a[0]|0;return b==d9?0:b}else
  if(a
  instanceof
  String)return iQ;else
  if(typeof
  a=="string")return iQ;else
  if(a
  instanceof
  Number)return cG;else
  if(a&&a.caml_custom)return fz;else
  if(a&&a.compare)return 1256;else
  if(typeof
  a=="function")return 1247;else
  if(typeof
  a=="symbol")return 1251;return 1001}function
  j0(a,b){if(a<b)return-1;if(a==b)return 0;return 1}function
  jR(a,b){a.t&6&&bM(a);b.t&6&&bM(b);return a.c<b.c?-1:a.c>b.c?1:0}function
  c9(a,b){return jR(a,b)}function
  f$(a,b,d){var
  e=[];for(;;){if(!(d&&a===b)){var
  f=jV(a);if(f==bo){a=a[1];continue}var
  g=jV(b);if(g==bo){b=b[1];continue}if(f!==g){if(f==cG){if(g==fz)return jU(a,b,-1,d);return-1}if(g==cG){if(f==fz)return jU(b,a,1,d);return 1}return f<g?-1:1}switch(f){case
  247:L(fK);break;case
  248:var
  c=j0(a[2],b[2]);if(c!=0)return c|0;break;case
  249:L(fK);break;case
  250:L("equal: got Forward_tag, should not happen");break;case
  251:L("equal: abstract value");break;case
  252:if(a!==b){var
  c=jR(a,b);if(c!=0)return c|0}break;case
  253:L("equal: got Double_tag, should not happen");break;case
  254:L("equal: got Double_array_tag, should not happen");break;case
  255:L("equal: got Custom_tag, should not happen");break;case
  1247:L(fK);break;case
  1255:var
  i=ga(a);if(i!=ga(b))return a.caml_custom<b.caml_custom?-1:1;if(!i)L("compare: abstract value");var
  c=i(a,b,d);if(c!=c)return d?-1:c;if(c!==(c|0))return-1;if(c!=0)return c|0;break;case
  1256:var
  c=a.compare(b,d);if(c!=c)return d?-1:c;if(c!==(c|0))return-1;if(c!=0)return c|0;break;case
  1000:a=+a;b=+b;if(a<b)return-1;if(a>b)return 1;if(a!=b){if(!d)return NaN;if(a==a)return 1;if(b==b)return-1}break;case
  1001:if(a<b)return-1;if(a>b)return 1;if(a!=b){if(!d)return NaN;if(a==a)return 1;if(b==b)return-1}break;case
  1251:if(a!==b){if(!d)return NaN;return 1}break;case
  1252:var
  a=aw(a),b=aw(b);if(a!==b){if(a<b)return-1;if(a>b)return 1}break;case
  12520:var
  a=a.toString(),b=b.toString();if(a!==b){if(a<b)return-1;if(a>b)return 1}break;case
  246:case
  254:default:if(a.length!=b.length)return a.length<b.length?-1:1;if(a.length>1)e.push(a,b,1);break}}if(e.length==0)return 0;var
  h=e.pop();b=e.pop();a=e.pop();if(h+1<a.length)e.push(a,b,h+1);a=a[h];b=b[h]}}function
  ce(a,b){return f$(a,b,true)}function
  zH(){return[0]}function
  D(a){if(a<0)L("Bytes.create");return new
  aW(a?2:9,e,a)}function
  bN(a,b){return+(f$(a,b,false)==0)}function
  zJ(a,c,b,d){if(b>0)if(c==0&&(b>=a.l||a.t==2&&b>=a.c.length))if(d==0){a.c=e;a.t=2}else{a.c=cf(b,String.fromCharCode(d));a.t=b==a.l?0:2}else{if(a.t!=4)ek(a);for(b+=c;c<b;c++)a.c[c]=d}return 0}function
  gm(d){d=aw(d);var
  e=d.length;if(e>31)L("format_int: format too long");var
  a={justify:b4,signstyle:br,filler:ae,alternate:false,base:0,signedconv:false,width:0,uppercase:false,sign:1,prec:-1,conv:"f"};for(var
  c=0;c<e;c++){var
  b=d.charAt(c);switch(b){case"-":a.justify=br;break;case"+":case" ":a.signstyle=b;break;case"0":a.filler=af;break;case"#":a.alternate=true;break;case"1":case"2":case"3":case"4":case"5":case"6":case"7":case"8":case"9":a.width=0;while(b=d.charCodeAt(c)-48,b>=0&&b<=9){a.width=a.width*10+b;c++}c--;break;case".":a.prec=0;c++;while(b=d.charCodeAt(c)-48,b>=0&&b<=9){a.prec=a.prec*10+b;c++}c--;case"d":case"i":a.signedconv=true;case"u":a.base=10;break;case"x":a.base=16;break;case"X":a.base=16;a.uppercase=true;break;case"o":a.base=8;break;case"e":case"f":case"g":a.signedconv=true;a.conv=b;break;case"E":case"F":case"G":a.signedconv=true;a.uppercase=true;a.conv=b.toLowerCase();break}}return a}function
  gb(b,g){if(b.uppercase)g=g.toUpperCase();var
  f=g.length;if(b.signedconv&&(b.sign<0||b.signstyle!=br))f++;if(b.alternate){if(b.base==8)f+=1;if(b.base==16)f+=2}var
  c=e;if(b.justify==b4&&b.filler==ae)for(var
  d=f;d<b.width;d++)c+=ae;if(b.signedconv)if(b.sign<0)c+=br;else
  if(b.signstyle!=br)c+=b.signstyle;if(b.alternate&&b.base==8)c+=af;if(b.alternate&&b.base==16)c+=b.uppercase?"0X":ir;if(b.justify==b4&&b.filler==af)for(var
  d=f;d<b.width;d++)c+=af;c+=g;if(b.justify==br)for(var
  d=f;d<b.width;d++)c+=ae;return a(c)}function
  gc(i,c){function
  j(a,b){if(Math.abs(a)<1.0)return a.toFixed(b);else{var
  c=parseInt(a.toString().split(b4)[1]);if(c>20){c-=20;a/=Math.pow(10,c);a+=new
  Array(c+1).join(af);if(b>0)a=a+bu+new
  Array(b+1).join(af);return a}else
  return a.toFixed(b)}}var
  a,e=gm(i),d=e.prec<0?6:e.prec;if(c<0||c==0&&1/c==-Infinity){e.sign=-1;c=-c}if(isNaN(c)){a=fC;e.filler=ae}else
  if(!isFinite(c)){a="inf";e.filler=ae}else
  switch(e.conv){case"e":var
  a=c.toExponential(d),b=a.length;if(a.charAt(b-3)==f2)a=a.slice(0,b-1)+af+a.slice(b-1);break;case"f":a=j(c,d);break;case"g":d=d?d:1;a=c.toExponential(d-1);var
  h=a.indexOf(f2),g=+a.slice(h+1);if(g<-4||c>=1e21||c.toFixed(0).length>d){var
  b=h-1;while(a.charAt(b)==af)b--;if(a.charAt(b)==bu)b--;a=a.slice(0,b+1)+a.slice(h);b=a.length;if(a.charAt(b-3)==f2)a=a.slice(0,b-1)+af+a.slice(b-1);break}else{var
  f=d;if(g<0){f-=g+1;a=c.toFixed(f)}else
  while(a=c.toFixed(f),a.length>d+1)f--;if(f){var
  b=a.length-1;while(a.charAt(b)==af)b--;if(a.charAt(b)==bu)b--;a=a.slice(0,b+1)}}break}return gb(e,a)}function
  el(f,c){if(aw(f)==fS)return a(e+c);var
  b=gm(f);if(c<0)if(b.signedconv){b.sign=-1;c=-c}else
  c>>>=0;var
  d=c.toString(b.base);if(b.prec>=0){b.filler=ae;var
  g=b.prec-d.length;if(g>0)d=cf(g,af)+d}return gb(b,d)}var
  j7=0;function
  be(){return j7++}function
  gk(a){return a.toUtf16()}function
  da(){return false}function
  AB(){function
  a(a){if(a.charAt(0)===aS)return[e,a.substring(1)];return}function
  b(c){var
  h=/^([a-zA-Z]:|[\\/]{2}[^\\/]+[\\/]+[^\\/]+)?([\\/])?([\s\S]*?)$/,a=h.exec(c),b=a[1]||e,f=Boolean(b&&b.charAt(1)!==bD);if(Boolean(a[2]||f)){var
  d=a[1]||e,g=a[2]||e;return[d,c.substring(d.length+g.length)]}return}return da()&&x.process&&x.process.platform?x.process.platform==="win32"?b:a:a}var
  gq=AB();function
  kb(a){return a.slice(-1)!==aS?a+aS:a}if(da()&&x.process&&x.process.cwd)var
  c3=x.process.cwd().replace(/\\/g,aS);else
  var
  c3="/static";c3=kb(c3);function
  Aa(a){a=gk(a);if(!gq(a))a=c3+a;var
  e=gq(a),d=e[1].split(aS),b=[];for(var
  c=0;c<d.length;c++)switch(d[c]){case"..":if(b.length>1)b.pop();break;case".":break;case"":break;default:b.push(d[c]);break}b.unshift(e[0]);b.orig=a;return b}function
  Ay(f){for(var
  g=e,b=g,a,i,c=0,h=f.length;c<h;c++){a=f.charCodeAt(c);if(a<aD){for(var
  d=c+1;d<h&&(a=f.charCodeAt(d))<aD;d++);if(d-c>it){b.substr(0,1);g+=b;b=e;g+=f.slice(c,d)}else
  b+=f.slice(c,d);if(d==h)break;c=d}if(a<jp){b+=String.fromCharCode(0xc0|a>>6);b+=String.fromCharCode(aD|a&bq)}else
  if(a<0xd800||a>=iH)b+=String.fromCharCode(iI|a>>12,aD|a>>6&bq,aD|a&bq);else
  if(a>=0xdbff||c+1==h||(i=f.charCodeAt(c+1))<iC||i>iH)b+="\xef\xbf\xbd";else{c++;a=(a<<10)+i-0x35fdc00;b+=String.fromCharCode(jx|a>>18,aD|a>>12&bq,aD|a>>6&bq,aD|a&bq)}if(b.length>cJ){b.substr(0,1);g+=b;b=e}}return g+b}function
  zF(a){var
  b=9;if(!kd(a))b=8,a=Ay(a);return new
  aW(b,a,a.length)}function
  ai(a){return zF(a)}var
  AE=["E2BIG","EACCES","EAGAIN",ft,"EBUSY","ECHILD","EDEADLK","EDOM",iy,"EFAULT","EFBIG","EINTR","EINVAL","EIO","EISDIR","EMFILE","EMLINK","ENAMETOOLONG","ENFILE","ENODEV",fU,"ENOEXEC","ENOLCK","ENOMEM","ENOSPC","ENOSYS",fB,jl,"ENOTTY","ENXIO","EPERM","EPIPE","ERANGE","EROFS","ESPIPE","ESRCH","EXDEV","EWOULDBLOCK","EINPROGRESS","EALREADY","ENOTSOCK","EDESTADDRREQ","EMSGSIZE","EPROTOTYPE","ENOPROTOOPT","EPROTONOSUPPORT","ESOCKTNOSUPPORT","EOPNOTSUPP","EPFNOSUPPORT","EAFNOSUPPORT","EADDRINUSE","EADDRNOTAVAIL","ENETDOWN","ENETUNREACH","ENETRESET","ECONNABORTED","ECONNRESET","ENOBUFS","EISCONN","ENOTCONN","ESHUTDOWN","ETOOMANYREFS","ETIMEDOUT","ECONNREFUSED","EHOSTDOWN","EHOSTUNREACH","ELOOP","EOVERFLOW"];function
  bx(d,g,f,a){var
  b=AE.indexOf(d);if(b<0){if(a==null)a=-9999;b=[0,a]}var
  c=[b,ai(g||e),ai(f||e)];return c}var
  j5={};function
  bP(a){return j5[a]}function
  bw(b,a){throw[0,b].concat(a)}function
  f_(a){if(!(a
  instanceof
  Uint8Array))a=new
  Uint8Array(a);return new
  aW(4,a,a.length)}function
  r(a){gn(aG.Sys_error,a)}function
  j_(a){r(a+dR)}function
  c$(a){if(a.t!=4)ek(a);return a.c}function
  ax(a){return a.l}function
  zx(){}function
  ab(a){this.data=a}ab.prototype=new
  zx();ab.prototype.constructor=ab;ab.prototype.truncate=function(a){var
  b=this.data;this.data=D(a|0);bc(b,0,this.data,0,a)};ab.prototype.length=function(){return ax(this.data)};ab.prototype.write=function(b,d,g,a){var
  c=this.length();if(b+a>=c){var
  e=D(b+a),f=this.data;this.data=e;bc(f,0,this.data,0,c)}bc(f_(d),g,this.data,b,a);return 0};ab.prototype.read=function(b,e,f,a){var
  c=this.length();if(b+a>=c)a=c-b;if(a){var
  d=D(a|0);bc(this.data,b,d,0,a);e.set(c$(d),f)}return a};function
  bJ(c,a,b){this.file=a;this.name=c;this.flags=b}bJ.prototype.err_closed=function(){r(this.name+ip)};bJ.prototype.length=function(){if(this.file)return this.file.length();this.err_closed()};bJ.prototype.write=function(c,a,d,b){if(this.file)return this.file.write(c,a,d,b);this.err_closed()};bJ.prototype.read=function(c,a,d,b){if(this.file)return this.file.read(c,a,d,b);this.err_closed()};bJ.prototype.close=function(){this.file=undefined};function
  U(b,a){this.content={};this.root=b;this.lookupFun=a}U.prototype.nm=function(a){return this.root+a};U.prototype.create_dir_if_needed=function(d){var
  c=d.split(aS),b=e;for(var
  a=0;a<c.length-1;a++){b+=c[a]+aS;if(this.content[b])continue;this.content[b]=Symbol("directory")}};U.prototype.slash=function(a){return/\/$/.test(a)?a:a+aS};U.prototype.lookup=function(b){if(!this.content[b]&&this.lookupFun){var
  c=this.lookupFun(a(this.root),a(b));if(c!==0){this.create_dir_if_needed(b);this.content[b]=new
  ab(au(c[1]))}}};U.prototype.exists=function(a){if(a==e)return 1;var
  b=this.slash(a);if(this.content[b])return 1;this.lookup(a);return this.content[a]?1:0};U.prototype.mkdir=function(c,f,d){var
  b=d&&bP(fO);if(this.exists(c))if(b)bw(b,bx(iy,fp,this.nm(c)));else
  r(c+": File exists");var
  a=/^(.*)\/[^/]+/.exec(c);a=a&&a[1]||e;if(!this.exists(a))if(b)bw(b,bx(fU,fp,this.nm(a)));else
  r(a+dR);if(!this.is_dir(a))if(b)bw(b,bx(fB,fp,this.nm(a)));else
  r(a+f0);this.create_dir_if_needed(this.slash(c))};U.prototype.rmdir=function(a,g){var
  b=g&&bP(fO),c=a==e?e:this.slash(a),f=new
  RegExp(iO+c+jm);if(!this.exists(a))if(b)bw(b,bx(fU,fN,this.nm(a)));else
  r(a+dR);if(!this.is_dir(a))if(b)bw(b,bx(fB,fN,this.nm(a)));else
  r(a+f0);for(var
  d
  in
  this.content)if(d.match(f))if(b)bw(b,bx(jl,fN,this.nm(a)));else
  r(this.nm(a)+": Directory not empty");delete
  this.content[c]};U.prototype.readdir=function(a){var
  g=a==e?e:this.slash(a);if(!this.exists(a))r(a+dR);if(!this.is_dir(a))r(a+f0);var
  h=new
  RegExp(iO+g+jm),d={},c=[];for(var
  f
  in
  this.content){var
  b=f.match(h);if(b&&!d[b[1]]){d[b[1]]=true;c.push(b[1])}}return c};U.prototype.opendir=function(a,f){var
  b=f&&bP(fO),c=this.readdir(a),d=false,e=0;return{readSync:function(){if(d)if(b)bw(b,bx(ft,js,this.nm(a)));else
  r(a+jA);if(e==c.length)return null;var
  f=c[e];e++;return{name:f}},closeSync:function(){if(d)if(b)bw(b,bx(ft,js,this.nm(a)));else
  r(a+jA);d=true;c=[]}}};U.prototype.is_dir=function(a){if(a==e)return true;var
  b=this.slash(a);return this.content[b]?1:0};U.prototype.unlink=function(a){var
  b=this.content[a]?true:false;delete
  this.content[a];return b};U.prototype.open=function(a,b){var
  c;if(b.rdonly&&b.wronly)r(this.nm(a)+fG);if(b.text&&b.binary)r(this.nm(a)+fQ);this.lookup(a);if(this.content[a]){if(this.is_dir(a))r(this.nm(a)+iL);if(b.create&&b.excl)r(this.nm(a)+fF);c=this.content[a];if(b.truncate)c.truncate()}else
  if(b.create){this.create_dir_if_needed(a);this.content[a]=new
  ab(D(0));c=this.content[a]}else
  j_(this.nm(a));return new
  bJ(this.nm(a),c,b)};U.prototype.open=function(a,b){var
  c;if(b.rdonly&&b.wronly)r(this.nm(a)+fG);if(b.text&&b.binary)r(this.nm(a)+fQ);this.lookup(a);if(this.content[a]){if(this.is_dir(a))r(this.nm(a)+iL);if(b.create&&b.excl)r(this.nm(a)+fF);c=this.content[a];if(b.truncate)c.truncate()}else
  if(b.create){this.create_dir_if_needed(a);this.content[a]=new
  ab(D(0));c=this.content[a]}else
  j_(this.nm(a));return new
  bJ(this.nm(a),c,b)};U.prototype.register=function(c,a){var
  b;if(this.content[c])r(this.nm(c)+fF);if(c8(a))b=new
  ab(a);if(eo(a))b=new
  ab(au(a));else
  if(a
  instanceof
  Array)b=new
  ab(f_(a));else
  if(typeof
  a==="string")b=new
  ab(jT(a));else
  if(a.toString){var
  d=au(ai(a.toString()));b=new
  ab(d)}if(b){this.create_dir_if_needed(c);this.content[c]=b}else
  r(this.nm(c)+" : registering file with invalid content type")};U.prototype.constructor=U;function
  jJ(){}function
  jY(b){var
  a=gq(b);if(!a)return;return a[0]+aS}var
  eq=jY(c3)||av("unable to compute caml_root"),cg=[];if(da())cg.push({path:eq,device:new
  jJ(eq)});else
  cg.push({path:eq,device:new
  U(eq)});cg.push({path:jb,device:new
  U(jb)});function
  ke(b){var
  g=Aa(b),b=g.join(aS),f=kb(b),c;for(var
  e=0;e<cg.length;e++){var
  a=cg[e];if(f.search(a.path)==0&&(!c||c.path.length<a.path.length))c={path:a.path,device:a.device,rest:b.substring(a.path.length,b.length)}}if(!c&&da()){var
  d=jY(b);if(d&&d.match(/^[a-zA-Z]:\/$/)){var
  a={path:d,device:new
  jJ(d)};cg.push(a);c={path:a.path,device:a.device,rest:b.substring(a.path.length,b.length)}}}if(c)return c;r("no device found for "+f)}function
  zI(c,b){var
  a=ke(c);if(!a.device.register)av("cannot register file");a.device.register(a.rest,b);return 0}function
  kc(c,b){var
  c=a(c),b=a(b);return zI(c,b)}function
  zL(){var
  b=x.caml_fs_tmp;if(b)for(var
  a=0;a<b.length;a++)kc(b[a].name,b[a].content);x.jsoo_create_file=kc;x.caml_fs_tmp=[];return 0}function
  jX(){return[0]}function
  ge(d,b){var
  e=b.length,a,c;for(a=0;a+4<=e;a+=4){c=b.charCodeAt(a)|b.charCodeAt(a+1)<<8|b.charCodeAt(a+2)<<16|b.charCodeAt(a+3)<<24;d=Y(d,c)}c=0;switch(e&3){case
  3:c=b.charCodeAt(a+2)<<16;case
  2:c|=b.charCodeAt(a+1)<<8;case
  1:c|=b.charCodeAt(a);d=Y(d,c)}d^=e;return d}function
  zR(a,b){return ge(a,aw(b))}function
  zO(d,b){var
  e=b.length,a,c;for(a=0;a+4<=e;a+=4){c=b[a]|b[a+1]<<8|b[a+2]<<16|b[a+3]<<24;d=Y(d,c)}c=0;switch(e&3){case
  3:c=b[a+2]<<16;case
  2:c|=b[a+1]<<8;case
  1:c|=b[a];d=Y(d,c)}d^=e;return d}function
  Ad(a){switch(a.t&6){default:bM(a);case
  0:return a.c;case
  4:return a.c}}function
  zN(b,c){var
  a=Ad(c);return typeof
  a==="string"?ge(b,a):zO(b,a)}function
  zP(a){a^=a>>>16;a=ep(a,0x85ebca6b|0);a^=a>>>13;a=ep(a,0xc2b2ae35|0);a^=a>>>16;return a}function
  zM(j,l,n,m){var
  f,g,h,d,c,b,a,e,i;d=l;if(d<0||d>aK)d=aK;c=j;b=n;f=[m];g=0;h=1;while(g<h&&c>0){a=f[g++];if(a&&a.caml_custom){if(c4[a.caml_custom]&&c4[a.caml_custom].hash){var
  k=c4[a.caml_custom].hash(a);b=Y(b,k);c--}}else
  if(a
  instanceof
  Array&&a[0]===(a[0]|0))switch(a[0]){case
  248:b=Y(b,a[2]);c--;break;case
  250:f[--g]=a[1];break;default:var
  o=a.length-1<<10|a[0];b=Y(b,o);for(e=1,i=a.length;e<i;e++){if(h>=d)break;f[h++]=a[e]}break}else
  if(c8(a)){b=zN(b,a);c--}else
  if(eo(a)){b=zR(b,a);c--}else
  if(typeof
  a==="string"){b=ge(b,a);c--}else
  if(a===(a|0)){b=Y(b,a+a+1);c--}else
  if(a===+a){b=gd(b,a);c--}}b=zP(b);return b&0x3FFFFFFF}function
  zS(a,c,l){if(!isFinite(a)){if(isNaN(a))return ai(fC);return ai(a>0?jz:"-infinity")}var
  j=a==0&&1/a==-Infinity?1:a>=0?0:1;if(j)a=-a;var
  d=0;if(a==0);else
  if(a<1)while(a<1&&d>-1022){a*=2;d--}else
  while(a>=2){a/=2;d++}var
  k=d<0?e:b4,f=e;if(j)f=br;else
  switch(l){case
  43:f=b4;break;case
  32:f=ae;break;default:break}if(c>=0&&c<13){var
  h=Math.pow(2,c*4);a=Math.round(a*h)/h}var
  b=a.toString(16);if(c>=0){var
  i=b.indexOf(bu);if(i<0)b+=bu+cf(c,af);else{var
  g=i+1+c;if(b.length<g)b+=cf(g-b.length,af);else
  b=b.substr(0,g)}}return ai(f+ir+b+aT+k+d.toString(10))}function
  zZ(a){return+a.isZero()}function
  z2(a){return new
  m(a&a7,a>>24&a7,a>>31&aV)}function
  z3(a){return a.toInt()}function
  zY(a){return+a.isNeg()}function
  z1(a){return a.neg()}function
  zW(h,c){var
  a=gm(h);if(a.signedconv&&zY(c)){a.sign=-1;c=z1(c)}var
  b=e,i=z2(a.base),g="0123456789abcdef";do{var
  f=c.udivmod(i);c=f.quotient;b=g.charAt(z3(f.modulus))+b}while(!zZ(c));if(a.prec>=0){a.filler=ae;var
  d=a.prec-b.length;if(d>0)b=cf(d,af)+b}return gb(a,b)}function
  ay(b,a){return bL(b,a)}function
  k(a){return ax(a)}function
  Am(c){var
  a=0,e=k(c),b=10,d=1;if(e>0)switch(ay(c,a)){case
  45:a++;d=-1;break;case
  43:a++;d=1;break}if(a+1<e&&ay(c,a)==48)switch(ay(c,a+1)){case
  120:case
  88:b=16;a+=2;break;case
  111:case
  79:b=8;a+=2;break;case
  98:case
  66:b=2;a+=2;break;case
  117:case
  85:a+=2;break}return[a,d,b]}function
  j8(a){if(a>=48&&a<=57)return a-48;if(a>=65&&a<=90)return a-55;if(a>=97&&a<=122)return a-87;return-1}function
  c7(f){var
  h=Am(f),c=h[0],i=h[1],d=h[2],g=k(f),j=-1>>>0,e=c<g?ay(f,c):0,b=j8(e);if(b<0||b>=d)av(dX);var
  a=b;for(c++;c<g;c++){e=ay(f,c);if(e==95)continue;b=j8(e);if(b<0||b>=d)break;a=d*a+b;if(a>j)av(dX)}if(c!=g)av(dX);a=i*a;if(d==10&&(a|0)!=a)av(dX);return a|0}function
  z6(){var
  b=console,c=["log","debug","info","warn",jD,"assert",bE,"dirxml","trace","group","groupCollapsed","groupEnd","time","timeEnd"];function
  d(){}for(var
  a=0;a<c.length;a++)if(!b[c[a]])b[c[a]]=d;return b}function
  z7(c){var
  d=c.length,b=new
  Array(d+1);b[0]=0;for(var
  a=0;a<d;a++)b[a+1]=c[a];return b}function
  z8(d){return function(){var
  b=arguments.length;if(b>0){var
  c=new
  Array(b);for(var
  a=0;a<b;a++)c[a]=arguments[a];return aF(d,c)}else
  return aF(d,[undefined])}}function
  z9(d){return function(){var
  c=arguments.length,b=new
  Array(c+1);b[0]=this;for(var
  a=0;a<c;a++)b[a+1]=arguments[a];return aF(d,b)}}function
  ah(b){b=aw(b);var
  d=b.length/2,c=new
  Array(d);for(var
  a=0;a<d;a++)c[a]=(b.charCodeAt(2*a)|b.charCodeAt(2*a+1)<<8)<<16>>16;return c}function
  z_(b,t,a){var
  n=2,o=3,r=5,d=6,h=7,g=8,j=9,m=1,l=2,q=3,s=4,p=5;if(!b.lex_default){b.lex_base=ah(b[m]);b.lex_backtrk=ah(b[l]);b.lex_check=ah(b[p]);b.lex_trans=ah(b[s]);b.lex_default=ah(b[q])}var
  e,c=t,k=c$(a[n]);if(c>=0){a[h]=a[r]=a[d];a[g]=-1}else
  c=-c-1;for(;;){var
  f=b.lex_base[c];if(f<0)return-f-1;var
  i=b.lex_backtrk[c];if(i>=0){a[h]=a[d];a[g]=i}if(a[d]>=a[o])if(a[j]==0)return-c-1;else
  e=aK;else{e=k[a[d]];a[d]++}if(b.lex_check[f+e]==c)c=b.lex_trans[f+e];else
  c=b.lex_default[c];if(c<0){a[d]=a[h];if(a[g]==-1)av(im);else
  return a[g]}else
  if(e==aK)a[j]=0}}function
  bO(c){var
  b=0;for(var
  a=c.length-1;a>=0;a--){var
  d=c[a];b=[0,d,b]}return b}function
  aM(a,d){if(a<0)c2();var
  a=a+1|0,b=new
  Array(a);b[0]=0;for(var
  c=1;c<a;c++)b[c]=d;return b}function
  zz(){var
  a=new
  ArrayBuffer(64),b=new
  Uint32Array(a),c=new
  Uint8Array(a);return{len:0,w:new
  Uint32Array([0x67452301,0xEFCDAB89,0x98BADCFE,0x10325476]),b32:b,b8:c}}var
  ej=function(){function
  k(a,b){return a+b|0}function
  a(d,a,c,f,b,e){a=k(k(a,d),k(f,e));return k(a<<b|a>>>32-b,c)}function
  f(c,b,d,e,h,f,g){return a(b&d|~b&e,c,b,h,f,g)}function
  g(d,b,e,c,h,f,g){return a(b&c|e&~c,d,b,h,f,g)}function
  h(c,b,d,e,h,f,g){return a(b^d^e,c,b,h,f,g)}function
  i(c,b,d,e,h,f,g){return a(d^(b|~e),c,b,h,f,g)}return function(j,e){var
  a=j[0],b=j[1],c=j[2],d=j[3];a=f(a,b,c,d,e[0],7,0xD76AA478);d=f(d,a,b,c,e[1],12,0xE8C7B756);c=f(c,d,a,b,e[2],17,0x242070DB);b=f(b,c,d,a,e[3],22,0xC1BDCEEE);a=f(a,b,c,d,e[4],7,0xF57C0FAF);d=f(d,a,b,c,e[5],12,0x4787C62A);c=f(c,d,a,b,e[6],17,0xA8304613);b=f(b,c,d,a,e[7],22,0xFD469501);a=f(a,b,c,d,e[8],7,0x698098D8);d=f(d,a,b,c,e[9],12,0x8B44F7AF);c=f(c,d,a,b,e[10],17,0xFFFF5BB1);b=f(b,c,d,a,e[11],22,0x895CD7BE);a=f(a,b,c,d,e[12],7,0x6B901122);d=f(d,a,b,c,e[13],12,0xFD987193);c=f(c,d,a,b,e[14],17,0xA679438E);b=f(b,c,d,a,e[15],22,0x49B40821);a=g(a,b,c,d,e[1],5,0xF61E2562);d=g(d,a,b,c,e[6],9,0xC040B340);c=g(c,d,a,b,e[11],14,0x265E5A51);b=g(b,c,d,a,e[0],20,0xE9B6C7AA);a=g(a,b,c,d,e[5],5,0xD62F105D);d=g(d,a,b,c,e[10],9,0x02441453);c=g(c,d,a,b,e[15],14,0xD8A1E681);b=g(b,c,d,a,e[4],20,0xE7D3FBC8);a=g(a,b,c,d,e[9],5,0x21E1CDE6);d=g(d,a,b,c,e[14],9,0xC33707D6);c=g(c,d,a,b,e[3],14,0xF4D50D87);b=g(b,c,d,a,e[8],20,0x455A14ED);a=g(a,b,c,d,e[13],5,0xA9E3E905);d=g(d,a,b,c,e[2],9,0xFCEFA3F8);c=g(c,d,a,b,e[7],14,0x676F02D9);b=g(b,c,d,a,e[12],20,0x8D2A4C8A);a=h(a,b,c,d,e[5],4,0xFFFA3942);d=h(d,a,b,c,e[8],11,0x8771F681);c=h(c,d,a,b,e[11],16,0x6D9D6122);b=h(b,c,d,a,e[14],23,0xFDE5380C);a=h(a,b,c,d,e[1],4,0xA4BEEA44);d=h(d,a,b,c,e[4],11,0x4BDECFA9);c=h(c,d,a,b,e[7],16,0xF6BB4B60);b=h(b,c,d,a,e[10],23,0xBEBFBC70);a=h(a,b,c,d,e[13],4,0x289B7EC6);d=h(d,a,b,c,e[0],11,0xEAA127FA);c=h(c,d,a,b,e[3],16,0xD4EF3085);b=h(b,c,d,a,e[6],23,0x04881D05);a=h(a,b,c,d,e[9],4,0xD9D4D039);d=h(d,a,b,c,e[12],11,0xE6DB99E5);c=h(c,d,a,b,e[15],16,0x1FA27CF8);b=h(b,c,d,a,e[2],23,0xC4AC5665);a=i(a,b,c,d,e[0],6,0xF4292244);d=i(d,a,b,c,e[7],10,0x432AFF97);c=i(c,d,a,b,e[14],15,0xAB9423A7);b=i(b,c,d,a,e[5],21,0xFC93A039);a=i(a,b,c,d,e[12],6,0x655B59C3);d=i(d,a,b,c,e[3],10,0x8F0CCC92);c=i(c,d,a,b,e[10],15,0xFFEFF47D);b=i(b,c,d,a,e[1],21,0x85845DD1);a=i(a,b,c,d,e[8],6,0x6FA87E4F);d=i(d,a,b,c,e[15],10,0xFE2CE6E0);c=i(c,d,a,b,e[6],15,0xA3014314);b=i(b,c,d,a,e[13],21,0x4E0811A1);a=i(a,b,c,d,e[4],6,0xF7537E82);d=i(d,a,b,c,e[11],10,0xBD3AF235);c=i(c,d,a,b,e[2],15,0x2AD7D2BB);b=i(b,c,d,a,e[9],21,0xEB86D391);j[0]=k(a,j[0]);j[1]=k(b,j[1]);j[2]=k(c,j[2]);j[3]=k(d,j[3])}}();function
  zA(a,e,b){var
  d=a.len&bq,c=0;a.len+=b;if(d){var
  f=64-d;if(b<f){a.b8.set(e.subarray(0,b),d);return}a.b8.set(e.subarray(0,f),d);ej(a.w,a.b32);b-=f;c+=f}while(b>=64){a.b8.set(e.subarray(c,c+64),0);ej(a.w,a.b32);b-=64;c+=64}if(b)a.b8.set(e.subarray(c,c+b),0)}function
  zy(b){var
  c=b.len&bq;b.b8[c]=aD;c++;if(c>56){for(var
  a=c;a<64;a++)b.b8[a]=0;ej(b.w,b.b32);for(var
  a=0;a<56;a++)b.b8[a]=0}else
  for(var
  a=c;a<56;a++)b.b8[a]=0;b.b32[14]=b.len<<3;b.b32[15]=b.len>>29&0x1FFFFFFF;ej(b.w,b.b32);var
  e=new
  Uint8Array(16);for(var
  d=0;d<4;d++)for(var
  a=0;a<4;a++)e[d*4+a]=b.w[d]>>8*a&aC;return e}function
  As(b){return a(c_(b,0,b.length))}function
  Ab(e,c,b){var
  a=zz(),d=c$(e);zA(a,d.subarray(c,c+b),b);return As(zy(a))}function
  Ac(c,b,a){return Ab(au(c),b,a)}function
  Ae(){return 0}var
  bf=new
  Array();function
  aX(b){var
  a=bf[b];if(!a.opened)r("Cannot flush a closed channel");if(!a.buffer||a.buffer_curr==0)return 0;if(a.output)a.output(c_(a.buffer,0,a.buffer_curr));else
  a.file.write(a.offset,a.buffer,0,a.buffer_curr);a.offset+=a.buffer_curr;a.buffer_curr=0;return 0}function
  Au(a,b){return null}var
  er=new
  Array(3);function
  c1(a,b){ab.call(this,D(0));this.log=function(a){return 0};if(a==1&&typeof
  console.log=="function")this.log=console.log;else
  if(a==2&&typeof
  console.error=="function")this.log=console.error;else
  if(typeof
  console.log=="function")this.log=console.log;this.flags=b}c1.prototype.length=function(){return 0};c1.prototype.write=function(e,c,b,a){if(this.log){if(a>0&&b>=0&&b+a<=c.length&&c[b+a-1]==10)a--;var
  d=D(a);bc(f_(c),b,d,0,a);this.log(d.toUtf16());return 0}r(this.fd+ip)};c1.prototype.read=function(c,a,d,b){r(this.fd+": file descriptor is write only")};c1.prototype.close=function(){this.log=undefined};function
  es(b,a){if(a==undefined)a=er.length;er[a]=b;return a}function
  AF(c,b,f){var
  a={};while(b){switch(b[1]){case
  0:a.rdonly=1;break;case
  1:a.wronly=1;break;case
  2:a.append=1;break;case
  3:a.create=1;break;case
  4:a.truncate=1;break;case
  5:a.excl=1;break;case
  6:a.binary=1;break;case
  7:a.text=1;break;case
  8:a.nonblock=1;break}b=b[2]}if(a.rdonly&&a.wronly)r(aw(c)+fG);if(a.text&&a.binary)r(aw(c)+fQ);var
  d=ke(c),e=d.device.open(d.rest,a);return es(e,undefined)}(function(){function
  a(a,b){return da()?Au(a,b):new
  c1(a,b)}es(a(0,{rdonly:1,altname:"/dev/stdin",isCharacterDevice:true}),0);es(a(1,{buffered:2,wronly:1,isCharacterDevice:true}),1);es(a(2,{buffered:2,wronly:1,isCharacterDevice:true}),2)}());function
  Af(c){var
  a=er[c];if(a.flags.wronly)r(jF+c+" is writeonly");var
  d=null,b={file:a,offset:a.flags.append?a.length():0,fd:c,opened:true,out:false,buffer_curr:0,buffer_max:0,buffer:new
  Uint8Array(iJ),refill:d};bf[b.fd]=b;return b.fd}function
  j3(c){var
  a=er[c];if(a.flags.rdonly)r(jF+c+" is readonly");var
  d=a.flags.buffered!==undefined?a.flags.buffered:1,b={file:a,offset:a.flags.append?a.length():0,fd:c,opened:true,out:true,buffer_curr:0,buffer:new
  Uint8Array(iJ),buffered:d};bf[b.fd]=b;return b.fd}function
  Ag(){var
  b=0;for(var
  a=0;a<bf.length;a++)if(bf[a]&&bf[a].opened&&bf[a].out)b=[0,bf[a].fd,b];return b}function
  O(a){return a}function
  Ah(c,b,f,g){var
  a=bf[c];if(!a.opened)r("Cannot output to a closed channel");var
  b=c$(b);b=b.subarray(f,f+g);if(a.buffer_curr+b.length>a.buffer.length){var
  e=new
  Uint8Array(a.buffer_curr+b.length);e.set(a.buffer);a.buffer=e}switch(a.buffered){case
  0:a.buffer.set(b,a.buffer_curr);a.buffer_curr+=b.length;aX(c);break;case
  1:a.buffer.set(b,a.buffer_curr);a.buffer_curr+=b.length;if(a.buffer_curr>=a.buffer.length)aX(c);break;case
  2:var
  d=b.lastIndexOf(10);if(d<0){a.buffer.set(b,a.buffer_curr);a.buffer_curr+=b.length;if(a.buffer_curr>=a.buffer.length)aX(c)}else{a.buffer.set(b.subarray(0,d+1),a.buffer_curr);a.buffer_curr+=d+1;aX(c);a.buffer.set(b.subarray(d+1),a.buffer_curr);a.buffer_curr+=b.length-d-1}break}return 0}function
  j4(b,a,d,c){return Ah(b,au(a),d,c)}function
  gl(c,b){var
  d=a(String.fromCharCode(b));j4(c,d,0,1);return 0}function
  Ai(b,a){if(a==0)go();return b%a}function
  z$(d,a,c,f){for(;;){var
  b=d.charCodeAt(a);a++;if(b==ag)return;var
  e=d.charCodeAt(a);a++;if(e==ag)c[b+1]=f;else
  c[b+1]=c[e+1]}}function
  j2(d,a,c){for(;;){var
  b=d.charCodeAt(a);a++;if(b==ag)return;var
  e=d.charCodeAt(a);a++;if(e==ag)c[b+1]=-1;else
  c[b+1]=c[e+1]}}function
  Ak(a,D,b){var
  t=2,u=3,A=5,f=6,i=7,h=8,n=9,j=10,r=1,p=2,y=3,B=4,v=5,s=6,q=7,z=8,C=9,w=10,x=11;if(!a.lex_default){a.lex_base=ah(a[r]);a.lex_backtrk=ah(a[p]);a.lex_check=ah(a[v]);a.lex_trans=ah(a[B]);a.lex_default=ah(a[y])}if(!a.lex_default_code){a.lex_base_code=ah(a[s]);a.lex_backtrk_code=ah(a[q]);a.lex_check_code=ah(a[w]);a.lex_trans_code=ah(a[C]);a.lex_default_code=ah(a[z])}if(a.lex_code==null)a.lex_code=aw(a[x]);var
  e,c=D,o=c$(b[t]);if(c>=0){b[i]=b[A]=b[f];b[h]=-1}else
  c=-c-1;for(;;){var
  g=a.lex_base[c];if(g<0){var
  d=a.lex_base_code[c];j2(a.lex_code,d,b[j]);return-g-1}var
  l=a.lex_backtrk[c];if(l>=0){var
  d=a.lex_backtrk_code[c];j2(a.lex_code,d,b[j]);b[i]=b[f];b[h]=l}if(b[f]>=b[u])if(b[n]==0)return-c-1;else
  e=aK;else{e=o[b[f]];b[f]++}var
  k=c;if(a.lex_check[g+e]==c)c=a.lex_trans[g+e];else
  c=a.lex_default[c];if(c<0){b[f]=b[i];if(b[h]==-1)av(im);else
  return b[h]}else{var
  m=a.lex_base_code[k],d;if(a.lex_check_code[m+e]==k)d=a.lex_trans_code[m+e];else
  d=a.lex_default_code[k];if(d>0)z$(a.lex_code,d,b[j],b[f]);if(e==aK)b[n]=0}}}function
  bQ(a,b){return+(f$(a,b,false)!=0)}function
  j6(d,c){var
  b=new
  Array(c+1);b[0]=d;for(var
  a=1;a<=c;a++)b[a]=0;return b}function
  Al(a,b){a[0]=bo;a[1]=b;return 0}function
  bg(a){if(a
  instanceof
  Array&&a[0]==a[0]>>>0)return a[0];else
  if(c8(a))return eb;else
  if(eo(a))return eb;else
  if(a
  instanceof
  Function||typeof
  a=="function")return 247;else
  if(a&&a.caml_custom)return b0;else
  return cG}function
  aH(b,c,a){if(a&&x.toplevelReloc)b=x.toplevelReloc(a);aG[b+1]=c;if(a)aG[a]=c}function
  gp(a,b){j5[aw(a)]=b;return 0}function
  Ap(a){a[2]=j7++;return a}function
  d(a,b){return jS(a,b)}function
  Ar(){L(fY)}function
  B(b,a){if(a>>>0>=k(b))Ar();return ay(b,a)}function
  g(a,b){return 1-d(a,b)}function
  At(){return 0x7FFFFFFF/4|0}function
  An(){j9(aG.Not_found)}function
  ka(c){var
  b=x.process,a=gk(c);if(b&&b.env&&b.env[a]!=undefined)return ai(b.env[a]);if(x.jsoo_static_env&&x.jsoo_static_env[a])return ai(x.jsoo_static_env[a]);An()}function
  Av(){if(x.crypto)if(typeof
  x.crypto.getRandomValues==="function"){var
  a=new
  Uint32Array(1);x.crypto.getRandomValues(a);return[0,a[0]]}else
  if(x.crypto.randomBytes===iG){var
  b=x.crypto.randomBytes(4),a=new
  Uint32Array(b);return[0,a[0]]}var
  c=new
  Date().getTime(),d=c^0xffffffff*Math.random();return[0,d]}function
  bR(a){var
  b=1;while(a&&a.joo_tramp){a=a.joo_tramp.apply(null,a.joo_args);b++}return a}function
  n(b,a){return{joo_tramp:b,joo_args:a}}function
  j$(a){return a}function
  C(a){if(a
  instanceof
  Array)return a;if(x.RangeError&&a
  instanceof
  x.RangeError&&a.message&&a.message.match(/maximum call stack/i))return j$(aG.Stack_overflow);if(x.InternalError&&a
  instanceof
  x.InternalError&&a.message&&a.message.match(/too much recursion/i))return j$(aG.Stack_overflow);if(a
  instanceof
  x.Error&&bP(fy))return[0,bP(fy),a];return[0,aG.Failure,ai(String(a))]}function
  j1(a){return a.slice(1)}function
  Aw(d){var
  c=k(d),b=new
  Array(c),a=0;for(;a<c;a++)b[a]=ay(d,a);return b}var
  AC=function(){var
  b=[an,an,an,an,an,an,aC,0x03,il,aC,aC,0x87,il,aC,aC,0x07,an,an,an,an,an,an,an,an,aC,aC,iS,aC,aC,aC,iS,aC],c={CHAR:0,CHARNORM:1,STRING:2,STRINGNORM:3,CHARCLASS:4,BOL:5,EOL:6,WORDBOUNDARY:7,BEGGROUP:8,ENDGROUP:9,REFGROUP:10,ACCEPT:11,SIMPLEOPT:12,SIMPLESTAR:13,SIMPLEPLUS:14,GOTO:15,PUSHBACK:16,SETMARK:17,CHECKPROGRESS:18};function
  r(a){return b[a>>3]>>(a&7)&1}function
  o(b,a){return B(b,a>>3)>>(a&7)&1}function
  a(m,b,a,B){var
  w=j1(m[1]),k=j1(m[2]),v=aw(m[3]),y=m[4]|0,z=m[5]|0,C=m[6]|0,b=Aw(b),l=0,x=false,t=[],i=new
  Array(y),q=new
  Array(z);for(var
  f=0;f<i.length;f++)i[f]={start:-1,end:-1};i[0].start=a;function
  e(){while(t.length){var
  b=t.pop();if(b.undo)b.undo.obj[b.undo.prop]=b.undo.value;else
  if(b.pos){l=b.pos.pc;a=b.pos.txt;return}}x=true}function
  p(a){t.push(a)}function
  u(){i[0].end=a;var
  d=new
  Array(1+i.length*2);d[0]=0;for(var
  c=0;c<i.length;c++){var
  b=i[c];if(b.start<0||b.end<0)b.start=b.end=-1;d[2*c+1]=b.start;d[2*c+1+1]=b.end}return d}function
  j(){if(B)return u();else
  e()}while(!x){var
  A=w[l]&ag,s=w[l]>>8,d=s&ag,g=b[a],h;l++;switch(A){case
  c.CHAR:if(a===b.length){j();break}if(g===d)a++;else
  e();break;case
  c.CHARNORM:if(a===b.length){j();break}if(v.charCodeAt(g)===d)a++;else
  e();break;case
  c.STRING:for(var
  n=aw(k[d]),f=0;f<n.length;f++){if(a===b.length){j();break}if(g===n.charCodeAt(f))g=b[++a];else{e();break}}break;case
  c.STRINGNORM:for(var
  n=aw(k[d]),f=0;f<n.length;f++){if(a===b.length){j();break}if(v.charCodeAt(g)===n.charCodeAt(f))g=b[++a];else{e();break}}break;case
  c.CHARCLASS:if(a===b.length){j();break}if(o(k[d],g))a++;else
  e();break;case
  c.BOL:if(a>0&&b[a-1]!=10)e();break;case
  c.EOL:if(a<b.length&&b[a]!=10)e();break;case
  c.WORDBOUNDARY:if(a==0){if(a===b.length){j();break}if(r(b[0]))break;e()}else
  if(a===b.length){if(r(b[a-1]))break;e()}else{if(r(b[a-1])!=r(b[a]))break;e()}break;case
  c.BEGGROUP:h=i[d];p({undo:{obj:h,prop:cX,value:h.start}});h.start=a;break;case
  c.ENDGROUP:h=i[d];p({undo:{obj:h,prop:"end",value:h.end}});h.end=a;break;case
  c.REFGROUP:h=i[d];if(h.start<0||h.end<0){e();break}for(var
  f=h.start;f<h.end;f++){if(a===b.length){j();break}if(b[f]!=b[a]){e();break}a++}break;case
  c.SIMPLEOPT:if(o(k[d],g))a++;break;case
  c.SIMPLESTAR:while(o(k[d],g))g=b[++a];break;case
  c.SIMPLEPLUS:if(a===b.length){j();break}if(o(k[d],g))do
  g=b[++a];while(o(k[d],g));else
  e();break;case
  c.ACCEPT:return u();case
  c.GOTO:l=l+s;break;case
  c.PUSHBACK:p({pos:{pc:l+s,txt:a}});break;case
  c.SETMARK:p({undo:{obj:q,prop:d,value:q[d]}});q[d]=a;break;case
  c.CHECKPROGRESS:if(q[d]===a)e();break;default:throw new
  Error("Invalid bytecode")}}return 0}return a}();function
  AD(d,b,a){if(a<0||a>k(b))L("Str.search_forward");while(a<=k(b)){var
  c=AC(d,b,a,0);if(c)return c;a++}return[0]}function
  z5(a){switch(a[2]){case-8:case-11:case-12:return 1;default:return 0}}function
  zK(b){var
  a=e;if(b[0]==0){a+=b[1][1];if(b.length==3&&b[2][0]==0&&z5(b[1]))var
  f=b[2],g=1;else
  var
  g=2,f=b;a+="(";for(var
  d=g;d<f.length;d++){if(d>g)a+=cB;var
  c=f[d];if(typeof
  c=="number")a+=c.toString();else
  if(c
  instanceof
  aW)a+=bF+c.toString()+bF;else
  if(typeof
  c=="string")a+=bF+c.toString()+bF;else
  a+=i4}a+=fD}else
  if(b[0]==A)a+=b[1];return a}function
  jW(a){if(a
  instanceof
  Array&&(a[0]==0||a[0]==A)){var
  c=bP(ix);if(c)c(a,false);else{var
  d=zK(a),b=bP(ik);if(b)b(0);console.error(fL+d+"\n")}}else
  throw a}function
  Aq(){var
  a=x.process;if(a&&a.on)a.on("uncaughtException",function(b,c){jW(b);a.exit(2)});else
  if(x.addEventListener)x.addEventListener(jD,function(a){if(a.error)jW(a.error)})}Aq();function
  b(a,b){return a.length==1?a(b):aF(a,[b])}function
  f(a,b,c){return a.length==2?a(b,c):aF(a,[b,c])}function
  aJ(a,b,c,d){return a.length==3?a(b,c,d):aF(a,[b,c,d])}function
  fl(a,b,c,d,e){return a.length==4?a(b,c,d,e):aF(a,[b,c,d,e])}function
  cz(a,b,c,d,e,f){return a.length==5?a(b,c,d,e,f):aF(a,[b,c,d,e,f])}function
  zw(a,b,c,d,e,f,g,h){return a.length==7?a(b,c,d,e,f,g,h):aF(a,[b,c,d,e,f,g,h])}zL();var
  eu=[A,a(jB),-1],gu=[A,a(iz),-2],db=[A,a(fw),-3],et=[A,a(i3),-4],p=[A,a(i_),-7],gs=[A,a(iM),-8],gt=[A,a(jc),-9],s=[A,a(jr),-11],gv=[A,a(ij),-12],di=[0,a(e),0,0,-1],gL=[0,a(e),1,0,0],zr=[4,0,0,0,[12,45,[4,0,0,0,0]]],eK=[0,[11,a('File "'),[2,0,[11,a('", line '),[4,0,0,0,[11,a(iR),[4,0,0,0,[12,45,[4,0,0,0,[11,a(ji),[2,0,0]]]]]]]]]],a('File "%s", line %d, characters %d-%d: %s')],a3=[0,a("\0\0\x01\0\xf9\xff\0\0A\0\xa3\0\xfd\xff\0\0\x01\0\xff\xff\xf1\0 \0\x80\0\xfd\xff\x01\0@\x01\x8e\x01.\x000\0\xfd\xff\x04\0\0\0\xff\xff\x7f\0\xa0\0\xfe\xff\xff\xff\xee\0\x0e\x01\xfd\xff\xfe\xff\x02\0\xff\xff\0\x02\xf7\xffr\x02\xf9\xff\xfa\xff\xfb\xff\x84\x02\0\0\xf8\x02\xff\xff\xfe\xff\xfe\x02\xf9\xff\x04\x03\xfb\xff\xfc\xff\x02\0\n\x03\xff\xff\xfe\xff\x03\0\xff\xffu\0\xff\xff"),a("\xff\xff\x07\0\xff\xff\x05\0\xff\xff\x03\0\xff\xff\x01\0\xff\xff\xff\xff\x04\0\xff\xff\x03\0\xff\xff\x01\0\xff\xff\0\0\xff\xff\x03\0\xff\xff\x01\0\xff\xff\xff\xff\xff\xff\x02\0\xff\xff\xff\xff\xff\xff\x04\0\xff\xff\xff\xff\x03\0\xff\xff\xff\xff\xff\xff\x07\0\xff\xff\xff\xff\xff\xff\x03\0\x02\0\x07\0\xff\xff\xff\xff\xff\xff\xff\xff\x05\0\xff\xff\xff\xff\x02\0\x05\0\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff"),a("\x01\0\x01\0\0\0\xff\xff\xff\xff\xff\xff\0\0\xff\xff\xff\xff\0\0\xff\xff\f\0\f\0\0\0\xff\xff\xff\xff\xff\xff\x12\0\x12\0\0\0\xff\xff\xff\xff\0\0\x18\0\x18\0\0\0\0\0\x1c\0\x1c\0\0\0\0\0\xff\xff\0\0#\0\0\0#\0\0\0\0\0\0\0#\0\xff\xff#\0\0\0\0\0.\0\0\0.\0\0\0\0\0\xff\xff.\0\0\0\0\x005\0\0\x007\0\0\0"),a("\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0(\0(\x001\x001\0(\0\0\x001\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0(\0\x07\x001\0\0\0\0\x006\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\b\0\t\0\x04\0\x0f\0\x15\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\x05\0\0\0\x03\0\xff\xff\x16\0\x06\0 \0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x14\0\x0e\0\xff\xff\0\0\x05\0\0\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\n\0\0\0\0\0\0\0\0\0\0\0\0\0\n\0\n\0\n\0\n\0\n\0\n\0\n\0\n\0\n\0\n\0\n\0\n\0\n\0\n\0\n\0\n\0\n\0\n\0\n\0\n\0\n\0\n\0\n\0\n\0\n\0\n\x008\0\0\0\0\0\0\0\n\0\0\0\n\0\n\0\n\0\n\0\n\0\n\0\n\0\n\0\n\0\n\0\n\0\n\0\n\0\n\0\n\0\n\0\n\0\n\0\n\0\n\0\n\0\n\0\n\0\n\0\n\0\n\0\xff\xff\x1a\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\x05\0\x05\0\0\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\xff\xff\0\0\0\0\0\0\0\0\0\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\0\0\0\0\x02\0\xff\xff\x05\0\xff\xff\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\n\0\n\0\r\0\n\0\n\0\n\0\n\0\n\0\n\0\n\0\n\0\n\0\n\0\n\0\x1e\0\x1f\0\x13\0\0\0\xff\xff\0\0\n\0\n\0\n\0\n\0\n\0\n\0\n\0\n\0\n\0\n\0\n\0\n\0\n\0\n\0\n\0\n\0\n\0\n\0\n\0\n\0\n\0\n\0\n\0\n\0\n\0\n\0\xff\xff\xff\xff\0\0\0\0\n\0\0\0\n\0\n\0\n\0\n\0\n\0\n\0\n\0\n\0\n\0\n\0\n\0\n\0\n\0\n\0\n\0\n\0\n\0\n\0\n\0\n\0\n\0\n\0\n\0\n\0\n\0\n\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\xff\xff\0\0\0\0\0\0\0\0\x10\0\0\0\0\0\0\0\0\0\x19\0\xff\xff\x10\0\x10\0\x10\0\x10\0\x10\0\x10\0\x10\0\x10\0\x10\0\x10\0\x10\0\x10\0\x10\0\x10\0\x10\0\x10\0\x10\0\x10\0\x10\0\x10\0\x10\0\x10\0\x10\0\x10\0\x10\0\x10\0\0\0\0\0\0\0\0\0\x10\0\xff\xff\x10\0\x10\0\x10\0\x10\0\x10\0\x10\0\x10\0\x10\0\x10\0\x10\0\x10\0\x10\0\x10\0\x10\0\x10\0\x10\0\x10\0\x10\0\x10\0\x10\0\x10\0\x10\0\x10\0\x10\0\x10\0\x10\0\x10\0\x10\0\0\0\x10\0\x10\0\x10\0\x10\0\x10\0\x10\0\x10\0\x10\0\x10\0\x10\0\x10\0\0\0\0\0\0\0\0\0\0\0\0\0\x10\0\x10\0\x10\0\x10\0\x10\0\x10\0\x10\0\x10\0\x10\0\x10\0\x10\0\x10\0\x10\0\x10\0\x10\0\x10\0\x10\0\x10\0\x10\0\x10\0\x10\0\x10\0\x10\0\x10\0\x10\0\x10\0\0\0\0\0\0\0\0\0\x10\0\x1d\0\x10\0\x10\0\x10\0\x10\0\x10\0\x10\0\x10\0\x10\0\x10\0\x10\0\x10\0\x10\0\x10\0\x10\0\x10\0\x10\0\x10\0\x10\0\x10\0\x10\0\x10\0\x10\0\x10\0\x10\0\x10\0\x10\0(\0(\0\0\0\0\0(\0\xff\xff\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0(\0\0\0%\0\0\0\0\0\0\0\0\0$\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0)\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0'\0\0\0\0\0&\0*\0\0\0\0\0'\0'\0'\0'\0'\0'\0'\0'\0'\0'\0'\0'\0'\0'\0'\0'\0'\0'\0'\0'\0'\0'\0'\0'\0'\0'\0\0\0\0\0\0\0\0\0'\0\0\0'\0'\0'\0'\0'\0'\0'\0'\0'\0'\0'\0'\0'\0'\0'\0'\0'\0'\0'\0'\0'\0'\0'\0'\0'\0'\0\xff\xff\xff\xff\0\0\0\0\xff\xff\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\xff\xff\xff\xff\0\0\0\0\xff\xff\xff\xff\0\0\xff\xff\0\0\0\0\0\0\0\0\xff\xff\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\xff\xff\0\0\xff\xff\0\0\0\0\0\0\0\0\xff\xff\0\0\0\0\0\0\xff\xff\xff\xff'\0'\0\0\0'\0'\0'\0'\0'\0'\0'\0'\0'\0'\0'\0\0\0\0\0\xff\xff\xff\xff\0\0\0\0'\0'\0'\0'\0'\0'\0'\0'\0'\0'\0'\0'\0'\0'\0'\0'\0'\0'\0'\0'\0'\0'\0'\0'\0'\0'\0\0\0\0\0\0\0\0\0'\0\0\0'\0'\0'\0'\0'\0'\0'\0'\0'\0'\0'\0'\0'\0'\0'\0'\0'\0'\0'\0'\0'\0'\0'\0'\0'\0'\0\0\0\"\0\xff\xff\xff\xff\0\0\0\0\xff\xff\0\x001\x001\0\0\0\0\x001\0\0\0\xff\xff\xff\xff\0\0\0\0\xff\xff\0\0\xff\xff\xff\xff\0\0\0\0\xff\xff\xff\xff\0\0\xff\xff\0\0\0\0\0\x001\0\xff\xff0\0\0\0\0\0\0\0\xff\xff/\0\xff\xff\0\0\0\0\0\0\xff\xff\xff\xff\xff\xff2\0\0\0\0\0\0\0\xff\xff\0\0\0\0\0\0\xff\xff+\0\0\0\0\0\0\0\0\0\0\x003\0\0\0\0\0\0\0\0\0\0\0\xff\xff\0\0\0\0\0\0\0\0\0\x004\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\xff\xff\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\xff\xff\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\xff\xff\0\0\0\0\0\0\0\0\0\0-\0\0\0\0\0\0\0\0\0\0\0\xff\xff\0\0\0\0\0\0\0\0\0\0\xff\xff"),a("\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff(\0(\x001\x001\0(\0\xff\xff1\0\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff(\0\x03\x001\0\xff\xff\xff\xff5\0\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\x07\0\b\0\x03\0\x0e\0\x14\0\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\x03\0\xff\xff\0\0\x01\0\x15\0\x03\0\x1f\0\x03\0\x03\0\x03\0\x03\0\x03\0\x03\0\x03\0\x03\0\x03\0\x03\0\x03\0\x03\0\x03\0\x03\0\x03\0\x03\0\x03\0\x03\0\x03\0\x03\0\x03\0\x03\0\x03\0\x03\0\x03\0\x03\0\x11\0\x0b\0\x12\0\xff\xff\x03\0\xff\xff\x03\0\x03\0\x03\0\x03\0\x03\0\x03\0\x03\0\x03\0\x03\0\x03\0\x03\0\x03\0\x03\0\x03\0\x03\0\x03\0\x03\0\x03\0\x03\0\x03\0\x03\0\x03\0\x03\0\x03\0\x03\0\x03\0\x04\0\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\x04\0\x04\0\x04\0\x04\0\x04\0\x04\0\x04\0\x04\0\x04\0\x04\0\x04\0\x04\0\x04\0\x04\0\x04\0\x04\0\x04\0\x04\0\x04\0\x04\0\x04\0\x04\0\x04\0\x04\0\x04\0\x04\x007\0\xff\xff\xff\xff\xff\xff\x04\0\xff\xff\x04\0\x04\0\x04\0\x04\0\x04\0\x04\0\x04\0\x04\0\x04\0\x04\0\x04\0\x04\0\x04\0\x04\0\x04\0\x04\0\x04\0\x04\0\x04\0\x04\0\x04\0\x04\0\x04\0\x04\0\x04\0\x04\0\f\0\x17\0\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\x05\0\x05\0\xff\xff\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x18\0\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\xff\xff\xff\xff\0\0\x01\0\x05\x005\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\n\0\n\0\x0b\0\n\0\n\0\n\0\n\0\n\0\n\0\n\0\n\0\n\0\n\0\n\0\x1b\0\x1b\0\x11\0\xff\xff\x12\0\xff\xff\n\0\n\0\n\0\n\0\n\0\n\0\n\0\n\0\n\0\n\0\n\0\n\0\n\0\n\0\n\0\n\0\n\0\n\0\n\0\n\0\n\0\n\0\n\0\n\0\n\0\n\0\x1c\0\x1c\0\xff\xff\xff\xff\n\0\xff\xff\n\0\n\0\n\0\n\0\n\0\n\0\n\0\n\0\n\0\n\0\n\0\n\0\n\0\n\0\n\0\n\0\n\0\n\0\n\0\n\0\n\0\n\0\n\0\n\0\n\0\n\0\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff7\0\xff\xff\xff\xff\xff\xff\xff\xff\x0f\0\xff\xff\xff\xff\xff\xff\xff\xff\x17\0\f\0\x0f\0\x0f\0\x0f\0\x0f\0\x0f\0\x0f\0\x0f\0\x0f\0\x0f\0\x0f\0\x0f\0\x0f\0\x0f\0\x0f\0\x0f\0\x0f\0\x0f\0\x0f\0\x0f\0\x0f\0\x0f\0\x0f\0\x0f\0\x0f\0\x0f\0\x0f\0\xff\xff\xff\xff\xff\xff\xff\xff\x0f\0\x18\0\x0f\0\x0f\0\x0f\0\x0f\0\x0f\0\x0f\0\x0f\0\x0f\0\x0f\0\x0f\0\x0f\0\x0f\0\x0f\0\x0f\0\x0f\0\x0f\0\x0f\0\x0f\0\x0f\0\x0f\0\x0f\0\x0f\0\x0f\0\x0f\0\x0f\0\x0f\0\x10\0\x10\0\xff\xff\x10\0\x10\0\x10\0\x10\0\x10\0\x10\0\x10\0\x10\0\x10\0\x10\0\x10\0\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\x10\0\x10\0\x10\0\x10\0\x10\0\x10\0\x10\0\x10\0\x10\0\x10\0\x10\0\x10\0\x10\0\x10\0\x10\0\x10\0\x10\0\x10\0\x10\0\x10\0\x10\0\x10\0\x10\0\x10\0\x10\0\x10\0\xff\xff\xff\xff\xff\xff\xff\xff\x10\0\x1b\0\x10\0\x10\0\x10\0\x10\0\x10\0\x10\0\x10\0\x10\0\x10\0\x10\0\x10\0\x10\0\x10\0\x10\0\x10\0\x10\0\x10\0\x10\0\x10\0\x10\0\x10\0\x10\0\x10\0\x10\0\x10\0\x10\0!\0!\0\xff\xff\xff\xff!\0\x1c\0\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff!\0\xff\xff!\0\xff\xff\xff\xff\xff\xff\xff\xff!\0\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff!\0\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff!\0\xff\xff\xff\xff!\0!\0\xff\xff\xff\xff!\0!\0!\0!\0!\0!\0!\0!\0!\0!\0!\0!\0!\0!\0!\0!\0!\0!\0!\0!\0!\0!\0!\0!\0!\0!\0\xff\xff\xff\xff\xff\xff\xff\xff!\0\xff\xff!\0!\0!\0!\0!\0!\0!\0!\0!\0!\0!\0!\0!\0!\0!\0!\0!\0!\0!\0!\0!\0!\0!\0!\0!\0!\0#\0#\0\xff\xff\xff\xff#\0\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff'\0'\0\xff\xff\xff\xff'\0#\0\xff\xff#\0\xff\xff\xff\xff\xff\xff\xff\xff#\0\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff'\0\xff\xff'\0\xff\xff\xff\xff\xff\xff\xff\xff'\0\xff\xff\xff\xff\xff\xff#\0#\0'\0'\0\xff\xff'\0'\0'\0'\0'\0'\0'\0'\0'\0'\0'\0\xff\xff\xff\xff'\0'\0\xff\xff\xff\xff'\0'\0'\0'\0'\0'\0'\0'\0'\0'\0'\0'\0'\0'\0'\0'\0'\0'\0'\0'\0'\0'\0'\0'\0'\0'\0\xff\xff\xff\xff\xff\xff\xff\xff'\0\xff\xff'\0'\0'\0'\0'\0'\0'\0'\0'\0'\0'\0'\0'\0'\0'\0'\0'\0'\0'\0'\0'\0'\0'\0'\0'\0'\0\xff\xff!\0)\0)\0\xff\xff\xff\xff)\0\xff\xff,\0,\0\xff\xff\xff\xff,\0\xff\xff.\0.\0\xff\xff\xff\xff.\0\xff\xff2\x002\0\xff\xff\xff\xff2\0)\0\xff\xff)\0\xff\xff\xff\xff\xff\xff,\0)\0,\0\xff\xff\xff\xff\xff\xff.\0,\0.\0\xff\xff\xff\xff\xff\xff2\0.\x002\0,\0\xff\xff\xff\xff\xff\xff2\0\xff\xff\xff\xff\xff\xff)\0)\0\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff,\0\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff.\0\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff2\0\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff#\0\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff'\0\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff)\0\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff,\0\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff.\0\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff2\0"),a(e),a(e),a(e),a(e),a(e),a(e)],g9=bO([a(aT),a(b1),a(b2),a(dY),a(cV),a(b_),a(cR),a(ba),a(cb),a(b6),a(a$),a(bG),a(cC),a(cU),a(cE),a(cP),a(cA),a(cQ),a(cF),a(cO),a($),a(W),a(bE),a(d2)]),zs=[0,a(cZ),[0,a(d5),0]],zt=[0,a(bG),[0,a(cb),[0,a(ec),0]]],hL=bO([a(aT),a(b1),a(b2),a(dY),a(cV),a(b_),a(cR),a(ba),a(cb),a(b6),a(a$),a(bG),a(cC),a(cU),a(cE),a(cP),a(cA),a(cQ),a(cF),a(cO),a($),a(W),a(bE),a(d2)]),zu=[0,a(d5),0],zv=[0,a(bG),[0,a(cb),[0,a(ec),0]]],hP=a("o:spid"),hQ=a("id"),dI=[0,a(ii)];aH(11,gv,ij);aH(10,s,jr);aH(9,[A,a(iX),-10],iX);aH(8,gt,jc);aH(7,gs,iM);aH(6,p,i_);aH(5,[A,a(jk),-6],jk);aH(4,[A,a(iF),-5],iF);aH(3,et,i3);aH(2,db,fw);aH(1,gu,iz);aH(0,eu,jB);var
  kl=a("%.12g"),kk=a(bu),kh=a(d8),ki=a("false"),kf=a("Stdlib.Exit"),km=a("CamlinternalLazy.Undefined"),kq=a("\\\\"),kr=a("\\'"),ks=a("\\b"),kt=a("\\t"),ku=a("\\n"),kv=a("\\r"),kp=a("Char.chr"),kz=a("nth"),kA=a("List.nth"),ky=a("tl"),kE=a("String.blit / Bytes.blit_string"),kD=a("Bytes.blit"),kC=a("String.sub / Bytes.sub"),kJ=a("String.contains_from / Bytes.contains_from"),kH=a(e),kG=a(e),kF=a("String.concat"),kM=a("Array.blit"),kL=a("Array.sub"),kR=a("Set.remove_min_elt"),kS=[0,0,0,0],kT=[0,0,0],kU=[0,a("set.ml"),570,18],kN=a(dP),kO=a(dP),kP=a(dP),kQ=a(dP),kZ=a("Map.remove_min_elt"),k0=[0,0,0,0],k1=[0,a("map.ml"),400,10],k2=[0,0,0],kV=a(d7),kW=a(d7),kX=a(d7),kY=a(d7),k3=a("Stdlib.Stack.Empty"),k7=a("Buffer.add: cannot grow buffer"),k6=[0,a(jh),93,2],k5=[0,a(jh),94,2],le=a("%c"),lf=a("%s"),lg=a(ih),lh=a(iK),li=a(i1),lj=a(io),lk=a("%f"),ll=a("%B"),lm=a("%{"),ln=a("%}"),lo=a("%("),lp=a("%)"),lq=a("%a"),lr=a("%t"),ls=a("%?"),lt=a("%r"),lu=a("%_r"),lv=[0,a(N),850,23],lG=[0,a(N),814,21],ly=[0,a(N),815,21],lH=[0,a(N),818,21],lz=[0,a(N),819,21],lI=[0,a(N),822,19],lA=[0,a(N),823,19],lJ=[0,a(N),826,22],lB=[0,a(N),827,22],lK=[0,a(N),831,30],lC=[0,a(N),832,30],lE=[0,a(N),836,26],lw=[0,a(N),837,26],lF=[0,a(N),846,28],lx=[0,a(N),847,28],lD=[0,a(N),851,23],mN=a(iV),mL=[0,a(N),1558,4],mM=a("Printf: bad conversion %["),mO=[0,a(N),1626,39],mP=[0,a(N),1649,31],mQ=[0,a(N),1650,31],mR=a("Printf: bad conversion %_"),mS=a(iU),mT=a(i2),mU=a(iU),mV=a(i2),mJ=a(fC),mH=a("neg_infinity"),mI=a(jz),mG=a(bu),mB=[0,iv],mp=a("%+nd"),mq=a("% nd"),ms=a("%+ni"),mt=a("% ni"),mu=a("%nx"),mv=a("%#nx"),mw=a("%nX"),mx=a("%#nX"),my=a("%no"),mz=a("%#no"),mo=a("%nd"),mr=a(i1),mA=a("%nu"),mc=a("%+ld"),md=a("% ld"),mf=a("%+li"),mg=a("% li"),mh=a("%lx"),mi=a("%#lx"),mj=a("%lX"),mk=a("%#lX"),ml=a("%lo"),mm=a("%#lo"),mb=a("%ld"),me=a(iK),mn=a("%lu"),l1=a("%+Ld"),l2=a("% Ld"),l4=a("%+Li"),l5=a("% Li"),l6=a("%Lx"),l7=a("%#Lx"),l8=a("%LX"),l9=a("%#LX"),l_=a("%Lo"),l$=a("%#Lo"),l0=a("%Ld"),l3=a(io),ma=a("%Lu"),lO=a("%+d"),lP=a("% d"),lR=a("%+i"),lS=a("% i"),lT=a("%x"),lU=a("%#x"),lV=a("%X"),lW=a("%#X"),lX=a("%o"),lY=a("%#o"),lN=a(fS),lQ=a(ih),lZ=a(iV),k8=a("@]"),k9=a("@}"),k_=a("@?"),k$=a("@\n"),la=a("@."),lb=a("@@"),lc=a("@%"),ld=a(ee),lL=a("CamlinternalFormat.Type_mismatch"),mZ=a(e),m0=[0,[11,a(cB),[2,0,[2,0,0]]],a(", %s%s")],nn=[0,[11,a(fL),[2,0,[12,10,0]]],a(jq)],no=[0,[11,a("Fatal error in uncaught exception handler: exception "),[2,0,[12,10,0]]],a("Fatal error in uncaught exception handler: exception %s\n")],nm=a("Fatal error: out of memory in uncaught exception handler"),nk=[0,[11,a(fL),[2,0,[12,10,0]]],a(jq)],ng=[0,[2,0,[12,10,0]],a("%s\n")],m_=a("Raised at"),m$=a("Re-raised at"),na=a("Raised by primitive operation at"),nb=a("Called from"),nc=a(" (inlined)"),ne=a(e),nd=[0,[2,0,[12,32,[2,0,[11,a(' in file "'),[2,0,[12,34,[2,0,[11,a(", line "),[4,0,0,0,[11,a(iR),zr]]]]]]]]]],a('%s %s in file "%s"%s, line %d, characters %d-%d')],nf=[0,[2,0,[11,a(" unknown location"),0]],a("%s unknown location")],m5=a("Out of memory"),m6=a("Stack overflow"),m7=a("Pattern matching failed"),m8=a("Assertion failed"),m9=a("Undefined recursive module"),m1=[0,[12,40,[2,0,[2,0,[12,41,0]]]],a("(%s%s)")],m2=a(e),m3=a(e),m4=[0,[12,40,[2,0,[12,41,0]]],a("(%s)")],mY=[0,[4,0,0,0,0],a(fS)],mW=[0,[3,0,0],a("%S")],mX=a(i4),nh=[0,a(e),a("(Cannot print locations:\n bytecode executable program file not found)"),a("(Cannot print locations:\n bytecode executable program file appears to be corrupt)"),a("(Cannot print locations:\n bytecode executable program file has wrong magic number)"),a("(Cannot print locations:\n bytecode executable program file cannot be opened;\n -- too many open files. Try running with OCAMLRUNPARAM=b=2)")],np=a("x"),ns=a("Hashtbl: unsupported hash table format"),zp=a("OCAMLRUNPARAM"),zn=a("CAMLRUNPARAM"),nq=a(e),nC=a(e),nH=a(e),nG=a(e),nE=a(e),nM=[2,a(fV)],nN=[2,a(fV)],pB=a(e),pC=a(e),p4=a(ae),p5=a('="'),p6=a(bF),p0=a(bb),p1=a(by),p2=a(fM),qa=a("<?"),qb=a(X),qc=a(cW),qd=a("<!--"),qe=a(X),qf=a("-->"),qg=a("<!"),qh=a(X),qi=a(cW),p3=a(fV),p7=a("/>"),p8=a(cW),p9=a(cW),p_=a(jj),p$=a(cW),pZ=a("&quot;"),qj=a("write"),pP=a(e),pQ=a(jj),pR=a(X),pS=a(by),pT=a(X),pU=a(bb),pV=a(X),pW=a(fM),pX=a(e),pL=[0,0,0],pM=[0,0,1],pN=[0,0,0],pO=[0,0,1],pK=[0,a("netstring/code/src/netstring/nethtml.ml"),356,27],pJ=a(e),pG=a(e),pH=[0,bA,fq],pI=[0,bA,fq],pF=a(e),pA=a(e),nO=a("Nethtml.End_of_scan"),nP=a("Nethtml.Found"),nQ=[0,[0,a(b$),[0,j,[0,l,[0,a(b$),[0,a(ed),[0,a(b_),0]]]]]],[0,[0,a(ed),[0,j,v]],0]],nR=bO([l,a(a_),a(cc),a(b8),a(ap),a(aE),a(bp),a(bC),a(bs),a(a9),a(b$)]),nS=a(cS),nT=[0,a(aE),[0,j,ca]],nU=[0,a(bp),[0,j,v]],nV=[0,a(b8),[0,j,v]],nW=[0,a(cc),[0,j,[0,l,0]]],nX=[0,a(a_),[0,j,[0,l,[0,a(cc),[0,a(b8),[0,a(ap),[0,a(aE),[0,a(bp),[0,a(bC),[0,a(bs),0]]]]]]]]]],nY=[0,a(bI),[0,j,i]],nZ=[0,a(c0),[0,j,i]],n0=[0,a(at),[0,j,[0,l,[0,a(c0),[0,a(bI),0]]]]],n1=[0,a(bt),[0,j,v]],n2=[0,a(d_),[0,j,[0,l,[0,a(bt),0]]]],n3=[0,a(cN),[0,j,[0,l,[0,a(at),0]]]],n4=[0,a(cH),[0,j,[0,l,[0,a(at),0]]]],n5=[0,a(cD),[0,j,[0,l,[0,a(at),0]]]],n6=[0,a(ei),[0,j,c]],n7=[0,a(d1),[0,j,c]],n8=[0,a(b7),[0,j,[0,l,0]]],n9=[0,a(d$),[0,j,[0,l,[0,a(b7),0]]]],n_=[0,a(aa),[0,j,i]],n$=[0,a(cM),[0,j,i]],oa=[0,a(dW),[0,j,c]],ob=[0,a(fI),[0,bA,i]],oc=[0,a(fn),[0,bA,i]],od=[0,a(b3),[0,j,v]],oe=[0,a(bC),[0,j,v]],of=[0,a(dT),[0,j,v]],oh=[0,l,[0,a(ap),0]],oi=a(a9),oj=[0,a(cb),[0,o,v]],ok=[0,a(b_),[0,o,i]],ol=[0,a(dY),[0,o,i]],om=[0,a(cC),[0,o,c]],oo=[0,l,[0,a(d1),0]],op=a(bG),oq=[0,a(a$),[0,o,[0,l,[0,a(ei),[0,a(bt),[0,a(d_),[0,a(cD),[0,a(cN),[0,a(cH),[0,a(at),0]]]]]]]]]],or=[0,a(b6),[0,o,v]],ot=[0,l,[0,a(ap),0]],ou=[0,a(ba),0],ov=a(ba),ox=[0,l,[0,a(ap),0]],oy=a(cR),oz=[0,a(cV),[0,o,i]],oA=[0,a(b2),[0,o,i]],oB=[0,a(b1),[0,o,[0,l,[0,a(dW),[0,a(cM),0]]]]],oC=[0,a(cO),[0,o,[0,R,[0,[0,a(aU),[0,a(bs),[0,a(d0),[0,a(eg),[0,a(d3),[0,a(cT),[0,a(cL),zs]]]]]]],c]]]],oD=[0,l,[0,a(aa),0]],oE=a(d2),oF=[0,l,[0,a(aa),0]],oG=a(bE),oH=[0,a(W),[0,o,[0,l,[0,a(aa),0]]]],oI=[0,a($),[0,o,[0,l,[0,a(aa),0]]]],oJ=[0,a(cF),[0,o,c]],oK=[0,a(cQ),[0,o,c]],oL=[0,a(cA),[0,o,c]],oM=[0,a(cP),[0,o,c]],oN=[0,a(cE),[0,o,c]],oO=[0,a(cU),[0,o,c]],oP=[0,a(aT),[0,o,c]],oQ=[0,a(eh),[0,c,[0,R,[0,[0,a(bB),[0,a(dS),[0,a(d6),[0,a(dO),[0,a(b5),[0,a(eh),[0,a(ba),zt]]]]]]],i]]]],oR=[0,a(b5),[0,c,[0,R,[0,[0,a(b5),0],c]]]],oS=[0,a(dO),[0,c,[0,l,0]]],oT=[0,a(d6),[0,c,[0,l,[0,a(d$),[0,a(b7),0]]]]],oU=[0,a(dS),[0,c,v]],oV=[0,a(ec),[0,c,i]],oW=[0,a(d5),[0,c,v]],oX=[0,a(cZ),[0,c,c]],oZ=[0,l,[0,a(b3),0]],o0=a(d0),o1=[0,a(jI),[0,c,c]],o3=[0,l,[0,a(dT),0]],o4=a(ju),o5=[0,a(ap),[0,c,ca]],o7=[0,l,[0,a(b3),0]],o8=a(bs),o9=[0,a(aU),[0,c,v]],o_=[0,a(bB),[0,c,[0,R,[0,[0,a(bB),0],c]]]],o$=[0,a(jf),[0,c,v]],pa=[0,a(iq),[0,c,c]],pb=[0,a(bz),[0,c,c]],pc=[0,a(cT),[0,c,c]],pd=[0,a(cL),[0,c,c]],pe=[0,a(fJ),[0,c,c]],pf=[0,a(iY),[0,c,c]],pg=[0,a(fs),[0,c,c]],ph=[0,a(f5),[0,c,c]],pi=[0,a(fr),[0,c,c]],pj=[0,a(f7),[0,c,c]],pk=[0,a(fv),[0,c,c]],pl=[0,a(fu),[0,c,c]],pm=[0,a(fE),[0,c,c]],pn=[0,a(f3),[0,c,c]],po=[0,a(iZ),[0,c,c]],pp=[0,a(dZ),[0,c,c]],pq=[0,a(dV),[0,c,c]],pr=[0,a(d3),[0,c,c]],ps=[0,a(eg),[0,c,c]],pt=[0,a(fZ),[0,c,c]],pu=[0,a(f8),[0,c,c]],pv=[0,a(fX),[0,c,c]],py=[0,a(a9),[0,a(a$),[0,a(W),[0,a($),[0,a(b1),0]]]]],qz=a(e),qA=a(e),qD=a("too many r* or r+ where r is nullable"),qE=a(e),qB=[0,a("str.ml"),214,11],qI=[0,0,0],qM=a(e),qL=a(e),qK=a(e),qJ=a(e),qS=a(ae),qQ=a("' is invalid"),qR=a("document list level '"),qN=a("\xef\x82\xb7"),qO=a("\xef\x82\xa7"),qP=a("o"),q0=a("excel"),qZ=a(cS),q3=[0,a("src/main/re/html/htmlStd.re"),144,9],qT=a(X),qU=a(fH),qV=bO([a(cC),a("article"),a("aside"),a("audio"),a(cR),a("canvas"),a(cM),a(b2),a(b1),a(bG),a("figcaption"),a("figure"),a("footer"),a(ba),a(cU),a(cE),a(cP),a(cA),a(cQ),a(cF),a("header"),a("hgroup"),a(b6),a(aa),a(cV),a(W),a("output"),a(aT),a(cO),a("section"),a(a$),a(cH),a(bI),a(cN),a(c0),a(cD),a(at),a($),a("video")]),qW=[0,a(a9),[0,a(a_),[0,a(cS),[0,a(bC),[0,a(bp),[0,a(aE),[0,a(fo),0]]]]]]],q1=[0,a(e)],re=a(aT),rc=a(jo),rb=a(i0),rd=[0,a(jo)],q$=a(i0),q9=a(aU),q5=a(iN),q6=a(iN),q7=a("file:"),q8=a("data-image-src"),rQ=a(ae),rP=a(dU),rM=a(":level"),rK=a(ae),rD=a(fx),rE=a(f4),rF=a(fx),rG=a(f4),rH=a(ee),rI=a(fx),rJ=a(f4),rL=a(e),rN=a(e),rO=a("@list "),rB=a(cB),rz=[0,a("\n  ")],rA=a("  "),ry=a(e),rw=a("color"),rx=a(ae),rt=a('""'),ru=a(";"),rv=a(bD),rj=a(ea),rk=a(e),rl=a(bb),rm=a(ae),rn=a(e),ri=[0,a(e),0],ro=a(e),rp=a(cB),rg=a(bF),rh=a(bF),rq=[0,a("font-family"),0],r4=a("Internal failure -- please contact the parser generator's developers.\n%!"),r5=[0,a("src/main/re/css_parser.ml"),494,4],r3=a(ea),r2=a(bD),r1=a(ee),r0=a(ea),rZ=a(bD),rY=a(ee),rV=a("list"),rX=a(bD),rW=a(e),rT=a(ea),rU=a(e),rS=[0,a(e),0],rR=a("Css_parser.MenhirBasics.Error"),sa=a(fD),sb=a("' ("),sc=a("Unexpected char: '"),r9=a("Css_lexer.SyntaxError"),r_=[0,a('\0\0\xeb\xff\x14\0\x03\0\x01\0\xd7\0\x0e\0H\x01\x9d\0\xf1\xff\x03\0\x06\0\x11\0u\x01\xe8\x01B\x02\x9c\x02\xf6\x02\x02\0\x1e\0\xf7\xff\xf8\xff\xf9\xff\xfa\xff\xfb\xff\xfc\xff\b\0\x01\0\xfe\xff\xff\xff\xf6\xff\xf5\xff\x12\0P\x03\xaa\x03|\0\xf4\xff\x04\x04^\x04\xc8\x01\x90\x04\x13\0\xbd\x04\xed\xff0\x05\x15\0\x16\0\x17\0\xf0\xff2\x052\x001\x054\0\x0b\0\x12\0\x0e\0"\0\xa1\x05\xa2\x055\0\xa3\x05\xa7\x05\xa8\x05\xa9\x05\xad\x051\x003\0\xee\xffY\0Z\0\xec\xff\\\0]\0'),a("\xff\xff\xff\xff\x14\0\x14\0\x14\0\x10\0\x14\0\x14\0\x14\0\xff\xff\x14\0\x14\0\x14\0\x10\0\r\0\x10\0\x10\0\x10\0\x14\0\x14\0\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\x02\0\x01\0\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\x10\0\x10\0\xff\xff\xff\xff\x10\0\x10\0\xff\xff\f\0\xff\xff\x10\0\xff\xff\r\0\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\x0f\0\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff"),a("\x01\0\0\0\xff\xff\xff\xff\x05\0\xff\xff\xff\xff3\x001\0\0\0.\0-\0\t\0\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\0\0\0\0\0\0\0\0\0\0\0\0\xff\xff\xff\xff\0\0\0\0\0\0\0\0\x05\0\xff\xff\xff\xff\xff\xff\0\0\xff\xff\xff\xff'\0'\0'\0\xff\xff\0\0\xff\xff\t\0/\0\t\0\0\x001\x001\x003\x003\0\xff\xff\xff\xff\xff\xff\xff\xff9\x009\x009\x009\x009\x009\x009\x009\0\xff\xff\xff\xff\0\0D\0G\0\0\0G\0G\0"),a("\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\x1a\0\x1c\0\x1c\0\0\0\x1b\0\0\0\0\0\0\0\x1a\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\x1a\0\x05\0\b\0\x05\0A\0\x05\0\x06\0\x07\0\x1a\0\0\0\t\0\x05\0\x19\0\x0f\0\r\0\x02\0\x0e\0\x0e\0\x0e\0\x0e\0\x0e\0\x0e\0\x0e\0\x0e\0\x0e\0\x0e\0\x15\0\x16\0\x03\0\x05\0D\0\x1f\0\x14\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x1e\0\x04\x009\0B\0\x05\0C\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x11\0\x05\0\x05\0\x10\0\x05\0\x05\0\x05\0\x05\0\x05\0\x18\0\x12\0\x17\0\x13\x005\x006\x007\x008\0E\0E\0#\0H\0H\0\0\0F\0\0\0\0\0F\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0#\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0$\0\0\0\xff\xff#\0\0\0\xff\xff\0\0#\0#\0#\0#\0#\0#\0#\0#\0#\0#\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\x000\0\f\0\f\0\f\0\f\0\f\0\f\0\f\0\f\0\f\0\f\0\f\0\f\0\f\0\f\0\f\0\f\0\f\0\f\0\f\0\f\0\f\0\f\0\f\0\f\0\f\0\f\0\f\0\f\0\f\0\f\0\f\0\f\0\x0b\0\x0b\0\x0b\0\x0b\0\x0b\0\x0b\0\x0b\0\x0b\0\x0b\0\x0b\0\x0b\0\x0b\0\x0b\0\x0b\0\x0b\0\x0b\0\n\0\n\0\n\0\n\0\n\0\n\0\n\0\n\0\x05\x002\0\x05\0\0\0\x05\0\0\0\0\0\0\0\x1d\0\xff\xff\x05\0\xff\xff\x05\0\x05\0\xff\xff\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\xff\xff\xff\xff\xff\xff\x05\0\xff\xff\xff\xff\xff\xff\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\xff\xff \0\xff\xff\xff\xff\x05\0\0\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\xff\xff\0\0\0\0\xff\xff\0\0\0\0\0\0\xff\xff\xff\xff\0\0\xff\xff\xff\xff\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\x000\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\x05\0\0\0\x05\0\0\0\x05\0\0\0\0\0\xff\xff\0\0\0\0\x05\0\0\0\x05\0\x05\x004\0,\0,\0,\0,\0,\0,\0,\0,\0,\0,\0\0\0\0\0\0\0\x05\0\0\0\0\0\0\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\0\0 \0\xff\xff\0\0\x05\0\xff\xff\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\0\0(\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\x05\0\0\0\x05\0\0\0\x05\0\0\0\0\0\0\0\0\0\0\0\x05\0\0\0\x05\0\r\0\0\0\x0e\0\x0e\0\x0e\0\x0e\0\x0e\0\x0e\0\x0e\0\x0e\0\x0e\0\x0e\0\0\0\0\0)\0\x05\0\0\0\0\0\0\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\0\0 \0\0\0\0\0\x05\0\xff\xff\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\0\0\x05\0\0\0\x05\0\0\0\0\0\0\0\0\0\0\0\x05\0\0\0*\0\r\0\0\0\x0e\0\x0e\0\x0e\0\x0e\0\x0e\0\x0e\0\x0e\0\x0e\0\x0e\0\x0e\0\0\0\0\0\0\0\x05\0\0\0\0\0\0\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\0\0 \0\0\0\0\0\x05\0\0\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\0\0\x05\0\0\0\x05\0\0\0\0\0\0\0\0\0\0\0\x05\0\xff\xff\x05\0\x05\0\0\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\0\0\0\0\0\0\x05\0\0\0\0\0\0\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\0\0 \0\0\0\0\0\x05\0\0\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0%\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\0\0\x05\0\0\0\x05\0\0\0\0\0\0\0\0\0\0\0\x05\0\0\0\x05\0\x05\0\0\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\0\0\0\0\0\0\x05\0\0\0\0\0\0\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\0\0 \0\0\0\0\0\x05\0\0\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0!\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\0\0\x05\0\0\0\x05\0\0\0\0\0\0\0\0\0\0\0\x05\0\0\0\x05\0\x05\0\0\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\0\0\0\0\0\0\x05\0\0\0\0\0\0\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\0\0 \0\0\0\0\0\x05\0\0\0\x05\0\"\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\0\0\x05\0\0\0\x05\0\0\0\0\0#\0\0\0\0\0\x05\0\0\0\x05\0\x05\0\0\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\0\0\0\0\0\0\x05\0\0\0\0\0\0\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\0\0 \0\0\0\0\0\x05\0\0\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\0\0\x05\0\0\0\x05\0\0\0\0\0\0\0\0\0\0\0\x05\0\0\0\x05\0\x05\0\0\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\0\0\0\0\0\0\x05\0\0\0\0\0\0\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\0\0 \0\0\0\0\0\x05\0\0\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0&\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\0\0\x05\0\0\0\x05\0\0\0\0\0'\0\0\0\0\0\x05\0\0\0\x05\0\x05\0\0\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\0\0\0\0\xff\xff\x05\0\0\0\xff\xff\0\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0(\0 \0\0\0\0\0\x05\0\0\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\0\0\0\0\0\0\0\0\0\0\x05\0\0\0\x05\0\0\0\x05\0\0\0\0\0\0\0\0\0\0\0\x05\0\0\0\x05\0\x05\0)\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\0\0\0\0\0\0\x05\0+\0\0\0\0\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\0\0 \0\0\0\0\0\x05\0\0\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\0\0\0\0\0\0\xff\xff\xff\xff\0\0\xff\xff\xff\xff\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\x05\0\0\0\x05\x000\0\x05\0\0\0\0\x000\0\0\0\0\0\x05\0\0\0\x05\0\x05\0\0\0,\0,\0,\0,\0,\0,\0,\0,\0,\0,\0\0\0\0\0\0\0\x05\0\0\0\0\0\0\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\0\0 \x004\x002\0\x05\0\xff\xff\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\0\0\0\0\xff\xff\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0:\0:\0:\0\0\0\0\0\0\0:\0:\0:\0\0\0\0\0\0\0:\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0@\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0;\0;\0;\0\0\0\0\0\0\0;\0;\0;\0\0\0\0\0\0\0;\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0<\0\0\0\0\0>\0\0\0=\0\0\0\0\0\0\0?\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\xff\xff\xff\xff\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\xff\xff\xff\xff\xff\xff\0\0\0\0\0\0\xff\xff\xff\xff\xff\xff\0\0\0\0\0\0\xff\xff"),a('\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\0\0\0\0\x1b\0\xff\xff\0\0\xff\xff\xff\xff\xff\xff\x1a\0\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\0\0\0\0\0\0\0\0\x03\0\0\0\0\0\0\0\x1a\0\xff\xff\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\x02\0\x12\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\x13\0\0\x008\0A\0\0\0B\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\x06\x005\x006\x007\0D\0E\0#\0G\0H\0\xff\xffE\0\xff\xff\xff\xffH\0\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff#\0\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff#\0\xff\xff\b\0#\0\xff\xff\b\0\xff\xff#\0#\0#\0#\0#\0#\0#\0#\0#\0#\0\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\b\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\x05\0\b\0\x05\0\xff\xff\x05\0\xff\xff\xff\xff\xff\xff\0\0\x04\0\x05\0\n\0\x05\0\x05\0\x0b\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\f\0 \0)\0\x05\0-\0.\0/\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\x002\0\x05\x004\0;\0\x05\0\xff\xff\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x05\0\x07\0\xff\xff\xff\xff\x07\0\xff\xff\xff\xff\xff\xffD\0E\0\xff\xffG\0H\0\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\x07\0\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\r\0\xff\xff\r\0\xff\xff\r\0\xff\xff\xff\xff\b\0\xff\xff\xff\xff\r\0\xff\xff\r\0\r\0\x07\0\r\0\r\0\r\0\r\0\r\0\r\0\r\0\r\0\r\0\r\0\xff\xff\xff\xff\xff\xff\r\0\xff\xff\xff\xff\xff\xff\r\0\r\0\r\0\r\0\r\0\r\0\r\0\r\0\r\0\r\0\r\0\r\0\r\0\r\0\r\0\r\0\r\0\r\0\r\0\r\0\r\0\r\0\r\0\r\0\r\0\r\0\xff\xff\r\0\'\0\xff\xff\r\0\'\0\r\0\r\0\r\0\r\0\r\0\r\0\r\0\r\0\r\0\r\0\r\0\r\0\r\0\r\0\r\0\r\0\r\0\r\0\r\0\r\0\r\0\r\0\r\0\r\0\r\0\r\0\xff\xff\'\0\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\x0e\0\xff\xff\x0e\0\xff\xff\x0e\0\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\x0e\0\xff\xff\x0e\0\x0e\0\xff\xff\x0e\0\x0e\0\x0e\0\x0e\0\x0e\0\x0e\0\x0e\0\x0e\0\x0e\0\x0e\0\xff\xff\xff\xff\'\0\x0e\0\xff\xff\xff\xff\xff\xff\x0e\0\x0e\0\x0e\0\x0e\0\x0e\0\x0e\0\x0e\0\x0e\0\x0e\0\x0e\0\x0e\0\x0e\0\x0e\0\x0e\0\x0e\0\x0e\0\x0e\0\x0e\0\x0e\0\x0e\0\x0e\0\x0e\0\x0e\0\x0e\0\x0e\0\x0e\0\xff\xff\x0e\0\xff\xff\xff\xff\x0e\0\x07\0\x0e\0\x0e\0\x0e\0\x0e\0\x0e\0\x0e\0\x0e\0\x0e\0\x0e\0\x0e\0\x0e\0\x0e\0\x0e\0\x0e\0\x0e\0\x0e\0\x0e\0\x0e\0\x0e\0\x0e\0\x0e\0\x0e\0\x0e\0\x0e\0\x0e\0\x0e\0\x0f\0\xff\xff\x0f\0\xff\xff\x0f\0\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\x0f\0\xff\xff\x0f\0\x0f\0\xff\xff\x0f\0\x0f\0\x0f\0\x0f\0\x0f\0\x0f\0\x0f\0\x0f\0\x0f\0\x0f\0\xff\xff\xff\xff\xff\xff\x0f\0\xff\xff\xff\xff\xff\xff\x0f\0\x0f\0\x0f\0\x0f\0\x0f\0\x0f\0\x0f\0\x0f\0\x0f\0\x0f\0\x0f\0\x0f\0\x0f\0\x0f\0\x0f\0\x0f\0\x0f\0\x0f\0\x0f\0\x0f\0\x0f\0\x0f\0\x0f\0\x0f\0\x0f\0\x0f\0\xff\xff\x0f\0\xff\xff\xff\xff\x0f\0\xff\xff\x0f\0\x0f\0\x0f\0\x0f\0\x0f\0\x0f\0\x0f\0\x0f\0\x0f\0\x0f\0\x0f\0\x0f\0\x0f\0\x0f\0\x0f\0\x0f\0\x0f\0\x0f\0\x0f\0\x0f\0\x0f\0\x0f\0\x0f\0\x0f\0\x0f\0\x0f\0\x10\0\xff\xff\x10\0\xff\xff\x10\0\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\x10\0\'\0\x10\0\x10\0\xff\xff\x10\0\x10\0\x10\0\x10\0\x10\0\x10\0\x10\0\x10\0\x10\0\x10\0\xff\xff\xff\xff\xff\xff\x10\0\xff\xff\xff\xff\xff\xff\x10\0\x10\0\x10\0\x10\0\x10\0\x10\0\x10\0\x10\0\x10\0\x10\0\x10\0\x10\0\x10\0\x10\0\x10\0\x10\0\x10\0\x10\0\x10\0\x10\0\x10\0\x10\0\x10\0\x10\0\x10\0\x10\0\xff\xff\x10\0\xff\xff\xff\xff\x10\0\xff\xff\x10\0\x10\0\x10\0\x10\0\x10\0\x10\0\x10\0\x10\0\x10\0\x10\0\x10\0\x10\0\x10\0\x10\0\x10\0\x10\0\x10\0\x10\0\x10\0\x10\0\x10\0\x10\0\x10\0\x10\0\x10\0\x10\0\x11\0\xff\xff\x11\0\xff\xff\x11\0\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\x11\0\xff\xff\x11\0\x11\0\xff\xff\x11\0\x11\0\x11\0\x11\0\x11\0\x11\0\x11\0\x11\0\x11\0\x11\0\xff\xff\xff\xff\xff\xff\x11\0\xff\xff\xff\xff\xff\xff\x11\0\x11\0\x11\0\x11\0\x11\0\x11\0\x11\0\x11\0\x11\0\x11\0\x11\0\x11\0\x11\0\x11\0\x11\0\x11\0\x11\0\x11\0\x11\0\x11\0\x11\0\x11\0\x11\0\x11\0\x11\0\x11\0\xff\xff\x11\0\xff\xff\xff\xff\x11\0\xff\xff\x11\0\x11\0\x11\0\x11\0\x11\0\x11\0\x11\0\x11\0\x11\0\x11\0\x11\0\x11\0\x11\0\x11\0\x11\0\x11\0\x11\0\x11\0\x11\0\x11\0\x11\0\x11\0\x11\0\x11\0\x11\0\x11\0!\0\xff\xff!\0\xff\xff!\0\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff!\0\xff\xff!\0!\0\xff\xff!\0!\0!\0!\0!\0!\0!\0!\0!\0!\0\xff\xff\xff\xff\xff\xff!\0\xff\xff\xff\xff\xff\xff!\0!\0!\0!\0!\0!\0!\0!\0!\0!\0!\0!\0!\0!\0!\0!\0!\0!\0!\0!\0!\0!\0!\0!\0!\0!\0\xff\xff!\0\xff\xff\xff\xff!\0\xff\xff!\0!\0!\0!\0!\0!\0!\0!\0!\0!\0!\0!\0!\0!\0!\0!\0!\0!\0!\0!\0!\0!\0!\0!\0!\0!\0"\0\xff\xff"\0\xff\xff"\0\xff\xff\xff\xff"\0\xff\xff\xff\xff"\0\xff\xff"\0"\0\xff\xff"\0"\0"\0"\0"\0"\0"\0"\0"\0"\0\xff\xff\xff\xff\xff\xff"\0\xff\xff\xff\xff\xff\xff"\0"\0"\0"\0"\0"\0"\0"\0"\0"\0"\0"\0"\0"\0"\0"\0"\0"\0"\0"\0"\0"\0"\0"\0"\0"\0\xff\xff"\0\xff\xff\xff\xff"\0\xff\xff"\0"\0"\0"\0"\0"\0"\0"\0"\0"\0"\0"\0"\0"\0"\0"\0"\0"\0"\0"\0"\0"\0"\0"\0"\0"\0%\0\xff\xff%\0\xff\xff%\0\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff%\0\xff\xff%\0%\0\xff\xff%\0%\0%\0%\0%\0%\0%\0%\0%\0%\0\xff\xff\xff\xff\xff\xff%\0\xff\xff\xff\xff\xff\xff%\0%\0%\0%\0%\0%\0%\0%\0%\0%\0%\0%\0%\0%\0%\0%\0%\0%\0%\0%\0%\0%\0%\0%\0%\0%\0\xff\xff%\0\xff\xff\xff\xff%\0\xff\xff%\0%\0%\0%\0%\0%\0%\0%\0%\0%\0%\0%\0%\0%\0%\0%\0%\0%\0%\0%\0%\0%\0%\0%\0%\0%\0&\0\xff\xff&\0\xff\xff&\0\xff\xff\xff\xff&\0\xff\xff\xff\xff&\0\xff\xff&\0&\0\xff\xff&\0&\0&\0&\0&\0&\0&\0&\0&\0&\0\xff\xff\xff\xff(\0&\0\xff\xff(\0\xff\xff&\0&\0&\0&\0&\0&\0&\0&\0&\0&\0&\0&\0&\0&\0&\0&\0&\0&\0&\0&\0&\0&\0&\0&\0&\0&\0(\0&\0\xff\xff\xff\xff&\0\xff\xff&\0&\0&\0&\0&\0&\0&\0&\0&\0&\0&\0&\0&\0&\0&\0&\0&\0&\0&\0&\0&\0&\0&\0&\0&\0&\0\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff*\0\xff\xff*\0\xff\xff*\0\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff*\0\xff\xff*\0*\0(\0*\0*\0*\0*\0*\0*\0*\0*\0*\0*\0\xff\xff\xff\xff\xff\xff*\0*\0\xff\xff\xff\xff*\0*\0*\0*\0*\0*\0*\0*\0*\0*\0*\0*\0*\0*\0*\0*\0*\0*\0*\0*\0*\0*\0*\0*\0*\0*\0\xff\xff*\0\xff\xff\xff\xff*\0\xff\xff*\0*\0*\0*\0*\0*\0*\0*\0*\0*\0*\0*\0*\0*\0*\0*\0*\0*\0*\0*\0*\0*\0*\0*\0*\0*\0\xff\xff\xff\xff\xff\xff3\x001\0\xff\xff3\x001\0\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff,\0\xff\xff,\x001\0,\0\xff\xff\xff\xff3\0\xff\xff\xff\xff,\0\xff\xff,\0,\0\xff\xff,\0,\0,\0,\0,\0,\0,\0,\0,\0,\0\xff\xff\xff\xff\xff\xff,\0\xff\xff\xff\xff\xff\xff,\0,\0,\0,\0,\0,\0,\0,\0,\0,\0,\0,\0,\0,\0,\0,\0,\0,\0,\0,\0,\0,\0,\0,\0,\0,\0\xff\xff,\x003\x001\0,\0(\0,\0,\0,\0,\0,\0,\0,\0,\0,\0,\0,\0,\0,\0,\0,\0,\0,\0,\0,\0,\0,\0,\0,\0,\0,\0,\x009\0:\0<\x009\0:\0<\0=\0>\0?\0=\0>\0?\0@\0\xff\xff\xff\xff@\0\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff9\0:\0<\0\xff\xff\xff\xff\xff\xff=\0>\0?\0\xff\xff\xff\xff\xff\xff@\0\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff?\0\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff9\0:\0<\0\xff\xff\xff\xff\xff\xff=\0>\0?\0\xff\xff\xff\xff\xff\xff@\0\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff:\0\xff\xff\xff\xff=\0\xff\xff<\0\xff\xff\xff\xff\xff\xff>\0\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff3\x001\0\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff9\0:\0<\0\xff\xff\xff\xff\xff\xff=\0>\0?\0\xff\xff\xff\xff\xff\xff@\0'),a("\0\0\0\0\0\0\0\0\0\0\0\0\0\0\x01\0\x02\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\t\0\x03\0\0\0\x07\0\x04\0\0\0\0\0\0\0\0\0\t\0\x0b\0\x05\0\r\0\x0f\0\x11\0\x13\0\x19\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0"),a("\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\t\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0"),a("\0\0\0\0\0\0\0\0\0\0\0\0\0\0\x06\0\x06\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\x06\0\x06\0\x06\0\x06\0\0\0\0\0\0\0\0\0\x06\0\x06\0\x06\0\x06\0\x06\0\x06\0\x06\0\x06\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0"),a("\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\x01\0\0\0\0\0\0\0\0\0\x01\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\x0e\0\0\0\x11\0\0\0\x11\0\0\0\x11\0\0\0\x11\0\0\0\x11\0\0\0\x01\0\0\0\0\0\0\0\x11\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\x0e\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\x0e\0\0\0\x0e\0\0\0\0\0\0\0\x0e\0\0\0\0\0\x0e\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0\0"),a("\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\x07\0\b\x001\0\x07\0\b\x001\x003\0\xff\xff9\x003\0:\x009\0<\0:\0=\0<\0>\0=\0?\0>\0\xff\xff?\0\xff\xff\0\0@\0\b\x001\0@\0\0\0\x07\0\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff3\x009\0\xff\xff:\0\xff\xff<\0\xff\xff=\0\xff\xff>\0\xff\xff?\0\xff\xff8\0\xff\xff\xff\xff\xff\xff@\0\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff?\0\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\x07\0\b\x001\0\xff\xff\xff\xff\xff\xff3\0\xff\xff9\0\xff\xff:\0\xff\xff<\0\xff\xff=\0\xff\xff>\0\xff\xff?\0\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff@\0\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff:\0\xff\xff=\0\xff\xff\xff\xff\xff\xff<\0\xff\xff\xff\xff>\0\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff2\0\x07\0\b\x001\x004\0;\0\xff\xff3\0\xff\xff9\0\xff\xff:\0\xff\xff<\0\xff\xff=\0\xff\xff>\0\xff\xff?\0\xff\xff\xff\xff\xff\xff\xff\xff\xff\xff@\0"),a("\xff\x03\xff\x02\xff\xff\x03\xff\xff\x01\x02\0\x03\xff\x04\xff\xff\x03\x04\x04\xff\xff")],sh=a(aE),sf=a(ji),sg=a(": syntax error parsing:"),sd=a(br),se=a(bD),si=a(a9),sj=a(aE),sx=a(e),sr=a(jg),ss=a("='"),sk=a(jg),sl=a("Data '"),sm=a(dU),sn=a(fD),so=a(dU),sp=a(e),sq=a("]"),st=a(cB),su=a(", ["),sv=a("Element ("),sw=a(dU),sy=[0,[0,a(b$),[0,j,[0,l,[0,a(b$),[0,a(ed),[0,a(b_),0]]]]]],[0,[0,a(ed),[0,j,v]],0]],sz=bO([l,a(a_),a(cc),a(b8),a(ap),a(aE),a(bp),a(bC),a(bs),a(a9),a(b$)]),sA=a(cS),sB=[0,a(aE),[0,j,ca]],sC=[0,a(bp),[0,j,v]],sD=[0,a(b8),[0,j,v]],sE=[0,a(cc),[0,j,[0,l,0]]],sF=[0,a(a_),[0,j,[0,l,[0,a(cc),[0,a(b8),[0,a(ap),[0,a(aE),[0,a(bp),[0,a(bC),[0,a(bs),0]]]]]]]]]],sG=[0,a(bI),[0,j,i]],sH=[0,a(c0),[0,j,i]],sI=[0,a(at),[0,j,[0,l,[0,a(c0),[0,a(bI),0]]]]],sJ=[0,a(bt),[0,j,v]],sK=[0,a(d_),[0,j,[0,l,[0,a(bt),0]]]],sL=[0,a(cN),[0,j,[0,l,[0,a(at),0]]]],sM=[0,a(cH),[0,j,[0,l,[0,a(at),0]]]],sN=[0,a(cD),[0,j,[0,l,[0,a(at),0]]]],sO=[0,a(ei),[0,j,c]],sP=[0,a(d1),[0,j,c]],sQ=[0,a(b7),[0,j,[0,l,0]]],sR=[0,a(d$),[0,j,[0,l,[0,a(b7),0]]]],sS=[0,a(aa),[0,j,i]],sT=[0,a(cM),[0,j,i]],sU=[0,a(dW),[0,j,c]],sV=[0,a(fI),[0,bA,i]],sW=[0,a(fn),[0,bA,i]],sX=[0,a(b3),[0,j,v]],sY=[0,a(bC),[0,j,v]],sZ=[0,a(dT),[0,j,v]],s1=[0,l,[0,a(ap),0]],s2=a(a9),s3=[0,a(cb),[0,o,v]],s4=[0,a(b_),[0,o,i]],s5=[0,a(dY),[0,o,i]],s6=[0,a(cC),[0,o,c]],s8=[0,l,[0,a(d1),0]],s9=a(bG),s_=[0,a(a$),[0,a8,[0,l,[0,a(ei),[0,a(bt),[0,a(d_),[0,a(cD),[0,a(cN),[0,a(cH),[0,a(at),0]]]]]]]]]],s$=[0,a(b6),[0,o,v]],tb=[0,l,[0,a(ap),0]],tc=[0,a(ba),0],td=a(ba),tf=[0,l,[0,a(ap),0]],tg=a(cR),th=[0,a(cV),[0,o,i]],ti=[0,a(b2),[0,o,i]],tj=[0,a(b1),[0,a8,[0,l,[0,a(dW),[0,a(cM),0]]]]],tk=[0,a(cO),[0,o,[0,R,[0,[0,a(bs),[0,a(d0),[0,a(eg),[0,a(d3),[0,a(cT),[0,a(cL),[0,a(cZ),zu]]]]]]],c]]]],tl=[0,l,[0,a(aa),0]],tm=a(d2),tn=[0,l,[0,a(aa),0]],to=a(bE),tp=[0,a(W),[0,a8,[0,l,[0,a(aa),0]]]],tq=[0,a($),[0,a8,[0,l,[0,a(aa),0]]]],tr=[0,a(cF),[0,o,c]],ts=[0,a(cQ),[0,o,c]],tt=[0,a(cA),[0,o,c]],tu=[0,a(cP),[0,o,c]],tv=[0,a(cE),[0,o,c]],tw=[0,a(cU),[0,o,c]],tx=[0,a(aT),[0,o,c]],ty=[0,a(eh),[0,c,[0,R,[0,[0,a(bB),[0,a(dS),[0,a(d6),[0,a(dO),[0,a(b5),[0,a(eh),[0,a(ba),zv]]]]]]],i]]]],tz=[0,a(b5),[0,c,[0,R,[0,[0,a(b5),0],c]]]],tA=[0,a(dO),[0,c,[0,l,0]]],tB=[0,a(d6),[0,c,[0,l,[0,a(d$),[0,a(b7),0]]]]],tC=[0,a(dS),[0,c,v]],tD=[0,a(ec),[0,c,i]],tE=[0,a(d5),[0,c,v]],tF=[0,a(cZ),[0,c,i]],tH=[0,l,[0,a(b3),0]],tI=a(d0),tJ=[0,a(jI),[0,c,i]],tL=[0,l,[0,a(dT),0]],tM=a(ju),tN=[0,a(ap),[0,c,ca]],tP=[0,l,[0,a(b3),0]],tQ=a(bs),tR=[0,a(aU),[0,c,v]],tS=[0,a(bB),[0,c,[0,R,[0,[0,a(bB),0],c]]]],tT=[0,a(jf),[0,c,v]],tU=[0,a(iq),[0,c,i]],tV=[0,a(bz),[0,c,i]],tW=[0,a(cT),[0,c,i]],tX=[0,a(cL),[0,c,i]],tY=[0,a(fJ),[0,c,i]],tZ=[0,a(iY),[0,c,i]],t0=[0,a(fs),[0,c,i]],t1=[0,a(f5),[0,c,i]],t2=[0,a(fr),[0,c,i]],t3=[0,a(f7),[0,c,i]],t4=[0,a(fv),[0,c,i]],t5=[0,a(fu),[0,c,i]],t6=[0,a(fE),[0,c,i]],t7=[0,a(f3),[0,c,i]],t8=[0,a(iZ),[0,c,i]],t9=[0,a(dZ),[0,c,i]],t_=[0,a(dV),[0,c,i]],t$=[0,a(d3),[0,c,i]],ua=[0,a(eg),[0,c,i]],ub=[0,a(fZ),[0,c,i]],uc=[0,a(f8),[0,c,i]],ud=[0,a(fX),[0,c,i]],uu=a(aE),ut=a(a_),ur=a(a9),us=a(cS),uo=a(a_),up=a(aE),un=a(aE),uk=a(X),ul=a(by),um=a(iT),uj=a(a_),ui=a(a9),uf=[0,1],ug=[0,0],uh=[0,1],uq=[1,a(bp),[0,[0,a("http-equiv"),a("Content-Type")],[0,[0,a("content"),a("text/html; charset=utf-8")],0]],0,0],u8=a(X),u9=a(bb),u_=a(X),u$=a("[if !msEquation]"),va=a(bb),vb=a(X),vc=a(fH),u6=[0,a("data-ms-equation"),a(d8)],uV=a(X),uW=a(bb),uX=a(X),uY=a("[if !vml]"),uZ=a(bb),u0=a(X),u1=a(fH),uT=a(fo),uO=a(aU),uP=a(aU),uN=a(aU),uQ=a(aU),uJ=a(fo),uH=a("v:imagedata"),uI=a(aU),uF=a("vshapedata"),uC=[0,[0,a("rtf-data-image"),a(d8)],0],uD=a("data-image-id"),uz=a("rotation"),uv=a("-90"),uw=a("90"),ux=a(cK),uy=a(bH),uA=a(cK),uB=a(bH),uE=a("unsupported"),uM=a(aU),uR=a("[if gte vml 1]>"),uS=a(jv),u2=a(by),u4=a("[if gte msEquation"),u5=a(jv),vd=a(by),vf=[0,0],vi=a(X),vj=a("[if !supportLists]"),vk=a(bb),vl=a(by),vY=a(iA),vZ=a(aa),v0=a(bn),v2=[0,a(iE),a(d8)],v1=a(aa),vV=a(b9),vW=a(bD),vX=a(fm),v3=a(aT),vT=a("mso-text-indent-alt"),vU=a(jH),vQ=a("mso-level-legacy"),vR=a("yes"),vS=[0,a($),0,0],vN=a(iw),vO=a(d4),vP=[0,a(dQ)],vK=a("margin-top"),vL=a("margin-bottom"),vC=[0,[0,a($),0]],vw=a("alpha-lower"),vx=a("alpha-upper"),vy=a("bullet"),vz=a("image"),vA=a("roman-lower"),vB=a("roman-upper"),vD=[0,[0,a(W),[0,a("upper-roman")]]],vE=[0,[0,a(W),[0,a("lower-roman")]]],vF=[0,[0,a(W),[0,a("upper-alpha")]]],vG=[0,[0,a(W),[0,a("lower-alpha")]]],vH=a("mso-level-number-format"),vI=[0,a(W),0],vt=[0,[0,a($),[0,a("square")]]],vu=[0,[0,a($),[0,a("circle")]]],vv=a("mso-level-text"),vs=a(af),vr=a(cX),vo=[0,a(jt)],vp=a($),vq=a("mso-level-start-at"),vm=[0,[0,a(dQ)],[0,[0,a(d4),0]]],vn=a(aa),vh=a(jC),v7=a(dV),v_=a(dZ),v$=a(bz),v8=a(jd),v9=a(i5),wa=a(aT),v5=[0,a(i5)],v6=[0,a(jd)],wK=[0,1],wL=[0,1],wM=[0,1],wI=a(b9),wE=a(W),wF=a($),wA=a($),wB=a(W),wC=a(W),wz=a(b9),wD=a(b9),ww=a(cX),wx=a(f1),wt=a(W),wu=a($),ws=a(aa),wo=a(dQ),wp=[0,a(d4),0],wq=a(b9),wr=a(b9),wl=a(W),wm=a($),wh=a(aa),wi=a(W),we=a($),wf=a(f1),wg=a(cX),wj=a(fm),wk=a(cX),wc=bO([a(cZ),a(bz),a(fZ),a(f8),a(dV),a(cT),a(cL),a(f3),a(fE),a(f7),a(fJ),a(fs),a(fv),a(fu),a(fr),a(fX),a(dZ),a(fn),a(fI),a(f5),a(a_)]),xB=a(bz),xy=a(bn),xz=a(f6),xx=a(f6),xv=a(bn),xw=a(f6),xt=a(bn),xu=a("Apple-converted-space"),xm=a(X),xh=a(bn),xi=a("msocomtxt"),xf=a(b6),xc=a(bn),xd=a("msocomoff"),w$=a("mso-comment-reference"),w8=a("comment-list"),w7=a(jy),w3=a(bn),w4=a("MsoCommentReference"),w0=a(by),w1=a("---"),wZ=a(X),wU=a(iT),wV=a("endfragment"),wW=a("[if "),wX=a("[endif"),wS=a(bB),wQ=a("name"),wR=a("OLE_LINK"),xn=a(bb),xp=[0,a("lang"),[0,a("onmouseover"),[0,a("onmouseout"),[0,a(fm),0]]]],xq=[0,a(bn),[0,a(iE),[0,a(iA),[0,a(jH),0]]]],xP=a(b2),xN=a(jy),xO=[0,a("para-border-div"),0],xL=a(ja),xJ=a("margin-left"),xK=a("data-border-margin"),xH=a(aT),xD=a(bz),xE=a(bE),xF=a(bz),xG=a(bE),x8=a("mso-"),x9=a(jC),xV=a("font-stretch"),xW=a("font-variant-caps"),xX=a("text-decoration"),xY=a("text-indent"),xZ=a("text-transform"),x0=a("vertical-align"),x1=a("white-space"),x2=a("word-spacing"),x4=a(iW),x5=a("baseline"),x7=a(iW),x6=a(d4),x3=a("0px"),xR=[0,a(jt),[0,a(dQ),[0,a(iw),0]]],xT=bO([a("layout-grid-mode"),a("tab-stops"),a(i9),a("text-underline"),a("text-effect"),a("text-line-through"),a("page"),a("font-color"),a("horiz-align"),a("language"),a("separator-image"),a("table-border-color-dark"),a("table-border-color-light"),a("vert-align"),a("widows"),a("letter-spacing"),a("caret-color"),a("orphans")]),yh=a("a:link"),ye=a(iD),yf=a("1px"),yd=a(iD),yc=a(ja),x$=[0,a(a$),[0,a(at),[0,a(bI),[0,a(bt),0]]]],yu=a(cK),yv=a(bH),yt=[0,a(bI),[0,a(bt),0]],ys=a(at),yq=a(bH),yr=a(cK),yp=a(a$),yn=a(a$),ym=a(bH),yk=a(bH),yj=a("px"),yi=[0,a(bH),[0,a(cK),0]],yC=a("before:"),yD=a("after:"),yz=a(i9),yA=a("data-tab-interval"),yy=[0,1],yx=[0,0],yE=a("Jsoo_runtime.Error.Exn"),yF=a(fy),y3=a("edge/"),y4=a(i8),y5=a(jw),y6=a(jG),yY=a(i8),yZ=a("chromeframe"),yU=a("msie"),yV=a("trident"),yJ=a(jw),yL=a("mobile/"),yK=a(jG),yM=a(je),yO=a("firefox"),yP=a("Firefox"),yR=a("opera"),yS=a("Opera"),yW=a("IE"),y0=[0,a(i6)],y1=a(i6),y7=a("Edge"),y$=a(je),zg=a(e),zh=a(ii),zb=a(iG),zl=a("ephox.wimp");function
  E(a){if(typeof
  a==="number")return 0;else
  switch(a[0]){case
  0:return[0,E(a[1])];case
  1:return[1,E(a[1])];case
  2:return[2,E(a[1])];case
  3:return[3,E(a[1])];case
  4:return[4,E(a[1])];case
  5:return[5,E(a[1])];case
  6:return[6,E(a[1])];case
  7:return[7,E(a[1])];case
  8:var
  c=a[1];return[8,c,E(a[2])];case
  9:var
  b=a[1];return[9,b,b,E(a[3])];case
  10:return[10,E(a[1])];case
  11:return[11,E(a[1])];case
  12:return[12,E(a[1])];case
  13:return[13,E(a[1])];default:return[14,E(a[1])]}}function
  Z(a,b){if(typeof
  a==="number")return b;else
  switch(a[0]){case
  0:return[0,Z(a[1],b)];case
  1:return[1,Z(a[1],b)];case
  2:return[2,Z(a[1],b)];case
  3:return[3,Z(a[1],b)];case
  4:return[4,Z(a[1],b)];case
  5:return[5,Z(a[1],b)];case
  6:return[6,Z(a[1],b)];case
  7:return[7,Z(a[1],b)];case
  8:var
  c=a[1];return[8,c,Z(a[2],b)];case
  9:var
  d=a[2],e=a[1];return[9,e,d,Z(a[3],b)];case
  10:return[10,Z(a[1],b)];case
  11:return[11,Z(a[1],b)];case
  12:return[12,Z(a[1],b)];case
  13:return[13,Z(a[1],b)];default:return[14,Z(a[1],b)]}}function
  y(a,b){if(typeof
  a==="number")return b;else
  switch(a[0]){case
  0:return[0,y(a[1],b)];case
  1:return[1,y(a[1],b)];case
  2:var
  c=a[1];return[2,c,y(a[2],b)];case
  3:var
  d=a[1];return[3,d,y(a[2],b)];case
  4:var
  e=a[3],f=a[2],g=a[1];return[4,g,f,e,y(a[4],b)];case
  5:var
  h=a[3],i=a[2],j=a[1];return[5,j,i,h,y(a[4],b)];case
  6:var
  k=a[3],l=a[2],m=a[1];return[6,m,l,k,y(a[4],b)];case
  7:var
  n=a[3],o=a[2],p=a[1];return[7,p,o,n,y(a[4],b)];case
  8:var
  q=a[3],r=a[2],s=a[1];return[8,s,r,q,y(a[4],b)];case
  9:var
  t=a[1];return[9,t,y(a[2],b)];case
  10:return[10,y(a[1],b)];case
  11:var
  u=a[1];return[11,u,y(a[2],b)];case
  12:var
  v=a[1];return[12,v,y(a[2],b)];case
  13:var
  w=a[2],x=a[1];return[13,x,w,y(a[3],b)];case
  14:var
  z=a[2],A=a[1];return[14,A,z,y(a[3],b)];case
  15:return[15,y(a[1],b)];case
  16:return[16,y(a[1],b)];case
  17:var
  B=a[1];return[17,B,y(a[2],b)];case
  18:var
  C=a[1];return[18,C,y(a[2],b)];case
  19:return[19,y(a[1],b)];case
  20:var
  D=a[2],E=a[1];return[20,E,D,y(a[3],b)];case
  21:var
  F=a[1];return[21,F,y(a[2],b)];case
  22:return[22,y(a[1],b)];case
  23:var
  G=a[1];return[23,G,y(a[2],b)];default:var
  H=a[2],I=a[1];return[24,I,H,y(a[3],b)]}}function
  ch(a){throw[0,db,a]}function
  I(a){throw[0,et,a]}var
  gr=[A,kf,be(0)];function
  dc(a){return 0<=a?a:-a|0}function
  h(d,c){var
  a=k(d),e=k(c),b=D(a+e|0);bd(d,0,b,0,a);bd(c,0,b,a,e);return O(b)}function
  kg(a){return a?kh:ki}function
  kj(a){try{var
  b=[0,c7(a)];return b}catch(a){a=C(a);if(a[1]===db)return 0;throw a}}function
  F(a,b){if(a){var
  c=a[1];return[0,c,F(a[2],b)]}return b}Af(0);var
  ev=j3(1),aY=j3(2);function
  bS(b,a){return j4(b,a,0,k(a))}function
  ci(a){bS(ev,a);gl(ev,10);return aX(ev)}function
  gw(a){bS(aY,a);gl(aY,10);return aX(aY)}function
  ew(b){function
  a(b){var
  a=b;for(;;){if(a){var
  c=a[2],d=a[1];try{aX(d)}catch(a){a=C(a);if(a[1]!==gu)throw a;var
  e=a}var
  a=c;continue}return 0}}return a(Ag(0))}gp(a(ik),ew);var
  ex=At(0),cj=(4*ex|0)-1|0,kn=[A,km,be(0)];function
  ko(a){throw kn}function
  ck(a){var
  d=a[1];a[1]=ko;try{var
  c=b(d,0);Al(a,c);return c}catch(b){b=C(b);a[1]=function(a){throw b};throw b}}function
  gx(h,g,e){var
  a=g,d=e;for(;;){var
  c=b(d,0);if(c){var
  i=c[2],a=f(h,a,c[1]),d=i;continue}return a}}function
  gy(c,a){return a?[0,b(c,a[1])]:0}function
  cl(a){if(0<=a&&!(b0<a))return a;return I(kp)}function
  cm(a){var
  b=a-192|0,c=0;if(30<b>>>0){if(!(25<b+cI>>>0))c=1}else
  if(23!==b)c=1;return c?a+32|0:a}function
  kw(a){return 25<a-65>>>0?a:a+32|0}function
  kx(a){return 25<a+i$>>>0?a:a+fW|0}function
  bh(c){var
  b=0,a=c;for(;;){if(a){var
  b=b+1|0,a=a[2];continue}return b}}function
  dd(a){return a?a[2]:ch(ky)}function
  ey(d,c){if(0<=c){var
  a=d,b=c;for(;;){if(a){var
  e=a[2],f=a[1];if(0===b)return f;var
  a=e,b=b-1|0;continue}return ch(kz)}}return I(kA)}function
  cn(d,c){var
  a=d,b=c;for(;;){if(a){var
  e=[0,a[1],b],a=a[2],b=e;continue}return b}}function
  G(a){return cn(a,0)}function
  aj(c,a){if(a){var
  d=a[2],e=b(c,a[1]);return[0,e,aj(c,d)]}return 0}function
  bT(d,c){var
  a=c;for(;;){if(a){var
  e=a[2];b(d,a[1]);var
  a=e;continue}return 0}}function
  bi(e,d,c){var
  b=d,a=c;for(;;){if(a){var
  g=a[2],b=f(e,b,a[1]),a=g;continue}return b}}function
  gz(c,a,b){if(a){var
  d=a[1];return f(c,d,gz(c,a[2],b))}return b}function
  gA(e,d){var
  a=d;for(;;){if(a){var
  f=a[2],c=b(e,a[1]);if(c){var
  a=f;continue}return c}return 1}}function
  V(e,d){var
  a=d;for(;;){if(a){var
  f=a[2],c=b(e,a[1]);if(c)return c;var
  a=f;continue}return 0}}function
  gB(d,c){var
  a=c;for(;;){if(a){var
  e=a[2],b=0===ce(a[1],d)?1:0;if(b)return b;var
  a=e;continue}return 0}}function
  co(d,c){var
  a=c;for(;;){if(a){var
  b=a[1],e=a[2],f=b[2];if(0===ce(b[1],d))return f;var
  a=e;continue}throw p}}function
  de(b,a){return a<=b?b:a}function
  aN(a,c){var
  b=D(a);zJ(b,0,a,c);return b}var
  kB=D(0);function
  ez(a){var
  b=ax(a),c=D(b);bc(a,0,c,0,b);return c}function
  df(a){return O(ez(a))}function
  eA(c,b,a){if(0<=b&&0<=a&&!((ax(c)-a|0)<b)){var
  d=D(a);bc(c,b,d,0,a);return d}return I(kC)}function
  dg(c,b,a){return O(eA(c,b,a))}function
  gC(e,c,d,b,a){if(0<=a&&0<=c&&!((ax(e)-a|0)<c)&&0<=b&&!((ax(d)-a|0)<b))return bc(e,c,d,b,a);return I(kD)}function
  aZ(e,c,d,b,a){if(0<=a&&0<=c&&!((k(e)-a|0)<c)&&0<=b&&!((ax(d)-a|0)<b))return bd(e,c,d,b,a);return I(kE)}function
  gD(c){var
  a=c-9|0,b=0;if(4<a>>>0){if(23===a)b=1}else
  if(2!==a)b=1;return b?1:0}function
  eB(g,c){var
  d=ax(c);if(0===d)return c;var
  e=D(d),f=d-1|0,h=0;if(!(f<0)){var
  a=h;for(;;){H(e,a,b(g,bL(c,a)));var
  i=a+1|0;if(f!==a){var
  a=i;continue}break}}return e}function
  gE(b,a){return O(aN(b,a))}function
  aq(c,b,a){return O(eA(au(c),b,a))}function
  az(l,g){if(g){var
  h=k(l),c=0,b=g,q=0;for(;;){if(b){var
  i=b[1];if(b[2]){var
  j=(k(i)+h|0)+c|0,n=b[2],o=c<=j?j:I(kF),c=o,b=n;continue}var
  m=k(i)+c|0}else
  var
  m=c;var
  f=D(m),e=q,d=g;for(;;){if(d){var
  a=d[1];if(d[2]){var
  p=d[2];bd(a,0,f,e,k(a));bd(l,0,f,e+k(a)|0,h);var
  e=(e+k(a)|0)+h|0,d=p;continue}bd(a,0,f,e,k(a))}return O(f)}}}return kG}function
  gF(c){var
  a=c-9|0,b=0;if(4<a>>>0){if(23===a)b=1}else
  if(2!==a)b=1;return b?1:0}function
  gG(e,d,c,b){var
  a=c;for(;;){if(d<=a)throw p;if(ay(e,a)===b)return a;var
  a=a+1|0;continue}}function
  eC(a,b){return gG(a,k(a),0,b)}function
  kI(b,a,d){var
  c=k(b);if(0<=a&&!(c<a))try{gG(b,c,a,d);var
  e=1;return e}catch(a){a=C(a);if(a===p)return 0;throw a}return I(kJ)}function
  gH(b,a){return kI(b,0,a)}function
  dh(a){return O(eB(kw,au(a)))}function
  gI(a){return O(eB(cm,au(a)))}var
  kK=c9;function
  gJ(e,c,d,b,a){if(0<=a&&0<=c&&!((e.length-1-a|0)<c)&&0<=b&&!((d.length-1-a|0)<b))return zB(e,c,d,b,a);return I(kM)}function
  gK(e,c){var
  d=c.length-1;if(0===d)return[0];var
  f=aM(d,b(e,c[1])),g=d-1|0,h=1;if(!(g<1)){var
  a=h;for(;;){f[1+a]=b(e,c[1+a]);var
  i=a+1|0;if(g!==a){var
  a=i;continue}break}}return f}function
  a0(f,e,a){var
  c=z_(f,e,a),d=0<=c?1:0,g=d?a[12]!==di?1:0:d;if(g){a[11]=a[12];var
  b=a[12];a[12]=[0,b[1],b[2],b[3],a[4]+a[6]|0]}return c}function
  gM(b,a){var
  c=b?b[1]:1,e=c?gL:di,f=c?gL:di,g=[0],h=1,i=0,j=0,l=0,m=0,n=0,o=k(a),d=ez(au(a));return[0,function(a){a[9]=1;return 0},d,o,n,m,l,j,i,h,g,f,e]}function
  J(a){return dg(a[2],a[5],a[6]-a[5]|0)}function
  eD(c,a,b){return dg(c[2],a,b-a|0)}function
  gN(d){function
  q(a){return a?a[4]:0}function
  e(b,e,a){var
  c=b?b[4]:0,d=a?a[4]:0,f=d<=c?c+1|0:d+1|0;return[0,b,e,a,f]}function
  g(b,f,a){var
  g=b?b[4]:0,h=a?a[4]:0;if((h+2|0)<g){if(b){var
  c=b[3],k=b[2],i=b[1],m=q(c);if(m<=q(i))return e(i,k,e(c,f,a));if(c){var
  n=c[2],o=c[1],p=e(c[3],f,a);return e(e(i,k,o),n,p)}return I(kN)}return I(kO)}if((g+2|0)<h){if(a){var
  j=a[3],l=a[2],d=a[1],r=q(d);if(r<=q(j))return e(e(b,f,d),l,j);if(d){var
  s=d[2],t=d[1],u=e(d[3],l,j);return e(e(b,f,t),s,u)}return I(kP)}return I(kQ)}var
  v=h<=g?g+1|0:h+1|0;return[0,b,f,a,v]}function
  a(c,b){if(b){var
  e=b[3],h=b[2],i=b[1],j=f(d[1],c,h);if(0===j)return b;if(0<=j){var
  k=a(c,e);return e===k?b:g(i,h,k)}var
  l=a(c,i);return i===l?b:g(l,h,e)}return[0,0,c,0,1]}function
  h(a){return[0,0,a,0,1]}function
  E(b,a){if(a){var
  c=a[3],d=a[2];return g(E(b,a[1]),d,c)}return h(b)}function
  F(b,a){if(a){var
  c=a[2],d=a[1];return g(d,c,F(b,a[3]))}return h(b)}function
  c(b,d,a){if(b){if(a){var
  f=a[4],h=b[4],i=a[3],j=a[2],k=a[1],l=b[3],m=b[2],n=b[1];return(f+2|0)<h?g(n,m,c(l,d,a)):(h+2|0)<f?g(c(b,d,k),j,i):e(b,d,a)}return F(d,b)}return E(d,a)}function
  j(c){var
  a=c;for(;;){if(a){var
  b=a[1];if(b){var
  a=b;continue}return a[2]}throw p}}function
  G(c){var
  a=c;for(;;){if(a){var
  b=a[1];if(b){var
  a=b;continue}return[0,a[2]]}return 0}}function
  H(b){var
  a=b;for(;;){if(a){if(a[3]){var
  a=a[3];continue}return a[2]}throw p}}function
  T(b){var
  a=b;for(;;){if(a){if(a[3]){var
  a=a[3];continue}return[0,a[2]]}return 0}}function
  r(a){if(a){var
  b=a[1];if(b){var
  c=a[3],d=a[2];return g(r(b),d,c)}return a[3]}return I(kR)}function
  m(b,a){if(b){if(a){var
  d=r(a);return c(b,j(a),d)}return b}return a}function
  i(b,a){if(a){var
  e=a[3],g=a[2],h=a[1],l=f(d[1],b,g);if(0===l)return[0,h,1,e];if(0<=l){var
  j=i(b,e),m=j[3],n=j[2];return[0,c(h,g,j[1]),n,m]}var
  k=i(b,h),o=k[2],p=k[1];return[0,p,o,c(k[3],g,e)]}return kS}var
  u=0;function
  U(a){return a?0:1}function
  V(g,e){var
  a=e;for(;;){if(a){var
  h=a[3],i=a[1],b=f(d[1],g,a[2]),c=0===b?1:0;if(c)return c;var
  j=0<=b?h:i,a=j;continue}return 0}}function
  v(e,b){if(b){var
  a=b[3],h=b[2],c=b[1],i=f(d[1],e,h);if(0===i){if(c){if(a){var
  m=r(a);return g(c,j(a),m)}return c}return a}if(0<=i){var
  k=v(e,a);return a===k?b:g(c,h,k)}var
  l=v(e,c);return c===l?b:g(l,h,a)}return 0}function
  k(d,b){if(d){if(b){var
  g=b[4],e=b[2],h=d[4],f=d[2],m=b[3],n=b[1],o=d[3],p=d[1];if(g<=h){if(1===g)return a(e,d);var
  j=i(f,b),q=j[1],r=k(o,j[3]);return c(k(p,q),f,r)}if(1===h)return a(f,b);var
  l=i(e,d),s=l[1],t=k(l[3],m);return c(k(s,n),e,t)}return d}return b}function
  n(a,d){if(a){if(d){var
  e=a[3],f=a[2],g=a[1],b=i(f,d),h=b[1];if(b[2]){var
  j=n(e,b[3]);return c(n(g,h),f,j)}var
  k=n(e,b[3]);return m(n(g,h),k)}return 0}return 0}function
  w(e,a){if(a){var
  j=a[3],g=a[2],k=a[1],l=f(d[1],e,g);if(0===l)return 0;if(0<=l){var
  h=w(e,j);if(h){var
  m=h[2];return[0,c(k,g,h[1]),m]}return 0}var
  i=w(e,k);if(i){var
  n=i[2],o=i[1];return[0,o,function(a){return c(b(n,0),g,j)}]}return 0}return[0,0,function(a){return 0}]}function
  J(g,f){var
  a=g,c=f;for(;;){if(a&&c){var
  h=a[3],i=a[2],j=a[1];if(a===c)return 0;var
  d=w(i,c);if(d){var
  k=d[2],e=J(j,d[1]);if(e){var
  a=h,c=b(k,0);continue}return e}return 0}return 1}}function
  o(a,d){if(a){if(d){var
  e=a[3],f=a[2],g=a[1],b=i(f,d),h=b[1];if(b[2]){var
  j=o(e,b[3]);return m(o(g,h),j)}var
  k=o(e,b[3]);return c(o(g,h),f,k)}return a}return 0}function
  l(d,c){var
  a=d,b=c;for(;;){if(a){var
  e=[0,a[2],a[3],b],a=a[1],b=e;continue}return b}}function
  K(m,k){var
  n=l(k,0),b=l(m,0),a=n;for(;;){if(b){if(a){var
  e=a[3],g=a[2],h=b[3],i=b[2],c=f(d[1],b[1],a[1]);if(0===c){var
  j=l(g,e),b=l(i,h),a=j;continue}return c}return 1}return a?-1:0}}function
  W(b,a){return 0===K(b,a)?1:0}function
  t(o,n){var
  a=o,b=n;for(;;){if(a){if(b){var
  h=b[3],i=b[1],c=a[3],e=a[2],g=a[1],j=f(d[1],e,b[2]);if(0===j){var
  k=t(g,i);if(k){var
  a=c,b=h;continue}return k}if(0<=j){var
  l=t([0,0,e,c,0],h);if(l){var
  a=g;continue}return l}var
  m=t([0,g,e,0,0],i);if(m){var
  a=c;continue}return m}return 0}return 1}}function
  L(c,d){var
  a=d;for(;;){if(a){var
  e=a[3],f=a[2];L(c,a[1]);b(c,f);var
  a=e;continue}return 0}}function
  M(c,e,d){var
  a=e,b=d;for(;;){if(a){var
  g=a[3],h=a[2],i=f(c,h,M(c,a[1],b)),a=g,b=i;continue}return b}}function
  N(c,g){var
  a=g;for(;;){if(a){var
  h=a[3],i=a[1],d=b(c,a[2]);if(d){var
  e=N(c,i);if(e){var
  a=h;continue}var
  f=e}else
  var
  f=d;return f}return 1}}function
  O(c,g){var
  a=g;for(;;){if(a){var
  h=a[3],i=a[1],d=b(c,a[2]);if(d)var
  e=d;else{var
  f=O(c,i);if(!f){var
  a=h;continue}var
  e=f}return e}return 0}}function
  x(d,a){if(a){var
  g=a[3],h=a[2],i=a[1],e=x(d,i),j=b(d,h),f=x(d,g);if(j){if(i===e&&g===f)return a;return c(e,h,f)}return m(e,f)}return 0}function
  y(d,a){if(a){var
  e=a[2],l=a[3],f=y(d,a[1]),g=f[2],h=f[1],n=b(d,e),i=y(d,l),j=i[2],k=i[1];if(n){var
  o=m(g,j);return[0,c(h,e,k),o]}var
  p=c(g,e,j);return[0,m(h,k),p]}return kT}function
  z(a){if(a){var
  b=a[1],c=z(a[3]);return(z(b)+1|0)+c|0}return 0}function
  P(d,c){var
  b=d,a=c;for(;;){if(a){var
  e=a[2],f=a[1],b=[0,e,P(b,a[3])],a=f;continue}return b}}function
  X(a){return P(0,a)}function
  Y(g,e){var
  a=e;for(;;){if(a){var
  b=a[2],h=a[3],i=a[1],c=f(d[1],g,b);if(0===c)return b;var
  j=0<=c?h:i,a=j;continue}throw p}}function
  Z(f,j){var
  c=j;for(;;){if(c){var
  g=c[2],k=c[3],l=c[1];if(b(f,g)){var
  d=g,a=l;for(;;){if(a){var
  e=a[2],h=a[3],i=a[1];if(b(f,e)){var
  d=e,a=i;continue}var
  a=h;continue}return d}}var
  c=k;continue}throw p}}function
  _(f,j){var
  c=j;for(;;){if(c){var
  g=c[2],k=c[3],l=c[1];if(b(f,g)){var
  d=g,a=l;for(;;){if(a){var
  e=a[2],h=a[3],i=a[1];if(b(f,e)){var
  d=e,a=i;continue}var
  a=h;continue}return[0,d]}}var
  c=k;continue}return 0}}function
  $(f,j){var
  c=j;for(;;){if(c){var
  g=c[2],k=c[3],l=c[1];if(b(f,g)){var
  d=g,a=k;for(;;){if(a){var
  e=a[2],h=a[3],i=a[1];if(b(f,e)){var
  d=e,a=h;continue}var
  a=i;continue}return d}}var
  c=l;continue}throw p}}function
  aa(f,j){var
  c=j;for(;;){if(c){var
  g=c[2],k=c[3],l=c[1];if(b(f,g)){var
  d=g,a=k;for(;;){if(a){var
  e=a[2],h=a[3],i=a[1];if(b(f,e)){var
  d=e,a=h;continue}var
  a=i;continue}return[0,d]}}var
  c=l;continue}return 0}}function
  ab(g,e){var
  a=e;for(;;){if(a){var
  b=a[2],h=a[3],i=a[1],c=f(d[1],g,b);if(0===c)return[0,b];var
  j=0<=c?h:i,a=j;continue}return 0}}function
  A(g,e,b){var
  h=0;if(0!==g){var
  m=H(g);if(0<=f(d[1],m,e))h=1}if(!h){var
  i=0;if(0!==b){var
  l=j(b);if(0<=f(d[1],e,l))i=1}if(!i)return c(g,e,b)}return k(g,a(e,b))}function
  B(c,a){if(a){var
  d=a[3],e=a[2],f=a[1],g=B(c,f),h=b(c,e),i=B(c,d);if(f===g&&e===h&&d===i)return a;return A(g,h,i)}return 0}function
  C(e,c){if(c){var
  f=c[3],g=c[2],h=c[1],d=C(e,h),i=b(e,g),a=C(e,f);if(i){var
  k=i[1];if(h===d&&g===k&&f===a)return c;return A(d,k,a)}if(d){if(a){var
  l=r(a);return A(d,j(a),l)}return d}return a}return 0}function
  ac(c){if(c){var
  k=c[2],g=c[1];if(k){var
  l=k[2],i=k[1];if(l){var
  n=l[2],o=l[1];if(n){var
  p=n[2],r=n[1];if(p){if(p[2]){var
  b=d[1],x=function(j,g){if(2===j){if(g){var
  n=g[2];if(n){var
  o=n[1],k=g[1],J=n[2],y=f(b,k,o),K=0===y?[0,k,0]:0<y?[0,k,[0,o,0]]:[0,o,[0,k,0]];return[0,K,J]}}}else
  if(3===j&&g){var
  p=g[2];if(p){var
  q=p[2];if(q){var
  c=q[1],a=p[1],d=g[1],N=q[2],D=f(b,d,a);if(0===D)var
  E=f(b,a,c),O=0===E?[0,a,0]:0<E?[0,a,[0,c,0]]:[0,c,[0,a,0]],r=O;else
  if(0<D){var
  F=f(b,a,c);if(0===F)var
  s=[0,d,[0,a,0]];else
  if(0<F)var
  s=[0,d,[0,a,[0,c,0]]];else
  var
  G=f(b,d,c),P=0===G?[0,d,[0,a,0]]:0<G?[0,d,[0,c,[0,a,0]]]:[0,c,[0,d,[0,a,0]]],s=P;var
  r=s}else{var
  H=f(b,d,c);if(0===H)var
  t=[0,a,[0,d,0]];else
  if(0<H)var
  t=[0,a,[0,d,[0,c,0]]];else
  var
  I=f(b,a,c),Q=0===I?[0,a,[0,d,0]]:0<I?[0,a,[0,c,[0,d,0]]]:[0,c,[0,a,[0,d,0]]],t=Q;var
  r=t}return[0,r,N]}}}var
  z=j>>1,A=m(z,g),L=A[1],B=m(j-z|0,A[2]),i=L,h=B[1],e=0,M=B[2];for(;;){if(i){if(h){var
  u=h[2],v=h[1],w=i[2],l=i[1],x=f(b,l,v);if(0===x){var
  i=w,h=u,e=[0,l,e];continue}if(0<=x){var
  h=u,e=[0,v,e];continue}var
  i=w,e=[0,l,e];continue}var
  C=cn(i,e)}else
  var
  C=cn(h,e);return[0,C,M]}},m=function(j,g){if(2===j){if(g){var
  m=g[2];if(m){var
  n=m[1],k=g[1],J=m[2],y=f(b,k,n),K=0===y?[0,k,0]:0<=y?[0,n,[0,k,0]]:[0,k,[0,n,0]];return[0,K,J]}}}else
  if(3===j&&g){var
  o=g[2];if(o){var
  p=o[2];if(p){var
  c=p[1],a=o[1],d=g[1],N=p[2],D=f(b,d,a);if(0===D)var
  E=f(b,a,c),O=0===E?[0,a,0]:0<=E?[0,c,[0,a,0]]:[0,a,[0,c,0]],q=O;else
  if(0<=D){var
  F=f(b,d,c);if(0===F)var
  r=[0,a,[0,d,0]];else
  if(0<=F)var
  G=f(b,a,c),P=0===G?[0,a,[0,d,0]]:0<=G?[0,c,[0,a,[0,d,0]]]:[0,a,[0,c,[0,d,0]]],r=P;else
  var
  r=[0,a,[0,d,[0,c,0]]];var
  q=r}else{var
  H=f(b,a,c);if(0===H)var
  s=[0,d,[0,a,0]];else
  if(0<=H)var
  I=f(b,d,c),Q=0===I?[0,d,[0,a,0]]:0<=I?[0,c,[0,d,[0,a,0]]]:[0,d,[0,c,[0,a,0]]],s=Q;else
  var
  s=[0,d,[0,a,[0,c,0]]];var
  q=s}return[0,q,N]}}}var
  z=j>>1,A=x(z,g),L=A[1],B=x(j-z|0,A[2]),i=L,h=B[1],e=0,M=B[2];for(;;){if(i){if(h){var
  t=h[2],u=h[1],v=i[2],l=i[1],w=f(b,l,u);if(0===w){var
  i=v,h=t,e=[0,l,e];continue}if(0<w){var
  i=v,e=[0,l,e];continue}var
  h=t,e=[0,u,e];continue}var
  C=cn(i,e)}else
  var
  C=cn(h,e);return[0,C,M]}},q=bh(c),t=2<=q?m(q,c)[1]:c,j=function(b,a){if(!(3<b>>>0))switch(b){case
  0:return[0,0,a];case
  1:if(a)return[0,[0,0,a[1],0,1],a[2]];break;case
  2:if(a){var
  d=a[2];if(d)return[0,[0,[0,0,a[1],0,1],d[1],0,2],d[2]]}break;default:if(a){var
  f=a[2];if(f){var
  g=f[2];if(g)return[0,[0,[0,0,a[1],0,1],f[1],[0,0,g[1],0,1],2],g[2]]}}}var
  h=b/2|0,i=j(h,a),c=i[2],l=i[1];if(c){var
  m=c[1],k=j((b-h|0)-1|0,c[2]),n=k[2];return[0,e(l,m,k[1]),n]}throw[0,s,kU]};return j(bh(t),t)[1]}var
  v=p[1];return a(v,a(r,a(o,a(i,h(g)))))}return a(r,a(o,a(i,h(g))))}return a(o,a(i,h(g)))}return a(i,h(g))}return h(g)}return u}function
  Q(c,b){return gx(function(c,b){return a(b,c)},b,c)}function
  ad(a){return Q(a,u)}function
  D(a,d){if(a){var
  b=a[1],c=l(a[2],a[3]);return[0,b,function(a){return D(c,a)}]}return 0}function
  ae(a){var
  b=l(a,0);return function(a){return D(b,a)}}function
  R(d,c){var
  a=d,b=c;for(;;){if(a){var
  e=[0,a[2],a[1],b],a=a[3],b=e;continue}return b}}function
  S(a,d){if(a){var
  b=a[1],c=R(a[2],a[3]);return[0,b,function(a){return S(c,a)}]}return 0}function
  af(a){var
  b=R(a,0);return function(a){return S(b,a)}}return[0,u,U,V,a,h,v,k,n,J,o,K,W,t,L,B,M,N,O,x,C,y,z,X,j,G,H,T,j,G,i,Y,ab,Z,_,$,aa,ac,function(j,i){var
  a=i,b=0;for(;;){if(a){var
  c=a[3],e=a[2],k=a[1],g=f(d[1],e,j);if(0!==g){if(0<=g){var
  a=k,b=[0,e,c,b];continue}var
  a=c;continue}var
  h=[0,e,c,b]}else
  var
  h=b;return function(a){return D(h,a)}}},ae,af,Q,ad]}function
  dj(g){function
  h(a){return a?a[5]:0}function
  d(b,f,e,a){var
  c=h(b),d=h(a),g=d<=c?c+1|0:d+1|0;return[0,b,f,e,a,g]}function
  q(b,a){return[0,0,b,a,0,1]}function
  a(b,g,f,a){var
  i=b?b[5]:0,j=a?a[5]:0;if((j+2|0)<i){if(b){var
  c=b[4],m=b[3],n=b[2],k=b[1],q=h(c);if(q<=h(k))return d(k,n,m,d(c,g,f,a));if(c){var
  r=c[3],s=c[2],t=c[1],u=d(c[4],g,f,a);return d(d(k,n,m,t),s,r,u)}return I(kV)}return I(kW)}if((i+2|0)<j){if(a){var
  l=a[4],o=a[3],p=a[2],e=a[1],v=h(e);if(v<=h(l))return d(d(b,g,f,e),p,o,l);if(e){var
  w=e[3],x=e[2],y=e[1],z=d(e[4],p,o,l);return d(d(b,g,f,y),x,w,z)}return I(kX)}return I(kY)}var
  A=j<=i?i+1|0:j+1|0;return[0,b,g,f,a,A]}var
  C=0;function
  Q(a){return a?0:1}function
  m(d,c,b){if(b){var
  e=b[4],i=b[3],j=b[2],h=b[1],o=b[5],k=f(g[1],d,j);if(0===k)return i===c?b:[0,h,d,c,e,o];if(0<=k){var
  l=m(d,c,e);return e===l?b:a(h,j,i,l)}var
  n=m(d,c,h);return h===n?b:a(n,j,i,e)}return[0,0,d,c,0,1]}function
  R(d,c){var
  a=c;for(;;){if(a){var
  e=a[4],h=a[3],i=a[1],b=f(g[1],d,a[2]);if(0===b)return h;var
  j=0<=b?e:i,a=j;continue}throw p}}function
  S(g,l){var
  c=l;for(;;){if(c){var
  h=c[2],m=c[4],n=c[3],o=c[1];if(b(g,h)){var
  e=h,d=n,a=o;for(;;){if(a){var
  f=a[2],i=a[4],j=a[3],k=a[1];if(b(g,f)){var
  e=f,d=j,a=k;continue}var
  a=i;continue}return[0,e,d]}}var
  c=m;continue}throw p}}function
  T(g,l){var
  c=l;for(;;){if(c){var
  h=c[2],m=c[4],n=c[3],o=c[1];if(b(g,h)){var
  e=h,d=n,a=o;for(;;){if(a){var
  f=a[2],i=a[4],j=a[3],k=a[1];if(b(g,f)){var
  e=f,d=j,a=k;continue}var
  a=i;continue}return[0,[0,e,d]]}}var
  c=m;continue}return 0}}function
  U(g,l){var
  c=l;for(;;){if(c){var
  h=c[2],m=c[4],n=c[3],o=c[1];if(b(g,h)){var
  e=h,d=n,a=m;for(;;){if(a){var
  f=a[2],i=a[4],j=a[3],k=a[1];if(b(g,f)){var
  e=f,d=j,a=i;continue}var
  a=k;continue}return[0,e,d]}}var
  c=o;continue}throw p}}function
  V(g,l){var
  c=l;for(;;){if(c){var
  h=c[2],m=c[4],n=c[3],o=c[1];if(b(g,h)){var
  e=h,d=n,a=m;for(;;){if(a){var
  f=a[2],i=a[4],j=a[3],k=a[1];if(b(g,f)){var
  e=f,d=j,a=i;continue}var
  a=k;continue}return[0,[0,e,d]]}}var
  c=o;continue}return 0}}function
  W(d,c){var
  a=c;for(;;){if(a){var
  e=a[4],h=a[3],i=a[1],b=f(g[1],d,a[2]);if(0===b)return[0,h];var
  j=0<=b?e:i,a=j;continue}return 0}}function
  X(e,d){var
  a=d;for(;;){if(a){var
  h=a[4],i=a[1],b=f(g[1],e,a[2]),c=0===b?1:0;if(c)return c;var
  j=0<=b?h:i,a=j;continue}return 0}}function
  n(c){var
  a=c;for(;;){if(a){var
  b=a[1];if(b){var
  a=b;continue}return[0,a[2],a[3]]}throw p}}function
  D(c){var
  a=c;for(;;){if(a){var
  b=a[1];if(b){var
  a=b;continue}return[0,[0,a[2],a[3]]]}return 0}}function
  Y(b){var
  a=b;for(;;){if(a){if(a[4]){var
  a=a[4];continue}return[0,a[2],a[3]]}throw p}}function
  Z(b){var
  a=b;for(;;){if(a){if(a[4]){var
  a=a[4];continue}return[0,[0,a[2],a[3]]]}return 0}}function
  r(b){if(b){var
  c=b[1];if(c){var
  d=b[4],e=b[3],f=b[2];return a(r(c),f,e,d)}return b[4]}return I(kZ)}function
  E(c,b){if(c){if(b){var
  d=n(b),e=d[2],f=d[1];return a(c,f,e,r(b))}return c}return b}function
  t(e,b){if(b){var
  c=b[4],i=b[3],h=b[2],d=b[1],j=f(g[1],e,h);if(0===j)return E(d,c);if(0<=j){var
  k=t(e,c);return c===k?b:a(d,h,i,k)}var
  l=t(e,d);return d===l?b:a(l,h,i,c)}return 0}function
  u(d,i,c){if(c){var
  e=c[4],j=c[3],k=c[2],h=c[1],r=c[5],l=f(g[1],d,k);if(0===l){var
  m=b(i,[0,j]);if(m){var
  n=m[1];return j===n?c:[0,h,d,n,e,r]}return E(h,e)}if(0<=l){var
  o=u(d,i,e);return e===o?c:a(h,k,j,o)}var
  p=u(d,i,h);return h===p?c:a(p,k,j,e)}var
  q=b(i,0);return q?[0,0,d,q[1],0,1]:0}function
  F(b,c){var
  a=c;for(;;){if(a){var
  d=a[4],e=a[3],g=a[2];F(b,a[1]);f(b,g,e);var
  a=d;continue}return 0}}function
  v(c,a){if(a){var
  d=a[5],e=a[4],f=a[3],g=a[2],h=v(c,a[1]),i=b(c,f);return[0,h,g,i,v(c,e),d]}return 0}function
  w(b,a){if(a){var
  c=a[2],d=a[5],e=a[4],g=a[3],h=w(b,a[1]),i=f(b,c,g);return[0,h,c,i,w(b,e),d]}return 0}function
  G(c,e,d){var
  a=e,b=d;for(;;){if(a){var
  f=a[4],g=a[3],h=a[2],i=aJ(c,h,g,G(c,a[1],b)),a=f,b=i;continue}return b}}function
  H(b,g){var
  a=g;for(;;){if(a){var
  h=a[4],i=a[1],c=f(b,a[2],a[3]);if(c){var
  d=H(b,i);if(d){var
  a=h;continue}var
  e=d}else
  var
  e=c;return e}return 1}}function
  J(b,g){var
  a=g;for(;;){if(a){var
  h=a[4],i=a[1],c=f(b,a[2],a[3]);if(c)var
  d=c;else{var
  e=J(b,i);if(!e){var
  a=h;continue}var
  d=e}return d}return 0}}function
  K(d,c,b){if(b){var
  e=b[4],f=b[3],g=b[2];return a(K(d,c,b[1]),g,f,e)}return q(d,c)}function
  L(d,c,b){if(b){var
  e=b[3],f=b[2],g=b[1];return a(g,f,e,L(d,c,b[4]))}return q(d,c)}function
  c(e,g,f,b){if(e){if(b){var
  h=b[5],i=e[5],j=b[4],k=b[3],l=b[2],m=b[1],n=e[4],o=e[3],p=e[2],q=e[1];return(h+2|0)<i?a(q,p,o,c(n,g,f,b)):(i+2|0)<h?a(c(e,g,f,m),l,k,j):d(e,g,f,b)}return L(g,f,e)}return K(g,f,b)}function
  j(b,a){if(b){if(a){var
  d=n(a),e=d[2],f=d[1];return c(b,f,e,r(a))}return b}return a}function
  o(d,e,b,a){return b?c(d,e,b[1],a):j(d,a)}function
  i(b,a){if(a){var
  d=a[4],e=a[3],h=a[2],j=a[1],m=f(g[1],b,h);if(0===m)return[0,j,[0,e],d];if(0<=m){var
  k=i(b,d),n=k[3],o=k[2];return[0,c(j,h,e,k[1]),o,n]}var
  l=i(b,j),p=l[2],q=l[1];return[0,q,p,c(l[3],h,e,d)]}return k0}function
  k(c,b,a){if(b){var
  d=b[2],j=b[5],l=b[4],m=b[3],n=b[1];if(h(a)<=j){var
  e=i(d,a),p=e[2],q=e[1],r=k(c,l,e[3]),t=aJ(c,d,[0,m],p);return o(k(c,n,q),d,t,r)}}else
  if(!a)return 0;if(a){var
  f=a[2],u=a[4],v=a[3],w=a[1],g=i(f,b),x=g[2],y=g[1],z=k(c,g[3],u),A=aJ(c,f,x,[0,v]);return o(k(c,y,w),f,A,z)}throw[0,s,k1]}function
  l(d,b,a){if(b){if(a){var
  j=a[3],e=a[2],k=b[3],f=b[2],u=a[4],v=a[1],w=b[4],x=b[1];if(a[5]<=b[5]){var
  g=i(f,a),m=g[2],y=g[3],n=l(d,x,g[1]),p=l(d,w,y);return m?o(n,f,aJ(d,f,k,m[1]),p):c(n,f,k,p)}var
  h=i(e,b),q=h[2],z=h[3],r=l(d,h[1],v),s=l(d,z,u);return q?o(r,e,aJ(d,e,q[1],j),s):c(r,e,j,s)}var
  t=b}else
  var
  t=a;return t}function
  x(b,a){if(a){var
  g=a[4],h=a[3],i=a[2],k=a[1],d=x(b,k),l=f(b,i,h),e=x(b,g);if(l){if(k===d&&g===e)return a;return c(d,i,h,e)}return j(d,e)}return 0}function
  y(b,a){if(a){var
  d=a[2],i=a[4],k=a[3],e=y(b,a[1]),g=f(b,d,k),h=y(b,i);return g?c(e,d,g[1],h):j(e,h)}return 0}function
  z(b,a){if(a){var
  d=a[3],e=a[2],n=a[4],g=z(b,a[1]),h=g[2],i=g[1],o=f(b,e,d),k=z(b,n),l=k[2],m=k[1];if(o){var
  p=j(h,l);return[0,c(i,e,d,m),p]}var
  q=c(h,e,d,l);return[0,j(i,m),q]}return k2}function
  e(d,c){var
  a=d,b=c;for(;;){if(a){var
  e=[0,a[2],a[3],a[4],b],a=a[1],b=e;continue}return b}}function
  _(j,i,h){var
  r=e(h,0),b=e(i,0),a=r;for(;;){if(b){if(a){var
  k=a[4],l=a[3],m=a[2],n=b[4],o=b[3],p=b[2],c=f(g[1],b[1],a[1]);if(0===c){var
  d=f(j,p,m);if(0===d){var
  q=e(l,k),b=e(o,n),a=q;continue}return d}return c}return 1}return a?-1:0}}function
  $(k,j,i){var
  s=e(i,0),b=e(j,0),a=s;for(;;){if(b){if(a){var
  l=a[4],m=a[3],n=a[2],o=b[4],p=b[3],q=b[2],c=0===f(g[1],b[1],a[1])?1:0;if(c){var
  d=f(k,q,n);if(d){var
  r=e(m,l),b=e(p,o),a=r;continue}var
  h=d}else
  var
  h=c;return h}return 0}return a?0:1}}function
  A(a){if(a){var
  b=a[1],c=A(a[4]);return(A(b)+1|0)+c|0}return 0}function
  M(d,c){var
  b=d,a=c;for(;;){if(a){var
  e=a[3],f=a[2],g=a[1],b=[0,[0,f,e],M(b,a[4])],a=g;continue}return b}}function
  aa(a){return M(0,a)}function
  N(b,a){return gx(function(b,a){return m(a[1],a[2],b)},a,b)}function
  ab(a){return N(a,C)}function
  B(a,f){if(a){var
  b=a[2],c=a[1],d=e(a[3],a[4]);return[0,[0,c,b],function(a){return B(d,a)}]}return 0}function
  ac(a){var
  b=e(a,0);return function(a){return B(b,a)}}function
  O(d,c){var
  a=d,b=c;for(;;){if(a){var
  e=[0,a[2],a[3],a[1],b],a=a[4],b=e;continue}return b}}function
  P(a,e){if(a){var
  b=a[2],c=a[1],d=O(a[3],a[4]);return[0,[0,c,b],function(a){return P(d,a)}]}return 0}function
  ad(a){var
  b=O(a,0);return function(a){return P(b,a)}}return[0,C,Q,X,m,u,q,t,k,l,_,$,F,G,H,J,x,y,z,A,aa,n,D,Y,Z,n,D,i,R,W,S,T,U,V,v,w,ac,ad,function(k,j){var
  a=j,b=0;for(;;){if(a){var
  c=a[4],e=a[3],d=a[2],l=a[1],h=f(g[1],d,k);if(0!==h){if(0<=h){var
  a=l,b=[0,d,e,c,b];continue}var
  a=c;continue}var
  i=[0,d,e,c,b]}else
  var
  i=b;return function(a){return B(i,a)}}},N,ab]}var
  eE=[A,k3,be(0)];function
  gO(a){return[0,0,0]}function
  eF(b,a){a[1]=[0,b,a[1]];a[2]=a[2]+1|0;return 0}function
  cp(a){var
  b=a[1];if(b){var
  c=b[1];a[1]=b[2];a[2]=a[2]-1|0;return c}throw eE}function
  k4(b,a){return bT(b,a[1])}function
  gP(a){var
  b=1<=a?a:1,c=cj<b?cj:b,d=D(c);return[0,d,0,c,d]}function
  gQ(a){return dg(a[1],0,a[2])}function
  gR(a,c){var
  d=a[2],b=[0,a[3]];for(;;){if(b[1]<(d+c|0)){b[1]=2*b[1]|0;continue}if(cj<b[1])if((d+c|0)<=cj)b[1]=cj;else
  ch(k7);var
  e=D(b[1]);gC(a[1],0,e,0,a[2]);a[1]=e;a[3]=b[1];if((a[2]+c|0)<=a[3]){if((d+c|0)<=a[3])return 0;throw[0,s,k5]}throw[0,s,k6]}}function
  bU(a,c){var
  b=k(c),d=a[2]+b|0;if(a[3]<d)gR(a,b);bd(c,0,a[1],a[2],b);a[2]=d;return 0}function
  eG(a){return 5===a[2]?12:-6}function
  gS(a){return[0,0,D(a)]}function
  gT(a,g){var
  b=ax(a[2]),c=a[1]+g|0,d=b<c?1:0;if(d){var
  e=D(de(b*2|0,c));gC(a[2],0,e,0,b);a[2]=e;var
  f=0}else
  var
  f=d;return f}function
  bV(a,b){gT(a,1);aL(a[2],a[1],b);a[1]=a[1]+1|0;return 0}function
  S(a,c){var
  b=k(c);gT(a,b);aZ(c,0,a[2],a[1],b);a[1]=a[1]+b|0;return 0}function
  gU(a){return dg(a[2],0,a[1])}function
  gV(a){if(typeof
  a==="number")switch(a){case
  0:return k8;case
  1:return k9;case
  2:return k_;case
  3:return k$;case
  4:return la;case
  5:return lb;default:return lc}else
  switch(a[0]){case
  0:return a[1];case
  1:return a[1];default:return h(ld,gE(1,a[1]))}}function
  eH(b,c){var
  a=c;for(;;)if(typeof
  a==="number")return 0;else
  switch(a[0]){case
  0:var
  d=a[1];S(b,le);var
  a=d;continue;case
  1:var
  e=a[1];S(b,lf);var
  a=e;continue;case
  2:var
  f=a[1];S(b,lg);var
  a=f;continue;case
  3:var
  g=a[1];S(b,lh);var
  a=g;continue;case
  4:var
  h=a[1];S(b,li);var
  a=h;continue;case
  5:var
  i=a[1];S(b,lj);var
  a=i;continue;case
  6:var
  j=a[1];S(b,lk);var
  a=j;continue;case
  7:var
  k=a[1];S(b,ll);var
  a=k;continue;case
  8:var
  l=a[2],m=a[1];S(b,lm);eH(b,m);S(b,ln);var
  a=l;continue;case
  9:var
  n=a[3],o=a[1];S(b,lo);eH(b,o);S(b,lp);var
  a=n;continue;case
  10:var
  p=a[1];S(b,lq);var
  a=p;continue;case
  11:var
  q=a[1];S(b,lr);var
  a=q;continue;case
  12:var
  r=a[1];S(b,ls);var
  a=r;continue;case
  13:var
  s=a[1];S(b,lt);var
  a=s;continue;default:var
  t=a[1];S(b,lu);var
  a=t;continue}}function
  K(a){if(typeof
  a==="number")return 0;else
  switch(a[0]){case
  0:return[0,K(a[1])];case
  1:return[1,K(a[1])];case
  2:return[2,K(a[1])];case
  3:return[3,K(a[1])];case
  4:return[4,K(a[1])];case
  5:return[5,K(a[1])];case
  6:return[6,K(a[1])];case
  7:return[7,K(a[1])];case
  8:var
  b=a[1];return[8,b,K(a[2])];case
  9:var
  c=a[2],d=a[1];return[9,c,d,K(a[3])];case
  10:return[10,K(a[1])];case
  11:return[11,K(a[1])];case
  12:return[12,K(a[1])];case
  13:return[13,K(a[1])];default:return[14,K(a[1])]}}function
  T(a){if(typeof
  a==="number"){var
  s=function(a){return 0},t=function(a){return 0},u=function(a){return 0};return[0,function(a){return 0},u,t,s]}else
  switch(a[0]){case
  0:var
  c=T(a[1]),v=c[4],w=c[3],x=c[2],y=c[1],z=function(a){b(x,0);return 0};return[0,function(a){b(y,0);return 0},z,w,v];case
  1:var
  d=T(a[1]),A=d[4],B=d[3],C=d[2],D=d[1],E=function(a){b(C,0);return 0};return[0,function(a){b(D,0);return 0},E,B,A];case
  2:var
  e=T(a[1]),F=e[4],G=e[3],H=e[2],I=e[1],J=function(a){b(H,0);return 0};return[0,function(a){b(I,0);return 0},J,G,F];case
  3:var
  f=T(a[1]),L=f[4],N=f[3],O=f[2],P=f[1],Q=function(a){b(O,0);return 0};return[0,function(a){b(P,0);return 0},Q,N,L];case
  4:var
  g=T(a[1]),R=g[4],S=g[3],U=g[2],V=g[1],W=function(a){b(U,0);return 0};return[0,function(a){b(V,0);return 0},W,S,R];case
  5:var
  h=T(a[1]),X=h[4],Y=h[3],Z=h[2],_=h[1],$=function(a){b(Z,0);return 0};return[0,function(a){b(_,0);return 0},$,Y,X];case
  6:var
  i=T(a[1]),aa=i[4],ab=i[3],ac=i[2],ad=i[1],ae=function(a){b(ac,0);return 0};return[0,function(a){b(ad,0);return 0},ae,ab,aa];case
  7:var
  j=T(a[1]),af=j[4],ag=j[3],ah=j[2],ai=j[1],aj=function(a){b(ah,0);return 0};return[0,function(a){b(ai,0);return 0},aj,ag,af];case
  8:var
  k=T(a[2]),ak=k[4],al=k[3],am=k[2],an=k[1],ao=function(a){b(am,0);return 0};return[0,function(a){b(an,0);return 0},ao,al,ak];case
  9:var
  ap=a[2],aq=a[1],l=T(a[3]),ar=l[4],as=l[3],at=l[2],au=l[1],m=T(M(K(aq),ap)),av=m[4],aw=m[3],ax=m[2],ay=m[1],az=function(a){b(av,0);b(ar,0);return 0},aA=function(a){b(as,0);b(aw,0);return 0},aB=function(a){b(ax,0);b(at,0);return 0};return[0,function(a){b(au,0);b(ay,0);return 0},aB,aA,az];case
  10:var
  n=T(a[1]),aC=n[4],aD=n[3],aE=n[2],aF=n[1],aG=function(a){b(aE,0);return 0};return[0,function(a){b(aF,0);return 0},aG,aD,aC];case
  11:var
  o=T(a[1]),aH=o[4],aI=o[3],aJ=o[2],aK=o[1],aL=function(a){b(aJ,0);return 0};return[0,function(a){b(aK,0);return 0},aL,aI,aH];case
  12:var
  p=T(a[1]),aM=p[4],aN=p[3],aO=p[2],aP=p[1],aQ=function(a){b(aO,0);return 0};return[0,function(a){b(aP,0);return 0},aQ,aN,aM];case
  13:var
  q=T(a[1]),aR=q[4],aS=q[3],aT=q[2],aU=q[1],aV=function(a){b(aR,0);return 0},aW=function(a){b(aS,0);return 0},aX=function(a){b(aT,0);return 0};return[0,function(a){b(aU,0);return 0},aX,aW,aV];default:var
  r=T(a[1]),aY=r[4],aZ=r[3],a0=r[2],a1=r[1],a2=function(a){b(aY,0);return 0},a3=function(a){b(aZ,0);return 0},a4=function(a){b(a0,0);return 0};return[0,function(a){b(a1,0);return 0},a4,a3,a2]}}function
  M(d,c){var
  a=0;if(typeof
  d==="number")if(typeof
  c==="number")return 0;else
  switch(c[0]){case
  10:break;case
  11:a=1;break;case
  12:a=2;break;case
  13:a=3;break;case
  14:a=4;break;case
  8:a=5;break;case
  9:a=6;break;default:throw[0,s,lv]}else
  switch(d[0]){case
  0:var
  e=0,u=d[1];if(typeof
  c!=="number")switch(c[0]){case
  0:return[0,M(u,c[1])];case
  8:a=5;e=1;break;case
  9:a=6;e=1;break;case
  10:e=1;break;case
  11:a=1;e=1;break;case
  12:a=2;e=1;break;case
  13:a=3;e=1;break;case
  14:a=4;e=1;break}if(!e)a=7;break;case
  1:var
  f=0,v=d[1];if(typeof
  c!=="number")switch(c[0]){case
  1:return[1,M(v,c[1])];case
  8:a=5;f=1;break;case
  9:a=6;f=1;break;case
  10:f=1;break;case
  11:a=1;f=1;break;case
  12:a=2;f=1;break;case
  13:a=3;f=1;break;case
  14:a=4;f=1;break}if(!f)a=7;break;case
  2:var
  g=0,w=d[1];if(typeof
  c==="number")g=1;else
  switch(c[0]){case
  2:return[2,M(w,c[1])];case
  8:a=5;break;case
  9:a=6;break;case
  10:break;case
  11:a=1;break;case
  12:a=2;break;case
  13:a=3;break;case
  14:a=4;break;default:g=1}if(g)a=7;break;case
  3:var
  h=0,x=d[1];if(typeof
  c==="number")h=1;else
  switch(c[0]){case
  3:return[3,M(x,c[1])];case
  8:a=5;break;case
  9:a=6;break;case
  10:break;case
  11:a=1;break;case
  12:a=2;break;case
  13:a=3;break;case
  14:a=4;break;default:h=1}if(h)a=7;break;case
  4:var
  i=0,y=d[1];if(typeof
  c==="number")i=1;else
  switch(c[0]){case
  4:return[4,M(y,c[1])];case
  8:a=5;break;case
  9:a=6;break;case
  10:break;case
  11:a=1;break;case
  12:a=2;break;case
  13:a=3;break;case
  14:a=4;break;default:i=1}if(i)a=7;break;case
  5:var
  j=0,z=d[1];if(typeof
  c==="number")j=1;else
  switch(c[0]){case
  5:return[5,M(z,c[1])];case
  8:a=5;break;case
  9:a=6;break;case
  10:break;case
  11:a=1;break;case
  12:a=2;break;case
  13:a=3;break;case
  14:a=4;break;default:j=1}if(j)a=7;break;case
  6:var
  k=0,A=d[1];if(typeof
  c==="number")k=1;else
  switch(c[0]){case
  6:return[6,M(A,c[1])];case
  8:a=5;break;case
  9:a=6;break;case
  10:break;case
  11:a=1;break;case
  12:a=2;break;case
  13:a=3;break;case
  14:a=4;break;default:k=1}if(k)a=7;break;case
  7:var
  l=0,B=d[1];if(typeof
  c==="number")l=1;else
  switch(c[0]){case
  7:return[7,M(B,c[1])];case
  8:a=5;break;case
  9:a=6;break;case
  10:break;case
  11:a=1;break;case
  12:a=2;break;case
  13:a=3;break;case
  14:a=4;break;default:l=1}if(l)a=7;break;case
  8:var
  m=0,C=d[2],D=d[1];if(typeof
  c==="number")m=1;else
  switch(c[0]){case
  8:var
  E=c[1],F=M(C,c[2]);return[8,M(D,E),F];case
  10:break;case
  11:a=1;break;case
  12:a=2;break;case
  13:a=3;break;case
  14:a=4;break;default:m=1}if(m)throw[0,s,lE];break;case
  9:var
  n=0,G=d[3],H=d[2],I=d[1];if(typeof
  c==="number")n=1;else
  switch(c[0]){case
  8:a=5;break;case
  9:var
  J=c[3],L=c[2],N=c[1],t=T(M(K(H),N)),O=t[4];b(t[2],0);b(O,0);return[9,I,L,M(G,J)];case
  10:break;case
  11:a=1;break;case
  12:a=2;break;case
  13:a=3;break;case
  14:a=4;break;default:n=1}if(n)throw[0,s,lF];break;case
  10:var
  P=d[1];if(typeof
  c!=="number"&&10===c[0])return[10,M(P,c[1])];throw[0,s,lG];case
  11:var
  o=0,Q=d[1];if(typeof
  c==="number")o=1;else
  switch(c[0]){case
  10:break;case
  11:return[11,M(Q,c[1])];default:o=1}if(o)throw[0,s,lH];break;case
  12:var
  p=0,R=d[1];if(typeof
  c==="number")p=1;else
  switch(c[0]){case
  10:break;case
  11:a=1;break;case
  12:return[12,M(R,c[1])];default:p=1}if(p)throw[0,s,lI];break;case
  13:var
  q=0,S=d[1];if(typeof
  c==="number")q=1;else
  switch(c[0]){case
  10:break;case
  11:a=1;break;case
  12:a=2;break;case
  13:return[13,M(S,c[1])];default:q=1}if(q)throw[0,s,lJ];break;default:var
  r=0,U=d[1];if(typeof
  c==="number")r=1;else
  switch(c[0]){case
  10:break;case
  11:a=1;break;case
  12:a=2;break;case
  13:a=3;break;case
  14:return[14,M(U,c[1])];default:r=1}if(r)throw[0,s,lK]}switch(a){case
  0:throw[0,s,ly];case
  1:throw[0,s,lz];case
  2:throw[0,s,lA];case
  3:throw[0,s,lB];case
  4:throw[0,s,lC];case
  5:throw[0,s,lw];case
  6:throw[0,s,lx];default:throw[0,s,lD]}}var
  P=[A,lL,be(0)];function
  dk(b,a){if(typeof
  b==="number")return[0,0,a];else{if(0===b[0])return[0,[0,b[1],b[2]],a];if(typeof
  a!=="number"&&2===a[0])return[0,[1,b[1]],a[1]];throw P}}function
  cq(e,b,d){var
  a=dk(e,d);if(typeof
  b==="number"){if(b){var
  c=a[2];if(typeof
  c!=="number"&&2===c[0])return[0,a[1],1,c[1]];throw P}return[0,a[1],0,a[2]]}return[0,a[1],[0,b[1]],a[2]]}function
  ac(c,d,a){if(typeof
  c==="number")return[0,0,u(d,a)];else
  switch(c[0]){case
  0:if(typeof
  a!=="number"&&0===a[0]){var
  g=ac(c[1],d,a[1]);return[0,[0,g[1]],g[2]]}break;case
  1:if(typeof
  a!=="number"&&1===a[0]){var
  h=ac(c[1],d,a[1]);return[0,[1,h[1]],h[2]]}break;case
  2:if(typeof
  a!=="number"&&2===a[0]){var
  i=ac(c[1],d,a[1]);return[0,[2,i[1]],i[2]]}break;case
  3:if(typeof
  a!=="number"&&3===a[0]){var
  j=ac(c[1],d,a[1]);return[0,[3,j[1]],j[2]]}break;case
  4:if(typeof
  a!=="number"&&4===a[0]){var
  k=ac(c[1],d,a[1]);return[0,[4,k[1]],k[2]]}break;case
  5:if(typeof
  a!=="number"&&5===a[0]){var
  l=ac(c[1],d,a[1]);return[0,[5,l[1]],l[2]]}break;case
  6:if(typeof
  a!=="number"&&6===a[0]){var
  m=ac(c[1],d,a[1]);return[0,[6,m[1]],m[2]]}break;case
  7:if(typeof
  a!=="number"&&7===a[0]){var
  n=ac(c[1],d,a[1]);return[0,[7,n[1]],n[2]]}break;case
  8:if(typeof
  a!=="number"&&8===a[0]){var
  o=a[1],x=a[2],y=c[2];if(bQ([0,c[1]],[0,o]))throw P;var
  p=ac(y,d,x);return[0,[8,o,p[1]],p[2]]}break;case
  9:if(typeof
  a!=="number"&&9===a[0]){var
  e=a[2],f=a[1],z=a[3],A=c[3],B=c[2],C=c[1],D=[0,E(f)];if(bQ([0,E(C)],D))throw P;var
  F=[0,E(e)];if(bQ([0,E(B)],F))throw P;var
  q=T(M(K(f),e)),G=q[4];b(q[2],0);b(G,0);var
  r=ac(E(A),d,z),H=r[2];return[0,[9,f,e,K(r[1])],H]}break;case
  10:if(typeof
  a!=="number"&&10===a[0]){var
  s=ac(c[1],d,a[1]);return[0,[10,s[1]],s[2]]}break;case
  11:if(typeof
  a!=="number"&&11===a[0]){var
  t=ac(c[1],d,a[1]);return[0,[11,t[1]],t[2]]}break;case
  13:if(typeof
  a!=="number"&&13===a[0]){var
  v=ac(c[1],d,a[1]);return[0,[13,v[1]],v[2]]}break;case
  14:if(typeof
  a!=="number"&&14===a[0]){var
  w=ac(c[1],d,a[1]);return[0,[14,w[1]],w[2]]}break}throw P}function
  u(b,a){if(typeof
  b==="number")return[0,0,a];else
  switch(b[0]){case
  0:if(typeof
  a!=="number"&&0===a[0]){var
  s=u(b[1],a[1]);return[0,[0,s[1]],s[2]]}break;case
  1:if(typeof
  a!=="number"&&0===a[0]){var
  t=u(b[1],a[1]);return[0,[1,t[1]],t[2]]}break;case
  2:var
  ae=b[2],v=dk(b[1],a),e=v[2],af=v[1];if(typeof
  e!=="number"&&1===e[0]){var
  w=u(ae,e[1]);return[0,[2,af,w[1]],w[2]]}throw P;case
  3:var
  ag=b[2],x=dk(b[1],a),f=x[2],ah=x[1];if(typeof
  f!=="number"&&1===f[0]){var
  y=u(ag,f[1]);return[0,[3,ah,y[1]],y[2]]}throw P;case
  4:var
  ai=b[4],aj=b[1],g=cq(b[2],b[3],a),h=g[3],al=g[1];if(typeof
  h!=="number"&&2===h[0]){var
  am=g[2],z=u(ai,h[1]);return[0,[4,aj,al,am,z[1]],z[2]]}throw P;case
  5:var
  an=b[4],ao=b[1],i=cq(b[2],b[3],a),j=i[3],ap=i[1];if(typeof
  j!=="number"&&3===j[0]){var
  aq=i[2],A=u(an,j[1]);return[0,[5,ao,ap,aq,A[1]],A[2]]}throw P;case
  6:var
  ar=b[4],as=b[1],k=cq(b[2],b[3],a),l=k[3],at=k[1];if(typeof
  l!=="number"&&4===l[0]){var
  au=k[2],B=u(ar,l[1]);return[0,[6,as,at,au,B[1]],B[2]]}throw P;case
  7:var
  av=b[4],aw=b[1],m=cq(b[2],b[3],a),n=m[3],ax=m[1];if(typeof
  n!=="number"&&5===n[0]){var
  ay=m[2],C=u(av,n[1]);return[0,[7,aw,ax,ay,C[1]],C[2]]}throw P;case
  8:var
  az=b[4],aA=b[1],o=cq(b[2],b[3],a),p=o[3],aB=o[1];if(typeof
  p!=="number"&&6===p[0]){var
  aC=o[2],D=u(az,p[1]);return[0,[8,aA,aB,aC,D[1]],D[2]]}throw P;case
  9:var
  aD=b[2],F=dk(b[1],a),q=F[2],aE=F[1];if(typeof
  q!=="number"&&7===q[0]){var
  G=u(aD,q[1]);return[0,[9,aE,G[1]],G[2]]}throw P;case
  10:var
  H=u(b[1],a);return[0,[10,H[1]],H[2]];case
  11:var
  aF=b[1],I=u(b[2],a);return[0,[11,aF,I[1]],I[2]];case
  12:var
  aG=b[1],J=u(b[2],a);return[0,[12,aG,J[1]],J[2]];case
  13:if(typeof
  a!=="number"&&8===a[0]){var
  K=a[1],aH=a[2],aI=b[3],aJ=b[1];if(bQ([0,b[2]],[0,K]))throw P;var
  L=u(aI,aH);return[0,[13,aJ,K,L[1]],L[2]]}break;case
  14:if(typeof
  a!=="number"&&9===a[0]){var
  M=a[1],aK=a[3],aL=b[3],aM=b[2],aN=b[1],aO=[0,E(M)];if(bQ([0,E(aM)],aO))throw P;var
  N=u(aL,E(aK));return[0,[14,aN,M,N[1]],N[2]]}break;case
  15:if(typeof
  a!=="number"&&10===a[0]){var
  O=u(b[1],a[1]);return[0,[15,O[1]],O[2]]}break;case
  16:if(typeof
  a!=="number"&&11===a[0]){var
  Q=u(b[1],a[1]);return[0,[16,Q[1]],Q[2]]}break;case
  17:var
  aP=b[1],R=u(b[2],a);return[0,[17,aP,R[1]],R[2]];case
  18:var
  S=b[2],r=b[1];if(0===r[0]){var
  W=r[1],aT=W[2],X=u(W[1],a),aU=X[1],Y=u(S,X[2]);return[0,[18,[0,[0,aU,aT]],Y[1]],Y[2]]}var
  Z=r[1],aV=Z[2],_=u(Z[1],a),aW=_[1],$=u(S,_[2]);return[0,[18,[1,[0,aW,aV]],$[1]],$[2]];case
  19:if(typeof
  a!=="number"&&13===a[0]){var
  T=u(b[1],a[1]);return[0,[19,T[1]],T[2]]}break;case
  20:if(typeof
  a!=="number"&&1===a[0]){var
  aQ=b[2],aR=b[1],U=u(b[3],a[1]);return[0,[20,aR,aQ,U[1]],U[2]]}break;case
  21:if(typeof
  a!=="number"&&2===a[0]){var
  aS=b[1],V=u(b[2],a[1]);return[0,[21,aS,V[1]],V[2]]}break;case
  23:var
  d=b[2],c=b[1];if(typeof
  c==="number")switch(c){case
  0:return ak(c,d,a);case
  1:return ak(c,d,a);case
  2:if(typeof
  a!=="number"&&14===a[0]){var
  aa=u(d,a[1]);return[0,[23,2,aa[1]],aa[2]]}throw P;default:return ak(c,d,a)}else
  switch(c[0]){case
  0:return ak(c,d,a);case
  1:return ak(c,d,a);case
  2:return ak(c,d,a);case
  3:return ak(c,d,a);case
  4:return ak(c,d,a);case
  5:return ak(c,d,a);case
  6:return ak(c,d,a);case
  7:return ak(c,d,a);case
  8:return ak([8,c[1],c[2]],d,a);case
  9:var
  aX=c[1],ab=ac(c[2],d,a),ad=ab[2];return[0,[23,[9,aX,ab[1]],ad[1]],ad[2]];case
  10:return ak(c,d,a);default:return ak(c,d,a)}}throw P}function
  ak(d,c,b){var
  a=u(c,b);return[0,[23,d,a[1]],a[2]]}function
  al(l,g,a){var
  b=k(a),h=0<=g?l:0,d=dc(g);if(d<=b)return a;var
  m=2===h?48:32,c=aN(d,m);switch(h){case
  0:aZ(a,0,c,0,b);break;case
  1:aZ(a,0,c,d-b|0,b);break;default:var
  e=0;if(0<b){var
  i=0;if(43!==B(a,0)&&45!==B(a,0)&&32!==B(a,0)){e=1;i=1}if(!i){aL(c,0,B(a,0));aZ(a,1,c,(d-b|0)+1|0,b-1|0)}}else
  e=1;if(e){var
  f=0;if(1<b&&48===B(a,0)){var
  j=0;if(iu!==B(a,1)&&88!==B(a,1)){f=1;j=1}if(!j){aL(c,1,B(a,1));aZ(a,2,c,(d-b|0)+2|0,b-2|0)}}else
  f=1;if(f)aZ(a,0,c,d-b|0,b)}}return O(c)}function
  bW(l,b){var
  c=dc(l),a=k(b),d=B(b,0),e=0;if(58<=d){if(71<=d){if(!(5<d+i$>>>0))e=1}else
  if(65<=d)e=1}else{var
  f=0;if(32!==d)if(43<=d)switch(d-43|0){case
  5:if(a<(c+2|0)&&1<a){var
  j=0;if(iu===B(b,1)||88===B(b,1))j=1;if(j){var
  h=aN(c+2|0,48);aL(h,1,B(b,1));aZ(b,2,h,(c-a|0)+4|0,a-2|0);return O(h)}}e=1;f=1;break;case
  0:case
  2:break;case
  1:case
  3:case
  4:f=1;break;default:e=1;f=1}else
  f=1;if(!f&&a<(c+1|0)){var
  g=aN(c+1|0,48);aL(g,0,d);aZ(b,1,g,(c-a|0)+2|0,a-1|0);return O(g)}}if(e&&a<c){var
  i=aN(c,48);aZ(b,0,i,c-a|0,a);return O(i)}return b}function
  lM(l){var
  j=0,B=k(l);for(;;){if(B<=j)var
  p=l;else{var
  o=ay(l,j)+fW|0,q=0;if(59<o>>>0){if(33<o-61>>>0)q=1}else
  if(2===o)q=1;if(!q){var
  j=j+1|0;continue}var
  e=au(l),a=[0,0],s=ax(e)-1|0,x=0;if(!(s<0)){var
  i=x;for(;;){var
  f=bL(e,i),g=0;if(32<=f){var
  m=f-34|0,r=0;if(58<m>>>0){if(93<=m)r=1}else
  if(56<m-1>>>0){g=1;r=1}if(!r){var
  n=1;g=2}}else
  if(11<=f){if(13===f)g=1}else
  if(8<=f)g=1;switch(g){case
  0:var
  n=4;break;case
  1:var
  n=2;break}a[1]=a[1]+n|0;var
  A=i+1|0;if(s!==i){var
  i=A;continue}break}}if(a[1]===ax(e))var
  u=ez(e);else{var
  b=D(a[1]);a[1]=0;var
  t=ax(e)-1|0,y=0;if(!(t<0)){var
  h=y;for(;;){var
  c=bL(e,h),d=0;if(35<=c)if(92===c)d=2;else
  if(cI<=c)d=1;else
  d=3;else
  if(32<=c)if(34<=c)d=2;else
  d=3;else
  if(14<=c)d=1;else
  switch(c){case
  8:H(b,a[1],92);a[1]++;H(b,a[1],98);break;case
  9:H(b,a[1],92);a[1]++;H(b,a[1],116);break;case
  10:H(b,a[1],92);a[1]++;H(b,a[1],110);break;case
  13:H(b,a[1],92);a[1]++;H(b,a[1],114);break;default:d=1}switch(d){case
  1:H(b,a[1],92);a[1]++;H(b,a[1],48+(c/fR|0)|0);a[1]++;H(b,a[1],48+((c/10|0)%10|0)|0);a[1]++;H(b,a[1],48+(c%10|0)|0);break;case
  2:H(b,a[1],92);a[1]++;H(b,a[1],c);break;case
  3:H(b,a[1],c);break}a[1]++;var
  z=h+1|0;if(t!==h){var
  h=z;continue}break}}var
  u=b}var
  p=O(u)}var
  v=k(p),w=aN(v+2|0,34);bd(p,0,w,1,v);return O(w)}}function
  gW(d,g){var
  h=dc(g),f=mB[1];switch(d[2]){case
  0:var
  b=102;break;case
  1:var
  b=101;break;case
  2:var
  b=69;break;case
  3:var
  b=iv;break;case
  4:var
  b=71;break;case
  5:var
  b=f;break;case
  6:var
  b=104;break;case
  7:var
  b=72;break;default:var
  b=70}var
  c=gS(16);bV(c,37);switch(d[1]){case
  0:break;case
  1:bV(c,43);break;default:bV(c,32)}if(8<=d[2])bV(c,35);bV(c,46);S(c,a(e+h));bV(c,b);return gU(c)}function
  dl(n,a){if(13<=n){var
  g=[0,0],h=k(a)-1|0,o=0;if(!(h<0)){var
  c=o;for(;;){if(!(9<ay(a,c)+iB>>>0))g[1]++;var
  r=c+1|0;if(h!==c){var
  c=r;continue}break}}var
  i=g[1],j=D(k(a)+((i-1|0)/3|0)|0),l=[0,0],d=function(a){aL(j,l[1],a);l[1]++;return 0},e=[0,((i-1|0)%3|0)+1|0],m=k(a)-1|0,p=0;if(!(m<0)){var
  b=p;for(;;){var
  f=ay(a,b);if(9<f+iB>>>0)d(f);else{if(0===e[1]){d(95);e[1]=3}e[1]+=-1;d(f)}var
  q=b+1|0;if(m!==b){var
  b=q;continue}break}}return O(j)}return a}function
  mC(b,c){switch(b){case
  1:var
  a=lO;break;case
  2:var
  a=lP;break;case
  4:var
  a=lR;break;case
  5:var
  a=lS;break;case
  6:var
  a=lT;break;case
  7:var
  a=lU;break;case
  8:var
  a=lV;break;case
  9:var
  a=lW;break;case
  10:var
  a=lX;break;case
  11:var
  a=lY;break;case
  0:case
  13:var
  a=lN;break;case
  3:case
  14:var
  a=lQ;break;default:var
  a=lZ}return dl(b,el(a,c))}function
  mD(b,c){switch(b){case
  1:var
  a=mc;break;case
  2:var
  a=md;break;case
  4:var
  a=mf;break;case
  5:var
  a=mg;break;case
  6:var
  a=mh;break;case
  7:var
  a=mi;break;case
  8:var
  a=mj;break;case
  9:var
  a=mk;break;case
  10:var
  a=ml;break;case
  11:var
  a=mm;break;case
  0:case
  13:var
  a=mb;break;case
  3:case
  14:var
  a=me;break;default:var
  a=mn}return dl(b,el(a,c))}function
  mE(b,c){switch(b){case
  1:var
  a=mp;break;case
  2:var
  a=mq;break;case
  4:var
  a=ms;break;case
  5:var
  a=mt;break;case
  6:var
  a=mu;break;case
  7:var
  a=mv;break;case
  8:var
  a=mw;break;case
  9:var
  a=mx;break;case
  10:var
  a=my;break;case
  11:var
  a=mz;break;case
  0:case
  13:var
  a=mo;break;case
  3:case
  14:var
  a=mr;break;default:var
  a=mA}return dl(b,el(a,c))}function
  mF(b,c){switch(b){case
  1:var
  a=l1;break;case
  2:var
  a=l2;break;case
  4:var
  a=l4;break;case
  5:var
  a=l5;break;case
  6:var
  a=l6;break;case
  7:var
  a=l7;break;case
  8:var
  a=l8;break;case
  9:var
  a=l9;break;case
  10:var
  a=l_;break;case
  11:var
  a=l$;break;case
  0:case
  13:var
  a=l0;break;case
  3:case
  14:var
  a=l3;break;default:var
  a=ma}return dl(b,zW(a,c))}function
  a1(b,e,a){function
  f(d){switch(b[1]){case
  0:var
  c=45;break;case
  1:var
  c=43;break;default:var
  c=32}return zS(a,e,c)}function
  l(c){var
  b=zG(a);return 3===b?a<0.?mH:mI:4<=b?mJ:c}switch(b[2]){case
  5:var
  d=gc(gW(b,e),a),c=0,m=k(d);for(;;){if(c===m)var
  j=0;else{var
  g=B(d,c)-46|0,i=0;if(23<g>>>0){if(55===g)i=1}else
  if(21<g-1>>>0)i=1;if(!i){var
  c=c+1|0;continue}var
  j=1}var
  n=j?d:h(d,mG);return l(n)}case
  6:return f(0);case
  7:return O(eB(kx,au(f(0))));case
  8:return l(f(0));default:return gc(gW(b,e),a)}}function
  cx(e,I,G,F){var
  c=I,a=G,d=F;for(;;)if(typeof
  d==="number")return b(c,a);else
  switch(d[0]){case
  0:var
  J=d[1];return function(b){return q(c,[5,a,b],J)};case
  1:var
  L=d[1];return function(b){var
  e=0;if(40<=b)if(92===b)var
  d=kq;else
  if(cI<=b)e=1;else
  e=2;else
  if(32<=b)if(39<=b)var
  d=kr;else
  e=2;else
  if(14<=b)e=1;else
  switch(b){case
  8:var
  d=ks;break;case
  9:var
  d=kt;break;case
  10:var
  d=ku;break;case
  13:var
  d=kv;break;default:e=1}switch(e){case
  1:var
  f=D(4);H(f,0,92);H(f,1,48+(b/fR|0)|0);H(f,2,48+((b/10|0)%10|0)|0);H(f,3,48+(b%10|0)|0);var
  d=O(f);break;case
  2:var
  g=D(1);H(g,0,b);var
  d=O(g);break}var
  h=k(d),i=aN(h+2|0,39);bd(d,0,i,1,h);return q(c,[4,a,O(i)],L)};case
  2:var
  M=d[2],N=d[1];return eI(c,a,M,N,function(a){return a});case
  3:return eI(c,a,d[2],d[1],lM);case
  4:return dm(c,a,d[4],d[2],d[3],mC,d[1]);case
  5:return dm(c,a,d[4],d[2],d[3],mD,d[1]);case
  6:return dm(c,a,d[4],d[2],d[3],mE,d[1]);case
  7:return dm(c,a,d[4],d[2],d[3],mF,d[1]);case
  8:var
  i=d[4],j=d[3],l=d[2],h=d[1];if(typeof
  l==="number"){if(typeof
  j==="number")return j?function(d,b){return q(c,[4,a,a1(h,d,b)],i)}:function(b){return q(c,[4,a,a1(h,eG(h),b)],i)};var
  ae=j[1];return function(b){return q(c,[4,a,a1(h,ae,b)],i)}}else{if(0===l[0]){var
  p=l[2],r=l[1];if(typeof
  j==="number")return j?function(d,b){return q(c,[4,a,al(r,p,a1(h,d,b))],i)}:function(b){return q(c,[4,a,al(r,p,a1(h,eG(h),b))],i)};var
  af=j[1];return function(b){return q(c,[4,a,al(r,p,a1(h,af,b))],i)}}var
  v=l[1];if(typeof
  j==="number")return j?function(e,d,b){return q(c,[4,a,al(v,e,a1(h,d,b))],i)}:function(d,b){return q(c,[4,a,al(v,d,a1(h,eG(h),b))],i)};var
  ag=j[1];return function(d,b){return q(c,[4,a,al(v,d,a1(h,ag,b))],i)}}case
  9:return eI(c,a,d[2],d[1],kg);case
  10:var
  a=[7,a],d=d[1];continue;case
  11:var
  a=[2,a,d[1]],d=d[2];continue;case
  12:var
  a=[3,a,d[1]],d=d[2];continue;case
  13:var
  Q=d[3],R=d[2],w=gS(16);eH(w,R);var
  C=gU(w);return function(b){return q(c,[4,a,C],Q)};case
  14:var
  S=d[3],T=d[2];return function(d){var
  e=d[1],b=u(e,E(K(T)));if(typeof
  b[2]==="number")return q(c,a,y(b[1],S));throw P};case
  15:var
  U=d[1];return function(d,b){return q(c,[6,a,function(a){return f(d,a,b)}],U)};case
  16:var
  V=d[1];return function(b){return q(c,[6,a,b],V)};case
  17:var
  a=[0,a,d[1]],d=d[2];continue;case
  18:var
  o=d[1];if(0===o[0]){var
  W=d[2],X=o[1][1],Y=0,c=function(b,c,d){return function(a){return q(c,[1,b,[0,a]],d)}}(a,c,W),a=Y,d=X;continue}var
  Z=d[2],_=o[1][1],$=0,c=function(b,c,d){return function(a){return q(c,[1,b,[1,a]],d)}}(a,c,Z),a=$,d=_;continue;case
  19:throw[0,s,mL];case
  20:var
  aa=d[3],ab=[8,a,mM];return function(a){return q(c,ab,aa)};case
  21:var
  ac=d[2];return function(b){return q(c,[4,a,el(mN,b)],ac)};case
  22:var
  ad=d[1];return function(b){return q(c,[5,a,b],ad)};case
  23:var
  g=d[2],m=d[1];if(typeof
  m==="number")switch(m){case
  0:return e<50?t(e+1|0,c,a,g):n(t,[0,c,a,g]);case
  1:return e<50?t(e+1|0,c,a,g):n(t,[0,c,a,g]);case
  2:throw[0,s,mO];default:return e<50?t(e+1|0,c,a,g):n(t,[0,c,a,g])}else
  switch(m[0]){case
  0:return e<50?t(e+1|0,c,a,g):n(t,[0,c,a,g]);case
  1:return e<50?t(e+1|0,c,a,g):n(t,[0,c,a,g]);case
  2:return e<50?t(e+1|0,c,a,g):n(t,[0,c,a,g]);case
  3:return e<50?t(e+1|0,c,a,g):n(t,[0,c,a,g]);case
  4:return e<50?t(e+1|0,c,a,g):n(t,[0,c,a,g]);case
  5:return e<50?t(e+1|0,c,a,g):n(t,[0,c,a,g]);case
  6:return e<50?t(e+1|0,c,a,g):n(t,[0,c,a,g]);case
  7:return e<50?t(e+1|0,c,a,g):n(t,[0,c,a,g]);case
  8:return e<50?t(e+1|0,c,a,g):n(t,[0,c,a,g]);case
  9:var
  B=m[2];return e<50?fi(e+1|0,c,a,B,g):n(fi,[0,c,a,B,g]);case
  10:return e<50?t(e+1|0,c,a,g):n(t,[0,c,a,g]);default:return e<50?t(e+1|0,c,a,g):n(t,[0,c,a,g])}default:var
  x=d[3],z=d[1],A=b(d[2],0);return e<50?fh(e+1|0,c,a,x,z,A):n(fh,[0,c,a,x,z,A])}}function
  fi(e,d,c,a,b){if(typeof
  a==="number")return e<50?t(e+1|0,d,c,b):n(t,[0,d,c,b]);else
  switch(a[0]){case
  0:var
  f=a[1];return function(a){return ar(d,c,f,b)};case
  1:var
  g=a[1];return function(a){return ar(d,c,g,b)};case
  2:var
  h=a[1];return function(a){return ar(d,c,h,b)};case
  3:var
  i=a[1];return function(a){return ar(d,c,i,b)};case
  4:var
  j=a[1];return function(a){return ar(d,c,j,b)};case
  5:var
  k=a[1];return function(a){return ar(d,c,k,b)};case
  6:var
  l=a[1];return function(a){return ar(d,c,l,b)};case
  7:var
  m=a[1];return function(a){return ar(d,c,m,b)};case
  8:var
  o=a[2];return function(a){return ar(d,c,o,b)};case
  9:var
  p=a[3],q=a[2],r=M(K(a[1]),q);return function(a){return ar(d,c,Z(r,p),b)};case
  10:var
  u=a[1];return function(e,a){return ar(d,c,u,b)};case
  11:var
  v=a[1];return function(a){return ar(d,c,v,b)};case
  12:var
  w=a[1];return function(a){return ar(d,c,w,b)};case
  13:throw[0,s,mP];default:throw[0,s,mQ]}}function
  t(d,b,e,a){var
  c=[8,e,mR];return d<50?cx(d+1|0,b,c,a):n(cx,[0,b,c,a])}function
  fh(h,c,f,a,e,d){if(e){var
  i=e[1];return function(e){return mK(c,f,a,i,b(d,e))}}var
  g=[4,f,d];return h<50?cx(h+1|0,c,g,a):n(cx,[0,c,g,a])}function
  q(a,b,c){return bR(cx(0,a,b,c))}function
  ar(a,b,c,d){return bR(fi(0,a,b,c,d))}function
  mK(a,b,c,d,e){return bR(fh(0,a,b,c,d,e))}function
  eI(f,e,d,a,c){if(typeof
  a==="number")return function(a){return q(f,[4,e,b(c,a)],d)};else{if(0===a[0]){var
  g=a[2],h=a[1];return function(a){return q(f,[4,e,al(h,g,b(c,a))],d)}}var
  i=a[1];return function(g,a){return q(f,[4,e,al(i,g,b(c,a))],d)}}}function
  dm(g,e,d,h,c,b,a){if(typeof
  h==="number"){if(typeof
  c==="number")return c?function(h,c){return q(g,[4,e,bW(h,f(b,a,c))],d)}:function(c){return q(g,[4,e,f(b,a,c)],d)};var
  l=c[1];return function(c){return q(g,[4,e,bW(l,f(b,a,c))],d)}}else{if(0===h[0]){var
  i=h[2],j=h[1];if(typeof
  c==="number")return c?function(h,c){return q(g,[4,e,al(j,i,bW(h,f(b,a,c)))],d)}:function(c){return q(g,[4,e,al(j,i,f(b,a,c))],d)};var
  m=c[1];return function(c){return q(g,[4,e,al(j,i,bW(m,f(b,a,c)))],d)}}var
  k=h[1];if(typeof
  c==="number")return c?function(i,h,c){return q(g,[4,e,al(k,i,bW(h,f(b,a,c)))],d)}:function(h,c){return q(g,[4,e,al(k,h,f(b,a,c))],d)};var
  n=c[1];return function(h,c){return q(g,[4,e,al(k,h,bW(n,f(b,a,c)))],d)}}}function
  a2(c,f){var
  a=f;for(;;)if(typeof
  a==="number")return 0;else
  switch(a[0]){case
  0:var
  g=a[1],h=gV(a[2]);a2(c,g);return bS(c,h);case
  1:var
  d=a[2],e=a[1];if(0===d[0]){var
  i=d[1];a2(c,e);bS(c,mS);var
  a=i;continue}var
  j=d[1];a2(c,e);bS(c,mT);var
  a=j;continue;case
  6:var
  m=a[2];a2(c,a[1]);return b(m,c);case
  7:a2(c,a[1]);return aX(c);case
  8:var
  n=a[2];a2(c,a[1]);return I(n);case
  2:case
  4:var
  k=a[2];a2(c,a[1]);return bS(c,k);default:var
  l=a[2];a2(c,a[1]);return gl(c,l)}}function
  bj(c,g){var
  a=g;for(;;)if(typeof
  a==="number")return 0;else
  switch(a[0]){case
  0:var
  h=a[1],i=gV(a[2]);bj(c,h);return bU(c,i);case
  1:var
  e=a[2],f=a[1];if(0===e[0]){var
  j=e[1];bj(c,f);bU(c,mU);var
  a=j;continue}var
  k=e[1];bj(c,f);bU(c,mV);var
  a=k;continue;case
  6:var
  n=a[2];bj(c,a[1]);return bU(c,b(n,0));case
  7:var
  a=a[1];continue;case
  8:var
  o=a[2];bj(c,a[1]);return I(o);case
  2:case
  4:var
  l=a[2];bj(c,a[1]);return bU(c,l);default:var
  m=a[2];bj(c,a[1]);var
  d=c[2];if(c[3]<=d)gR(c,1);H(c[1],d,m);c[2]=d+1|0;return 0}}function
  gX(d,c){var
  a=c[1],b=0;return q(function(a){a2(d,a);return 0},b,a)}function
  eJ(a){return gX(aY,a)}function
  aO(b){var
  a=b[1];return q(function(b){var
  a=gP(64);bj(a,b);return gQ(a)},0,a)}var
  dn=[0,0];function
  eL(j,i){var
  a=j[1+i];if(1-(typeof
  a==="number"?1:0)){if(bg(a)===eb)return b(aO(mW),a);if(bg(a)===253){var
  d=gc(kl,a),c=0,g=k(d);for(;;){if(g<=c)return h(d,kk);var
  e=B(d,c),f=0;if(48<=e){if(!(58<=e))f=1}else
  if(45===e)f=1;if(f){var
  c=c+1|0;continue}return d}}return mX}return b(aO(mY),a)}function
  gY(b,a){if(b.length-1<=a)return mZ;var
  c=gY(b,a+1|0),d=eL(b,a);return f(aO(m0),d,c)}function
  eM(a){function
  q(f){var
  c=f;for(;;){if(c){var
  g=c[2],h=c[1];try{var
  e=0,d=b(h,a);e=1}catch(a){}if(e&&d)return[0,d[1]];var
  c=g;continue}return 0}}var
  j=q(dn[1]);if(j)return j[1];if(a===eu)return m5;if(a===gt)return m6;if(a[1]===gs){var
  d=a[2],k=d[3],r=d[2],t=d[1];return cz(aO(eK),t,r,k,k+5|0,m7)}if(a[1]===s){var
  e=a[2],l=e[3],u=e[2],v=e[1];return cz(aO(eK),v,u,l,l+6|0,m8)}if(a[1]===gv){var
  g=a[2],m=g[3],w=g[2],x=g[1];return cz(aO(eK),x,w,m,m+6|0,m9)}if(0===bg(a)){var
  i=a.length-1,y=a[1][1];if(2<i>>>0)var
  n=gY(a,2),o=eL(a,1),c=f(aO(m1),o,n);else
  switch(i){case
  0:var
  c=m2;break;case
  1:var
  c=m3;break;default:var
  p=eL(a,1),c=b(aO(m4),p)}return h(y,c)}return a[1]}function
  eN(t,s){var
  e=zH(s),g=e.length-1-1|0,p=0;if(!(g<0)){var
  c=p;for(;;){var
  a=w(e,c)[1+c],f=function(a){return function(b){return b?0===a?m_:m$:0===a?na:nb}}(c);if(0===a[0])var
  h=a[5],i=a[4],j=a[3],k=a[6]?nc:ne,l=a[2],m=a[7],n=f(a[1]),d=[0,zw(aO(nd),n,m,l,k,j,i,h)];else
  if(a[1])var
  d=0;else
  var
  o=f(0),d=[0,b(aO(nf),o)];if(d){var
  q=d[1];b(gX(t,ng),q)}var
  r=c+1|0;if(g!==c){var
  c=r;continue}break}}return 0}function
  gZ(c){for(;;){var
  a=dn[1],d=[0,c,a],e=dn[1]===a?(dn[1]=d,1):0,b=1-e;if(b)continue;return b}}var
  ni=nh.slice();function
  nj(e,d){var
  f=eM(e);b(eJ(nk),f);eN(aY,d);var
  a=Ae(0);if(a<0){var
  c=dc(a);gw(w(ni,c)[1+c])}return aX(aY)}var
  nl=[0];gp(a(ix),function(e,i){try{try{var
  c=i?nl:jX(0);try{ew(0)}catch(a){}try{var
  a=nj(e,c),d=a}catch(a){a=C(a);var
  g=eM(e);b(eJ(nn),g);eN(aY,c);var
  h=eM(a);b(eJ(no),h);eN(aY,jX(0));var
  d=aX(aY)}var
  f=d}catch(a){a=C(a);if(a!==eu)throw a;var
  f=gw(nm)}return f}catch(a){return 0}});try{var
  zq=ka(zp),g1=zq}catch(a){a=C(a);if(a!==p)throw a;try{var
  zo=ka(zn),g0=zo}catch(a){a=C(a);if(a!==p)throw a;var
  g0=nq}var
  g1=g0}var
  nr=gH(g1,82),dp=[ao,function(A){var
  p=Av(0),c=[0,aM(55,0),0],l=0===p.length-1?[0,0]:p,m=l.length-1,b=0;for(;;){w(c[1],b)[1+b]=b;var
  z=b+1|0;if(54!==b){var
  b=z;continue}var
  i=[0,np],n=54+de(55,m)|0,u=0;if(!(n<0)){var
  d=u;for(;;){var
  f=d%55|0,o=Ai(d,m),v=w(l,o)[1+o],j=h(i[1],a(e+v));i[1]=Ac(j,0,k(j));var
  g=i[1],q=B(g,3)<<24,r=B(g,2)<<16,s=B(g,1)<<8,t=((B(g,0)+s|0)+r|0)+q|0,x=(w(c[1],f)[1+f]^t)&fA;w(c[1],f)[1+f]=x;var
  y=d+1|0;if(n!==d){var
  d=y;continue}break}}c[2]=0;return c}}];function
  eO(a,b){return 4<=a.length-1?zM(10,fR,a[3],b)&(a[2].length-1-1|0):I(ns)}function
  g2(f,b){var
  g=eO(f,b),c=w(f[2],g)[1+g];if(c){var
  d=c[3],j=c[2];if(0===ce(b,c[1]))return j;if(d){var
  e=d[3],k=d[2];if(0===ce(b,d[1]))return k;if(e){var
  l=e[2],m=e[3];if(0===ce(b,e[1]))return l;var
  a=m;for(;;){if(a){var
  h=a[2],i=a[3];if(0===ce(b,a[1]))return h;var
  a=i;continue}throw p}}throw p}throw p}throw p}function
  nt(b,a){var
  c=bg(a)===A?a:a[1];return gp(b,c)}var
  nu=2;function
  nv(c){var
  a=[0,0],d=k(c)-1|0,e=0;if(!(d<0)){var
  b=e;for(;;){var
  g=B(c,b);a[1]=(223*a[1]|0)+g|0;var
  h=b+1|0;if(d!==b){var
  b=h;continue}break}}a[1]=a[1]&2147483647;var
  f=fA<a[1]?a[1]+2147483648|0:a[1];return f}var
  eP=dj([0,c9]),dq=dj([0,c9]),dr=dj([0,j0]),g3=j6(0,0),nw=[0,0];function
  g4(a){return 2<a?g4((a+1|0)/2|0)*2|0:a}function
  g5(d){nw[1]++;var
  b=d.length-1,c=aM((b*2|0)+2|0,g3);w(c,0)[1]=b;var
  g=((g4(b)*32|0)/8|0)-1|0;w(c,1)[2]=g;var
  e=b-1|0,h=0;if(!(e<0)){var
  a=h;for(;;){var
  f=(a*2|0)+3|0,i=w(d,a)[1+a];w(c,f)[1+f]=i;var
  j=a+1|0;if(e!==a){var
  a=j;continue}break}}return[0,nu,c,dq[1],dr[1],0,0,eP[1],0]}function
  eQ(a,b){var
  c=a[2].length-1,d=c<b?1:0;if(d){var
  e=aM(b,g3);gJ(a[2],0,e,0,c);a[2]=e;var
  f=0}else
  var
  f=d;return f}var
  g6=[0,0],nx=[0,0];function
  ny(a){var
  b=a[2].length-1;eQ(a,b+1|0);return b}function
  nz(a,d){try{var
  b=f(dq[28],d,a[3]);return b}catch(b){b=C(b);if(b===p){var
  c=ny(a);a[3]=aJ(dq[4],d,c,a[3]);a[4]=aJ(dr[4],c,1,a[4]);return c}throw b}}function
  nA(a){var
  b=a[1];a[1]=b+1|0;return b}function
  nB(a,c){try{var
  b=f(eP[28],c,a[7]);return b}catch(b){b=C(b);if(b===p){var
  d=nA(a);if(g(c,nC))a[7]=aJ(eP[4],c,d,a[7]);return d}throw b}}function
  nD(b,a){var
  c=k(b);return c<a?nE:aq(b,a,c-a|0)}var
  nF=34;function
  nI(a){var
  d=0;for(;;){var
  c=a0(a3,d,a);if(7<c>>>0){b(a[1],a);var
  d=c;continue}switch(c){case
  0:return 0;case
  1:return 3;case
  2:return 6;case
  3:var
  e=J(a);return[0,aq(e,1,k(e)-1|0)];case
  4:var
  f=J(a);return[1,aq(f,2,k(f)-2|0)];case
  5:return nM;case
  6:return 13;default:return[2,J(a)]}}}function
  g7(a){var
  d=33;for(;;){var
  c=a0(a3,d,a);if(9<c>>>0){b(a[1],a);var
  d=c;continue}switch(c){case
  0:return 9;case
  1:return 10;case
  2:return[3,k(J(a))];case
  3:return[4,J(a)];case
  4:return 11;case
  5:return 12;case
  6:return 12;case
  7:return[5,J(a)];case
  8:return 13;default:return 12}}}function
  nL(a){var
  c=55;for(;;){var
  d=a0(a3,c,a);if(0===d)return eD(a,a[5],a[6]-1|0);b(a[1],a);var
  c=d;continue}}function
  nK(a){var
  c=53;for(;;){var
  d=a0(a3,c,a);if(0===d)return eD(a,a[5],a[6]-1|0);b(a[1],a);var
  c=d;continue}}var
  aP=[A,nO,be(0)],g8=[A,nP,be(0)];function
  nJ(a,e){var
  d=e;for(;;){var
  c=a0(a3,d,a);if(7<c>>>0){b(a[1],a);var
  d=c;continue}switch(c){case
  0:return 9;case
  1:return 10;case
  2:return[3,k(J(a))];case
  3:try{var
  f=[5,nK(a)];return f}catch(a){return 12}case
  4:try{var
  g=[5,nL(a)];return g}catch(a){return 12}case
  5:return[5,J(a)];case
  6:return 13;default:return 12}}}function
  bk(b,a){return[0,fP,[0,b,a]]}var
  og=[0,of,[0,oe,[0,od,[0,oc,[0,ob,[0,oa,[0,n$,[0,n_,[0,n9,[0,n8,[0,n7,[0,n6,[0,n5,[0,n4,[0,n3,[0,n2,[0,n1,[0,n0,[0,nZ,[0,nY,[0,nX,[0,nW,[0,nV,[0,nU,[0,nT,[0,[0,nS,[0,j,bk(i,nR)]],nQ]]]]]]]]]]]]]]]]]]]]]]]]]],on=[0,om,[0,ol,[0,ok,[0,oj,[0,[0,oi,[0,j,bk(i,oh)]],og]]]]],os=[0,or,[0,oq,[0,[0,op,[0,o,bk(i,oo)]],on]]],ow=[0,[0,ov,[0,o,[0,R,[0,ou,bk(i,ot)]]]],os],oY=[0,oX,[0,oW,[0,oV,[0,oU,[0,oT,[0,oS,[0,oR,[0,oQ,[0,oP,[0,oO,[0,oN,[0,oM,[0,oL,[0,oK,[0,oJ,[0,oI,[0,oH,[0,[0,oG,[0,o,[0,R,[0,g9,oF]]]],[0,[0,oE,[0,o,[0,R,[0,g9,oD]]]],[0,oC,[0,oB,[0,oA,[0,oz,[0,[0,oy,[0,o,bk(i,ox)]],ow]]]]]]]]]]]]]]]]]]]]]]]],o2=[0,o1,[0,[0,o0,[0,c,bk(i,oZ)]],oY]],o6=[0,o5,[0,[0,o4,[0,c,bk(i,o3)]],o2]],eR=[0,pv,[0,pu,[0,pt,[0,ps,[0,pr,[0,pq,[0,pp,[0,po,[0,pn,[0,pm,[0,pl,[0,pk,[0,pj,[0,pi,[0,ph,[0,pg,[0,pf,[0,pe,[0,pd,[0,pc,[0,pb,[0,pa,[0,o$,[0,o_,[0,o9,[0,[0,o8,[0,c,bk(i,o7)]],o6]]]]]]]]]]]]]]]]]]]]]]]]]];function
  pw(a){function
  b(a){if(typeof
  a==="number"){if(c===a)return i}else
  if(R===a[1]){var
  d=a[2],e=d[1];return[0,R,[0,e,b(d[2])]]}return a}return aj(function(d){var
  e=d[2],f=e[2],a=e[1],g=d[1];return c===a?[0,g,[0,a,b(f)]]:[0,g,[0,a,f]]},a)}function
  px(a,f){return aj(function(b){var
  c=b[2],d=c[2],e=c[1],a=b[1];if(o===e&&gB(a,f))return[0,a,[0,a8,d]];return[0,a,[0,e,d]]},a)}px(pw(eR),py);function
  pz(c){var
  e=pA;a:for(;;){var
  f=17;for(;;){var
  d=a0(a3,f,c);if(3<d>>>0){b(c[1],c);var
  f=d;continue}switch(d){case
  0:var
  a=1;break;case
  1:var
  a=2;break;case
  2:var
  a=13;break;default:var
  a=2}if(2===a){var
  e=h(e,J(c));continue a}if(13<=a)throw aP;return e}}}function
  g_(a){var
  e=23;for(;;){var
  d=a0(a3,e,a);if(2<d>>>0){b(a[1],a);var
  e=d;continue}switch(d){case
  0:var
  c=4;break;case
  1:var
  c=13;break;default:var
  c=5}if(5===c){var
  f=J(a);return h(f,g_(a))}if(13<=c)throw aP;return pB}}function
  g$(c){var
  e=27;for(;;){var
  d=a0(a3,e,c);if(4<d>>>0){b(c[1],c);var
  e=d;continue}switch(d){case
  0:var
  a=7;break;case
  1:var
  a=7;break;case
  2:var
  a=13;break;case
  3:var
  a=8;break;default:var
  a=8}if(8===a){var
  f=J(c);return h(f,g$(c))}if(13<=a)throw aP;return pC}}function
  pD(k){var
  c=16,l=bh(k);for(;;){if(!(l<=c)&&!(ex<(c*2|0))){var
  c=c*2|0;continue}if(nr){var
  i=bg(dp),b=bo===i?dp[1]:ao===i?ck(dp):dp;b[2]=(b[2]+1|0)%55|0;var
  d=b[2],e=w(b[1],d)[1+d],f=(b[2]+24|0)%55|0,g=(w(b[1],f)[1+f]+(e^(e>>>25|0)&31)|0)&fA,h=b[2];w(b[1],h)[1+h]=g;var
  j=g}else
  var
  j=0;var
  a=[0,0,aM(c,0),j,c];bT(function(v){var
  x=v[1],H=v[2],f=eO(a,x),G=[0,x,H,w(a[2],f)[1+f]];w(a[2],f)[1+f]=G;a[1]=a[1]+1|0;var
  u=a[2].length-1<<1<a[1]?1:0;if(u){var
  i=a[2],s=(i.length-1)*2|0,t=s<ex?1:0;if(t){var
  j=aM(s,0),y=a.length-1<4?1:0,z=y||(a[4]<0?1:0),k=1-z;a[2]=j;var
  l=j.length-1,g=aM(l,0),o=i.length-1-1|0,C=0;if(!(o<0)){var
  e=C;a:for(;;){var
  b=w(i,e)[1+e];for(;;){if(b){var
  m=b[1],A=b[2],B=b[3],h=k?b:[0,m,A,0],c=eO(a,m),n=w(g,c)[1+c];if(n)n[3]=h;else
  w(j,c)[1+c]=h;w(g,c)[1+c]=h;var
  b=B;continue}var
  F=e+1|0;if(o!==e){var
  e=F;continue a}break}break}}if(k){var
  p=l-1|0,D=0;if(!(p<0)){var
  d=D;for(;;){var
  r=w(g,d)[1+d];if(r)r[3]=0;var
  E=d+1|0;if(p!==d){var
  d=E;continue}break}}var
  q=0}else
  var
  q=k;return q}return t}return u},k);return a}}var
  eS=gN([0,c9]);function
  pE(M,L,K,I,H,j){var
  X=M?M[1]:eR,Y=L?L[1]:0,Z=K?K[1]:0,_=I?I[1]:0,$=H?H[1]:0,e=[0,pF],l=[0,0],a=[0,0],q=[0,eS[1]],r=gO(0),N=pD(X),m=$?function(a){return a}:gI;function
  u(a){if(d(a,pG))return pH;function
  b(a){var
  b=a[2];if(typeof
  b!=="number"&&R===b[1])return[0,a[1],b[2][2]];return a}try{var
  c=b(g2(N,a));return c}catch(a){a=C(a);if(a===p)return pI;throw a}}function
  ac(k,j,e){var
  b=u(e)[1];function
  d(n){var
  a=n;for(;;){if(typeof
  a==="number"){if(fq<=a){if(ca<=a){if(i<=a){var
  g=c===b?1:0;if(g)var
  h=g;else
  var
  p=o===b?1:0,h=p||(a8===b?1:0);return h}return 0}return v<=a?0:1}if(c<=a)return c===b?1:0;var
  q=o===b?1:0,r=q||(a8===b?1:0);return r}var
  f=a[1];if(fP<=f){if(R<=f)throw[0,s,pK];var
  j=a[2],t=j[2],k=d(j[1]);if(k)return k;var
  a=t;continue}if(-260921543<=f){var
  l=a[2],u=l[2],m=d(l[1]),w=m?1-d(u):m;return w}return gB(e,a[2])}}var
  a=bA===b?1:0;if(a)var
  g=a;else{var
  h=1-f(eS[3],e,j);if(h)return d(u(k)[2]);var
  g=h}return g}function
  z(d){var
  c=gO(0),f=e[1],g=l[1],h=a[1],i=q[1];try{for(;;){if(ac(e[1],q[1],d)){var
  j=0;return j}if(a8===u(e[1])[1])throw eE;var
  b=cp(r);eF(b,c);var
  k=b[4],m=b[3],n=b[2],o=b[1],p=G(a[1]),s=[0,[0,e[1],l[1],p]];e[1]=o;l[1]=n;q[1]=k;a[1]=[0,s,m];continue}}catch(b){b=C(b);if(b===eE)for(;;){if(0<c[2]){eF(cp(c),r);continue}e[1]=f;l[1]=g;a[1]=h;q[1]=i;return 0}throw b}}function
  A(a){function
  b(b){for(;;){var
  a=b?nJ(j,44):g7(j);if(typeof
  a!=="number"&&3===a[0])continue;return a}}function
  e(j){var
  c=j;for(;;){if(typeof
  c==="number")switch(c){case
  9:return pL;case
  10:return pM;case
  13:throw aP}else
  if(4===c[0]){var
  a=c[1],f=b(0);if(typeof
  f==="number")switch(f){case
  9:var
  o=m(a);return[0,[0,[0,m(a),o],0],0];case
  10:var
  p=m(a);return[0,[0,[0,m(a),p],0],1];case
  11:var
  d=b(1);if(typeof
  d==="number")switch(d){case
  9:return pN;case
  10:return pO;case
  13:throw aP}else
  switch(d[0]){case
  4:var
  q=d[1],h=e(b(0)),r=h[2],s=h[1];return[0,[0,[0,m(a),q],s],r];case
  5:var
  t=d[1],i=e(b(0)),u=i[2],v=i[1];return[0,[0,[0,m(a),t],v],u]}var
  c=b(0);continue;case
  13:throw aP}var
  g=e(f),k=g[2],l=g[1],n=m(a);return[0,[0,[0,m(a),n],l],k]}var
  c=b(0);continue}}return e(b(0))}function
  B(e){a:for(;;){var
  f=11;for(;;){var
  c=a0(a3,f,j);if(3<c>>>0){b(j[1],j);var
  f=c;continue}switch(c){case
  0:var
  g=J(j),a=[1,aq(g,2,k(g)-2|0)];break;case
  1:var
  a=nN;break;case
  2:var
  a=13;break;default:var
  a=[2,J(j)]}if(typeof
  a==="number"){if(13===a)throw aP}else
  switch(a[0]){case
  1:var
  i=a[1];return d(m(i),e)?pP:h(pQ,h(i,B(e)));case
  2:var
  l=a[1];return h(l,B(e))}continue a}}}function
  O(b){for(;;){var
  a=g7(j);if(typeof
  a==="number")if(11<=a){if(13<=a)throw aP}else
  if(9<=a)return 0;continue}}try{a:for(;;){var
  t=nI(j);if(typeof
  t==="number")switch(t){case
  0:var
  ad=pz(j);if(_)a[1]=[0,[0,[0,pS,[0,[0,pR,ad],0],0]],a[1]];continue;case
  3:var
  ae=g_(j);if(Y)a[1]=[0,[0,[0,pU,[0,[0,pT,ae],0],0]],a[1]];continue;case
  6:var
  af=g$(j);if(Z)a[1]=[0,[0,[0,pW,[0,[0,pV,af],0],0]],a[1]];continue;case
  13:throw aP}else
  switch(t[0]){case
  0:var
  n=m(t[1]),P=u(n)[2];if(v===P){var
  ag=A(0)[1];z(n);a[1]=[0,[0,[0,n,ag,0]],a[1]];continue}if(ca===P){var
  Q=A(0),ah=Q[2],ai=Q[1];z(n);if(ah)var
  S=pX;else{var
  aj=B(n);O(0);var
  S=aj}a[1]=[0,[0,[0,n,ai,[0,[1,S],0]]],a[1]];continue}var
  T=A(0),U=T[1],ak=T[2];z(n);if(ak)a[1]=[0,[0,[0,n,U,0]],a[1]];else{if(d(n,pJ))var
  D=0;else{var
  aa=function(b){var
  a=b[2];if(typeof
  a!=="number"&&R===a[1])return a[2][1];return 0};try{var
  ab=aa(g2(N,n)),D=ab}catch(a){a=C(a);if(a!==p)throw a;var
  D=0,aC=a}}eF([0,e[1],l[1],a[1],q[1]],r);e[1]=n;l[1]=U;a[1]=0;bT(function(a){q[1]=f(eS[4],a,q[1]);return 0},D)}continue;case
  1:var
  E=m(t[1]);O(0);var
  V=d(E,e[1]);if(V)var
  F=V;else
  try{k4(function(c){return function(b){var
  a=b[1];if(d(c,a))throw g8;if(a8===u(a)[1])throw p;return 0}}(E),r);var
  aw=0,F=aw}catch(a){a=C(a);if(a===g8)var
  W=1;else{if(a!==p)throw a;var
  W=0}var
  F=W,aD=a}if(F)for(;;){if(g(e[1],E)){var
  w=cp(r),al=w[4],am=w[3],an=w[2],ao=w[1],ap=G(a[1]);a[1]=[0,[0,[0,e[1],l[1],ap]],am];e[1]=ao;l[1]=an;q[1]=al;continue}var
  x=cp(r),ar=x[4],as=x[3],at=x[2],au=x[1],av=G(a[1]);a[1]=[0,[0,[0,e[1],l[1],av]],as];e[1]=au;l[1]=at;q[1]=ar;continue a}continue;case
  2:a[1]=[0,[1,t[1]],a[1]];continue}continue}}catch(b){b=C(b);if(b===aP)for(;;){if(0<r[2]){var
  y=cp(r),ax=y[4],ay=y[3],az=y[2],aA=y[1],aB=G(a[1]);a[1]=[0,[0,[0,e[1],l[1],aB]],ay];e[1]=aA;l[1]=az;q[1]=ax;continue}return G(a[1])}throw b}}function
  pY(l,k,a,c){function
  i(f){if(0===f[0]){var
  h=f[1],e=h[2],c=h[1],m=h[3];if(g(c,p0)){if(g(c,p1)){if(g(c,p2)){try{var
  o=v===co(c,l)[2]?1:0,j=o}catch(a){a=C(a);if(a!==p)throw a;var
  j=0}b(a,p3);b(a,c);bT(function(e){var
  f=e[2],g=e[1];b(a,p4);b(a,g);b(a,p5);function
  c(b,a){try{var
  e=eC(a,nF),f=aq(a,0,e),g=nD(a,e+1|0),h=d(f,nH)?c(b,g):c([0,f,b],g);return h}catch(c){c=C(c);if(c===p)return d(a,nG)?b:[0,a,b];throw c}}b(a,az(pZ,G(c(0,f))));return b(a,p6)},e);if(j){var
  n=k?p7:p8;return b(a,n)}b(a,p9);bT(i,m);b(a,p_);b(a,c);return b(a,p$)}b(a,qa);b(a,co(qb,e));return b(a,qc)}b(a,qd);b(a,co(qe,e));return b(a,qf)}b(a,qg);b(a,co(qh,e));return b(a,qi)}return b(a,f[1])}try{var
  e=bT(i,c);return e}catch(a){a=C(a);if(a===p)return ch(qj);throw a}}function
  qk(b,a){return aq(b,0,a)}function
  ql(b,a){return aq(b,a,k(b)-a|0)}var
  a4=aN(32,b0);function
  ha(a){return aN(32,0)}function
  eT(b,a){return aL(b,a>>>3|0,cl(bK(b,a>>>3|0)|1<<(a&7)))}function
  ds(b){var
  a=ha(0);eT(a,b);return a}function
  dt(c){var
  b=D(32),a=0;for(;;){aL(b,a,cl(bK(c,a)^b0));var
  d=a+1|0;if(31!==a){var
  a=d;continue}return b}}function
  eU(d,c){var
  b=D(32),a=0;for(;;){var
  e=bK(c,a);aL(b,a,cl(bK(d,a)|e));var
  f=a+1|0;if(31!==a){var
  a=f;continue}return b}}function
  qm(c,b){try{var
  a=0;for(;;){var
  e=bK(b,a);if(0!==(bK(c,a)&e))throw gr;var
  f=a+1|0;if(31!==a){var
  a=f;continue}var
  d=1;return d}}catch(a){a=C(a);if(a===gr)return 0;throw a}}function
  hb(f,e){var
  a=0;for(;;){var
  d=bK(e,a);if(0!==d){var
  c=0;for(;;){if(0!==(d&1<<c))b(f,cl((a<<3)+c|0));var
  h=c+1|0;if(7!==c){var
  c=h;continue}break}}var
  g=a+1|0;if(31!==a){var
  a=g;continue}return 0}}function
  cr(a){var
  c=ha(0);hb(function(a){eT(c,cm(a));var
  b=a-224|0,d=0;if(30<b>>>0){if(!(25<b+cI>>>0))d=1}else
  if(23!==b)d=1;var
  e=d?a+fW|0:a;return eT(c,e)},a);return c}var
  hc=0,hd=1,qn=3,qo=4,qp=5,qq=6,qr=7,qs=8,qt=9,qu=10,qv=11,qw=12,qx=13,qy=14,eV=15,du=16,he=17,hf=18;function
  hg(b,a){return b|a<<8}function
  eW(b,a){return(b-a|0)-1|0}function
  eX(c){var
  a=c;for(;;)if(typeof
  a==="number")switch(a){case
  0:return 1;case
  1:return 1;default:return 1}else
  switch(a[0]){case
  0:return 0;case
  1:return d(a[1],qz);case
  2:return 0;case
  3:return gA(eX,a[1]);case
  4:var
  e=a[2],b=eX(a[1]);if(b)return b;var
  a=e;continue;case
  5:return 1;case
  6:var
  a=a[1];continue;case
  7:return 1;case
  8:var
  a=a[2];continue;default:return 1}}function
  fk(f,g){var
  a=g;for(;;)if(typeof
  a==="number")switch(a){case
  0:return a4;case
  1:return a4;default:return a4}else
  switch(a[0]){case
  0:return ds(a[1]);case
  1:var
  b=a[1];return d(b,qA)?a4:ds(B(b,0));case
  2:var
  c=a[1];return a[2]?dt(c):c;case
  3:var
  e=a[1];return f<50?fj(f+1|0,e):n(fj,[0,e]);case
  4:var
  h=a[1],i=bl(a[2]);return eU(bl(h),i);case
  5:return a4;case
  6:var
  a=a[1];continue;case
  7:return a4;case
  8:var
  a=a[2];continue;default:return a4}}function
  fj(c,d){var
  a=d;for(;;){if(a){var
  b=a[1];if(typeof
  b==="number"){var
  a=a[2];continue}else
  switch(b[0]){case
  5:var
  e=b[1],f=cs(a[2]);return eU(bl(e),f);case
  7:var
  g=b[1],h=cs(a[2]);return eU(bl(g),h);default:return c<50?fk(c+1|0,b):n(fk,[0,b])}}return a4}}function
  bl(a){return bR(fk(0,a))}function
  cs(a){return bR(fj(0,a))}function
  eY(f,a){var
  c=0;if(typeof
  a!=="number")switch(a[0]){case
  0:var
  d=0,b=ds(a[1]);c=1;break;case
  2:var
  d=a[2],b=a[1];c=1;break}if(c){var
  e=f?cr(b):b,g=d?dt(e):e;return df(g)}throw[0,s,qB]}var
  hh=D(aK),cw=0;for(;;){aL(hh,cw,cm(cl(cw)));var
  zm=cw+1|0;if(b0!==cw){var
  cw=zm;continue}var
  qC=df(hh),dv=dj([0,c9]);dt(ds(10));var
  qF=function(K){var
  z=[1,K],b=[0,aM(32,0)],c=[0,0],j=[0,dv[1]],l=[0,0],g=[0,1],m=[0,0],q=1;function
  a(g,f){if(b[1].length-1<=c[1]){var
  a=[0,b[1].length-1];for(;;){if(a[1]<=c[1]){a[1]=a[1]*2|0;continue}var
  d=aM(a[1],0);gJ(b[1],0,d,0,b[1].length-1);b[1]=d;break}}var
  h=hg(g,f),e=c[1];w(b[1],e)[1+e]=h;c[1]++;return 0}function
  h(d){var
  b=c[1];a(hc,0);return b}function
  i(a,d,c){var
  e=hg(d,eW(c,a));w(b[1],a)[1+a]=e;return 0}function
  e(b){try{var
  a=f(dv[28],b,j[1]);return a}catch(a){a=C(a);if(a===p){var
  c=l[1];j[1]=aJ(dv[4],b,c,j[1]);l[1]++;return c}throw a}}function
  s(b){if(eX(b)){var
  a=m[1];if(64<=a)ch(qD);m[1]++;return a}return-1}function
  n(b,a){var
  c=cr(a);return qm(cr(b),c)}function
  d(b){if(typeof
  b==="number")switch(b){case
  0:return a(qp,0);case
  1:return a(qq,0);default:return a(qr,0)}else
  switch(b[0]){case
  0:return a(hd,cm(b[1]));case
  1:var
  f=b[1],n=k(f);if(0===n)return 0;if(1===n)return a(hd,cm(B(f,0)));try{var
  o=eC(f,0);d([1,qk(f,o)]);a(hc,0);var
  w=d([1,ql(f,o+1|0)]);return w}catch(b){b=C(b);if(b===p)return a(qn,e(gI(f)));throw b}case
  2:var
  x=b[2],q=cr(b[1]),y=x?dt(q):q;return a(qo,e(df(y)));case
  3:return D(b[1]);case
  4:var
  z=b[2],A=b[1],E=h(0);d(A);var
  F=h(0),G=c[1];d(z);var
  H=c[1];i(E,du,G);return i(F,eV,H);case
  5:var
  r=b[1],j=s(r),t=h(0);if(0<=j)a(he,j);d(r);if(0<=j)a(hf,j);a(eV,eW(t,c[1]));return i(t,du,c[1]);case
  6:var
  u=b[1],l=s(u),I=c[1];d(u);if(0<=l)a(hf,l);var
  J=h(0);if(0<=l)a(he,l);a(eV,eW(I,c[1]));return i(J,du,c[1]);case
  7:var
  K=b[1],L=h(0);d(K);return i(L,du,c[1]);case
  8:var
  m=b[1],M=b[2];a(qs,m);d(M);a(qt,m);g[1]=de(g[1],m+1|0);return 0;default:var
  v=b[1];a(qu,v);g[1]=de(g[1],v+1|0);return 0}}function
  D(p){var
  b=p;for(;;){if(b){var
  c=b[1];if(typeof
  c!=="number")switch(c[0]){case
  5:var
  f=c[1],l=0;if(typeof
  f==="number")l=1;else
  switch(f[0]){case
  0:case
  2:var
  i=b[2],s=cs(i);if(n(bl(f),s)){a(qx,e(eY(q,f)));var
  b=i;continue}break;default:l=1}break;case
  6:var
  g=c[1],m=0;if(typeof
  g==="number")m=1;else
  switch(g[0]){case
  0:case
  2:var
  j=b[2],t=cs(j);if(n(bl(g),t)){a(qy,e(eY(q,g)));var
  b=j;continue}break;default:m=1}break;case
  7:var
  h=c[1],o=0;if(typeof
  h==="number")o=1;else
  switch(h[0]){case
  0:case
  2:var
  k=b[2],u=cs(k);if(n(bl(h),u)){a(qw,e(eY(q,h)));var
  b=k;continue}break;default:o=1}break}var
  r=b[2];d(c);var
  b=r;continue}return 0}}d(z);a(qv,0);var
  t=bl(z),E=cr(t);if(jS(t,a4))var
  u=-1;else{var
  r=aN(aK,0);hb(function(a){return aL(r,a,1)},E);var
  u=e(df(r))}var
  v=aM(l[1],qE),F=j[1];function
  G(b,a){w(v,a)[1+a]=b;return 0}f(dv[12],G,F);var
  o=c[1],x=b[1],A=0,H=m[1],J=g[1];if(0<=o&&!((x.length-1-o|0)<0)){var
  y=zC(x,0,o);A=1}if(!A)var
  y=I(kL);return[0,y,v,qC,J,H,u]},qG=[0,[0]],qH=function(d,c,b){var
  a=AD(d,c,b);qG[1]=a;if(0===a.length-1)throw p;return w(a,0)[1]},Q=function(b,a){return a?a[1]:b},dw=function(a,b){if(b)return b;var
  c=bg(a);return bo===c?a[1]:ao===c?ck(a):a},ct=function(c,a){return a?[0,b(c,a[1])]:0},dx=function(c,a){return a?b(c,a[1])?a:0:0},hi=function(a,b){if(b)return b[1];var
  c=bg(a);return bo===c?a[1]:ao===c?ck(a):a},aA=function(a,c){return a?b(c,a[1]):0},am=function(b,a){return ct(a,b)},hj=function(a){return a?[0,a[1],0]:0},hk=function(c,a){if(a){var
  d=a[2],e=a[1],f=b(c,e),g=hk(c,d),h=e===f?1:0,i=h?d===g?1:0:h;return i?a:[0,f,g]}return a},ad=function(d,a){if(a){var
  e=a[2],f=a[1],g=b(d,f),c=ad(d,e);return g?e===c?a:[0,f,c]:c}return a},dy=function(c,b,a){var
  d=G(b);return bi(function(b,a){return f(c,a,b)},a,d)},dz=function(a){return a?[0,a[1]]:0},aQ=function(e,d){var
  a=d;for(;;){if(a){var
  f=a[2],c=b(e,a[1]);if(c)return c;var
  a=f;continue}return 0}},eZ=function(d,a){var
  c=0;return dy(function(e,a){var
  c=b(d,e);return c?[0,c[1],a]:a},a,c)},e0=function(e,d){var
  a=d;for(;;){if(a){var
  c=a[1];if(b(e,c))return[0,c];var
  a=a[2];continue}return 0}},e1=function(f){function
  a(a,c){var
  d=c[2],e=c[1];return b(f,a)?[0,[0,a,e],d]:[0,e,[0,a,d]]}return function(b){return dy(a,b,qI)}},dA=function(c,a){var
  d=0;return dy(function(d,a){return F(b(c,d),a)},a,d)},e2=function(c,a){if(a){var
  d=a[1],f=a[2];if(b(c,d)){var
  e=e2(c,f);return[0,[0,d,e[1]],e[2]]}}return[0,0,a]},hl=function(c,a){return e2(function(a){return 1-b(c,a)},a)},hm=function(b){if(d(b,kH))var
  f=b;else{var
  h=0;if(gF(ay(b,0))||gF(ay(b,k(b)-1|0)))h=1;else
  var
  f=b;if(h){var
  e=au(b),g=ax(e),a=[0,0];for(;;){if(a[1]<g&&gD(bL(e,a[1]))){a[1]++;continue}var
  c=[0,g-1|0];for(;;){if(a[1]<=c[1]&&gD(bL(e,c[1]))){c[1]+=-1;continue}var
  i=a[1]<=c[1]?eA(e,a[1],(c[1]-a[1]|0)+1|0):kB,f=O(i);break}break}}}return d(f,qJ)},a5=function(b,a){var
  c=k(a);return k(b)<c?0:d(aq(b,0,c),a)},e3=function(c,b){var
  e=k(c),a=k(b);return e<a?0:d(aq(c,e-a|0,a),b)},hn=function(b,a){var
  c=k(b);return c<a?qK:aq(b,a,c-a|0)},dB=function(a,h){function
  c(b,a){try{var
  e=eC(a,h),f=aq(a,0,e),g=hn(a,e+1|0),i=d(f,qM)?c(b,g):c([0,f,b],g);return i}catch(c){c=C(c);if(c===p)return d(a,qL)?b:[0,a,b];throw c}}return G(c(0,a))},as=function(b,a){var
  c=qF(a);try{qH(c,b,0);var
  d=1;return d}catch(a){a=C(a);if(a===p)return 0;throw a}},z=gN([0,kK]),ho=function(b){var
  a=aq(b,5,k(b)-5|0);try{var
  c=c7(a);return c}catch(a){a=C(a);if(a[1]===db)throw[0,et,h(qR,h(b,qQ))];throw a}},hp=function(a){return a?[0,az(qS,a)]:0},hq=[0,0],bX=[0,0],qX=F(qW,qV),qY=b(z[37],qX),hr=function(a){return f(z[3],a,qY)},e4=function(b,a){return bN(a[1],b)},aI=function(a){function
  b(b){return e4(a,b)}return function(a){return e0(b,a)}},e5=function(c,b,a){var
  d=hk(function(a){var
  d=a[1];return bN(c,d)?[0,d,b]:a},a);return d===a?[0,[0,c,b],a]:d},q2=function(b,a){return aA(b,function(b){return 0===a[0]?[0,h(b,a[1])]:0})},hs=function(a){return bi(q2,q1,a)},ht=function(b){function
  c(a){if(1===a[0]&&d(a[1],b))return[0,a];return 0}return function(a){if(0===a[0])throw[0,s,q3];return eZ(c,a[4])}},hu=function(a){return 0===a[0]?0:a[4]},e6=function(c,b,a){if(0===a[0])return 0;var
  e=a[3],g=a[2];if(d(a[1],c))return f(b,g,e);var
  h=a[4];return aQ(function(a){return e6(c,b,a)},h)},dC=function(d,c,g,f,b){var
  a=bN(c,d),e=a?0===b?1:0:a;return e},q4=function(k,j,i,h){var
  c=b(e1(function(a){return d(a[1],q5)}),j),a=c[1],l=c[2];if(a){var
  e=a[1];if(!g(e[1],q6)&&!a[2]){var
  f=e[2];return a5(f,q7)?[0,[1,k,F(l,[0,[0,q8,f],0]),i,h]]:0}}return 0},q_=[0,function(c,b,g,f){var
  a=d(c,q9),e=a?0!==b?1:0:a;return e},q4],hv=function(a){return d(a[1],q$)},ra=function(f,b,a,e){var
  c=e0(hv,b);if(c){var
  h=c[1][2],i=ad(function(a){return g(a[1],rb)},b),j=V(function(a){return d(a[1][1],rc)},a)?a:[0,[0,rd,[0,[0,h,0]]],a];return[0,[1,f,i,j,e]]}return 0},rf=[0,function(c,b,f,e){var
  a=d(c,re);return a?V(hv,b):a},ra],dD=function(a){return gH(a,32)?h(rh,h(a,rg)):a},rr=b(z[37],rq),rs=function(m){var
  b=m[2][1],c=m[1][1];if(f(z[3],c,rr)){var
  a=bi(function(b,a){if(b){var
  c=b[2],e=b[1];if(d(a,rj))return[0,rk,[0,dD(e),c]];if(a5(a,rl)){var
  f=h(rm,a);return[0,rn,[0,h(dD(e),f),c]]}return[0,h(e,a),c]}return 0},ri,b);if(a){var
  i=a[1];if(g(i,ro))var
  n=a[2],j=[0,dD(i),n];else
  var
  j=a[2];var
  l=j}else
  var
  l=a;var
  e=az(rp,G(l))}else
  var
  p=e3(c,rw)?b:aj(dD,b),e=az(rx,p);var
  o=0===k(e)?rt:e;return h(c,h(rv,h(o,ru)))},hw=function(a,b){var
  c=a?a[1]:ry;return az(c,aj(rs,b))},e7=function(a){return h(rA,hw(rz,a))},rC=function(b){switch(b[0]){case
  0:var
  c=b[1],d=h(rE,h(e7(b[2]),rD));return h(az(rB,c[1]),d);case
  1:var
  f=b[1];return h(rH,h(f,h(rG,h(e7(b[2]),rF))));default:var
  g=b[3],i=b[2],j=b[1][1],k=h(rJ,h(e7(b[4]),rI)),l=h(Q(rL,ct(function(a){return h(rK,a)},g)),k);return h(rO,h(j,h(Q(rN,ct(function(b){return h(rM,a(e+b[1]))},i)),l)))}},cu=function(a){return az(rQ,a[1])},hx=[A,rR,be(0)],aB=function(a){throw hx},hy=function(a){return rZ},hz=function(a){return r0},hA=function(a){return[0,a,0]},e8=function(a){return r1},e9=function(a){return r2},e_=function(a){return r3},bY=function(a){ci(r4);throw[0,s,r5]},hB=function(c,b,a){if(typeof
  a==="number"&&3===a)return Q(0,b);return aB(0)},cy=function(e,H,c,a,G,F,g){var
  d=[0,H,F,G];if(typeof
  g==="number"&&5===g){var
  f=b(a,c);if(typeof
  f==="number")switch(f){case
  4:var
  h=b(a,c),i=hz(0),j=2;return e<50?_(e+1|0,d,c,a,i,j,h):n(_,[0,d,c,a,i,j,h]);case
  5:var
  k=b(a,c),l=hy(0),m=2;return e<50?_(e+1|0,d,c,a,l,m,k):n(_,[0,d,c,a,l,m,k]);default:return aB(0)}else
  switch(f[0]){case
  0:var
  o=f[1],p=b(a,c),q=2;return e<50?_(e+1|0,d,c,a,o,q,p):n(_,[0,d,c,a,o,q,p]);case
  1:var
  r=f[1],s=b(a,c),t=2;return e<50?_(e+1|0,d,c,a,r,t,s):n(_,[0,d,c,a,r,t,s]);case
  2:var
  u=f[1],v=2;return e<50?dN(e+1|0,d,c,a,u,v):n(dN,[0,d,c,a,u,v]);case
  3:var
  w=f[1],x=b(a,c),y=2;return e<50?_(e+1|0,d,c,a,w,y,x):n(_,[0,d,c,a,w,y,x]);case
  4:var
  z=f[1],A=b(a,c),B=2;return e<50?_(e+1|0,d,c,a,z,B,A):n(_,[0,d,c,a,z,B,A]);default:var
  C=f[1],D=b(a,c),E=2;return e<50?_(e+1|0,d,c,a,C,E,D):n(_,[0,d,c,a,C,E,D])}}return aB(0)},_=function(h,B,g,f,A,z,y){var
  e=B,d=A,c=z,a=y;for(;;)if(typeof
  a==="number")switch(a){case
  4:var
  C=b(f,g),e=[0,e,c,d],d=hz(0),c=3,a=C;continue;case
  5:var
  D=b(f,g),e=[0,e,c,d],d=hy(0),c=3,a=D;continue;case
  2:case
  6:return aB(0);default:var
  i=e,l=[0,d,0],k=c;for(;;){if(2===k){var
  j=i[1],m=[0,i[3],[0,l]];switch(i[2]){case
  0:var
  t=hA(m),u=0;return h<50?bZ(h+1|0,j,g,f,t,u,a):n(bZ,[0,j,g,f,t,u,a]);case
  1:var
  v=j[2],w=j[1],x=[0,m,j[3]];return h<50?bZ(h+1|0,w,g,f,x,v,a):n(bZ,[0,w,g,f,x,v,a]);case
  7:var
  r=hA(m),s=7;return h<50?bZ(h+1|0,j,g,f,r,s,a):n(bZ,[0,j,g,f,r,s,a]);default:return bY(0)}}if(3===k){var
  J=i[2],K=[0,i[3],l],i=i[1],l=K,k=J;continue}return bY(0)}}else
  switch(a[0]){case
  0:var
  E=a[1],e=[0,e,c,d],d=E,c=3,a=b(f,g);continue;case
  1:var
  F=a[1],e=[0,e,c,d],d=F,c=3,a=b(f,g);continue;case
  2:var
  o=a[1],p=[0,e,c,d],q=3;return h<50?dN(h+1|0,p,g,f,o,q):n(dN,[0,p,g,f,o,q]);case
  3:var
  G=a[1],e=[0,e,c,d],d=G,c=3,a=b(f,g);continue;case
  4:var
  H=a[1],e=[0,e,c,d],d=H,c=3,a=b(f,g);continue;default:var
  I=a[1],e=[0,e,c,d],d=I,c=3,a=b(f,g);continue}},dN=function(g,f,c,a,i,e){var
  d=b(a,c);if(typeof
  d==="number")switch(d){case
  2:case
  6:return aB(0)}else
  if(4===d[0]){var
  l=d[1],k=b(a,c),j=h(i,l);return g<50?dM(g+1|0,f,c,a,j,e,k):n(dM,[0,f,c,a,j,e,k])}return g<50?dM(g+1|0,f,c,a,i,e,d):n(dM,[0,f,c,a,i,e,d])},dM=function(g,f,e,d,c,b,a){return g<50?_(g+1|0,f,e,d,c,b,a):n(_,[0,f,e,d,c,b,a])},bZ=function(f,m,c,a,l,k,g){var
  d=[0,m,k,l];if(typeof
  g==="number")switch(g){case
  0:var
  e=b(a,c);if(typeof
  e==="number")switch(e){case
  1:case
  3:return f<50?dL(f+1|0,d,c,a,e):n(dL,[0,d,c,a,e])}else
  if(4===e[0]){var
  o=e[1],h=b(a,c),i=[0,o],j=1;return f<50?cy(f+1|0,d,c,a,i,j,h):n(cy,[0,d,c,a,i,j,h])}return aB(0);case
  1:case
  3:return f<50?dL(f+1|0,d,c,a,g):n(dL,[0,d,c,a,g])}return bY(0)},dL=function(k,g,e,d,c){var
  i=g[2],h=g[1],m=g[3],l=[0,z[1],0],a=bi(function(a,b){var
  c=b[1][1],d=a[1],e=a[2];return f(z[3],c,d)?a:[0,f(z[4],c,d),[0,b,e]]},l,m)[2];if(i){if(7<=i){if(typeof
  c==="number"&&1===c){var
  j=b(d,e);return k<50?dK(k+1|0,h,e,d,a,j):n(dK,[0,h,e,d,a,j])}return aB(0)}return bY(0)}return hB(h,[0,a],c)},dK=function(e,r,c,a,k,j){var
  l=r[3],K=0,O=r[2],P=r[1];if(l&&!g(l[1],rY)){var
  f=l[2],s=0;if(f&&!g(f[1],rV)){var
  o=f[2];if(o){var
  p=o[2],t=o[1];if(p){if(!g(p[1],rX)){var
  q=p[2];if(q){var
  L=q[1],M=hp(q[2]),m=[2,[0,t],[0,[0,ho(L)]],M,k];s=1}}}else{var
  m=[2,[0,t],0,0,k];s=1}}}if(!s)var
  m=[1,az(rW,f),k]}else
  K=1;if(K)var
  m=[0,[0,G(bi(function(a,b){if(a){var
  c=a[2],d=a[1];return g(b,rT)?[0,h(d,b),c]:[0,rU,[0,d,c]]}return 0},rS,l))],k];var
  d=[0,P,O,m];if(typeof
  j==="number")switch(j){case
  3:var
  i=d,u=0;for(;;){var
  v=i[2],w=[0,i[3],u],N=i[1];if(4===v)return w;if(5===v){var
  i=N,u=w;continue}return bY(0)}case
  4:var
  x=b(a,c),y=e_(0),z=5;return e<50?a6(e+1|0,d,c,a,y,z,x):n(a6,[0,d,c,a,y,z,x]);case
  5:var
  A=b(a,c),B=e9(0),C=5;return e<50?a6(e+1|0,d,c,a,B,C,A):n(a6,[0,d,c,a,B,C,A]);case
  6:var
  D=b(a,c),E=e8(0),F=5;return e<50?a6(e+1|0,d,c,a,E,F,D):n(a6,[0,d,c,a,E,F,D])}else
  if(4===j[0]){var
  H=j[1],I=b(a,c),J=5;return e<50?a6(e+1|0,d,c,a,H,J,I):n(a6,[0,d,c,a,H,J,I])}return aB(0)},a6=function(k,p,f,e,o,m,l){var
  d=p,c=o,a=m,g=l;for(;;){if(typeof
  g==="number")switch(g){case
  2:var
  h=d,j=[0,c,0],i=a;for(;;)switch(i){case
  4:return k<50?dJ(k+1|0,h,f,e,j,i):n(dJ,[0,h,f,e,j,i]);case
  5:return k<50?dJ(k+1|0,h,f,e,j,i):n(dJ,[0,h,f,e,j,i]);case
  6:var
  u=h[2],v=[0,h[3],j],h=h[1],j=v,i=u;continue;default:return bY(0)}case
  4:var
  q=b(e,f),d=[0,d,a,c],c=e_(0),a=6,g=q;continue;case
  5:var
  r=b(e,f),d=[0,d,a,c],c=e9(0),a=6,g=r;continue;case
  6:var
  s=b(e,f),d=[0,d,a,c],c=e8(0),a=6,g=s;continue}else
  if(4===g[0]){var
  t=g[1],d=[0,d,a,c],c=t,a=6,g=b(e,f);continue}return aB(0)}},dJ=function(f,o,c,a,m,l){var
  d=[0,o,l,m],e=b(a,c);if(typeof
  e==="number"){if(1===e){var
  g=b(a,c),h=0;return f<50?dK(f+1|0,d,c,a,h,g):n(dK,[0,d,c,a,h,g])}}else
  if(4===e[0]){var
  p=e[1],i=b(a,c),j=[0,p],k=7;return f<50?cy(f+1|0,d,c,a,j,k,i):n(cy,[0,d,c,a,j,k,i])}return aB(0)},r6=function(a,b,c,d,e,f){return bR(cy(0,a,b,c,d,e,f))},dE=function(a,b,c,d,e,f){return bR(a6(0,a,b,c,d,e,f))},r7=function(c,a){var
  d=b(c,a),e=0;if(typeof
  d==="number")switch(d){case
  3:return 0;case
  4:var
  f=b(c,a);return dE(e,a,c,e_(0),4,f);case
  5:var
  g=b(c,a);return dE(e,a,c,e9(0),4,g);case
  6:var
  h=b(c,a);return dE(e,a,c,e8(0),4,h)}else
  if(4===d[0]){var
  i=d[1];return dE(e,a,c,i,4,b(c,a))}return aB(0)},hC=[A,r9,be(0)],r8=function(d,c){var
  a=b(d,c),e=0;if(typeof
  a==="number"){if(3===a)return hB(e,0,a)}else
  if(4===a[0]){var
  f=a[1];return r6(e,c,d,[0,f],0,b(d,c))}return aB(0)},r$=function(c){a:for(;;){c[10]=aM(5,-1);var
  k=0;for(;;){var
  d=Ak(r_,k,c),j=0<=d?1:0,m=j?c[12]!==di?1:0:j;if(m){c[11]=c[12];var
  f=c[12];c[12]=[0,f[1],f[2],f[3],c[4]+c[6]|0]}if(20<d>>>0){b(c[1],c);var
  k=d;continue}switch(d){case
  0:return 3;case
  1:var
  g=c[12];c[12]=[0,g[1],g[2]+1|0,c[6],g[4]];continue a;case
  2:continue a;case
  3:return 4;case
  4:return 2;case
  5:return 1;case
  6:return 0;case
  7:return 5;case
  8:return 6;case
  9:return[3,J(c)];case
  10:return[5,J(c)];case
  11:return[4,J(c)];case
  12:return[0,J(c)];case
  13:return[2,J(c)];case
  14:return[1,J(c)];case
  15:var
  n=w(c[10],0)[1];return[1,eD(c,w(c[10],1)[2],n)];case
  16:return[4,J(c)];case
  17:continue a;case
  18:continue a;case
  19:continue a;default:var
  i=J(c),l=B(i,0);if(aK<l)return[1,i];throw[0,hC,h(sc,h(i,h(sb,h(a(e+l),sa))))]}}}},hD=function(c){var
  d=c[11],b=c[12],f=h(sd,a(e+((b[4]-b[3]|0)+1|0))),g=h(se,h(a(e+((d[4]-d[3]|0)+1|0)),f));return h(a(e+b[2]),g)},hE=function(c,a){var
  b=gM(0,c);try{var
  g=f(a,r$,b);return g}catch(a){a=C(a);if(a[1]===hC){var
  d=h(sf,a[2]);ci(h(hD(b),d));return 0}if(a===hx){var
  e=h(sg,c);ci(h(hD(b),e));return 0}throw a}},hF=function(a){return hE(a,r7)},e$=function(a){if(0===a[0])return hm(a[1]);var
  b=a[4],c=g(a[1],si);if(c){var
  d=0!==b?1:0;if(d)return gA(e$,b);var
  e=d}else
  var
  e=c;return e},hG=function(a){var
  c=0;return G(bi(function(f,a){if(0===a[0]){var
  c=a[1],j=c[3],k=c[2],l=c[1],h=function(a){return hE(a[2],r8)},e=b(e1(function(a){return d(a[1],sh)}),k),i=e[2],g=bi(F,0,aj(h,e[1]));return[0,[1,l,i,g,hG(j)],f]}return[0,[0,a[1]],f]},c,a))},hH=function(b,a){return 0===a?b:[0,[0,sj,hw(0,a)],b]},hI=function(a){var
  b=0;return G(bi(function(b,a){if(0===a[0])return[0,[1,a[1]],b];var
  c=a[4],d=a[1],e=hH(a[2],a[3]);return[0,[0,[0,d,e,hI(c)]],b]},b,a))},hJ=function(u){var
  d=0,a=u;for(;;){if(a){var
  e=a[1],j=0;if(0===e[0]){var
  f=a[2];if(f){var
  g=f[1],k=e[1];if(0===g[0]){var
  v=f[2],a=[0,[0,h(k,g[1])],v];continue}var
  o=f[2],n=k,m=g,l=g[1];j=1}}else{var
  i=a[2];if(i){var
  s=i[1],z=e[1];if(0===s[0]){var
  o=i[2],n=s[1],m=e,l=z;j=1}}}if(j&&hr(l)&&hm(n)){var
  a=[0,m,o];continue}var
  b=a[1];if(0===b[0]){var
  d=[0,[0,b[1]],d],a=a[2];continue}var
  p=b[4],q=b[1],w=a[2],x=b[3],y=b[2];if(hr(q)){var
  c=p;for(;;){if(c){var
  t=c[2];if(e$(c[1])){var
  c=t;continue}}var
  r=dy(function(b,a){if(e$(b)&&0===a)return a;return[0,b,a]},c,0);break}}else
  var
  r=p;var
  d=[0,[1,q,y,x,hJ(r)],d],a=w;continue}return G(d)}},hK=function(a){function
  c(d,a){var
  b=gE(d*2|0,32);if(0===a[0])return h(sm,h(b,h(sl,h(a[1],sk))));var
  e=a[4],f=a[1],g=hH(a[2],a[3]),i=h(so,h(b,sn)),j=d+1|0,k=h(sq,h(az(sp,aj(function(a){return c(j,a)},e)),i));return h(sw,h(b,h(sv,h(f,h(su,h(az(st,aj(function(a){var
  b=a[1];return h(b,h(ss,h(a[2],sr)))},g)),k))))))}return c(0,a)},bm=function(b,a){return[0,fP,[0,b,a]]},s0=[0,sZ,[0,sY,[0,sX,[0,sW,[0,sV,[0,sU,[0,sT,[0,sS,[0,sR,[0,sQ,[0,sP,[0,sO,[0,sN,[0,sM,[0,sL,[0,sK,[0,sJ,[0,sI,[0,sH,[0,sG,[0,sF,[0,sE,[0,sD,[0,sC,[0,sB,[0,[0,sA,[0,j,bm(i,sz)]],sy]]]]]]]]]]]]]]]]]]]]]]]]]],s7=[0,s6,[0,s5,[0,s4,[0,s3,[0,[0,s2,[0,j,bm(i,s1)]],s0]]]]],ta=[0,s$,[0,s_,[0,[0,s9,[0,o,bm(i,s8)]],s7]]],te=[0,[0,td,[0,o,[0,R,[0,tc,bm(i,tb)]]]],ta],tG=[0,tF,[0,tE,[0,tD,[0,tC,[0,tB,[0,tA,[0,tz,[0,ty,[0,tx,[0,tw,[0,tv,[0,tu,[0,tt,[0,ts,[0,tr,[0,tq,[0,tp,[0,[0,to,[0,o,[0,R,[0,hL,tn]]]],[0,[0,tm,[0,o,[0,R,[0,hL,tl]]]],[0,tk,[0,tj,[0,ti,[0,th,[0,[0,tg,[0,o,bm(i,tf)]],te]]]]]]]]]]]]]]]]]]]]]]]],tK=[0,tJ,[0,[0,tI,[0,c,bm(i,tH)]],tG]],tO=[0,tN,[0,[0,tM,[0,c,bm(i,tL)]],tK]],ue=[0,ud,[0,uc,[0,ub,[0,ua,[0,t$,[0,t_,[0,t9,[0,t8,[0,t7,[0,t6,[0,t5,[0,t4,[0,t3,[0,t2,[0,t1,[0,t0,[0,tZ,[0,tY,[0,tX,[0,tW,[0,tV,[0,tU,[0,tT,[0,tS,[0,tR,[0,[0,tQ,[0,c,bm(i,tP)]],tO]]]]]]]]]]]]]]]]]]]]]]]]]],hM=function(a){return hJ(hG(pE([0,ue],uh,ug,uf,0,gM(0,a))))},hN=function(a){return[0,[0,uD,a],uC]},fa=hN(uE),hO=function(n,m,a){var
  f=ad(function(b){var
  a=b[1],c=d(a,hP),e=c||d(a,hQ);return e},n);function
  o(a){return hn(a[2],7)}var
  p=b(aI(hP),f),q=Q(fa,am(am(dw([ao,function(a){return b(aI(hQ),f)}],p),o),hN)),c=b(aI(uA),a),e=b(aI(uB),a),i=0;if(e&&c){var
  j=c[1][2],k=e[1][2],l=function(b){if(g(b,uv)&&g(b,uw))return a;return e5(uy,j,e5(ux,k,a))},h=am(aQ(function(a){return g(a[1][1],uz)?0:[0,cu(a[2])]},m),l);i=1}if(!i)var
  h=0;return F(q,Q(a,h))},hR=function(b,a){var
  c=[1,uF,0,0,a];return e6(b,function(b,a){return[0,[0,b,a]]},c)},uG=function(f,d,a,c){function
  e(b){var
  c=b[2],e=hO(d,a,b[1]);return[0,e,F(c,a)]}var
  b=Q([0,fa,0],am(hR(uH,c),e));return[0,[1,uI,b[1],b[2],0]]},uK=[0,function(a,e,c,b){return d(a,uJ)},uG],uL=function(a,b){return[0,a]},hS=function(a){return e6(uM,uL,a)},hT=function(d,a){if(0===a[0])return 0;var
  b=a[1];if(g(b,uN)){var
  e=a[4],f=a[3],h=a[2],c=function(b){if(b){var
  a=b[1];if(0===a[0])return[0,a,c(b[2])];var
  e=a[1];if(g(e,uO)){var
  f=a[4],h=a[3],i=a[2],j=c(b[2]);return[0,[1,e,i,h,c(f)],j]}return[0,[1,uP,d,a[3],a[4]],b[2]]}return 0};return[0,[1,b,h,f,c(e)]]}return[0,[1,uQ,d,a[3],a[4]]]},uU=function(w,c,v,u,b){if(c){var
  n=c[1];if(!g(n[1],uV)&&!c[2]){var
  a=n[2],m=a5(a,uR),s=m?e3(a,uS):m;if(s){if(b){var
  d=b[1];if(0!==d[0]&&!g(d[1],uW)){var
  e=d[2];if(e){var
  o=e[1];if(!g(o[1],uX)&&!g(o[2],uY)&&!e[2]){var
  f=b[2];if(f){var
  h=f[2];if(h){var
  i=h[1],j=f[1];if(0!==i[0]&&!g(i[1],uZ)){var
  l=i[2];if(l){var
  p=l[1];if(!g(p[1],u0)&&!g(p[2],u1)&&!l[2]){var
  t=h[2],q=hM(aq(a,15,(k(a)-9|0)-15|0)),r=function(a){function
  b(b){return hO(b[1],b[2],a)}var
  c=am(hR(uT,q),b);return hT(hi([ao,function(b){return F(fa,a)}],c),j)};return[0,[0,Q(j,aA(hS(j),r)),t]]}}}}}}}}}return 0}return 0}}return 0},u3=[0,function(a,b,c,d){return dC(u2,a,b,c,d)},uU],u7=function(u,b,t,s,a){if(b){var
  l=b[1];if(!g(l[1],u8)&&!b[2]){var
  m=l[2],k=a5(m,u4),q=k?e3(m,u5):k;if(q){if(a){var
  c=a[1];if(0!==c[0]&&!g(c[1],u9)){var
  d=c[2];if(d){var
  n=d[1];if(!g(n[1],u_)&&!g(n[2],u$)&&!d[2]){var
  e=a[2];if(e){var
  f=e[2];if(f){var
  h=f[1],i=e[1];if(0!==h[0]&&!g(h[1],va)){var
  j=h[2];if(j){var
  o=j[1];if(!g(o[1],vb)&&!g(o[2],vc)&&!j[2]){var
  r=f[2],p=function(a){return hT([0,u6,a],i)};return[0,[0,Q(i,aA(hS(i),p)),r]]}}}}}}}}}return 0}return 0}}return 0},ve=[0,function(a,b,c,d){return dC(vd,a,b,c,d)},u7],hU=function(h,g,o){var
  i=h?h[1]:0,c=0,a=o;for(;;){if(a){var
  b=a[1];if(0===b[0]){var
  c=[0,b,c],a=a[2];continue}var
  d=a[2],f=b[4],j=b[3],k=b[2],l=b[1],e=cz(g,l,k,j,f,d);if(typeof
  e==="number"){if(0===e){var
  a=d;continue}var
  m=hU([0,i],g,f),n=[1,l,k,j,m];if(i&&bQ(m,f)){var
  a=[0,n,d];continue}var
  c=[0,n,c],a=d;continue}else{if(0===e[0]){var
  a=[0,e[1],d];continue}var
  a=e[1];continue}}return G(c)}},hV=function(a,e,d,c,b){return Q(0,aQ(function(f){if(fl(f[1],e,d,c,b)){var
  a=fl(f[2],e,d,c,b);if(typeof
  a==="number"&&!a)return 0;return[0,a]}return 0},a))},dF=function(a,i,h,b){var
  c=a?a[1]:0;return hU([0,c],function(f,e,d,b,c){var
  a=hV(i,f,e,d,b);if(typeof
  a==="number")switch(a){case
  0:var
  g=aQ(function(a){return fl(a[1],f,e,d,b)?cz(a[2],f,e,d,b,c):0},h);return g?[1,g[1]]:1;case
  1:return[1,F(b,c)];default:return 0}else
  return 0===a[0]?[0,a[1]]:[1,F(a[1],c)]},b)},vg=function(c){if(!g(c[1][1],vh)){var
  a=c[2][1];if(a){var
  b=a[2];if(b){var
  d=b[2],e=b[1],f=a[1],h=[ao,function(a){return hp(d)}];return[0,[0,[0,f],ho(e),h]]}}}return 0},fb=function(a){return aQ(vg,a)},dG=function(b,a){return aQ(function(a){var
  c=a[2];return d(a[1][1],b)?[0,cu(c)]:0},a)},hW=function(o){var
  c=0,a=o;a:for(;;){if(a){var
  b=a[1];if(0===b[0]){var
  c=[0,b,c],a=a[2];continue}var
  k=a[2],f=b[2],h=b[1],p=b[3],l=hW(b[4]);if(f){var
  m=f[1];if(!g(m[1],vi)&&!g(m[2],vj)&&!f[2]&&!l){var
  n=0;if(d(h,vk)||d(h,vl))n=1;if(n){var
  i=1,e=hl(function(f){return function(a){if(1===a[0]){var
  b=a[2];if(b){var
  c=b[1],e=a[1];if(!g(c[1],qT)&&!g(c[2],qU)&&!b[2]&&d(e,f))return 1}}return 0}}(h),k)[2];for(;;){if(0===i)var
  j=e;else{if(e){var
  i=i-1|0,e=e[2];continue}var
  j=0}var
  a=j;continue a}}}}var
  c=[0,[1,h,f,p,l],c],a=k;continue}return G(c)}},hX=function(a){return[0,vn,a[2],[0,vm,a[3]],0]},vJ=function(b){var
  a=b[1][1],c=d(a,vK),e=c||d(a,vL);return e},vM=function(a){if(!g(a[1][1],vN)){var
  c=a[2],b=c[1];if(b&&!g(b[1],vO)&&!b[2])return[0,vP,c]}return a},hY=function(j,b,c){function
  p(h){var
  k=h[3],c=h[2],i=c[3],f=c[2],l=c[1],o=c[4],p=h[1],m=0===k?0:hY(j,b,k),n=[0,vY,a(e+p)];if(d(l,vZ))return[1,l,[0,n,f],i,m];var
  q=V(function(a){return e4(v0,a)},f)?f:[0,v2,f],r=[0,n,F(eZ(function(a){return g(a[1][1],vT)?0:[0,[0,vU,cu(a[2])]]},i),q)],s=aj(vM,i);return[1,v1,r,s,F(hW(o),m)]}if(c){var
  k=c[1],l=k[2][3],m=k[1],i=function(g,e,c){function
  f(a){if(2===a[0]){var
  b=a[3];if(d(a[1][1],g)){if(c)var
  f=c[1],e=b?bN(f,b[1]):0;else
  var
  e=0===b?1:0;if(e)return 1}}return 0}function
  h(a){if(2===a[0]){var
  b=a[2];if(b){var
  c=a[4];if(b[1][1]===e)return[0,c]}}return 0}function
  i(a){if(2===a[0]&&!a[2])return[0,a[4]];return 0}var
  b=ad(f,j),a=aQ(h,b);return a?a:aQ(i,b)},n=function(b){var
  a=b[3],d=b[2],e=b[1][1],c=bg(a),f=bo===c?a[1]:ao===c?ck(a):a;return i(e,d,f)},o=function(c){var
  a=[ao,function(e){function
  a(a){return aA(fb(l),n)}var
  b=dG(vQ,c);return aA(dx(function(a){return d(vR,a)},b),a)}];function
  m(h){var
  a=dh(h);if(g(a,vw))if(g(a,vx)){var
  e=0;if(g(a,vy)&&g(a,vz))var
  b=g(a,vA)?g(a,vB)?0:vD:vE;else
  e=1;if(e)var
  f=function(a){return d(a,qO)?vt:d(a,qP)?vu:d(a,qN)?0:0},b=aA(dG(vv,c),f)}else
  var
  b=vF;else
  var
  b=vG;return dw([ao,function(a){return vC}],b)}var
  h=Q(vI,aA(dG(vH,c),m)),b=h[1],j=h[2],e=hj(am(j,function(a){return[0,vo,[0,[0,a,0]]]}));if(d(b,vp))return[0,b,0,e];function
  f(a){return dG(vq,a)}var
  i=[ao,function(d){var
  b=bg(a),c=bo===b?a[1]:ao===b?ck(a):a;return aA(c,f)}];function
  k(a){return[0,vr,a]}var
  o=dw(i,f(c));return[0,b,hj(am(dx(function(a){return g(a,vs)},o),k)),e]},f=Q(vS,am(i(b,m,0),o)),q=f[3],r=f[2],s=f[1],t=aj(p,c),u=F([0,[0,vX,h(b,h(vW,a(e+m)))],[0,[0,vV,b],0]],r);return[0,[1,s,u,F(q,ad(vJ,l)),t],0]}return 0},v4=function(c,b,a,f){var
  d=0,e=bX[1]?v5:v6;return[0,[1,c,b,a,[0,e,d]]]},wb=[0,function(o,r,q,b){var
  m=d(o,wa);if(m){var
  n=1===bh(b)?1:0;if(n){if(b){var
  h=b[1];if(0!==h[0]&&!b[2]){var
  c=h[1],a=h[4];for(;;){var
  j=d(c,v7);if(j)var
  f=j;else
  var
  p=d(c,v_),f=p||d(c,v$);if(f){if(a){var
  e=a[1];if(0===e[0]){var
  k=e[1];if(g(k,v8)){if(!g(k,v9)&&!a[2]&&bX[1])return 1}else
  if(!a[2])return 1}else
  if(!a[2]){var
  c=e[1],a=e[4];continue}}var
  l=0}else
  var
  l=f;return l}}}var
  i=0}else
  var
  i=n}else
  var
  i=m;return i},v4],wd=b(z[37],wc),hZ=function(e){var
  a=e;for(;;){if(a){var
  b=a[1];if(0===b[0]){var
  c=a[2];if(c){var
  d=c[1],f=b[1];if(0===d[0]){var
  g=c[2],a=[0,[0,h(f,d[1])],g];continue}}}return[0,b,hZ(a[2])]}return a}},h0=function(e,d){var
  b=0,a=e,c=d;for(;;){if(a){var
  b=[0,a[1],b],a=a[2];continue}if(c){var
  b=[0,c[1],b],a=0,c=c[2];continue}return G(b)}},wn=function(e,c,h,i,a){var
  j=b(aI(wq),c);if(a){var
  f=a[1];if(0===f[0])var
  k=0;else{var
  r=f[4],s=f[2],t=f[1],u=b(aI(wr),s),m=g(e,t);if(m)var
  n=m;else
  var
  p=j?1:0,q=1-p,n=q||bQ(j,u);if(n)var
  o=0;else
  var
  v=function(a){if(0===a[0])return 0;var
  g=a[4],b=d(a[1],ws);if(b){if(0===a[0])var
  c=0;else
  var
  f=a[3],c=V(function(a){var
  c=a[2][1],b=d(a[1][1],wo),e=b?bN(c,wp):b;return e},f);var
  e=c}else
  var
  e=b;return e?[0,g]:0},o=aA(dz(r),v);var
  k=o}var
  l=k}else
  var
  l=0;return ct(function(g){var
  f=G(i);if(f)var
  j=f[1],d=[0,j,G(f[2])];else
  var
  d=0;if(d){var
  b=d[1],k=d[2];if(0===b[0]){var
  l=dd(a);return[0,[1,e,c,h,F(i,g)],l]}var
  m=b[4],n=b[3],o=b[2],p=b[1],q=dd(a);return[0,[1,e,c,h,F(k,[0,[1,p,o,n,F(m,g)],0])],q]}return[0,[1,e,c,h,g],dd(a)]},l)},wv=[0,function(a,g,f,e){var
  b=d(a,wt),c=b||d(a,wu);return c},wn],h1=function(a){var
  c=b(aI(ww),a);return c7(Q(wx,ct(function(a){return a[2]},c)))},wy=function(e,c,o,g,f){function
  a(a){var
  h=a[2],i=dz(f);return aA(i,function(a){if(0===a[0])return 0;var
  i=a[2],p=a[4],q=a[1],j=b(aI(wz),i);return aA(j,function(r){var
  b=r[2],s=h1(c),t=h1(i),u=(s+bh(g)|0)===t?1:0,j=d(e,wA),k=j?d(b,h):j;if(k)var
  a=k;else{var
  l=d(e,wB);if(l){var
  m=d(q,wC);if(m)var
  n=d(b,h),a=n?u:n;else
  var
  a=m}else
  var
  a=l}if(a){var
  v=dd(f);return[0,[0,[1,e,c,o,F(g,p)],v]]}return 0})})}return aA(b(aI(wD),c),a)},wG=[0,function(a,g,f,e){var
  b=d(a,wE),c=b||d(a,wF);return c},wy],wH=function(e,a,d,c){var
  b=ad(function(a){return g(a[1],wI)},a);return a===b?0:[0,[1,e,b,d,c]]},wJ=[0,function(d,a,c,b){return 0!==a?1:0},wH],h2=function(a){var
  e=3<=k(a)?1:0;if(e){var
  f=58===B(a,1)?1:0;if(f){var
  b=B(a,0),d=0;if(113<=b){if(!(2<b-118>>>0))d=1}else
  if(111<=b)d=1;if(!d)return 0;var
  c=1}else
  var
  c=f}else
  var
  c=e;return c},wN=function(d,c,b,a){return a?1:2},wO=[0,function(a,d,c,b){return h2(a)},wN],wP=function(e,a,d,c){if(a){var
  b=a[1];if(!g(b[1],wQ)&&!a[2]&&a5(b[2],wR))return 1}return 0},wT=[0,function(a,e,c,b){return d(a,wS)},wP],wY=function(h,b,f,e){if(b){var
  c=b[1];if(!g(c[1],wZ)&&!b[2]){var
  a=dh(c[2]);if(!d(a,wU)&&!d(a,wV)&&!a5(a,wW)&&!a5(a,wX))return 0;return 2}}return 0},w2=[0,function(a,h,g,c){var
  e=d(a,w0),b=e||d(a,w1),f=b?0===c?1:0:b;return f},wY],w5=function(d,c,b,a){return 2},w6=[0,function(e,a,c,b){return V(function(a){var
  c=a[2],b=d(a[1],w3),e=b?d(c,w4):b;return e},a)},w5],w9=function(d,c,b,a){return 2},w_=[0,function(e,c,a,b){return V(function(a){var
  c=a[2][1],b=d(a[1][1],w7);return b?V(function(a){return d(a,w8)},c):b},a)},w9],xa=function(d,c,b,a){return 1},xb=[0,function(e,c,a,b){return V(function(a){return d(a[1][1],w$)},a)},xa],xe=function(d,c,b,a){return 2},xg=[0,function(f,e,h,g){var
  a=bX[1];if(a){var
  b=d(f,xf);if(b)return V(function(a){var
  c=a[2],b=d(a[1],xc),e=b?d(c,xd):b;return e},e);var
  c=b}else
  var
  c=a;return c},xe],xj=function(d,c,b,a){return 2},xk=[0,function(f,b,e,c){var
  a=bX[1];return a?V(function(a){var
  c=a[2],b=d(a[1],xh),e=b?d(c,xi):b;return e},b):a},xj],xl=function(d,a,c,b){if(a&&!g(a[1][1],xm)&&!a[2])return 2;return 0},xo=[0,function(a,b,c,d){return dC(xn,a,b,c,d)},xl],h3=b(z[37],xp),xr=b(z[37],xq),xs=b(b(z[7],h3),xr),xA=function(d,c,b,a){return 1},h4=[0,function(g,f,e,h){var
  a=d(g,xB);if(a)var
  b=0===f?1:0,c=b?0===e?1:0:b;else
  var
  c=a;return c},xA],xC=function(i,h,f,c){if(c){var
  a=c[1];if(0!==a[0]&&!g(a[1],xD)&&!c[2]){var
  j=a[4],k=a[3],l=a[2],e=b(e1(function(a){return e4(xE,a)}),l),d=e[1],m=e[2];if(d&&!d[2])return[0,[1,i,e5(xG,d[1][2],h),f,[0,[1,xF,m,k,j],0]]];return 0}}return 0},xI=[0,function(a,e,c,b){return d(a,xH)},xC],xM=function(q,p,c,n){if(V(function(a){var
  c=a[2][1],b=d(a[1][1],xN),e=b?bN(c,xO):b;return e},c)){var
  b=G(n),g=0;if(b){var
  a=b[1];if(0!==a[0]){var
  i=b[2],j=a[4],k=a[3],l=a[2],m=a[1],o=Q(0,aQ(function(a){var
  b=a[2];return d(a[1][1],xJ)?[0,[0,[0,xK,cu(b)],0]]:0},c)),e=ad(function(a){return a5(a[1][1],xL)},c),h=F(ad(function(a){var
  b=a[1][1];return 1-V(function(a){return d(a[1][1],b)},e)},k),e),f=[0,[1,m,F(o,l),h,j],i];g=1}}if(!g)var
  f=0;return[1,G(f)]}return 0},xQ=[0,function(a,e,c,b){return d(a,xP)},xM],xS=b(z[37],xR),h5=function(a){return f(z[3],a,xS)},xU=b(z[37],xT),x_=function(c,a,b){return h5(a)},fc=function(s,b,a){var
  n=h5(b);if(n)var
  o=n;else{var
  p=f(z[3],b,xU);if(p)var
  j=p;else{var
  l=45===B(b,0)?1:0;if(l)var
  i=l;else{var
  m=a5(b,x8);if(m)var
  r=1-s,i=r||g(b,x9);else
  var
  i=m}var
  j=i}if(j)var
  q=j;else{var
  e=0;if(g(b,xV)&&g(b,xW)){var
  c=0;if(g(b,xX)){var
  d=0;if(g(b,xY))if(g(b,xZ))if(g(b,x0))if(g(b,x1)){if(g(b,x2)){c=1;d=1}}else
  if(a&&!(g(a[1],x4)||a[2])){var
  k=bX[1];e=2;c=1;d=1}else{c=1;d=1}else
  if(a&&!(g(a[1],x5)||a[2])){e=1;c=1;d=1}else{c=1;d=1}else
  d=1;if(!d)if(a&&!(g(a[1],x3)||a[2])){e=1;c=1}else
  c=1}if(!c&&a&&!g(a[1],x6)&&!a[2])e=1}else
  e=3;var
  h=0;switch(e){case
  3:if(a&&!g(a[1],x7)&&!a[2])h=2;break;case
  0:break;case
  1:h=2;break;default:h=1}switch(h){case
  0:var
  k=0;break;case
  2:var
  k=1;break}var
  q=k}var
  o=1-q}return o},ya=b(z[37],x$),h6=function(a,c,b){var
  d=a?a[1]:0,e=c?fc:x_;return ad(function(a){return e(d,a[1][1],a[2][1])},b)},yb=function(a){var
  b=a[2][1],c=a[1][1],e=d(c,yc);if(e)var
  f=hq[1],g=f?V(function(a){return d(a,yd)},b):f;else
  var
  g=e;return g?[0,[0,c],[0,aj(function(a){return d(a,ye)?yf:a},b)]]:a},h7=function(a,c){var
  d=a?a[1]:0;function
  b(g,f,a,e){var
  b=h6([0,d],c,a);return a===b?0:[0,[1,g,f,b,e]]}return[0,function(c,g,b,e){var
  a=0!==b?1:0,d=a?1-f(z[3],c,ya):a;return d},b]},yg=function(b,a){if(0===b[0]){var
  d=b[1][1],c=h6(0,1,b[2]);if(0===c)return a;var
  e=aj(yb,c);return[0,[0,[0,ad(function(a){return g(a,yh)},d)],e],a]}return a},h8=b(z[37],yi),fd=function(a){return ad(function(a){return 1-f(z[3],a[1],h8)},a)},h9=function(g,f,e,a){return ad(function(c){var
  a=c[1][1],h=c[2][1];if(g){var
  b=1-d(a,e);return b?fc(1,a,h):b}return d(a,f)},a)},yl=function(k,e,c,j){var
  b=e0(function(a){return d(a[1],yk)},e);if(b){var
  f=b[1];if(!V(function(a){return d(a[1][1],ym)},c)){var
  a=f[2],g=f[1],i=kj(a)?h(a,yj):a;return[0,[1,k,e,[0,[0,[0,g],[0,[0,i,0]]],c],j]]}}return 0},yo=[0,function(a,e,c,b){return d(a,yn)},yl],yw=[0,uK,[0,wO,[0,wT,[0,w6,[0,w_,[0,xb,[0,xg,[0,xk,0]]]]]]]],h_=[A,yE,be(0)],yB=function(H,E,D,aw,av){var
  c=E?E[1]:0,I=D?D[1]:0,l=hM(aw);if(I)ci(h(yC,az(sx,aj(hK,l))));var
  i=l;for(;;){if(i){var
  j=i[1],K=0;if(1===j[0]&&!g(j[1],qZ)){var
  M=j[4],N=j[2];hq[1]=V(function(a){return as(a[2],q0)},N);var
  q=[0,M];K=1}if(!K){var
  i=i[2];continue}}else
  var
  q=0;if(q){var
  R=q[1],t=e2(function(a){if(1===a[0]&&!g(a[1],uj))return 1;return 0},R),n=t[2],O=t[1],L=0,aC=Q(0,am(hs(dA(hu,dA(ht(un),O))),hF));if(n){var
  k=n[1];if(0!==k[0]&&!g(k[1],ui)){var
  o=[0,k[3],k[4]];L=1}}if(!L)var
  o=[0,0,n];var
  m=[0,aC,o[2],o[1]]}else
  var
  u=hl(function(a){if(1===a[0]){var
  b=a[2];if(b){var
  c=b[1],f=a[1];if(!g(c[1],uk)&&!b[2]){var
  h=c[2],e=dC(ul,f,b,a[3],a[4]),i=e?d(dh(h),um):e;return i}}}return 0},l),v=u[2],P=u[1],m=v?[0,Q(0,am(hs(dA(hu,dA(ht(up),[0,[1,uo,0,0,P],0]))),hF)),v,0]:[0,0,l,0];var
  s=m[1],ax=m[3],ay=m[2],au=0,at=[0,xQ,[0,h7(yy,c),[0,h4,0]]],W=function(o,m,f,l,k){function
  a(p){var
  n=p[1][1],q=[0,[1,o,m,f,l],k];function
  i(t,j){var
  a=t;for(;;){if(a){var
  c=a[1];if(0!==c[0]){var
  k=a[2],f=c[4],g=c[3],l=c[2],m=c[1],o=fb(g);if(o){var
  p=o[1],h=p[2],q=p[1][1];if(j){var
  u=j[1];if(d(q,n)&&h===u){var
  r=i(k,j);return[0,[0,[0,h,[0,m,l,g,f]],r[1]],r[2]]}}else
  if(d(q,n)){var
  s=i(k,[0,h]);return[0,[0,[0,h,[0,m,l,g,f]],s[1]],s[2]]}return[0,0,a]}var
  b=hV(at,m,l,g,f);if(typeof
  b==="number")switch(b){case
  0:var
  e=0;break;case
  1:var
  e=[0,f];break;default:var
  e=vf}else
  var
  e=0===b[0]?[0,[0,b[1],0]]:[0,b[1]];if(e){var
  a=F(e[1],k);continue}return[0,0,a]}}return[0,0,a]}}var
  b=i(q,0),a=b[1],r=b[2];function
  e(k,j){var
  a=k,b=j;for(;;){if(b){var
  f=b[1],g=f[2],d=f[1],l=b[2];if(a){var
  h=a[1],c=h[1],m=a[2],n=h[2];if(c<d){var
  o=d===(c+1|0)?0:[0,[0,c+1|0,hX(g),0],0],i=e(o,b),p=i[2],a=[0,[0,c,n,G(i[1])],m],b=p;continue}if(d<c)return[0,a,b]}var
  a=[0,[0,d,g,0],a],b=l;continue}return[0,a,0]}}if(a)var
  c=a[1],h=c[2],j=1===c[1]?0:[0,[0,1,hX(h),0],0],g=G(e(j,a)[1]);else
  var
  g=0;return[0,F(hY(s,n,g),r)]}return Q(0,am(fb(f),a))},aA=dF(0,yw,[0,ve,[0,u3,[0,[0,function(a,e,c,b){return d(a,v3)},W],au]]],ay),ah=[0,h4,[0,xI,0]],af=b(z[37],yt),ag=function(g,b,a,f){var
  d=fd(b),e=h9(c,yv,yu,a);if(d===b&&e===a)return 0;return[0,[1,g,d,e,f]]},ai=[0,[0,function(a,d,c,b){return f(z[3],a,af)},ag],ah],ae=function(g,b,a,f){var
  d=fd(b),e=h9(c,yr,yq,a);if(d===b&&e===a)return 0;return[0,[1,g,d,e,f]]},ak=[0,[0,function(a,e,c,b){return d(a,ys)},ae],ai],ac=function(h,b,a,g){var
  d=fd(b),e=ad(function(a){var
  b=a[1][1],d=a[2][1];return c?fc(1,b,d):f(z[3],b,h8)},a);if(d===b&&e===a)return 0;return[0,[1,h,d,e,g]]},al=[0,yo,[0,[0,function(a,e,c,b){return d(a,yp)},ac],ak]],ab=function(d,c,b,a){return a?1:2},an=[0,[0,function(a,h,g,e){if(c)return 0;var
  d=b(z[37],av);return f(z[3],a,d)},ab],al],ap=[0,xo,[0,w2,[0,wb,[0,h7(yx,c),an]]]],aa=function(e,c,b,a){return V(function(b){if(0===b[0]){var
  a=b[1][1];if(a&&!g(a[1],xx)&&!a[2])return 1}return 0},s)?[0,[1,e,ad(function(a){var
  c=a[2],b=d(a[1],xy),e=b?d(c,xz):b;return 1-e},c),b,a]]:1},aq=[0,[0,function(f,a,e,b){return c?V(function(a){var
  c=a[2],b=d(a[1],xv),e=b?d(c,xw):b;return e},a):0},aa],ap],_=c?h3:xs,$=function(g,a,e,c){var
  b=ad(function(c){var
  a=c[1],i=c[2],e=h2(a);if(e)var
  g=e;else
  var
  b=d(a,xt),h=b?d(i,xu):b,g=h||f(z[3],a,_);return 1-g},a);return a===b?0:[0,[1,g,b,e,c]]},r=[0,0],ar=[0,rf,[0,q_,[0,[0,function(d,a,c,b){return 0!==a?1:0},$],aq]]],Y=[0,wv,0],X=function(m,i,u,c){var
  f=[ao,function(h){function
  f(v){var
  b=v[2],f=r[1];try{var
  n=co(b,f)}catch(a){a=C(a);if(a===p){r[1]=[0,[0,b,bh(c)],f];return 0}throw a}if(c){var
  j=c[1];if(0!==j[0]&&!g(j[1],wh)){var
  l=j[4];if(l){var
  t=l[1];if(0!==t[0]&&!g(t[1],wi)&&!l[2]&&!c[2])return 0}}}function
  w(a){return a[1]}var
  x=am(dz(f),w),h=function(a){return Q(b,a)}(x),y=function(a){return dB(a,58)}(b),z=c7(function(a){return ey(a,1)}(y)),A=function(a){return dB(a,58)}(b),k=function(a){return ey(a,0)}(A),B=function(a){return dB(a,58)}(h),D=c7(function(a){return ey(a,1)}(B)),E=dz(function(a){return dB(a,58)}(h)),o=function(a){return Q(k,a)}(E),q=z<D?1:0,F=d(h,b),G=F||q||g(o,k),s=G?a(e+(n+1|0)):a(f1),H=d(h,b),I=H||q||g(o,k),J=I?n+bh(c)|0:bh(c);r[1]=[0,[0,b,J],f];if(!d(m,we)&&!d(s,wf))return[0,[1,m,[0,[0,wg,s],i],u,c]];return 0}return Q(0,am(b(aI(wj),i),f))}];function
  h(a){return 0}return hi(f,am(b(aI(wk),i),h))},Z=dF(wK,[0,[0,function(a,g,f,e){var
  b=d(a,wl),c=b||d(a,wm);return c},X],ar],Y,aA),B=function(i,h){var
  a=i,c=h;for(;;){if(a){var
  b=a[1];if(0===b[0]){var
  j=a[2],a=j,c=h0(c,[0,b,0]);continue}var
  d=a[2],e=b[1],k=b[3],l=b[2],g=B(b[4],0);if(f(z[3],e,wd)&&0===g){var
  a=d;continue}var
  a=d,c=h0(c,[0,[1,e,l,k,hZ(g)],0]);continue}return c}},aD=dF(wM,[0,wJ,0],0,dF(wL,0,[0,wG,0],B(Z,0))),J=c?gz(yg,s,0):0,aB=c?eZ(function(a){var
  b=a[2];return d(a[1][1],yz)?[0,[0,yA,cu(b)]]:0},ax):0,S=H?H[1]:1,w=0===J?0:[0,[1,uu,0,0,[0,[0,az(rP,aj(rC,J))],0]],0],x=S?[0,uq,0]:0,y=0===x?1:0,T=y?0===w?1:0:y,U=T?0:[0,[1,ut,0,0,F(x,w)],0],A=[1,us,0,0,F(U,[0,[1,ur,aB,0,aD],0])];if(I)ci(h(yD,hK(A)));return[0,A,0]}};nt(yF,[0,h_,[0]]);(function(a){throw a});var
  cv=x,fe=null,ff=undefined,dH=function(a){return a==fe?0:[0,a]},yG=cv.Array;gZ(function(a){return a[1]===h_?[0,ai(a[2].toString())]:0});gZ(function(a){return a
  instanceof
  yG?0:[0,ai(a.toString())]});var
  h$=cv.document;cv.HTMLElement===ff;var
  yH=z6(0),ia=function(b,a){return as(a,b)},yI=0,yN=[0,[0,yM,0,function(a){var
  c=as(a,yJ),b=c||as(a,yL);return b?as(a,yK):b}],yI],yQ=[0,[0,yP,0,function(a){return ia(yO,a)}],yN],yT=[0,[0,yS,0,function(a){return ia(yR,a)}],yQ],yX=[0,[0,yW,0,function(a){var
  b=as(a,yU);return b?b:as(a,yV)}],yT],y2=[0,[0,y1,y0,function(a){var
  b=as(a,yY),c=b?1-as(a,yZ):b;return c}],yX],y8=[0,[0,y7,0,function(a){var
  c=as(a,y3);if(c){var
  d=as(a,y4);if(d){var
  e=as(a,y5);if(e)return as(a,y6);var
  b=e}else
  var
  b=d}else
  var
  b=c;return b}],y2],y9=ai(cv.navigator.userAgent),y_=function(k){var
  a=y8,h=dh(y9);for(;;){if(a){var
  c=a[1],g=a[2];if(!b(c[3],h)){var
  a=g;continue}var
  e=[0,c]}else
  var
  e=0;var
  j=gy(function(a){return[0,a[1]]},e),f=gy(function(a){return d(a[1],y$)},j),i=0;return f?f[1]:i}},ib=function(a){var
  b=bN(a,fe)?ff:a;return b===ff?0:[0,b]},za=function(a){return d(ai(typeof
  a),zb)},zc=function(d){var
  a=d.src,b=a.length;if(0<b){var
  c=a.indexOf(fM),e=c<0?b:c,f=iP["length"];return(a.indexOf(iP)+f|0)===e?1:0}return 0},ic=function(a){var
  b=a.split(bu),c=[0,cv];return b.reduce(z8(function(a,b,d,c){return a?ib(a[1][b]):a}),c)},id=function(a){if(a){var
  b=a[1],c=dH(b.getAttribute(jn));if(c){var
  d=c[1];b.removeAttribute(jn);return dx(za,ic(d))}return 0}return 0},zd=[ao,function(g){var
  d=h$.getElementsByTagName(ap),b=0,a=0,e=d.length;for(;;){if(a<e){var
  c=dH(d.item(a));if(c){var
  b=[0,c[1],b],a=a+1|0;continue}var
  a=a+1|0;continue}var
  f=G(b);return aQ(function(a){var
  b=a.tagName.toLowerCase()===ap?a:fe;return id(dx(zc,dH(b)))},f)}}],ze=id(ib(h$.currentScript)),ie=function(a){return dw(zd,a)}(ze);bX[1]=y_(0);var
  fg=[0,0,0,0],zf=function(n,m){if(!fg[1]){if(0===dI)var
  a=g5([0]);else{var
  d=g5(gK(nv,dI)),g=dI.length-1-1|0,k=0;if(!(g<0)){var
  c=k;for(;;){var
  h=(c*2|0)+2|0;d[3]=aJ(dq[4],dI[1+c],h,d[3]);d[4]=aJ(dr[4],h,1,d[4]);var
  l=c+1|0;if(g!==c){var
  c=l;continue}break}}var
  a=d}var
  i=nB(a,zg),e=nz(a,zh),j=function(c){var
  a=c[1+i];return b(a[2],a[1])};nx[1]++;if(f(dr[28],e,a[4])){eQ(a,e+1|0);w(a[2],e)[1+e]=j}else
  a[6]=[0,[0,e,j],a[6]];var
  o=function(d){var
  b=j6(A,a[1]);b[1]=a[2];var
  c=Ap(b);c[1+i]=d;return c};g6[1]=(g6[1]+a[1]|0)-1|0;a[8]=G(a[8]);eQ(a,3+((w(a[2],1)[2]*16|0)/32|0)|0);fg[1]=o}return b(fg[1],[0,n,m])},zi=function(n,m,l,j){var
  e=dH(j);if(e){var
  d=gK(ai,z7(e[1])),a=d.length-1-1|0,b=0;for(;;){if(0<=a){var
  h=[0,d[1+a],b],a=a-1|0,b=h;continue}var
  f=b;break}}else
  var
  f=0;var
  g=ai(m),i=hI(yB(0,[0,l|0],[0,0],g,f)),c=gP(k(g));pY(eR,1,function(a){return bU(c,a)},i);return gk(gQ(c))},ig=function(a,b){return{"cleanDocument":z9(a)}}(zi,zf),zj=function(c,b){var
  a=ic("tinymce.Resource.add");return a?a[1](c,b):yH.error("Unable to find Word Import registration API")},zk=function(a){return a(ig)};if(ie)zk(ie[1]);else
  zj(zl,ig);ew(0);return}}(globalThis));