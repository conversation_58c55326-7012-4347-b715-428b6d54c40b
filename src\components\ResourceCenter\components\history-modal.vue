<template>
  <el-dialog
    v-model="show"
    title="文件操作历史记录"
    width="1000"
    append-to-body
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    @closed="emit('close')"
  >
    <template #default>
      <div v-loading="loading" style="min-height: 600px;">
        <CustomTable
          ref="tableRef"
          :data="tableData"
          :columns="columns"
          :table-method="getList"
          height="600"
        />
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="tsx">
import { ref } from 'vue'
import { ElMessage } from 'element-plus'
import useLoading from '@/hooks/use-loading'
import useTryCatch from '@/hooks/use-try-catch'
import { getFileLog } from '@/api/resource-center'
import type { fileLogItem } from '../types'

import CustomTable from '@/components/CustomTable/index.vue'

defineOptions( { name: 'FileHistory' } )

const props = defineProps<{
  logId: number;
}>()

const emit = defineEmits(['close'])

const { loading, setLoading } = useLoading()
const show = ref(true)

const tableRef = ref()
const tableData = ref<fileLogItem[]>([])
const columns = ref([
  {
    title: 'ID',
    dataKey: 'id'
  },
  {
    title: '操作人',
    dataKey: 'add_name'
  },
  {
    title: '操作时间',
    dataKey: 'add_time'
  },
  {
    title: '操作类型',
    dataKey: 'action_type_name'
  }
])

const getList = ( { page, pageSize } ) => {
  useTryCatch( async () => {
    setLoading(true)

    const params = {
      resource_id: props.logId,
      action_type: '',
      page,
      page_size: pageSize
    }

    const res = await getFileLog(params)
    if (res.code === 200) {
      tableData.value = res.data.list
      tableRef.value?.setTotal( res.data.total )
    } else {
      ElMessage.error(res.msg)
    }

    setLoading(false)
  }, () => setLoading(false) )
}
</script>