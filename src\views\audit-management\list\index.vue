<template>
  <div class="scroll-y with-flex">
    <QueryForm
      :form-list="formList"
      :loading="loading"
      @search="handleSearch" 
      @reset="reset"
    />
    <div v-loading="loading" class="sticky-table">
      <CustomTable
        ref="tableRef"
        :columns="columns"
        :data="tableData"
        :table-method="getList"
        :index-method="indexMethod"
        :selectable="selectable"
        :operate-list="operateList"
        @selection-change="handleSelectionChange"
      />
    </div>

    <AuditDialog 
      ref="auditDialogRef" 
      v-if="loadAuditDiglog" 
      :task-id="taskIds"
      @success="handleAuditSuccess"
    />
  </div>
</template>

<script setup lang="tsx">
import { ref, reactive, toRef, computed, defineAsyncComponent, onActivated } from 'vue'
import { ElMessage, ElButton, ElNotification } from 'element-plus'
import { useRoute, useRouter } from 'vue-router'
import { storeToRefs } from 'pinia'
import { useParamsStore } from '@/store'
import useLoading from '@/hooks/use-loading'
import useTryCatch from '@/hooks/use-try-catch'
import { getCheckList } from '@/api/audit-management'
import type { searchParams, listItem } from '@/api/audit-management'
import Listener from '@/utils/site-channel-listeners'

import QueryForm from '@/components/QueryForm/index.vue'
import CustomTable from '@/components/CustomTable/index.vue'

const AuditDialog = defineAsyncComponent(() => import('../components/audit-dialog.vue'))

defineOptions({ name: 'ReviewList' })

const router = useRouter()
const route = useRoute()
const { site_id_all, channel_item_all } = storeToRefs(useParamsStore())
const { loading, setLoading } = useLoading()

const { typeName } = route.query
const statusList = [
  {
    label: '流程中',
    value: 2
  },
  {
    label: '已通过',
    value: 3
  },
  {
    label: '已拒绝',
    value: 4
  },
  {
    label: '已撤销',
    value: 5
  },
]
const queryParams_raw = {
  status: '',
  start_time: '',
  end_time: '',
  task_id: '',
  group: typeName ? typeName as string : '',
  task_ids: ''
}

const queryParams = reactive<searchParams>({
  ...queryParams_raw,
  site_id: site_id_all.value,
  channel_id: channel_item_all.value.channel_id,
})
const tableRef = ref()
const auditDialogRef = ref()
const taskIds = ref('')
const loadAuditDiglog = ref(false)
const formList = ref([
  {
    label: '全部审核',
    clearable: true,
    placeholder: '全部审核',
    value: toRef(queryParams, 'group'),
    component: 'select',
    selections: [
      {
        label: '待办',
        value: 'pending'
      },
      {
        label: '已办',
        value: 'done'
      },
    ],
    handleChange: (value: string) => {
      if (value === 'done') {
        taskIds.value = ''
      }
      handleSearch()
    }
  },
  {
    label: '流程ID',
    placeholder: '流程ID, 多个,隔开',
    value: toRef(queryParams, 'task_ids'),
    component: 'input',
  },
  {
    label: '状态',
    clearable: true,
    placeholder: '状态',
    value: toRef(queryParams, 'status'),
    component: 'select',
    selections: statusList
  },
  {
    label: '时间日期',
    clearable: true,
    start_placeholder: '开始日期',
    end_placeholder: '结束日期',
    placeholder: '审核人',
    value: '',
    component: 'datetimerange',
    dateChange: (value: [string, string] | null) => {
      if (value) {
        queryParams.start_time = value[0]
        queryParams.end_time = value[1]
      } else {
        queryParams.start_time = ''
        queryParams.end_time = ''
      }
    }
  }
])
const columns = ref([
  {
    title: '流程ID',
    dataKey: 'task_id',
    width: 200
  },
  {
    title: '业务类型',
    dataKey: 'type_name',
  },
  {
    title: '标题',
    dataKey: 'name',
    minWidth: 250,
  },
  {
    title: '渠道名',
    dataKey: 'channel_name',
  },
  {
    title: '当前审核人',
    dataKey: 'handlers',
  },
  {
    title: '审核状态',
    dataKey: 'status_name',
    width: 100,
    cellRenderer: (scope: { row: listItem }) => {
      return (
        <span style={ { color: scope.row.status === 3 ? 'var(--el-color-success)' : scope.row.status === 4 ? 'var(--el-color-error)' : '' } }>
          {scope.row.status_name}
        </span>
      )
    }
  },
  {
    title: '申请人',
    dataKey: 'user_name',
  },
  {
    title: '申请时间',
    dataKey: 'add_time',
    width: 180,
  },
  {
    title: '操作',
    dataKey: '',
    width: 120,
    cellRenderer: (scope: { row: listItem }) => {
      return (
        <ElButton
          type='primary'
          plain
          onClick={() => {
            handleJumpDetail(scope.row)
          }}
        >
          查看详情
        </ElButton>
      )
    }
  },
])
const operateList = ref([{
  title: '批量审核',
  method: () => {
    if (!loadAuditDiglog.value) {
      loadAuditDiglog.value = true
    } else {
      auditDialogRef.value.handleShow()
    }
  },
  disabled: computed(() => !taskIds.value ? true : false)
}])
const tableData = ref<listItem[]>([])

const selectable = computed(() => queryParams.group !== 'done')

const getList = ( { page, pageSize } ) => {
  useTryCatch( async () => {
    setLoading(true)
    const params = {
      q: { ...queryParams, site_id: site_id_all.value, channel_id: channel_item_all.value.channel_id },
      page,
      limit: pageSize
    }
    const res = await getCheckList(params)
    if (res.code === 200) {
      tableData.value = res.data.list
      tableRef.value.setTotal(res.data.total)
    } else {
      ElMessage.error(res.msg)
    }
    setLoading(false)
  }, () => setLoading(false))
}
const reset = () => {
  Object.entries(queryParams_raw).forEach(( [ key, value ] ) => {
    queryParams[key] = value
  })
}
const handleSearch = () => {
  tableRef.value.setPage(1)
  const { page, pageSize } = tableRef.value
  getList( { page, pageSize } )
}
const indexMethod = (index: number) => {
  const { page, pageSize } = tableRef.value.getPagination()
  return (page - 1) * pageSize + index + 1
}
const handleSelectionChange = (selections: listItem[]) => {
  if (selections.length > 20) {
    ElNotification({
      title: '提示',
      message: '单次任务处理数不能超出20',
      type: 'warning',
    })
    taskIds.value = ''
    return
  }
  taskIds.value = selections.map( ({ task_id }) => task_id ).join(',')
}
const handleJumpDetail = (row: listItem) => {
  router.push({
    name: 'ReviewDetail',
    query: {
      id: row.task_id
    }
  })
}
const handleAuditSuccess = () => {
  handleSearch()
  ElMessage.success('批量审核成功')
}

onActivated(() => {
  const { typeName } = route.query
  if (typeName !== queryParams.type) {
    queryParams.group = typeName as string
    handleSearch()
  }
})

Listener(route, () => {
  handleSearch()
})
</script>