import axiosReq from './axios'
import qs from 'query-string'

export type queryParams = {
  channel_id?: string|number;
  site_id: number;
  blk_name: string;
  blk_type: string;
  language: string;
  user_id_a: string|number;
  user_id_e: string|number;
  user_id_c: string|number;
  created_state: string;
  tpl_ids: string;
  art_ids: string;
  blk_ids: string;
  file_path: string;
  is_main: string;
  time_field: string;
  start_time?: string;
  end_time?: string;
}

export type listQueryParams = {
  S: queryParams;
  page: number;
  page_size: number;
  sort_key?: string;
  sort_type?: string;
  is_public?: number;
}

export type listItem = {
  blk_id: number;
  blk_level: string;
  blk_name: string;
  blk_type: number;
  parent_id: number;
  channel_name: string;
  color: string;
  template_quote_count: number;
  article_quote_count: number;
  file_path: string;
  created_state: string;
  created_state_name: string;
  user_name_a: string;
  add_time: string;
  user_name_e: string;
  edit_time: string;
  user_name_c: string;
  create_time: string;
}

export type blockData = {
  site_id: number;
  channel_id?: number|string;
  blk_name: string;
  blk_type: number|string;
  language: string;
  html_path?: string;
  file_path: string;
  content: string;
  remark: string;
  parent_id?: number;
  level?: string;
}

export type historyListItem = {
  add_time: string;
  blk_name: string;
  edit_time: string;
  entity_id: number;
  es_id: string;
  file_path: string;
  id: number;
  remark: string;
  site_id: number;
  state: string;
  type: number;
  user_id_a: number;
  user_id_e: number;
  user_name: string;
  version: number;
}

export type comparisonItem = {
  add_time: string;
  blk_id: number;
  blk_name: string;
  blk_type: number;
  channel_id: number;
  content: string;
  create_time: string;
  created_state: string;
  edit_time: string;
  file_path: string;
  language: string;
  parent_id: number;
  remark: string;
  site_id: number;
  src_id: number;
  src_version: string;
  state: string;
  user_name: string;
  version: string|number;
}

// 获取块管理列表
export const getBlockList = (params: listQueryParams) => 
axiosReq('get', '/api/v1/blocks', {
  params,
  paramsSerializer: {
    serialize: (obj: listQueryParams) => qs.stringify({
      S: JSON.stringify(obj.S),
      page: obj.page,
      page_size: obj.page_size,
      ...obj.sort_type ? {
        sort_key: obj.sort_key,
        sort_type: obj.sort_type
      } : {},
      ...obj.is_public ? { is_public: obj.is_public } : {}
    })
  }
})

// 添加块
export const addBlock = (data: { data: blockData }) => 
  axiosReq('post', `/api/v1/blocks`, data)

// 编辑块
export const editBlock = (data: { data: blockData }, id: number) => 
  axiosReq('put', `/api/v1/blocks/${id}`, data)

// 获取块详情
export const getBlockDetail = (blk_id: number) => 
  axiosReq('get', `/api/v1/public/entities/block/${blk_id}`)

// 查看块历史版本列表
export const getBlockHistory = (params: { blk_id: string|number; site_id: number; page: Number; page_size: number }) => 
  axiosReq('get', `/api/v1/blocks/${params.blk_id}/versions?site_id=${params.site_id}&page=${params.page}&page_size=${params.page_size}`)

// 回退至指定版本
export const restoreBlockVersion = (blk_id: number|string, version_id: number, site_id: number) => 
  axiosReq('post', `/api/v1/blocks/${blk_id}/versions/${version_id}/restoration?site_id=${site_id}`,)

// 查看历史版本详情
export const getHistoryDetail = (blk_id: number|string, version: number, site_id: number,) => 
  axiosReq('get', `/api/v1/blocks/${blk_id}/versions/comparisons?site_id=${site_id}&version_ids=${version}`)

// 版本对比
export const getComparisons = (site_id: number, blk_id: string|number, version_ids: string) => 
  axiosReq('get', `/api/v1/blocks/${blk_id}/versions/comparisons?site_id=${site_id}&version_ids=${version_ids}`)

// 获取字块列表
export const getChildList = (blk_id: string|number) => 
  axiosReq('get', `/api/v1/blocks/${blk_id}/channels`)

// 获取引用公共块的块列表
export const getEmbededList = ( params: { blk_id: number; created_state: string; page: number; page_size: number; blk_name: string; } ) => 
  axiosReq('get', `/api/v1/blocks/embedded/${params.blk_id}?created_state=${params.created_state}&page=${params.page}&page_size=${params.page_size}&blk_name=${params.blk_name}`)