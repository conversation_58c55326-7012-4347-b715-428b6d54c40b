import axiosReq from './axios'
import qs from 'query-string'

export type queryParams = {
  id: string;
  cms_id: string;
  site_name: string;
  html_path: string;
  channel_code: string;
  language: string;
  cms_state: string;
}

export type listQueryParams = {
  S: queryParams;
  page: number;
  page_size: number;
}

export type listItem = {
  channel_code: string;
  channel_name: string;
  channels: string;
  cms_can_use: string;
  cms_id: number;
  cms_state: string;
  color: string;
  edit_time: string;
  html_path: string;
  id: number;
  language: string;
  site_name: string;
  task_head_state: string;
  task_id: number;
  task_site_info: string;
  task_state: string;
  type: string;
  user_name: string;
}

export type langListItem = {
  lang_code: string;
  lang_id: number;
  name: string;
  smart_lang_code: string;
}

export type langData = {
  lang_code: string;
  smart_lang_code: string;
  name: string;
}

export type channelListItem = {
  add_time: string;
  channel_code: string;
  channel_language: string;
  channel_language_name: string;
  channel_name: string;
  color: string;
  edit_time: string;
  id: number;
}

export type channelData = {
  channel_code: string;
  channel_name: string;
  channel_language: string;
  color: string;
  id?: number|string;
}

export type siteData = {
  name: string;
  html_path: string;
  image_path: string;
  video_path: string;
  host: string;
  host_js: string;
  host_css: string;
  host_image: string;
  host_video: string;
  brand: string;
  main_currency: string;
  language: string;
  state: string;
  channel_code: string;
  secondary_dir: string;
  site_file_limits?: string;
  leads_mail: string;
  syn_mail: string;
  examiner: string;
  is_check_cms: string|number;
  is_check_task: string|number;
  task_site_name: string;
  task_site_host: string;
  task_site_path: string;
  task_site_is_cdn?: string;
  task_site_service_ids?: string;
  task_site_state: string;
  is_create_audit: number;
  cms_site_id?: number;
  task_site_id?: number;
}

export type siteChannelItem = {
  channel_code: string;
  channel_id: number;
  channel_name: string;
  color: string;
  edit_time: string;
  host: string;
  host_css: string;
  host_image: string;
  host_js: string;
  host_video: string;
  html_path: string;
  image_path: string;
  is_cdn: string;
  language: string;
  language_name: string;
  name: string;
  secondary_dir: string;
  service_ids: string;
  site_id: number;
  site_name: string;
  state: string;
  state_name: string;
  task_head_state: string;
  type: string;
  type_name: string;
  user_name: string;
  video_path: string;
}

export type siteChannelData = {
  channel_code: string;
  name: string;
  site_id?: number;
  language: string;
  host: string;
  html_path: string;
  image_path: string;
  video_path: string;
  host_js: string;
  host_css: string;
  host_image: string;
  host_video: string;
  state: string;
  is_create_audit: number;
}

export type announceItem = {
  add_time: string;
  announcement_id: number;
  content: string;
  edit_time: string;
  is_read: number;
  is_top: number;
  is_top_name: string;
  publish_time: string;
  remark: string;
  state: string;
  state_name: string;
  title: string;
  type: string;
  type_name: string;
  user_name: string;
  user_name_p: string;
}

export type announceForm = {
  announcement_id?: number;
  title: string;
  content: string;
  type: string;
  is_top: number;
  remark: string;
}

export type permissionQueryParams = {
  site_id: string|number;
  role_id: string|number;
  real_name: string;
  wsid: string|number;
  state: string;
  department: string;
  email: string;
  phone: string;
  page?: number;
  page_size?: number;
}

export type permissionListItem = {
  department: string;
  edit_time: string;
  email: string;
  phone: string;
  real_name: string;
  role_ids: string;
  role_name: string;
  state: string;
  wsId: string;
  user_parent_id: string;
  user_id: number;
}

export type menuTree =  {
  name: string;
  route: string;
  method: string;
  type: number;
  child : menuTree[];
}

export type menuData = {
  name: string;
  route: string;
  method: string;
  type: number;
  route_md5: string;
  parent_route_md5: string;
  id: number;
  children : menuData[];
}

export const getList = (params: listQueryParams) => 
  axiosReq('get', '/api/v1/sites', {
    params,
    paramsSerializer: {
      serialize: (obj: listQueryParams) => qs.stringify({
        S: JSON.stringify(obj.S),
        page: obj.page,
        page_size: obj.page_size,
      })
    }
  })

export const getLangList = () => axiosReq('get', '/api/v1/sites/langs')
// 添加语言
export const addLang = (data: langData) => axiosReq('post', '/api/v1/sites/langs', { data })
// 编辑语言
export const editLang = (data: langData, lang_id: string|number) => axiosReq('put', `/api/v1/sites/langs/${lang_id}`, { data })

export const getChannelList = (params: { page: number; page_size: number }) => 
  axiosReq('get', `/api/v1/sites/channel_code?page=${params.page}&page_size=${params.page_size}`)
// 添加渠道
export const addChannel = (data: channelData) => axiosReq('post', '/api/v1/sites/channel_code', data)
// 编辑渠道
export const editChannel = (data: channelData) => axiosReq('post', `/api/v1/sites/channel_code`, data)

// 添加站点
export const addCmsSite = (data: siteData) => 
  axiosReq<any, { code: number; msg: string; data: { cms_site_id: number; task_site_id: number; map_site_id: number; }}>('post', '/api/v1/site', { data })
// 编辑站点
export const editCmsSite = (data: siteData, id: string|number) => 
  axiosReq('put', `/api/v1/site/${id}`, { data })

// 禁用&启用站点
export const siteStatus = (id: number, data: { state: 'able'|'disable' }) => 
  axiosReq('put', `/api/v1/site/${id}/state`, { data })

// 获取站点详情
export const siteDetail = (id: string|number) => 
  axiosReq('get', `/api/v1/sites/info?id=${id}`)

// 子渠道列表
export const siteChannelList = (params: { site_id: number; page: number; page_size: number; }) => 
  axiosReq('get', `/api/v1/channels`, {
    params,
    paramsSerializer: {
      serialize: (obj: any) => qs.stringify({
        S: JSON.stringify({ site_id: obj.site_id }),
        page: obj.page,
        page_size: obj.page_size,
      })
    }
  })

// 添加站点子渠道
export const addSiteChannel = (data: siteChannelData) => axiosReq('post', `/api/v1/channel`, { data })
// 编辑站点子渠道
export const editSiteChannel = (data: siteChannelData, id: number) => axiosReq('put', `/api/v1/channel/${id}`, { data })

// 禁用&启用站点子渠道
export const siteChannelStatus = (id: number, data: { state: 'able'|'disable' }) => 
  axiosReq('put', `/api/v1/channel/${id}/state`, { data })

/* 公告配置 */
// 配置列表
export const siteAnnounceList = (params: { type: string|number; state: string; is_top: string|number; page: number; page_size: number; }) => 
  axiosReq('get', '/api/v1/announcement', {
    params,
    paramsSerializer: {
      serialize: (obj: any) => qs.stringify({
        S: JSON.stringify({ type: obj.type, state: obj.state, is_top: obj.is_top }),
        page: obj.page,
        page_size: obj.page_size,
      })
    }
  })

// 添加公告
export const addAnnounce = (data: announceForm) => 
  axiosReq('post', '/api/v1/announcement', { data })

// 编辑公告
export const editAnnounce = (data: announceForm, announcement_id: number) => 
  axiosReq('put', `/api/v1/announcement/${announcement_id}`, { data })

// 发布公告
export const publishAnnounce = (announcement_ids: string) => 
  axiosReq('put', `/api/v1/announcement/state`, { data: { announcement_ids, state: 'able'} })

// 删除公告
export const deleteAnnounce = (announcement_ids: string) => 
  axiosReq('put', `/api/v1/announcement/state`, { data: { announcement_ids, state: 'deleted'} })
/* 公告配置 */

/* 站点权限 */
// 角色列表
export const permissionList = (params: permissionQueryParams) => 
  axiosReq('get', '/api/v1/users/list', {
    params,
    paramsSerializer: {
      serialize: (obj: permissionQueryParams) => qs.stringify(obj)
    }
  })

// 兴云数据同步
export const syncWSUsers = () => axiosReq('post', '/api/v1/users/sync_user')

// 用户角色部门列表
export const userRoleList = () =>
  axiosReq('get', '/api/v1/users/department_role')

// 用户路由权限列表
export const userPermissionList = () => 
  axiosReq('get', '/api/v1/users/user_permission_list')

// 保存CMS角色
export const saveRole = (data: { role_id: string; cms_name: string; cms_desc: string; }) => 
  axiosReq('post', '/api/v1/users/save_role_info', data)

// 删除CMS角色配置
export const deleteRole = (role_id: number|string) => 
  axiosReq('post', `/api/v1/users/del_role_info`, { role_id })

// 角色路由权限
export const rolePermission = (role_id: number) => 
  axiosReq('get', `/api/v1/users/get_role_permission?role_id=${role_id}`)

// 用户路由权限树
export const userPermissionTree = () => 
  axiosReq('get', `/api/v1/users/user_permission_tree`)

// 编辑角色路由权限
export const editRolePermission = (data: { role_id: number; route_id: string; }) => 
  axiosReq('post', `/api/v1/users/edit_role_permission`, data)

// 完整路由树
export const allRouteTree = () => 
  axiosReq('get', `/api/v1/users/all_route_tree`)

// 保存路由菜单
export const saveRoute = (data: menuTree) =>
  axiosReq('post', `/api/v1/users/save_route_menu`, data)

// 用户信息编辑
export const editUserInfo = (data: { user_id: number; state: string; user_parent_id: string; }) => 
  axiosReq('post', `/api/v1/users/edit_info`, data)
/* 站点权限 */