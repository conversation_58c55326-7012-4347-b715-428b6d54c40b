import { resolve } from 'path'
import { defineConfig, loadEnv } from 'vite'
import vue from '@vitejs/plugin-vue'
import vueJsx from '@vitejs/plugin-vue-jsx'
import { createSvgIconsPlugin } from 'vite-plugin-svg-icons'
import { viteMockServe } from 'vite-plugin-mock'
import { createHtmlPlugin } from 'vite-plugin-html'
import UnoCSS from 'unocss/vite'
import { presetAttributify, presetIcons, presetUno } from 'unocss'
import mkcert from 'vite-plugin-mkcert'
import DefineOptions from 'unplugin-vue-define-options/vite'
import AutoImport from 'unplugin-auto-import/vite'
import setting from './src/settings'
const prodMock = setting.openProdMock
// import { visualizer } from 'rollup-plugin-visualizer'
const pathSrc = resolve(__dirname, 'src')
export default defineConfig(({ command, mode }) => {
  const env = loadEnv(mode, process.cwd(), '') //获取环境变量
  return {
    base: '/',  // 非常重要，一定不要用./，否则生产环境下非根路径的刷新全崩 !!!!!!
    define: {
      //define global var
      GLOBAL_STRING: JSON.stringify('i am global var from vite.config.js define'),
      GLOBAL_VAR: { test: 'i am global var from vite.config.js define' }
    },
    publicDir: 'public',
    clearScreen: false, //设为 false 可以避免 Vite 清屏而错过在终端中打印某些关键信息
    envPrefix: ['VITE_', 'VUE_'],
    server: {
      hmr: { overlay: false }, //设置 server.hmr.overlay 为 false 可以禁用开发服务器错误的屏蔽。方便错误查看
      port: 80, // 类型： number 指定服务器端口;
      open: false, // 类型： boolean | string在服务器启动时自动在浏览器中打开应用程序；
      host: 'beta-cms40.wondershare.cn',
      https: false,
      proxy: {
        [env.VUE_APP_BASE_API]: {
          target: `http://dev-cms40.300624.cn`,
          changeOrigin: true,
          pathRewrite: {
            ['^' + env.VUE_APP_BASE_API]: ''
          }
        },
        [env.VUE_APP_BASE_API40]: {
          target: `http://dev-cms40.wondershare.cn`,
          changeOrigin: true,
          pathRewrite: {
              ['^' + env.VUE_APP_BASE_API40]: ''
          }
        }
      },
    },
    preview: {
      port: 80,
      host: true,
      strictPort: true,
      proxy: {
        '/api': {
          target: 'http://dev-cms40.wondershare.cn',
          changeOrigin: true,
          pathRewrite: {
            ['^' + '/api']: ''
          }
        }
      }
    },
    plugins: [
      vue({ reactivityTransform: true }),
      vueJsx(),
      UnoCSS({
        presets: [presetUno(), presetAttributify(), presetIcons()]
      }),
      DefineOptions(),
      mkcert(),
      //compatible with old browsers
      // legacy({
      //   targets: ['chrome 52'],
      //   additionalLegacyPolyfills: ['regenerator-runtime/runtime']
      // }),
      createSvgIconsPlugin({
        iconDirs: [
          resolve(process.cwd(), 'src/icons/common'), 
          resolve(process.cwd(), 'src/icons/nav-bar'), 
          resolve(process.cwd(), 'src/icons/files'),
          resolve(process.cwd(), 'src/icons/menu'),
          resolve(process.cwd(), 'src/icons/operate'),
        ],
        symbolId: 'icon-[dir]-[name]'
      }),
      //https://github.com/anncwb/vite-plugin-mock/blob/HEAD/README.zh_CN.md
      viteMockServe({
        supportTs: true,
        mockPath: 'mock',
        localEnabled: command === 'serve',
        prodEnabled: prodMock,
        injectCode: `
          import { setupProdMockServer } from '../mock-prod-server';
          setupProdMockServer();
        `,
        logger: true
      }),
      AutoImport({
        imports: [
          'vue',
          'vue-router',
          {
            'pinia/dist/pinia': ['storeToRefs']
          }
        ],
        eslintrc: {
          enabled: true, // Default `false`
          filepath: './eslintrc/.eslintrc-auto-import.json', // Default `./.eslintrc-auto-import.json`
          globalsPropValue: true // Default `true`, (true | false | 'readonly' | 'readable' | 'writable' | 'writeable')
        },
        dts: './typings/auto-imports.d.ts'
      }),
      // auto config of index.html title
      createHtmlPlugin({
        inject: { data: { title: setting.title } }
      }),
    ],
    build: {
      outDir: 'dist',
      chunkSizeWarningLimit: 10000, //消除触发警告的 chunk, 默认500k
      assetsDir: 'static/assets',
      rollupOptions: {
        output: {
          chunkFileNames: 'static/js/[name]-[hash].js',
          entryFileNames: 'static/js/[name]-[hash].js',
          assetFileNames: 'static/[ext]/[name]-[hash].[ext]'
        }
      },
      assetsInlineLimit: 1024 * 10 // 小于10kb的文件会被转码为base64,开发环境不生效
    },
    resolve: {
      alias: {
        '@/': `${pathSrc}/`,
        'vue-i18n': 'vue-i18n/dist/vue-i18n.cjs.js' //remove i18n waring
      }
    },
    optimizeDeps: {
      //include: [...optimizeDependencies,...optimizeElementPlus] //on-demand element-plus use this
      include: ['moment-mini']
    }
  }
})
