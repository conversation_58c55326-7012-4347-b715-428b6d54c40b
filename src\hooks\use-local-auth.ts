import Cookies from 'js-cookie'

import { getAppName } from './use-env'
const APP_NAME = getAppName()
const defaultTokenKey = `${APP_NAME}AuthToken`

const TOP_DOMAIN = '300624.cn'
const CMS_TOP_DOMAIN = 'wondershare.cn'

// 在顶域设置cookie，dev-pms和dev-wcw可以共享

export function getLocalToken(TokenKey = defaultTokenKey) {
    return Cookies.get(TokenKey, { domain: TOP_DOMAIN })
}

export function setLocalToken(token, TokenKey = defaultTokenKey) {
    return Cookies.set(TokenKey, token, { domain: TOP_DOMAIN })
}

export function removeLocalToken(TokenKey = defaultTokenKey) {
    return Cookies.remove(TokenKey, { domain: TOP_DOMAIN })
}

// 获取导出token，AuthToken + *** + IdToken
export function getDownloadLocalToken() {
    return getLocalToken() + '***' + getLocalToken(`${APP_NAME}IdToken`)
}

export function getDomainWsidToken() {
    return Cookies.get('wsid', { domain: TOP_DOMAIN })
}

export function getWSDomainWsidToken() {
  return Cookies.get('wsid', { domain: CMS_TOP_DOMAIN })
}
