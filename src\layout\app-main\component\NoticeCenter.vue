<template>
  <el-popover
    popper-class="notice-popover"
    placement="bottom"
    ref="popover"
    width="400"
    trigger="click"
    v-model:visible="showNotice"
    @show="() => opened = true"
    @after-leave="handleLeave">
    <template #reference>
      <el-badge :value="unReadMsg" :max="99" :hidden="unReadMsg === 0" class="notice-title">
        <el-tooltip content="消息中心" placement="top">
          <SvgIcon icon-class="bell" style="width: 23px; height: 23px;" />
        </el-tooltip>
      </el-badge>
    </template>
    <div class="notice-body">
      <div class="top-container">
        <div class="tabs" :style="{'--x': queryParams.type === '10' ? '125px' :'0px'}">
          <div class="item with-hand" :class="{ active: queryParams.type === '20' }" @click="handleTabClick('20')"> 业务消息 ({{ businessMsg }}) </div>
          <div class="item with-hand" :class="{ active: queryParams.type === '10' }" @click="handleTabClick('10')"> 系统消息 ({{ sysMsg }}) </div>
        </div>

        <el-popover 
          placement="bottom" 
          trigger="click" 
          width="280" 
          v-model:visible="showConfig"
          :teleported="false"
        >
          <template #reference>
            <Setting class="setting with-hand"/>
          </template>
          <el-space :size="12" nowrap>
            <span>每分钟消息刷新</span>
            <el-switch
              v-model="loopNotify"
              @change="handleLoopSave"
              active-value="1"
              inactive-value="0"
              active-text="开启"
              inactive-text="关闭"
            />
          </el-space>
          <el-space :size="12" nowrap>
            <span>全局弹窗提醒</span>
            <el-switch
              v-model="popConfig.globalPop"
              @change="handlePopSave"
              active-value="1"
              inactive-value="0"
              active-text="开启"
              inactive-text="关闭"
            />
          </el-space>
          
        </el-popover>
      </div>
      <div class="msg-container">
        <div 
          v-if="opened"
          class="infinite-list"
          v-infinite-scroll="loadList"
          :infinite-scroll-disabled="loading"
          style="overflow:auto; height: 100%;"
        >
          <template v-if="msgList.length > 0">
            <div 
              v-for="(d, index) in msgList"
              :key="`${d.id}${index}`"
              class="msg-item with-hand"
              :class="{ unread: d.is_read === 0 }"
              @click="handleDetail(d)"
            >
              <div class="rowBC">
                <div class="bell">
                  <SvgIcon icon-class="bell-notice" />
                </div>
                <div class="content">
                  <div class="title" :title="d.title">{{ d.title }}</div>
                  <span class="time">{{ d.add_time }}</span>
                </div>
                <div v-if="d.url">
                  <ArrowRight style="width: 16px; height: 16px;" />
                </div>
              </div>
            </div>
            <div class="text-center" v-if="msgList.length > 0 && noMore"> 已经到底啦！</div>
          </template>
          <template v-else>
            <el-empty description="暂无可读消息" />
          </template>
        </div>
      </div>
      <div class="footer-container" @click="handleReadAll">
        <el-space :size="6">
          <SvgIcon icon-class="broom" style="width: 16px; height: 16px;" />
          <span> 一键已读 </span>
        </el-space>
      </div>
    </div>
  </el-popover>
</template>

<script setup lang="ts">
import { ref, reactive, computed, h } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { ElMessage, ElNotification } from 'element-plus'
import { Setting, ArrowRight } from '@element-plus/icons-vue'
import { useBasicStore } from '@/store/basic'
import useLoading from '@/hooks/use-loading'
import useTryCatch from '@/hooks/use-try-catch'
import { get1MinUnread, getMsgList, getUnreadNum, setMessageReaded, setMessageAllReaded } from '@/api/notice-center'
import type { msgListItem, msgListParams } from '@/api/notice-center'

import emitter from '@/utils/bus'

import SvgIcon from '@/icons/SvgIcon.vue'

const urlMap = {
  '/reviewManagement/reviewDetail': '/publish-center/audit-detail',
  '/GenerateRecord/GeneratePublishDetail': '/publish-center/generate-publish-detail',
  '/GenerateRecord/ArticleImportDetail': '/cms-tools/article-import-detail',
}

type popConfig = {
  globalPop: string,
  sysPop: string,
  businessPop: string
}
let timer: any = null
const loopNotify = ref(localStorage.getItem('loopNotify') || '1')
const route = useRoute()
const router = useRouter()
const basicStore = useBasicStore()
const { loading, setLoading } = useLoading(true)
/* 参数定义 */
const showPopover = ref(false)
const opened = ref(false)
const showNotice = ref(false)
const activeName = ref('global')
const readMsg = ref(0)
const unReadMsg = ref(0)
const sysMsg = ref(0)
const businessMsg = ref(0)
const msgList = ref<msgListItem[]>([])
const showOperate = ref(false)
const checkedAll = ref(false)
const showConfig = ref(false)
const queryParams = reactive<msgListParams>({
  type: '20',
  is_read: '',
  page_size: 6,
  page: 0
})
const currentTotal = ref(0)

const noMore = computed(() => {
  const { type, is_read } = queryParams
  const msgNumObj = is_read === '1' ? readMsg : type === '10' ? sysMsg : type === '20' ? businessMsg : unReadMsg
  return msgList.value.length >= msgNumObj.value
})

const hideCheckBox = computed(() => {
  return queryParams.is_read === '1'
})

const selection = new Set()

const noticePop = localStorage.getItem('noticePop')
const popConfig = reactive<popConfig>({
  globalPop: '1',
  sysPop: '1',
  businessPop: '1'
})

if (noticePop) {
  const noticePopConfig = JSON.parse(noticePop) as popConfig
  Object.entries(noticePopConfig).forEach(([key, value]) => {
    popConfig[key] = value
  })
} else {
  localStorage.setItem('noticePop', JSON.stringify(popConfig))
}
/* 参数定义 */

/* 方法定义 */
const handleTabClick = (type: string) => {
  queryParams.type = type
  handleTabChange()
}
const resetAll = (refresList = true) => {
  queryParams.type = '0'
  queryParams.is_read = '0'
  handleTabChange(refresList)
}
const handleTabChange = (refresList = true) => {
  if (refresList) {
    showOperate.value = false
    selection.clear()
    queryParams.page = 0
    msgList.value = []
    getList()
  }
  getUnread()
}
// 获取数据列表
const getList = (notice?: boolean) => {
  useTryCatch( async () => {
    queryParams.page++
    checkedAll.value = false
    setLoading(true)
    const res = await getMsgList(queryParams)
    if (res.code === 200) {
      msgList.value.push(...res.data.list)
      if (queryParams.is_read === '1') {
        readMsg.value = res.data.total
      }
      currentTotal.value = res.data.total
      if (notice && msgList.value[0]) {
        handleNotification(msgList.value[0])
      }
    } else {
      ElMessage.error(res.msg)
    }
    setLoading(false)
  }, () => setLoading(false) )
}
const loadList = () => {
  if (msgList.value.length > 0 && noMore.value) {
    return
  }
  if (currentTotal.value === 0) {
    return 
  }
  getList()
}
// 重置未读数
const resetRead = () => {
  unReadMsg.value = 0
  sysMsg.value = 0
  businessMsg.value = 0
}
// 获取未读数
const getUnread = () => {
  useTryCatch( async () => {
    const res = await getUnreadNum()
    if (res.code === 200) {
      resetRead()
      const list = res.data as {type: number; total: number}[]
      for ( const { type, total } of list) {
        type === 10 && (sysMsg.value = total)
        type === 20 && (businessMsg.value = total)
        type === 0 && (unReadMsg.value = total)
      }
    }
  } )
}

// 处理全选
const handleSelectAll = (value: boolean) => {
  msgList.value.forEach((item) => {
    item.checked = value
    value && selection.add(item.id)
  })
  !value && selection.clear()
}
// 处理单选
const handleSelect = (item: msgListItem) => {
  const { checked, id } = item
  selection[checked? 'add' : 'delete'](id)
}

// 标记勾选为已读
const handleToReaded = () => {
  if (selection.size === 0) {
    return ElMessage.warning('请至少勾选一项！')
  }
  useTryCatch( async () => {
    const params = {
      message_id: [...selection].join(',')
    }
    const res = await setMessageReaded(params)
    if (res.code === 200) {
      handleTabChange(true)
      ElMessage.success('操作成功')
    } else {
      ElMessage.error(res.msg)
    }
  } )
}

// 处理跳转消息详情
const handleDetail = async (item: msgListItem, isNotify = false) => {
  useTryCatch( async () => {
    const { id, url } = item
    const params = { message_id: `${id}` }
    if (queryParams.is_read === '1' && !isNotify) {
      await handleJump(url)
      showNotice.value = false
      return
    }

    const res = await setMessageReaded(params)
    if (res.code === 200) {
      await handleJump(url)
      showNotice.value = false
    } else {
      ElMessage.error(res.msg)
    }
    item.is_read = 1
  } )
}

// 保存弹窗设置
const handlePopSave = () => {
  localStorage.setItem('noticePop', JSON.stringify(popConfig))
  ElMessage.success('保存设置成功')
}
const handleLoopSave = () => {
  localStorage.setItem('loopNotify', loopNotify.value)
  if (loopNotify.value === '0') {
    timer && clearTimeout(timer)
  } else {
    handleLoopPerMin()
  }
}

// 同步弹出会自动折叠，此处异步优化需异步优化
const handleNotification = (item: msgListItem) => {
  return new Promise((resolve, reject) => {
    ElNotification({
      title: item.type === 10 ? '系统消息' : '业务消息',
      message: h('div', null, [
        h('div', { class: 'one-line' }, item.title),
        item.url 
        ? h('div', { class: 'pop-footer' }, [
            h('span', { 
              class: 'pop-link',
              onClick: () => handleDetail(item, true)
            }, '查看详情')
          ]) 
        : ''
      ]),
      position: 'bottom-right',
      duration: 5000,
      showClose: true
    })
    resolve(null)
  })
}

// 轮询弹窗
const handleLoopNotify = async (list: msgListItem[]) => {
  for ( const item of list ) {
    await handleNotification(item)
  }
}
// 每分钟轮询查询未读信息
const handleLoopPerMin = () => {
  timer = setTimeout( () => {
    useTryCatch( async () => {
      const res = await get1MinUnread()
      if (res.code === 200) {
        const list = res.data.list as msgListItem[]
        const sysList = list.filter(({type}) => type === 10)
        const businessList = list.filter(({type}) => type === 20)
        const { sysPop, businessPop, globalPop } = popConfig
        const popList = sysPop === '1' && businessPop === '1' ? list : sysPop === '1' ? sysList : businessPop === '1' ? businessList : []

        if (globalPop === '1') {
          handleLoopNotify(popList)
        }
        handleLoopPerMin()
        if (list.length > 0 && !showNotice.value) {
          resetAll(false)
        }
      }
    } )
  }, 60 * 1000)
}

// 路由跳转处理
const handleJump = async (url: string) => {
  const rawPath = url.split('?')[0]
  const rawQuery = url.split('?')[1]
  basicStore.delCachedView(route.name)
  const rawArr = url.match(/(?<=\/CommonIndex\/AnnounceUserDetail\?announcement_id=)\d+/g)
  if (rawArr) {
    const id = +rawArr[0]
    emitter.emit('announceModal', id)
    return
  }
  await router.push(urlMap[rawPath] ? `${urlMap[rawPath]}?${rawQuery}` : url)
}

const handleLeave = () => {
  showConfig.value = false
}

const handleReadAll = () => {
  if (msgList.value.length === 0) {
    return ElMessage.warning('暂无可阅读消息')
  }
  useTryCatch( async () => {
    const res = await setMessageAllReaded( { type: queryParams.type })
    if (res.code === 200) {
      handleTabChange(true)
      ElMessage.success('操作成功')
    } else {
      ElMessage.error(res.msg)
    }
  } )
}
/* 方法定义 */

// emit注册全局消息更新，后续在特定场景进行分发调用
// emitter.on('noticeUpdate', () => {
//   queryParams.type = '20'
//   getUnread()
//   getList(true)
// })

/* 初始化执行 */
getUnread()
getList()
if (loopNotify.value === '1') {
  handleLoopPerMin()
}
</script>

<style lang="scss" scoped>
.notice-title {
  .el-badge__content.is-fixed {
    right: 16px;
  }
}
.top-container {
  position: relative;
  text-align: center;
  border-bottom: 1px solid var(--el-border-color-light);
  .tabs {
    --x: 0px;
    display: inline-block;
    width: 250px;
    font-size: 14px;
    position: relative;
    .item {
      display: inline-block;
      width: 50%;
      height: 50px;
      line-height: 50px;
      &.active {
        color: var(--el-color-primary);
      }
    }
    &::after {
      content: '';
      position: absolute;
      left: 0;
      bottom: 0;
      width: 50%;
      height: 2px;
      background-color: var(--el-color-primary);
      transition: transform .3s;
      transform: translateX(var(--x));
    }
  }
  .setting {
    width: 16px;
    height: 16px;
    position: absolute;
    right: 16px;
    top: 16px;
    color: var(--title-color);
  }
}
.msg-container {
  height: 536px;
  background-color: var(--bg-scroll);
  .infinite-list {
    padding: 8px 0;
    .msg-item {
      height: 80px;
      background-color: var(--el-bg-color);
      border-radius: 4px;
      padding: 16px;
      margin-bottom: 8px;
      position: relative;
      &.unread::after{
        content: '';
        position: absolute;
        width: 6px;
        height: 6px;
        border-radius: 50%;
        background-color: var(--el-color-danger);
        right: 36px;
        top: 34px;
      }
      .bell {
        .svg-icon {
          width: 40px;
          height: 40px;
        }
      }
      .content {
        padding-left: 8px;
        flex-grow: 1;
      }
      .title {
        font-size: 16px;
        margin-bottom: 4px;
        color:  var(--title-color);
        max-width: 284px;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
      }
      .time {
        font-size: 12px;
        color:  var(--time-color);
      }
    }
  }
}
.footer-container {
  background-color: var(--el-bg-color);
  text-align: center;
  padding: 14px 0;
  color: var(--el-color-primary);
  cursor: pointer;
}
</style>

<style lang="scss">
.lighting-theme {
  .notice-popover {
    --title-color: #000;
    --time-color: rgba(36, 41, 52, 0.4);
    --bg-scroll: #fafafa;
  }
}
.notice-popover {
  --el-popover-padding: 0 !important;
  --title-color: #fff;
  --time-color: #fff;
  --bg-scroll: #000;
}
.one-line {
  width: 290px;
}
.pop-footer {
  padding-top: 10px;
  text-align: right;
  .pop-link {
    color: var(--el-color-primary);
    cursor: pointer;
  }
}
</style>