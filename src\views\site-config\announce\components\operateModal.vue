<template>
  <el-dialog
    v-model="show"
    :title="props.row ? '编辑公告' : '添加公告'"
    width="90%"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    append-to-body
    align-center
    @closed="emit('close')"
  >
    <template #default>
      <el-form 
        ref="formRef" 
        label-suffix=":"
        label-width="140px"
        :model="submitData"
        :rules="rules"
      >
        <el-row :gutter="20">
          <el-col :span="8">
            <el-card shadow="never">
              <template #header>
                <strong style="font-size: 1.25rem;">基础信息</strong>
              </template>
              <template #default>
                <el-form-item label="公告ID" v-if="submitData.announcement_id">
                  {{ submitData.announcement_id }}
                </el-form-item>
                <el-form-item label="公告标题" prop="title">
                  <el-input v-model="submitData.title" placeholder="公告标题" />
                </el-form-item>
                <el-form-item label="公告类型" prop="type">
                  <el-radio-group v-model="submitData.type">
                    <el-radio label="B"> B类型 </el-radio>
                    <el-radio label="A"> A类型 </el-radio>
                  </el-radio-group>
                </el-form-item>
                <el-form-item label="列表展示状态" prop="is_top">
                  <el-radio-group v-model="submitData.is_top">
                    <el-radio :label="2"> 不置顶 </el-radio>
                    <el-radio :label="1"> 置顶 </el-radio>
                  </el-radio-group>
                </el-form-item>
                <el-form-item label="备份说明" prop="remark">
                  <el-input type="textarea" v-model="submitData.remark" placeholder="备份说明" rows="8" />
                </el-form-item>
              </template>
            </el-card>
          </el-col>
          <el-col :span="16">
            <el-card shadow="never">
              <template #header>
                <strong style="font-size: 1.25rem;">公告内容</strong>
              </template>
              <template #default>
                <el-form-item label="" label-width="0" prop="content">
                  <el-input type="textarea" v-model="submitData.content" placeholder="公告内容" rows="40" />
                </el-form-item>
              </template>
            </el-card>
          </el-col>
        </el-row>
      </el-form>
    </template>
    <template #footer>
      <div class="text-center">
        <el-button @click="show = false"> 取消 </el-button>
        <el-button type="primary" @click="handleSave(formRef)" :loading="loading"> 确认 </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { reactive, ref } from 'vue'
import { ElMessage } from 'element-plus'
import useTryCatch from '@/hooks/use-try-catch'
import useLoading from '@/hooks/use-loading'
import { formatDatetime } from '@/utils/common'
import { addAnnounce, editAnnounce } from '@/api/site-config'
import type { announceItem, announceForm } from '@/api/site-config'
import type { FormInstance, FormRules } from 'element-plus'

const props = defineProps<{
  row?: announceItem|null
}>()
const emit = defineEmits(['close', 'success'])

const rules = reactive<FormRules>({
  title: [
    { required: true, message: '请输入公告标题', trigger: 'blur' }
  ],
  content: [
    { required: true, message: '请输入公告内容', trigger: 'blur' }
  ],
  remark: [
    { required: true, message: '请输入备注', trigger: 'blur' }
  ]
})

const { loading, setLoading } = useLoading()
const show = ref(true)
const formRef = ref<FormInstance>()
const submitData = reactive<announceForm>({
  title: '',
  content: '',
  type: 'B',
  is_top: 2,
  remark: formatDatetime(new Date()),
})

if (props.row) {
  const row = props.row as announceItem
  Object.keys(submitData).forEach((key) => {
    submitData[key] = row[key]
  })
  submitData.announcement_id = row.announcement_id
}

const handleSave = async ( formEl: FormInstance | undefined ) => {
  if (!formEl) {
    return
  }
  await formEl.validate( (valid) => {
    if (valid) {
      const api = props.row ? editAnnounce : addAnnounce
      useTryCatch( async () => {
        setLoading(true)
        const res = await api( submitData, props.row ? props.row.announcement_id : 0 )
        if (res.code === 200) {
          show.value = false
          ElMessage.success(res.msg)
          emit('success')
        } else {
          ElMessage.error(res.msg)
        }
        setLoading(false)
      }, () => setLoading(false) )
    }
  } )
}
</script>