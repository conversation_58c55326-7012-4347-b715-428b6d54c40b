<template>
  <el-dialog
    title="以下文件名重复,请确认后续操作"
    width="800px"
    v-model="show"
    append-to-body
    @close="() => emit('close')"
  >
    <el-table
      :data="fileList"
    >
      <el-table-column type="index" :index="(index: number) => (index+1)"/>
      <el-table-column label="文件名" prop="fileName" />
      <el-table-column label="操作">
        <template #default="scope: { row: sameFileItem, $index: number }">
          <el-button type="primary" plain @click="handleReplace(scope.row.file, scope.$index)"> 替换 </el-button>
          <el-button type="primary" plain @click="handleSaveBoth(scope.row.file, scope.$index)"> 保留两个 </el-button>
        </template>
      </el-table-column>
    </el-table>
    <template #footer>
      <el-button @click="handleClose" > 取消 </el-button>
      <el-button type="primary" @click="handleReplaceAll"> 全部替换 </el-button>
      <el-button type="primary" @click="handleSaveBothAll"> 全部保留 </el-button>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { ElMessageBox } from 'element-plus'
import type { UploadRawFile } from 'element-plus'

type sameFileItem = {
  file: UploadRawFile;
  fileName: string;
}

const props = defineProps<{
  fileList: sameFileItem[]
}>()

const emit = defineEmits(['replace', 'save', 'replaceAll', 'saveAll', 'close'])

const show = ref(true)

const handleShow = () => {
  show.value = true
}
const handleClose = () => {
  show.value = false
}

const handleReplace = (file: UploadRawFile, index: number) => {
  ElMessageBox.confirm('请确认是否替换', '替换文件', {
    callback: (action: string) => {
      if (action === 'confirm') {
        emit('replace', file, index)
      }
    }
  })
}
const handleSaveBoth = (file: UploadRawFile, index: number) => {
  ElMessageBox.confirm('请确认是否保留两个文件', '保留两个文件', {
    callback: (action: string) => {
      if (action === 'confirm') {
        emit('save', file, index)
      }
    }
  })
}
const handleReplaceAll = () => {
  ElMessageBox.confirm('请确认是否全部替换', '全部替换', {
    callback: (action: string) => {
      if (action === 'confirm') {
        emit('replaceAll')
        handleClose()
      }
    }
  })
}
const handleSaveBothAll = () => {
  ElMessageBox.confirm('请确认是否全部保留', '全部保留', {
    callback: (action: string) => {
      if (action === 'confirm') {
        emit('saveAll')
        handleClose()
      }
    }
  })
}

defineExpose({
  handleShow,
  handleClose
})
</script>