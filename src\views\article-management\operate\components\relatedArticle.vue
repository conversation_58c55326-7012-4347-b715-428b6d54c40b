<template>
  <el-dialog
    v-model="show"
    title="选取您需要关联的文章"
    width="90%"
    :destroy-on-close="false"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    append-to-body
    align-center
    @closed="emit('close')"
  >
    <QueryForm 
      :loading="loading"
      :form-list="formList"
      @search="handleSearch"
      hide-reset
    />
    <div v-loading="loading" style="min-height: 100px; margin-bottom: 20px; margin-top: 30px;" class="rel_art_table">
      <CustomTable 
        ref="tableRef"
        :columns="columns"
        :data="tableData"
        :table-method="getList"
        :selectable="true"
        @select="handleSelect"
        height="800px"
      />
    </div>
    <template v-if="tableSelectionMap.size > 0">
      <el-space :size="10" wrap>
        <el-tag
          v-for="([ art_id, row ]) in tableSelectionMap"
          :key="art_id"
          class="tag-wrap"
          closable
          @close="handleDelete(art_id, row)"
        >
          {{ row.title }}
        </el-tag>
      </el-space>
    </template>
    <template #footer>
      <div class="text-center">
        <el-button type="primary" @click="handleConfirm">确认</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="tsx">
import { ref, reactive, toRef, nextTick } from 'vue'
import { ElMessage } from 'element-plus'
import { storeToRefs } from 'pinia'
import { useParamsStore } from '@/store'
import useTryCatch from '@/hooks/use-try-catch'
import useLoading from '@/hooks/use-loading'
import { getArticleList } from '@/api/article-management'
import type { queryParams, listItem, classifyTreeItem, authorListItem } from '@/api/article-management'

import QueryForm from '@/components/QueryForm/index.vue'
import CustomTable from '@/components/CustomTable/index.vue'

const props = withDefaults(defineProps<{
  relIds?: string;
  classifyData?: classifyTreeItem[];
  authorList?: authorListItem[];
  channelId?: string|number;
}>(), {
  classifyData: () => [],
  authorList: () => []
})

const emit = defineEmits(['confirm', 'close'])

const { site_id_all, channel_item_all, userList } = storeToRefs(useParamsStore())
const { pageStateList, page_state_map } = useParamsStore()
const { loading, setLoading } = useLoading()

const limit = 10
const show = ref(true)
const initRelIds = ref(false)
const queryParams_raw = {
  user_id_a: '',
  user_id_e: '',
  author_id: '',
  cat_id: '',
  page_state: '',
  art_ids: '',
  title: '',
  page_url: '',
  art_group_id: '',
  content: '',
  is_recom: '',
  is_hot: '',
  page_id: '',
  tpl_id: '',
}
const queryParams = reactive<queryParams>({
  ...queryParams_raw,
  art_ids: props.relIds || '',
  site_id: 0,
  channel_id: 0,
  page: 1,
  page_size: 10,
})
const isList = [
  {
    label: '是',
    value: '1',
  },
  {
    label: '否',
    value: '0',
  }
]
const formList = ref([
  {
    label: '文章ID',
    placeholder: '文章ID',
    value: toRef(queryParams, 'art_ids'),
    component: 'input',
  },
  {
    label: '文章标题',
    placeholder: '文章标题',
    value: toRef(queryParams, 'title'),
    component: 'input',
  },
  {
    label: '页面状态',
    placeholder: '页面状态',
    value: toRef(queryParams, 'page_state'),
    component: 'select',
    selections: pageStateList
  },
  {
    label: '页面url',
    placeholder: '页面url,逗号分隔',
    value: toRef(queryParams, 'page_url'),
    component: 'input',
  },
  {
    label: '页面ID',
    placeholder: '页面ID',
    value: toRef(queryParams, 'page_id'),
    component: 'input',
    append: true,
  },
  {
    label: '模板ID',
    placeholder: '模板ID',
    value: toRef(queryParams, 'tpl_id'),
    component: 'input',
     append: true,
  },
  {
    label: '文章类别',
    placeholder: '文章类别',
    value: toRef(queryParams, 'cat_id'),
    component: 'tree-select',
    append: true,
    valueKey: 'id',
    props: { label: 'label', children: 'children' },
    data: props.classifyData
  },
  {
    label: '更新人',
    placeholder: '更新人',
    value: toRef(queryParams, 'user_id_e'),
    component: 'select',
    labelKey: 'real_name',
    valueKey: 'user_id',
    append: true,
    selections: userList
  },
  {
    label: '创建人',
    placeholder: '创建人',
    value: toRef(queryParams, 'user_id_a'),
    component: 'select',
    labelKey: 'real_name',
    valueKey: 'user_id',
    append: true,
    selections: userList
  },
  {
    label: '文章作者',
    placeholder: '文章作者',
    value: toRef(queryParams, 'author_id'),
    component: 'select',
    append: true,
    labelKey: 'name',
    valueKey: 'author_id',
    selections: props.authorList
  },
  {
    label: '推荐',
    placeholder: '是否推荐',
    value: toRef(queryParams, 'is_recom'),
    component: 'select',
    append: true,
    selections: isList
  },
  {
    label: '热门',
    placeholder: '是否热门',
    value: toRef(queryParams, 'is_hot'),
    component: 'select',
    append: true,
    selections: isList
  },
  {
    label: '文章集ID',
    placeholder: '文章集ID',
    value: toRef(queryParams, 'art_group_id'),
    component: 'input',
    append: true,
  },
  {
    label: '文章内容',
    placeholder: '文章内容',
    value: toRef(queryParams, 'content'),
    component: 'input',
    append: true,
  },
])
const tableRef = ref()
const tableData = ref<listItem[]>([])
const tableSelectionMap = ref(new Map<number, listItem>())
const columns = ref([
  {
    title: '文章ID',
    dataKey: 'art_id',
    width: 100,
  },
  {
    title: '文章标题',
    dataKey: 'title',
    minWidth: 280,
  },
  {
    title: 'URL',
    dataKey: 'online_url',
    minWidth: 200,
  },
  {
    title: '页面ID',
    dataKey: 'page_id',
    width: 100,
  },
  {
    title: '页面状态',
    dataKey: 'state',
    width: 90,
    cellRenderer: (scope: { row: listItem }) => (
      <span style={ { 
        color: page_state_map[scope.row.page_state]?.color
      } }>
        { page_state_map[scope.row.page_state]?.text }
      </span>
    )
  },
  {
    title: '文章所属模板ID',
    dataKey: 'tpl_id',
    width: 130,
  },
  {
    title: '所属渠道',
    dataKey: 'channel_name',
    width: 100,
    cellRenderer: ( scope: { row: listItem } ) => (<span style={ { color: scope.row.color } }>{ scope.row.channel_name }</span>)
  },
  {
    title: '文章更新人',
    dataKey: 'user_name_e',
    width: 100,
  },
  {
    title: '更新时间',
    dataKey: 'edit_time',
    width: 150,
  },
])

const handleReset = () => {
  Object.entries(queryParams_raw).forEach(( [ key, value ] ) => {
    queryParams[key] = value
  })
}
const handleSearch = () => {
  tableRef.value?.setPage(1)
  const { page, pageSize } = tableRef.value ? tableRef.value : { page: 1, pageSize: 10 }
  getList( { page, pageSize } )
}
const getList = ( { page, pageSize } ) => {
  useTryCatch( async () => {
    setLoading(true)

    const params = {
      ...queryParams,
      site_id: site_id_all.value,
      channel_id: props.channelId as number || channel_item_all.value?.channel_id as number,
      page,
      page_size: pageSize,
      is_hot: queryParams.is_hot || '-1',
      is_recom: queryParams.is_recom || '-1',
      cat_id: String(queryParams.cat_id),
    }

    const res = await getArticleList(params)
    if (res.code === 200) {
      const { items, total } = res.data
      tableData.value = items
      tableRef.value?.setTotal(total)
      if (initRelIds.value) {
        initRelIds.value = false
        handleDefalut(items)
      }
    } else {
      ElMessage.error(res.msg)
    }
    setLoading(false)
  }, () => setLoading(false) )
}
const handleSelect = (selection: listItem[], row: listItem) => {
  if (tableSelectionMap.value.has(row.art_id)) {
    tableSelectionMap.value.delete(row.art_id)
    return
  }

  if (tableSelectionMap.value.size >= limit) {
    const { customTableRef } = tableRef.value?.$refs
    if (customTableRef) {
      customTableRef.toggleRowSelection(row)
    }
    ElMessage.warning(`最多可添加${limit}个`)
    return
  }

  tableSelectionMap.value.set(row.art_id, row)
}
const handleDelete = (art_id: number, row: listItem) => {
  tableSelectionMap.value.delete(art_id)
  const { customTableRef } = tableRef.value?.$refs
  if (customTableRef) {
    for ( const row of tableData.value ) {
      if (art_id === row.art_id) {
        customTableRef.toggleRowSelection(row, false)
        return
      }
    }
  }
}
const handleConfirm = () => {
  show.value = false
  const selections: listItem[] = []
  for ( const [ art_id, row ] of  tableSelectionMap.value) {
    selections.push(row)
  }
  emit('confirm', selections)
}
const handleDefalut = (selection: listItem[]) => {
  nextTick(() => {
    const { customTableRef } = tableRef.value?.$refs
    selection.forEach((row: listItem) => {
      tableSelectionMap.value.set(row.art_id, row)
      if (customTableRef) {
        customTableRef.toggleRowSelection(row)
      }
    })
  })
}

defineExpose({
  show: () => (show.value = true),
})

if (props.relIds) {
  initRelIds.value = true
}
</script>

<style>
.rel_art_table .el-table__header .el-checkbox { display: none; }
.el-tag.tag-wrap { white-space: normal; }
</style>