
import { h } from 'vue'
import { ElCheckbox, ElMessageBox } from 'element-plus'
// 初始化页面表头字段缓存
export const handleCachedTableHead = (columns: any[]) => {
  const { pathname } = window.location
  const cachedTableHeadString = window.localStorage.getItem('cachedTableHead')
  if (cachedTableHeadString) {
    const cachedTableHead = window.JSON.parse(cachedTableHeadString)
    const keys = cachedTableHead[pathname]

    if (keys) {
      const keySet = new Set(keys.split(','))
      columns.forEach((column) => {
        column.hideColumn = keySet.has(column.dataKey) ? true : false
      })
    }
  }
}

// 清空隐藏的表头字段缓存
export const handleResetTableHead = () => {
  window.localStorage.removeItem('cachedTableHead')
}

// 保存隐藏的表头字段缓存
export const handleSaveTableHead = (columns: any[]) => {
  const { pathname } = location
  const cachedTableHeadString = window.localStorage.getItem('cachedTableHead')
  const cachedTableHead = cachedTableHeadString ? window.JSON.parse(cachedTableHeadString) : {}
  const keys = columns.filter(({ hideColumn }) => hideColumn).map(({ dataKey }) => dataKey).join(',')

  cachedTableHead[pathname] = keys
  window.localStorage.setItem('cachedTableHead', window.JSON.stringify(cachedTableHead))
}

// 打开交互弹窗
export const handleTableHeadModal = (columns: any[]) => {
  ElMessageBox({
    title: '需要展示的表头字段',
    showConfirmButton: false,
    showCancelButton: true,
    confirmButtonText: '确认',
    cancelButtonText: '取消',
    callback: (action) => {

    },
    message: () => {
      return h('div', columns.map(column => {
        return column.dataKey ? h(ElCheckbox, {
          key: column.dataKey,
          modelValue: !column.hideColumn,
          label: column.title,
          onChange: (value) => {
            column.hideColumn = !value
            handleSaveTableHead(columns)
          }
        }) : ''
      }))
    }
  })
}

// 表格过长时固定高度
export const getTableHeight = (extraHeight: number, className = 'sticky-table'): string => {
  const container_el = document.querySelector(`.${className}`)

  if (container_el) {
    const { height } = container_el.getBoundingClientRect()
    const h = height - extraHeight
    return `${h}px`
  }

  return 'auto'
}