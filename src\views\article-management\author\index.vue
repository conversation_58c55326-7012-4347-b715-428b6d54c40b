<template>
  <div class="scroll-y with-flex">
    <QueryForm 
      hide-reset
      :loading="loading"
      :form-list="formList"
      @search="handleSearch"
    />
    <div class="sticky-table" v-loading="loading" style="min-height: 500px;">
      <CustomTable
        ref="tableRef"
        customize-head
        :columns="columns"
        :data="tableData"
        :table-method="getList"
        :operate-list="operateList"
      />
    </div>
  </div>
</template>

<script setup lang="tsx">
import { ref, toRef, reactive, onActivated } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { ElMessage, ElMessageBox, ElButton } from 'element-plus'
import { storeToRefs } from 'pinia'
import { useParamsStore } from '@/store'
import { useBasicStore } from '@/store/basic'
import useTryCatch from '@/hooks/use-try-catch'
import useLoading from '@/hooks/use-loading'
import { getAuthorList, deleteAuthor } from '@/api/article-management'
import type { authorListItem } from '@/api/article-management'
import Listener from '@/utils/site-channel-listeners'

import QueryForm from '@/components/QueryForm/index.vue'
import CustomTable from '@/components/CustomTable/index.vue'

defineOptions( { name: 'ArticleAuthorList' } )

const levelMap = {
  'chief': '主编',
  'staff': '普通编辑',
  'contributor': '站外编辑'
}

const route = useRoute()
const router = useRouter()
const { site_id_all } = storeToRefs(useParamsStore())
const basicStore = useBasicStore()
const { loading, setLoading } = useLoading()

const queryParams_raw = {
  language: '',
  author_id: '',
  name: ''
}
const queryParams = reactive({
  ...queryParams_raw
})

const formList = ref([
  {
    placeholder: '作者ID',
    value: toRef(queryParams, 'author_id'),
    component: 'input',
  },
  {
    placeholder: '作者名称',
    value: toRef(queryParams, 'name'),
    component: 'input',
  }
])
const operateList = ref([
  {
    title: '添加作者',
    button: true,
    append: true,
    method: () => {
      router.push( { 
        name: 'ArticleAuthorOperate',
        query: {
          type: 'add'
        }
      } )
    }
  },
  {
    title: '',
    icon: 'refresh',
    append: true,
    method: () => {
      handleSearch()
    }
  }
])
const columns = ref([
  {
    title: '作者ID',
    dataKey: 'author_id',
    width: 100,
  },
  {
    title: '作者名称',
    dataKey: 'name',
    width: 200,
  },
  {
    title: '作者分类',
    dataKey: 'level',
    cellRenderer: ( scope: { row: authorListItem } ) => <> { levelMap[scope.row.level] } </>
  },
  {
    title: '头像地址',
    dataKey: 'avatar',
  },
  {
    title: '作者简介',
    dataKey: 'summary',
  },
  {
    title: '作者文章数',
    dataKey: 'article_count',
    emptyText: '0',
  },
  {
    title: 'Facebook账号',
    dataKey: 'facebook_account',
  },
  {
    title: 'Twitter账号',
    dataKey: 'twitter_account',
  },
  {
    title: 'Google+账号',
    dataKey: 'google_plus',
  },
  {
    title: 'Linkedin账号',
    dataKey: 'linkedin_account',
  },
  {
    title: '排序',
    dataKey: 'sort',
  },
  {
    title: '更新时间',
    dataKey: 'edit_time',
  },
  {
    title: '编辑人',
    dataKey: 'user_name',
  },
  {
    title: '操作',
    dataKey: '',
    width: 160,
    cellRenderer: ( scope: { row: authorListItem } ) => (
      <>
        <ElButton type='primary' plain onClick={() => handleEdit(scope.row)}> 编辑 </ElButton>
        <ElButton type='danger' plain onClick={() => handleDelete(scope.row)}> 删除 </ElButton>
      </>
    )
  },
])
const tableRef = ref()
const tableData = ref([])

const handleSearch = () => {
  tableRef.value?.setPage(1)
  const { page, pageSize } = tableRef.value ? tableRef.value : { page: 1, pageSize: 10 }
  getList( { page, pageSize } )
}
const getList = ( { page, pageSize } ) => {
  useTryCatch( async () => {
    setLoading(true)

    const params = {
      S: {
        ...queryParams,
        site_id: site_id_all.value
      },
      page,
      page_size: pageSize
    }

    const res = await getAuthorList(params)
    if (res.code === 200) {
      tableData.value = res.data.items
      tableRef.value?.setTotal(res.data.total)
    } else {
      ElMessage.error(res.msg)
    }
    setLoading(false)
  }, () => setLoading(false) )
}

const handleEdit = (row: authorListItem) => {
  router.push({
    name: 'ArticleAuthorOperate',
    query: {
      type: 'edit',
      author_id: row.author_id
    }
  })
}
const handleDelete = (row: authorListItem) => {
  ElMessageBox.confirm(
    '请确认是否要删除该作者',
    '提示',
    {
      confirmButtonText: '确认',
      cancelButtonText: '取消',
      callback: (action: string) => {
        if (action === 'confirm') {
          useTryCatch( async () => {
            setLoading(true)
            const res = await deleteAuthor(row.author_id)
            if (res.code === 200) {
              const { page, pageSize } = tableRef.value ? tableRef.value : { page: 1, pageSize: 10 }
              getList( { page, pageSize } )
              ElMessage.success(res.msg)
            } else {
              ElMessage.error(res.msg)
            }
            setLoading(false)
          }, () => setLoading(false) )
        }
      }
    }
  )
}

onActivated(() => {
  if (basicStore.refresh) {
    basicStore.setRefresh(false)
    handleSearch()
  }
})

Listener(route, () => {
  handleSearch()
})
</script>