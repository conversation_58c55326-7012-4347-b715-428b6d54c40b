<template>
  <div class="scroll-y">
    <div style="overflow: hidden;" :style="{ minHeight: `calc(100% - ${fixBottomHeight}px)` }">
      <el-form
        ref="formRef"
        label-width="160px"
        :rules="formRules"
        :model="blockData"
      >
        <el-row :gutter="20">
          <el-col :span="10">
            <el-card shadow="never" style="height: 100%;">
              <template #header>
                <strong style="font-size: 1.25rem;">基础信息</strong>
              </template>
              <template #default>
                <el-form-item label="块ID:" v-if="blk_id">
                  {{ blk_id }}
                </el-form-item>
                <el-form-item label="所属主块ID:" v-if="parent_id && !is_public">
                  {{ parent_id }}
                </el-form-item>
                <el-form-item label="块名称:" prop="blk_name">
                  <el-input v-model="blockData.blk_name" />
                </el-form-item>
                <el-form-item label="所属渠道:" prop="channel_id" v-if="!is_public">
                  <el-select v-model="blockData.channel_id" :disabled="type === 'edit' || (type === 'add' && !parent_id) ">
                    <el-option 
                      v-for="(d, index) in channelList"
                      :key="index"
                      :label="d.channel_code"
                      :value="d.channel_id"
                      :disabled="d.type === 'master'"
                    />
                  </el-select>
                </el-form-item>
                <el-form-item label="块类型:" prop="blk_type">
                  <el-select v-model="blockData.blk_type" :disabled="!!parent_id" @change="handleTypeChange">
                    <el-option 
                      v-for="(d, index) in blockTypeList"
                      :key="index"
                      :label="d.label"
                      :value="d.value"
                    />
                  </el-select>
                </el-form-item>
                <el-form-item label="块级别:" prop="level">
                  <el-select v-model="blockData.level" :disabled="lockType.has(blockData.blk_type as number)">
                    <el-option label="A" value="A" />
                    <el-option label="B" value="B" />
                  </el-select>
                </el-form-item>
                <el-form-item label="块语言:" prop="language" v-if="is_public">
                  <el-select v-model="blockData.language" filterable clearable placeholder="请选择语言" @focus="handleGetLangList">
                    <el-option 
                      v-for="(d, index) in langList"
                      :key="index"
                      :label="d.name"
                      :value="d.smart_lang_code"
                    />
                  </el-select>
                </el-form-item>
                <el-form-item label="静态文件路径:" prop="html_path">
                  {{ blockData.html_path }}
                </el-form-item>
                <el-form-item label="文件路径: /library/" prop="file_path">
                  <el-input v-model="blockData.file_path" :disabled="!!parent_id" />
                </el-form-item>
                <el-form-item label="备份说明:" prop="remark">
                  <el-input v-model="blockData.remark" type="textarea" :rows="4" />
                </el-form-item>
              </template>
            </el-card>
          </el-col>
          <el-col :span="14">
            <el-card shadow="never" style="height: 100%;">
              <template #header>
                <strong style="font-size: 1.25rem;">块内容</strong>
              </template>
              <template #default>
                <el-form-item label-width="0" prop="content">
                  <el-input 
                    v-model="blockData.content" 
                    type="textarea" 
                    :rows="20" 
                    v-ace="{ 
                      getVal: () => blockData.content, 
                      save: (val: string) => blockData.content = val, 
                      showTips: true 
                    }" 
                  />
                </el-form-item>
              </template>
            </el-card>
          </el-col>
        </el-row>
      </el-form>
    </div>
    <div class="fixed-bottom text-center" v-if="type !== 'view'">
      <el-button plain :icon="Back" @click="handleBack">返回列表</el-button>
      <el-button
        type="primary"
        :icon="Finished"
        :loading="loading"
        @click="handleSave(formRef)"
      >
        保存
      </el-button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive } from 'vue'
import { ElMessage } from 'element-plus'
import { Back, Finished } from '@element-plus/icons-vue'
import { useRoute, useRouter } from 'vue-router'
import { storeToRefs } from 'pinia'
import { useParamsStore, useTagsViewStore } from '@/store'
import { useConfigStore } from '@/store/config'
import { useBasicStore } from '@/store/basic'
import useTryCatch from '@/hooks/use-try-catch'
import useLoading from '@/hooks/use-loading'
import { formatDatetime } from '@/utils/common'
import { addBlock, editBlock, getBlockDetail, getHistoryDetail as historyDetail } from '@/api/block-management'
import { getLangList } from '@/api/site-config'
import type { blockData } from '@/api/block-management'
import type { langListItem } from '@/api/site-config'
import type { FormInstance } from 'element-plus'
import { formRules } from './rules'

defineOptions( { name: 'BlockManagementOperate' } )

const route = useRoute()
const router = useRouter()

const lockType = new Set([2,3,5])
const { type, blk_id, version_id, parent_id, is_public } = route.query as unknown as {
  type: string;
  blk_id?: number;
  version_id?: number;
  parent_id?: number;
  is_public?: number;
}
const { siteDetailInfo, site_id_all, channel_item_all } = storeToRefs(useParamsStore())
const { blockTypeList, channelList } = useParamsStore()
const { delVisitedView, changeViewTitle, updateHistoryTitle } = useTagsViewStore()
const { fixBottomHeight } = useConfigStore()
const basicStore = useBasicStore()
const { loading, setLoading } = useLoading()
const langList = ref<langListItem[]>([])

const blockData_raw = {
  site_id: site_id_all.value,
  channel_id: channel_item_all.value.channel_id,
  blk_name: '',
  blk_type: '',
  language: '',
  html_path: siteDetailInfo.value.html_path,
  file_path: '',
  content: '',
  level: '',
  remark: formatDatetime(new Date())
}
const formRef = ref<FormInstance>()
const blockData = reactive<blockData>({ ...blockData_raw })

const handleTypeChange = () => {
  if (lockType.has(blockData.blk_type as number)) {
    blockData.level = 'A'
  }
}

const handleSave = async ( formEl: FormInstance | undefined ) => {
  if (!formEl) {
    return
  }
  await formEl.validate(( valid ) => {
    if (valid) {
      useTryCatch( async () => {
        setLoading(true)

        const params = {
          data: {
            ...blockData,
            ...parent_id ? { parent_id } : {}
          },
          ...is_public ? { is_public: 1 } : {}
        }
        const api = type === 'add' ? addBlock : editBlock
        const res = await api(params, blk_id as number)
        if (res.code === 200) {
          basicStore.setRefresh(true)
          type === 'add' && handleBack()
          ElMessage.success(`${type === 'add' ? '添加' : '编辑'}成功`)
        } else {
          ElMessage.error(res.msg)
        }
        setLoading(false)
      }, () => setLoading(false) )
    }
  })
}
const handleBack = () => {
  delVisitedView(route)
  router.push( { name: is_public ? 'PublicBlockManagement' : 'BlockManagement' } )
}

const getDetail = (id?: number) => {
  useTryCatch( async () => {
    setLoading(true)

    const res = await getBlockDetail(id || blk_id as number)
    if (res.code === 200) {
      const data = res.data
      Object.keys(blockData_raw).forEach((key) => {
        if (data[key] !== undefined) {
          blockData[key] = data[key]
        }
      })
      blockData.remark = formatDatetime(new Date())
      if (type === 'add') {
        blockData.channel_id = ''
      }
      updateHistoryTitle(route, blockData.blk_name)
    }
    setLoading(false)
  }, () => setLoading(false) )
}

const getHistoryDetail = () => {
  useTryCatch( async () => {
    const res = await historyDetail(blk_id as number, version_id as number, site_id_all.value)
    if (res.code === 200) {
      const data = res.data[0]
      Object.keys(blockData_raw).forEach((key) => {
        if (data[key] !== undefined) {
          blockData[key] = data[key]
        }
      })
      updateHistoryTitle(route, blockData.blk_name)
    }
  } )
}

const handleGetLangList = () => {
  if (langList.value.length === 0) {
    useTryCatch(async () => {
      const res = await getLangList()
      if (res.code === 200) {
        const list = res.data
        langList.value = list
      }
    })
  }
}

changeViewTitle(route, type === 'view' ? '块历史详情' : type === 'edit' ? '编辑块' : '添加块')
if (type === 'add') {
  updateHistoryTitle(route, '添加块')
}
if (blk_id) {
  type === 'view'
  ? getHistoryDetail()
  : getDetail()

  handleGetLangList()
}
if (type === 'add' && parent_id) {
  getDetail(parent_id)
}
</script>