<template>
  <div class="scroll-y with-flex">
    <QueryForm
      :form-list="formList"
      :loading="loading"
      @search="handleSearch" 
      @reset="reset"
    />
    <div v-loading="loading" class="sticky-table">
      <CustomTable
        ref="tableRef"
        :columns="columns"
        :data="tableData"
        :table-method="getList"
        :customize-head="true"
        :selectable="true"
        :operate-list="operateList"
        @selection-change="handleSelectionChange"
        @sort-change="handleSortChange"
      />
    </div>
    <Teleport to="body">
      <TempGroup 
        v-if="showGruopTempModal"
        :current-row="currentRow"
        @success="handleCopySuccess"
        @close="showGruopTempModal = false"
      />
    </Teleport>
    <Teleport to="body">
      <CopyTempPage 
        v-if="showCopyTempPageModal"
        ref="copyTempPageRef"
        :current-row="currentRow"
        @success="ElMessage.success('页面复用成功')"
        @close="showCopyTempPageModal = false"
      />
    </Teleport>
  </div>
</template>
 
<script setup lang="tsx">
import { ref, reactive, toRef, computed } from 'vue'
import { ElMessage, ElButton, ElDropdown, ElDropdownMenu, ElDropdownItem, ElMessageBox } from 'element-plus'
import { Check, Close } from '@element-plus/icons-vue'
import { useRoute, useRouter } from 'vue-router'
import { storeToRefs } from 'pinia'
import { useParamsStore, useUserStroe } from '@/store'
import { useBasicStore } from '@/store/basic'
import useTryCatch from '@/hooks/use-try-catch'
import { getHost } from '@/hooks/use-env'
import useLoading from '@/hooks/use-loading'
import { getDownloadLocalToken } from '@/hooks/use-local-auth'
import { useTempGenerate } from '@/hooks/use-generate'
import { getTemplateList, changeStatus } from '@/api/template-management'
import type { queryParams, listItem } from '@/api/template-management'
import Listener from '@/utils/site-channel-listeners'
import { stringify } from '@/utils/common'

import QueryForm from '@/components/QueryForm/index.vue'
import CustomTable from '@/components/CustomTable/index.vue'
import batchGenModal from './components/batchGenModal'
const TempGroup = defineAsyncComponent(() => import('./components/editGroupTemplate.vue'))
const CopyTempPage = defineAsyncComponent(() => import('./childTemplate/components/copyTempPage.vue'))

defineOptions( { name: 'TemplateManage' } )

const router = useRouter()
const route = useRoute()
const tpl_ids = route.query.tpl_id as string || route.query.entity_ids as string || ''
const { site_id_all, channel_item_all, userList, moduleList, moduleTypeMap } = storeToRefs(useParamsStore())
const { banIds, isBanned } = useUserStroe()
const basicStore = useBasicStore()
const { loading, setLoading } = useLoading()
const queryParams_raw = {
  tpl_ids: '',
  name: '',
  module: '',
  type: '',
  user_id_e: '',
  user_id_a: '',
  state: '',
  block_id: '',
  channel_id: '',
  parent_id: '',
  time_field: '',
  start_time: '',
  end_time: ''
}
const queryParams = reactive<queryParams>({...queryParams_raw, tpl_ids})
const tableRef = ref()
const timeRange = ref('')
const formList = ref([
  {
    label: '模板id',
    clearable: true,
    placeholder: '模板id',
    value: toRef(queryParams, 'tpl_ids'),
    component: 'input',
    parse: true
  },
  {
    label: '模板名称',
    clearable: true,
    placeholder: '模板名称',
    value: toRef(queryParams, 'name'),
    component: 'input',
  },
  {
    label: '模板类别',
    clearable: true,
    placeholder: '模板类别',
    value: toRef(queryParams, 'module'),
    component: 'select',
    selections: moduleList,
    handleChange: () => {
      queryParams.type = ''
    }
  },
  {
    label: '模板类型',
    clearable: true,
    placeholder: '模板类型',
    value: toRef(queryParams, 'type'),
    component: 'select',
    selections: computed(() => moduleTypeMap.value[queryParams.module] || [])
  },
  {
    label: '更新人',
    clearable: true,
    placeholder: '更新人',
    value: toRef(queryParams, 'user_id_e'),
    component: 'select',
    labelKey: 'real_name',
    valueKey: 'user_id',
    append: true,
    selections: userList
  },
  {
    label: '创建人',
    clearable: true,
    placeholder: '创建人',
    value: toRef(queryParams, 'user_id_a'),
    component: 'select',
    labelKey: 'real_name',
    valueKey: 'user_id',
    append: true,
    selections: userList
  },
  {
    label: '模板状态',
    clearable: true,
    placeholder: '模板状态',
    value: toRef(queryParams, 'state'),
    component: 'select',
    append: true,
    selections: [
      {
        label: '启用',
        value: 'able'
      },
      {
        label: '禁用',
        value: 'disable'
      },
    ]
  },
  {
    label: '模板集ID',
    clearable: true,
    placeholder: '模板集ID',
    value: toRef(queryParams, 'parent_id'),
    component: 'input',
    append: true,
  },
  {
    component: 'time-range-tab',
    field_key: toRef(queryParams, 'time_field'),
    value: timeRange,
    start_time: toRef(queryParams, 'start_time'),
    end_time: toRef(queryParams, 'end_time'),
    append: true,
    field_list: [
      {
        value: 'add_time',
        label: '创建时间'
      },
      {
        value: 'edit_time',
        label: '更新时间'
      },
    ],
  },
])
const columns = ref([
  {
    title: '模板ID',
    dataKey: 'tpl_id',
    width: 100,
    sortable: true,
  },
  {
    title: '模板名称',
    dataKey: 'name',
    minWidth: 300,
  },
  {
    title: '模板集ID',
    dataKey: 'parent_id',
    width: 120,
    cellRenderer: (scope: { row: listItem }) => (
      <u class='with-hand' onClick={() => handleGroupTempManage(scope.row)}> { scope.row.parent_id || scope.row.tpl_id } </u>
    )
  },
  {
    title: '模板渠道',
    dataKey: 'channel_name',
    minWidth: 120,
    showOverflowTooltip: true,
    cellRenderer: (scope: { row: listItem }) => (
      <span class='dot' style={ { '--color': scope.row.color } }>
        {scope.row.channel_name}
      </span>
    )
  },
  {
    title: '发布状态',
    dataKey: 'pub_switch',
    width: 90,
    hideColumn: true,
    cellRenderer: (scope: { row: listItem }) => (
      <span style={ { color: scope.row.pub_switch === 'able' ? 'var(--el-color-success)' : 'var(--el-color-error)' } }>
        { scope.row.pub_switch === 'able' ? <Check style='width: 16px' /> : <Close style='width: 16px' /> }
      </span>
    )
  },
  {
    title: '模板等级',
    dataKey: 'level',
    width: 90,
    showOverflowTooltip: true,
    cellRenderer: (scope: { row: listItem }) => (
      <span style={ { 
        color: scope.row.level === 'A' ? 'var(--el-color-error)' : 'inherit', 
        fontWeight: scope.row.level === 'A' ? 700 : 400 
      } }>
        { scope.row.level || '--' }
      </span>
    )
  },
  {
    title: '创建人',
    dataKey: 'user_name_a',
  },
  {
    title: '创建时间',
    dataKey: 'add_time',
    minWidth: 160,
    sortable: true
  },
  {
    title: '更新人',
    dataKey: 'user_name_e',
  },
  {
    title: '更新时间',
    dataKey: 'edit_time',
    minWidth: 160,
    sortable: true
  },
  {
    title: '模板状态',
    dataKey: 'state',
    width: 90,
    hideColumn: true,
    cellRenderer: (scope: { row: listItem }) => (
      <span style={ { color: scope.row.state === 'able' ? 'var(--el-color-success)' : 'var(--el-color-error)' } }>
        {scope.row.state === 'able' ? '已启用' : scope.row.state === 'disable' ? '已禁用' : '已删除'}
      </span>
    )
  },
  {
    title: '操作',
    dataKey: '',
    fixed: 'right',
    width: 320,
    cellRenderer: (scope: { row: listItem }) => (
      <>
        <ElButton type='primary' plain onClick={() => handleEdit(scope.row)}>编辑</ElButton>
        <ElButton type='primary' plain onClick={() => handlePageList(scope.row)}>页面列表</ElButton>
        <ElButton type='success' plain onClick={() => handleGeneratePage(scope.row)} v-RoleIdsBanPermission={banIds}>生成页面</ElButton>
        <ElDropdown trigger='click' style={{ marginLeft: '10px' }}>
          {
            {
              default: () => <ElButton type='primary' link> <strong>...</strong> </ElButton>,
              dropdown: () => 
              <ElDropdownMenu>
                <ElDropdownItem>
                  <ElButton type="primary" plain onClick={() => handleHistory(scope.row)}>历史版本</ElButton>
                  <ElButton type={scope.row.state === 'disable' ? 'success' : 'danger'} plain onClick={() => handleOperate(scope.row)}>
                    {scope.row.state === 'disable' ? '启用' : '禁用'}
                  </ElButton>
                  <ElButton disabled={scope.row.state === 'disable'} type="primary" plain onClick={() => handleEditGroupTemp(scope.row)}>复用模板</ElButton>
                  <ElButton type="primary" plain onClick={() => handleCopyTempPage(scope.row)}>复用页面</ElButton>
                </ElDropdownItem>
              </ElDropdownMenu>
            }
          }
        </ElDropdown>
      </>
    )
  },
])
const operateList = ref([
  {
    title: '创建模板',
    button: true,
    append: true,
    method: () => {
      basicStore.delCachedView('TemplateManageOperate')
      router.push({
        name: 'TemplateManageOperate',
        query: {
          type: 'add',
          parent_id: 0
        }
      })
    },
  },
  {
    title: '',
    icon: 'refresh',
    append: true,
    tooltip: '刷新列表',
    method: () => {
      reset()
      handleSearch()
    },
  },
  {
    title: '导出页面EXCEL',
    icon: 'export',
    method: () => {
      const tpl_ids = tableSelections.value.filter(({ state }) => state !== 'disable').map(({ tpl_id }) => tpl_id).join(',')
      const { page, pageSize } = tableRef.value
      const S = {} as any
      Object.entries(queryParams).forEach(([ key, value ]) => {
        key !== 'tpl_ids' && (S[key] = value)
      })
      if (channel_item_all.value.channel_id) {
        S.channel_id = channel_item_all.value.channel_id
      }
      const params = {
        S,
        page,
        page_size: pageSize,
        site_id: site_id_all.value
      }
      const qsParams = stringify(params)
      const qsParam = stringify({
        site_id: site_id_all.value,
        is_export: 1,
        S: { tpl_ids }
      })
      location.href = `${getHost()}/api/v1/templates/pages?&ws-login-token=${getDownloadLocalToken()}&${qsParam}&${qsParams}`
    },
    disabled: computed(() => tableSelections.value.length > 0 ? false : true)
  },
  ...isBanned() ? [] : [
    {
      title: '批量生成页面',
      icon: 'generate',
      method: () => {
        const list = tableSelections.value
        let buy_count = 0
        for ( const item of list ) {
          item.type === 'buy' && buy_count++
        }
        if ( buy_count && buy_count !== list.length ) {
          return ElMessageBox.confirm(`检测到当前批量发布模板中，包含${buy_count}个购买页模板，购买页模板请单独发布!`, '提示', {
            type: 'warning',
            callback: () => {}
          })
        }

        const tpl_ids = tableSelections.value.map(({ tpl_id }) => tpl_id).join(',')
        batchGenModal((selection: string[]) => {
          const states = selection.toString()
          useTempGenerate({
            tpl_ids,
            site_id: site_id_all.value,
            curr: 0,
            ...states ? { stage: states } : {},
            ...buy_count > 0 ? { store_buy_type: 2 } : {}
          }, () => {
            refresh()
          })
        })
      },
      disabled: computed(() => tableSelections.value.length > 0 ? false : true)
    }
  ]
])
const tableData = ref<listItem[]>([])
const tableSelections = ref<listItem[]>([])
const showGruopTempModal = ref(false)
const showCopyTempPageModal = ref(false)
const copyTempPageRef = ref()
const currentRow = ref()

const reset = () => {
  timeRange.value = ''
  Object.entries(queryParams_raw).forEach(([ key, value ]) => {
    queryParams[key] = value
  })
}
const refresh = () => {
  const { page, pageSize } = tableRef?.value?.getPagination()
  getList( { page, pageSize } )
}
const handleSearch = () => {
  tableRef.value.setPage(1)
  refresh()
}
const getList = ( { page, pageSize }, prop?: string, order?: 'asc'|'desc' ) => {
  if (tableRef?.value) {
    const sortObj = tableRef?.value?.getSort()
    if (sortObj.order && !order) {
      prop = sortObj.prop
      order = sortObj.order
    }
  }
  
  useTryCatch( async () => {
    const params = {
      S: {
        ...queryParams,
        channel_id: channel_item_all.value.channel_id as number
      },
      page,
      page_size: pageSize,
      site_id: site_id_all.value,
      ...order ? {
        sort_key: prop,
        sort_type: order
      } : {}
    }
    setLoading(true)
    const res = await getTemplateList(params)
    if (res.code === 200) {
      tableRef.value?.setTotal(res.data.total)
      tableData.value = res.data.items
    } else {
      ElMessage.error(res.msg)
    }
    setLoading(false)
  }, () => setLoading(false))
}
const handleSelectionChange = (selections: listItem[]) => {
  tableSelections.value = selections
}
const handleSortChange = ({ prop, order }: { prop: string; order: 'ascending'|'descending'|null }) => {
  const { page, pageSize } = tableRef?.value?.getPagination()
  getList( { page, pageSize }, prop, order ? order.replace('ending', '') as 'asc'|'desc' : undefined )
}
/* table methods */
const handleEdit = (row: listItem) => {
  basicStore.delCachedView('TemplateManageOperate')
  router.push({
    name: 'TemplateManageOperate',
    query: {
      type: 'edit',
      tpl_id: row.tpl_id,
      parent_id: row.parent_id,
      channel_id: row.channel_id,
    }
  })
}
const handlePageList = (row: listItem) => {
  const name = 'TemplatPageManagement'
  if (basicStore.cachedViews.indexOf(name) > -1) {
    basicStore.delCachedView(name)
  }
  router.push({
    name: 'TemplatPageManagement',
    query: {
      tpl_id: row.tpl_id,
      tpl_name: row.name,
      module: row.module,
      tpl_type: row.type,
      state: row.state,
      channel_id: row.channel_id,
      parent_id: row.parent_id.toString()
    }
  })
}
const handleGeneratePage = (row: listItem) => {
  const tpl_ids = `${row.tpl_id}`
  batchGenModal((selection: string[]) => {
    const states = selection.toString()
    useTempGenerate({
      tpl_ids,
      site_id: site_id_all.value,
      curr: 0,
      ...states ? { stage: states } : {},
      ...row.type === 'buy' ? { store_buy_type: 2 } : {}
    }, () => {
      refresh()
    })
  })
}
const handleHistory = (row: listItem) => {
  basicStore.delCachedView('TemplateHistory')
  router.push({
    name: 'TemplateHistory',
    query: {
      id: row.tpl_id
    }
  })
}
const handleOperate = (row: listItem) => {
  const isDisable = row.state === 'disable' ? true : false
  ElMessageBox.confirm(
    isDisable 
    ? `请确认是否启用此模板`
    : `<p>此操作会将该模板下页面文件, 实时从线上下架, 请确认清楚, 当前是否要下架线上页面</p>
      <div style="color: var(--el-color-danger)">Tips: 如模板等级为A级, 需要审核通过后, 才会自动发布（下架任务）</div>
    `,
    '提示',
    {
      dangerouslyUseHTMLString: true,
      confirmButtonText: '确认',
      cancelButtonText: '取消',
      confirmButtonClass: isDisable ? '' : 'el-button--danger',
      callback: (action: string) => {
        if (action === 'confirm') {
          useTryCatch(
            async () => {
              const res = await changeStatus(row.tpl_id, { state: isDisable ? 'able' : 'disable', site_id: site_id_all.value })
              if (res.code === 200) {
                refresh()
                ElMessage.success('操作成功')
              } else {
                ElMessage.error(res.msg)
              }
            }
          )
        }
      }
    }
  )
}
const handleEditGroupTemp = (row: listItem) => {
  currentRow.value = row
  showGruopTempModal.value = true
}
const handleGroupTempManage = (row: listItem) => {
  router.push({
    name: 'CreateChildTemplate',
    query: {
      tpl_id: row.parent_id || row.tpl_id
    }
  })
}
const handleCopyTempPage = (row: listItem) => {
  currentRow.value = row
  showCopyTempPageModal.value = true
}
const handleCopySuccess = () => {
  handleSearch()
  showGruopTempModal.value = false
}
/* table methods */
onActivated(() => {
  if (basicStore.refresh) {
    basicStore.setRefresh(false)
    refresh()
  }
})
Listener(route, () => {
  reset()
  handleSearch()
})
</script>