{
  "extends": "./tsconfig.base.json",
  "compilerOptions": {
    "jsx": "preserve",
    // 解决ts报错 using jsx fragments requires fragmentfactory 'react' to be in scope, but it could not be found, 添加下面2行
    "jsxFactory": "h",
    "jsxFragmentFactory": "h",
    "paths": {
      "@/*": ["src/*"],
      "~/*": ["typings/*"]
    },
    "types": ["vite/client"]
  },
  "include": ["src", "typings"],
  "exclude": ["node_modules", "**/dist"]
}
