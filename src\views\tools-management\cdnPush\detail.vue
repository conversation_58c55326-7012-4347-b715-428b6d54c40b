<template>
  <div class="scroll-y with-flex">
    <QueryForm
      :form-list="formList"
      :loading="loading"
      @search="handleSearch"
      hide-reset
    />
    <div v-loading="loading" class="sticky-table">
      <CustomTable
        ref="tableRef"
        :columns="columns"
        :data="tableData"
        :table-method="getList"
      />
    </div>
  </div>
</template>

<script setup lang="tsx">
import { ref, reactive, toRef } from 'vue'
import { useRoute } from 'vue-router'
import { ElMessage } from 'element-plus'
import useLoading from '@/hooks/use-loading'
import useTryCatch from '@/hooks/use-try-catch'
import { getCdnDetailList } from '@/api/tools-management'
import type { cdnDetailListItem } from '@/api/tools-management'

import QueryForm from '@/components/QueryForm/index.vue'
import CustomTable from '@/components/CustomTable/index.vue'

defineOptions( { name: 'CdnDetail' } )

const route = useRoute()
const { task_id } = route.query as unknown as { task_id: number }
const { loading, setLoading } = useLoading()
const statusMap = new Map([
  [0, '刷新中'],
  [1, '成功'],
  [2, '失败'],
])

const queryParams = reactive({
  id: +task_id,
  state: '',
})
const formList = ref([
  {
    label: '刷新状态',
    clearable: true,
    placeholder: '刷新状态',
    value: toRef(queryParams, 'state'),
    component: 'select',
    selections: [
      {
        label: '刷新中',
        value: 0
      },
      {
        label: '成功',
        value: 1
      },
      {
        label: '失败',
        value: 2
      },
    ]
  },
])

const tableData = ref<cdnDetailListItem[]>([])
const tableRef = ref()
const columns = ref([
  {
    title: '所属渠道',
    dataKey: 'channel_code',
    minWidth: 100,
  },
  {
    title: '页面ID',
    dataKey: 'page_id',
    minWidth: 100,
  },
  {
    title: '页面url',
    dataKey: 'page_url',
    minWidth: 150,
  },
  {
    title: '线上地址',
    dataKey: 'line_url',
    minWidth: 280,
    cellRenderer: (scope: { row: cdnDetailListItem }) => (
      <a href={scope.row.line_url} target='_blank'>{scope.row.line_url}</a>
    )
  },
  {
    title: '刷新状态',
    dataKey: 'status',
    width: 80,
    cellRenderer: (scope: { row: cdnDetailListItem }) => (
      <span style={ { color: scope.row.state === 2 ? 'var(--el-color-danger)' : scope.row.state === 1 ? 'var(--el-color-success)' : '' } }>
        {statusMap.get(scope.row.state) || '--'}
      </span>
    )
  },
  {
    title: '当前状态更新时间',
    dataKey: 'add_time',
    minWidth: 200,
  },
  {
    title: '备注',
    dataKey: 'remark',
    minWidth: 150,
  },
])

const refresh = () => {
  const { page, pageSize } = tableRef?.value?.getPagination()
  getList( { page, pageSize } )
}
const handleSearch = () => {
  tableRef.value.setPage(1)
  refresh()
}

const getList = ( { page, pageSize } ) => {
  useTryCatch( async () => {
    setLoading(true)
    const params = {
      ...queryParams,
      page,
      page_size: pageSize
    }

    const res = await getCdnDetailList(params)
    if (res.code === 200) {
      tableData.value = res.data.list
      tableRef.value?.setTotal(res.data.total)
    } else {
      ElMessage.error(res.msg)
    }
    setLoading(false)
  }, () => setLoading(false) )
}
</script>