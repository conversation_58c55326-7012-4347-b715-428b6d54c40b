<template>
  <div class="scroll-y with-flex">
    <h2> 页面ID: {{ page_id }} </h2>
    <div v-loading="loading" class="sticky-table" style="min-height: 300px;">
      <CustomTable
        ref="tableRef"
        :columns="columns"
        :data="tableData"
        :table-method="getList"
        :index-method="(index: number) => index + 1"
        hide-pagination
      />
    </div>
  </div>
</template>

<script setup lang="tsx">
import { ref } from 'vue'
import { ElMessage } from 'element-plus'
import { useRoute } from 'vue-router'
import useTryCatch from '@/hooks/use-try-catch'
import useLoading from '@/hooks/use-loading'
import { pageQualityCheck } from '@/api/page-management'
import type { pageQualityItem } from '@/api/page-management'

import CustomTable from '@/components/CustomTable/index.vue'

const route = useRoute()
const { loading, setLoading } = useLoading()
const { tpl_id, page_id, site_id } = route.query as unknown as {
  tpl_id: number;
  page_id: number;
  site_id: number;
}

const tableData = ref([])
const columns = ref([
  {
    title: '质量问题类型',
    dataKey: 'title',
    minWidth: 100,
  },
  {
    title: '检测状态',
    dataKey: 'error',
    width: 100,
    cellRenderer: (scope: { row: pageQualityItem }) => (
      <span style={ { color: scope.row.error === 1 ? 'var(--el-color-danger)' : 'inherit' } }> { scope.row.error === 1 ? '异常' : '无异常' } </span>
    ), 
  },
  {
    title: '错误数量',
    dataKey: 'num',
    width: 100,
  },
  {
    title: '错误详情',
    dataKey: 'error_detail',
    minWidth: 150,
    cellRenderer: (scope: { row: pageQualityItem }) => (
      <>
        {  
          scope.row.error_detail.split(',').map(( str1: string ) => <div style='white-space: normal'>
            { 
              str1.indexOf('<br/>') === -1
              ? str1
              : str1.split(',').map(( str2: string ) => <div> { str2 } </div>)
            }
          </div>) 
        }
      </>
    )
  },
  {
    title: 'FAQ文档',
    dataKey: 'faq',
    minWidth: 200,
    cellRenderer: (scope: { row: pageQualityItem }) => (
      <>
        {
          !scope.row.faq
          ? '/'
          : <a href={ scope.row.faq }  target='_blank' style={ { color: 'var(--el-color-primary)', textDecoration: 'underline' } }> { scope.row.faq } </a>
        }
      </>
    )
  },
])

const getList = () => {
  useTryCatch( async () => {
    setLoading(true)

    const res = await pageQualityCheck(tpl_id, page_id, site_id)
    if (res.code === 200) {
      tableData.value = Object.values(res.data as {})
    } else {
      ElMessage.error(res.msg)
    }
    setLoading(false)
  }, () => setLoading(false) )
}
</script>