import axiosReq from './axios'
import qs from 'query-string'

export type buyInfo = {
  card_num: number;
  card_type: string;
  ip_option: string;
  card_detail: {
    [key: string]: {
      default: buyUrlItem[];
      ip_option: buyUrlItem[];
      tab_switch: buyUrlItem[];
    }
  };
}

export type buyUrlItem = {
  sku_id: string;
  activity_id: string;
  additional_sku: string;
  additional_activity: string;
  url_ab_testing: string;
  url: string;
}

export type urlListQueryParams = {
  site_id: number;
  channel_id?: number; 
  url?: string; 
  page_id?: string;
  sku_id?: string;
  page?: number; 
  page_size?: number;
}

export type urlListItem = {
  id: number;
  page_id: string;
  position: string;
  sku_id: string;
  channel_id: number;
  buy_url: string;
  time: string;
  position_name: string;
}

/* 购买页定制化配置 */

// 购买卡片基础配置
export const getBuyCardConfig = () => 
  axiosReq('get', `/api/v1/buy/cardConf`)

// 购买链接规范化
export const getBuyUrl = (urls: string) => 
  axiosReq('post', `/api/v1/buy/url_parse`, { urls })

// 购买链接与页面映射列表
export const getBuyUrlList = (params: urlListQueryParams) =>
  axiosReq('get', '/api/v1/page/buy_url/list', {
    params,
    paramsSerializer: {
      serialize: (obj) => qs.stringify(obj)
    }
  })

// 购买列表编辑链接
export const editBuyUrl = (data: { id: string|number; url: string; }) =>
  axiosReq('post', '/api/v1/page/buy_url/edit', data)

/* 购买页定制化配置 */