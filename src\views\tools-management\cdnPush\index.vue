<template>
  <div class="scroll-y with-flex">
    <QueryForm
      :form-list="formList"
      :loading="loading"
      @search="handleSearch"
      hide-reset
    />
    <div v-loading="loading" class="sticky-table">
      <CustomTable
        ref="tableRef"
        :columns="columns"
        :data="tableData"
        :table-method="getList"
      />
    </div>
  </div>
</template>

<script setup lang="tsx">
import { ref, reactive, toRef } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { ElMessage, ElButton, ElMessageBox } from 'element-plus'
import { storeToRefs } from 'pinia'
import { useParamsStore } from '@/store'
import useLoading from '@/hooks/use-loading'
import useTryCatch from '@/hooks/use-try-catch'
import { getCdnList, refreshCdn } from '@/api/tools-management'
import type { cdnQueryParams, cdnListItem } from '@/api/tools-management'
import Listener from '@/utils/site-channel-listeners'

import QueryForm from '@/components/QueryForm/index.vue'
import CustomTable from '@/components/CustomTable/index.vue'

defineOptions ( { name: 'CdnManagement' } )

const route = useRoute()
const router = useRouter()
const { site_id_all, channel_item_all, userList } = storeToRefs(useParamsStore())
const { loading, setLoading } = useLoading()
const statusMap = new Map([
  [1, '已收录'],
  [2, '执行中'],
  [3, '执行成功'],
  [4, '执行失败']
])

const queryParams = reactive<cdnQueryParams>({
  site_id: site_id_all.value,
  user_id: '',
  channel_id: channel_item_all.value.channel_id,
  status: '',
  url: '',
})
const formList = ref([
  {
    label: '发起人',
    clearable: true,
    placeholder: '发起人',
    value: toRef(queryParams, 'user_id'),
    component: 'select',
    labelKey: 'real_name',
    valueKey: 'user_id',
    selections: userList
  },
  {
    label: '状态',
    clearable: true,
    placeholder: '状态',
    value: toRef(queryParams, 'status'),
    component: 'select',
    selections: [
      {
        label: '已收录',
        value: 1
      },
      {
        label: '执行中',
        value: 2
      },
      {
        label: '执行成功',
        value: 3
      },
      {
        label: '执行失败',
        value: 4
      }
    ]
  },
  {
    label: '页面URL',
    clearable: true,
    placeholder: '页面URL',
    value: toRef(queryParams, 'url'),
    component: 'input',
  },
])

const tableData = ref<cdnListItem[]>([])
const tableRef = ref()
const columns = ref([
  {
    title: '任务ID',
    dataKey: 'id',
    width: 150,
  },
  {
    title: '所属渠道',
    dataKey: 'channel_code',
    minWidth: 150,
  },
  {
    title: '发起人',
    dataKey: 'user_name',
    minWidth: 150,
  },
  {
    title: '文件数量',
    dataKey: 'file_count',
    width: 150,
  },
  {
    title: '任务开始时间',
    dataKey: 'add_time',
    minWidth: 200,
  },
  {
    title: '刷新状态',
    dataKey: 'status',
    minWidth: 150,
    cellRenderer: (scope: { row: cdnListItem }) => (
      <span style={ { color: scope.row.status === 4 ? 'var(--el-color-danger)' : scope.row.status === 3 ? 'var(--el-color-success)' : '' } }>
        {statusMap.get(scope.row.status) || '--'}
      </span>
    )
  },
  {
    title: '操作',
    dataKey: '',
    fixed: 'right',
    width: 220,
    cellRenderer: (scope: { row: cdnListItem }) => (
      <>
        <ElButton type='primary' plain onClick={() => handleDetail(scope.row)}> 查看详情 </ElButton>
        <ElButton type='success' plain disabled={ scope.row.status !== 4 } onClick={() => handleRefresh(scope.row)}> 重新刷新 </ElButton>
      </>
    )
  }
])

const refresh = () => {
  const { page, pageSize } = tableRef?.value?.getPagination()
  getList( { page, pageSize } )
}
const handleSearch = () => {
  tableRef.value.setPage(1)
  refresh()
}

const getList = ( { page, pageSize } ) => {
  useTryCatch( async () => {
    setLoading(true)
    const params = {
      ...queryParams,
      site_id: site_id_all.value,
      channel_id: channel_item_all.value.channel_id,
      page,
      page_size: pageSize
    }

    const res = await getCdnList(params)
    if (res.code === 200) {
      tableData.value = res.data.list
      tableRef.value?.setTotal(res.data.total)
    } else {
      ElMessage.error(res.msg)
    }
    setLoading(false)
  }, () => setLoading(false) )
}

const handleDetail = (row: cdnListItem) => {
  router.push({
    name: 'CdnDetail',
    query: {
      task_id: row.id
    }
  })
}
const handleRefresh = (row: cdnListItem) => {
  ElMessageBox.confirm(
    '请确认是否需要进行重新刷新操作',
    '提示',
    {
      confirmButtonText: '确认',
      cancelButtonText: '取消',
      type: 'warning',
      callback: (action: string) => {
        if (action === 'confirm') {
          useTryCatch( async () => {
            setLoading(true)
            const res = await refreshCdn(row.id)
            if (res.code === 200) {
              refresh()
              ElMessage.success(res.msg)
            } else {
              ElMessage.error(res.msg)
            }
            setLoading(false)
          }, () => setLoading(false) )
        }
      }
    }
  )
}

Listener(route, () => {
  handleSearch()
})
</script>