<svg width="44" height="44" viewBox="0 0 44 44" fill="none" xmlns="http://www.w3.org/2000/svg">
<g clip-path="url(#clip0_1640_30212)">
<rect width="44" height="44" rx="10" fill="url(#paint0_linear_1640_30212)"/>
<g filter="url(#filter0_d_1640_30212)">
<path fill-rule="evenodd" clip-rule="evenodd" d="M31.3046 34.8277C33.3944 34.8398 35.2762 33.3868 35.7278 31.2624L38.7033 17.2637C39.22 14.8327 37.6682 12.4431 35.2372 11.9264L34.9663 11.8688C34.9885 12.0392 34.9999 12.2128 34.9999 12.3886V29.9996C34.9999 32.3096 33.4333 34.2538 31.3046 34.8277Z" fill="url(#paint1_linear_1640_30212)" fill-opacity="0.7" shape-rendering="crispEdges"/>
</g>
<g filter="url(#filter1_d_1640_30212)">
<path fill-rule="evenodd" clip-rule="evenodd" d="M13.5 8C11.0147 8 9 10.0147 9 12.5V31.5C9 33.9853 11.0147 36 13.5 36H30.5C32.9853 36 35 33.9853 35 31.5V12.5C35 10.0147 32.9853 8 30.5 8H13.5ZM19 28.8475C19 29.3998 19.4477 29.8475 20 29.8475H24C24.5523 29.8475 25 29.3998 25 28.8475V22.0975H29.6605C30.0216 22.0975 30.1978 21.657 29.9364 21.4079L22.276 14.1104C22.1215 13.9632 21.8787 13.9632 21.7242 14.1104L14.0637 21.4079C13.8023 21.657 13.9786 22.0975 14.3396 22.0975L19 22.0975V28.8475Z" fill="url(#paint2_linear_1640_30212)"/>
</g>
</g>
<defs>
<filter id="filter0_d_1640_30212" x="25.3046" y="8.86881" width="19.498" height="34.9589" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="3"/>
<feGaussianBlur stdDeviation="3"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.430798 0 0 0 0 0.398247 0 0 0 0 0.845833 0 0 0 0.69 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_1640_30212"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_1640_30212" result="shape"/>
</filter>
<filter id="filter1_d_1640_30212" x="2" y="3" width="42" height="44" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dx="1" dy="3"/>
<feGaussianBlur stdDeviation="4"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.423204 0 0 0 0 0.390399 0 0 0 0 0.829167 0 0 0 0.68 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_1640_30212"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_1640_30212" result="shape"/>
</filter>
<linearGradient id="paint0_linear_1640_30212" x1="22" y1="0" x2="22" y2="49.5" gradientUnits="userSpaceOnUse">
<stop stop-color="#6A61FF"/>
<stop offset="1" stop-color="#AAA3FF"/>
</linearGradient>
<linearGradient id="paint1_linear_1640_30212" x1="36.5" y1="9.99967" x2="36" y2="38.4997" gradientUnits="userSpaceOnUse">
<stop stop-color="white"/>
<stop offset="1" stop-color="#CBC7FF" stop-opacity="0.44"/>
</linearGradient>
<linearGradient id="paint2_linear_1640_30212" x1="22" y1="14" x2="22" y2="35" gradientUnits="userSpaceOnUse">
<stop stop-color="white"/>
<stop offset="1" stop-color="#DCD9FF"/>
</linearGradient>
<clipPath id="clip0_1640_30212">
<rect width="44" height="44" rx="10" fill="white"/>
</clipPath>
</defs>
</svg>
