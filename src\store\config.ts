import { defineStore } from 'pinia'
import { langTitle } from '@/hooks/use-common'
import settings from '@/settings'
import { toggleHtmlClass } from '@/theme/utils'
import { i18n } from '@/lang'

type skinEnum = 'oxide'|'oxide-dark'

export const useConfigStore = defineStore('config', {
  state: () => {
    return {
      language: settings.defaultLanguage,
      theme: settings.defaultTheme,
      size: settings.defaultSize,
      tinymceSkin: 'oxide' as skinEnum
    }
  },
  getters: {
    fixBottomHeight: (state) => state.size === 'large' ? 50 : state.size === 'small' ? 34 : 42
  },
  persist: {
    storage: localStorage,
    paths: ['language', 'theme', 'size']
  },
  actions: {
    setTheme(data: string) {
      this.theme = data
      toggleHtmlClass(data)
      this.setTinymceSkin( data === 'dark' ? 'oxide-dark' : 'oxide' )
    },
    setSize(data: string) {
      this.size = data
    },
    setLanguage(lang: string, title) {
      const { locale }: any = i18n.global
      this.language = lang
      locale.value = lang
      document.title = langTitle(title) // i18 page title
    },
    setTinymceSkin(data: skinEnum) {
      this.tinymceSkin = data
    },
  }
})
