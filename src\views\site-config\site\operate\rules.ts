import { reactive } from 'vue'
import type { FormRules } from 'element-plus'

export const formRules = reactive<FormRules>({
  name: [ { required: true, message: '站点名称不能为空', trigger: 'blur' } ],
  host: [ { required: true, message: '站点域名不能为空', trigger: 'blur' } ],
  channel_code: [ { required: true, message: '渠道不能为空', trigger: 'change' } ],
  html_path: [ { required: true, message: '静态文件路径不能为空', trigger: 'blur' } ],
  host_image: [ { required: true, message: '图片主机路径不能为空', trigger: 'blur' } ],
  image_path: [ { required: true, message: '图片文件存放路径不能为空', trigger: 'blur' } ],
  host_video: [ { required: true, message: '视频主机路径不能为空', trigger: 'blur' } ],
  video_path: [ { required: true, message: '视频文件存放路径不能为空', trigger: 'blur' } ],
  task_site_name: [ { required: true, message: '站点名称不能为空', trigger: 'blur' } ],
  task_site_host: [ { required: true, message: '站点域名不能为空', trigger: 'blur' } ],
  task_site_path: [ { required: true, message: '站点路径不能为空', trigger: 'blur' } ],
})