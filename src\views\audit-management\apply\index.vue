<template>
  <div class="scroll-y with-flex">
    <QueryForm
      hide-reset
      :form-list="formList"
      :loading="loading"
      @search="handleSearch" 
    />
    <div v-loading="loading" class="sticky-table">
      <CustomTable
        ref="tableRef"
        :columns="columns"
        :data="tableData"
        :table-method="getList"
        :index-method="indexMethod"
      />
    </div>
  </div>
</template>

<script setup lang="tsx">
import { ref, reactive, toRef } from 'vue'
import { ElMessage, ElButton, ElMessageBox } from 'element-plus'
import { useRoute, useRouter } from 'vue-router'
import { storeToRefs } from 'pinia'
import { useParamsStore, useUserStroe } from '@/store'
import useLoading from '@/hooks/use-loading'
import useTryCatch from '@/hooks/use-try-catch'
import { getApplyList, taskRevoke } from '@/api/audit-management'
import type { searchParams, listItem } from '@/api/audit-management'
import Listener from '@/utils/site-channel-listeners'

import QueryForm from '@/components/QueryForm/index.vue'
import CustomTable from '@/components/CustomTable/index.vue'

defineOptions({ name: 'ReviewApply' })

const router = useRouter()
const route = useRoute()
const { site_id_all, channel_item_all } = storeToRefs(useParamsStore())
const { userInfo } = useUserStroe()
const { loading, setLoading } = useLoading()

const statusList = [
  {
    label: '流程中',
    value: 2
  },
  {
    label: '已通过',
    value: 3
  },
  {
    label: '已拒绝',
    value: 4
  },
  {
    label: '已撤销',
    value: 5
  }
]
const queryParams_raw = {
  site_id: site_id_all.value,
  channel_id: channel_item_all.value.channel_id,
  status: '',
  start_time: '',
  end_time: ''
}

const queryParams = reactive<searchParams>({...queryParams_raw})
const tableRef = ref()
const formList = ref([
  {
    label: '审核状态',
    clearable: true,
    placeholder: '审核状态',
    value: toRef(queryParams, 'status'),
    component: 'select',
    selections: statusList
  },
  {
    label: '时间日期',
    clearable: true,
    start_placeholder: '开始日期',
    end_placeholder: '结束日期',
    placeholder: '审核人',
    value: '',
    component: 'datetimerange',
    dateChange: (value: [string, string] | null) => {
      if (value) {
        queryParams.start_time = value[0]
        queryParams.end_time = value[1]
      } else {
        queryParams.start_time = ''
        queryParams.end_time = ''
      }
    }
  }
])
const columns = ref([
  {
    title: '流程ID',
    dataKey: 'task_id',
    width: 200
  },
  {
    title: '业务类型',
    dataKey: 'type_name',
  },
  {
    title: '标题',
    dataKey: 'name',
    width: 250,
    showOverflowTooltip: true,
  },
  {
    title: '渠道名',
    dataKey: 'channel_name',
  },
  {
    title: '当前审核人',
    dataKey: 'handlers',
  },
  {
    title: '审核状态',
    dataKey: 'status_name',
    width: 100,
    cellRenderer: (scope: { row: listItem }) => {
      return (
        <span style={ { color: scope.row.status === 3 ? 'var(--el-color-success)' : scope.row.status === 4 ? 'var(--el-color-error)' : '' } }>
          {scope.row.status_name}
        </span>
      )
    }
  },
  {
    title: '申请人',
    dataKey: 'user_name',
  },
  {
    title: '申请时间',
    dataKey: 'add_time',
  },
  {
    title: '操作',
    dataKey: '',
    width: 200,
    cellRenderer: (scope: { row: listItem }) => {
      return (
        <>
          <ElButton
            type='primary'
            plain
            onClick={() => {
              handleJumpDetail(scope.row)
            }}
          >
            查看详情
          </ElButton>
          {
            <ElButton
              type={ scope.row.status === 5 ? 'info' : 'danger' }
              disabled={ scope.row.user_id !== userInfo.wsid || (scope.row.status !== 2) }
              plain
              onClick={() => handleRevoke(scope.row.task_id)}
              style='width: 72px'
            >
              { scope.row.status === 5 ? '已撤销' : '撤销' }
            </ElButton>
          }
        </>
      )
    }
  },
])
const tableData = ref<listItem[]>([])

const getList = ( { page, pageSize } ) => {
  useTryCatch( async () => {
    setLoading(true)
    const params = {
      q: { ...queryParams, site_id: site_id_all.value, channel_id: channel_item_all.value.channel_id },
      page,
      limit: pageSize
    }
    const res = await getApplyList(params)
    if (res.code === 200) {
      tableData.value = res.data.list
      tableRef.value.setTotal(res.data.total)
    } else {
      ElMessage.error(res.msg)
    }
    setLoading(false)
  }, () => setLoading(false) )
}
const handleSearch = () => {
  tableRef.value.setPage(1)
  const { page, pageSize } = tableRef.value
  getList( { page, pageSize } )
}
const indexMethod = (index: number) => {
  const { page, pageSize } = tableRef.value.getPagination()
  return (page - 1) * pageSize + index + 1
}
const handleJumpDetail = (row: listItem) => {
  router.push({
    name: 'ReviewDetail',
    query: {
      id: row.task_id
    }
  })
}
const handleRevoke = (task_id: string) => {
  ElMessageBox.confirm('确认撤销当前流程吗?', '提示', {
    type: 'warning',
    callback: (action: string) => {
      if (action === 'confirm') {
        useTryCatch( async () => {
          setLoading(true)

          const res = await taskRevoke( { task_ids: task_id } )
          if (res.code === 200) {
            ElMessage.success('撤销成功!')
            handleSearch()
          } else {
            ElMessage.error(res.msg)
          }
          setLoading(false)
        }, () => setLoading(false) )
      }
    }
  })
}

Listener(route, () => {
  handleSearch()
})
</script>