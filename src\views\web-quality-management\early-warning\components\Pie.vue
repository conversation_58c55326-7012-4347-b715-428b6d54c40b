<template>
  <div 
    ref="chartRef"
    :style="{ width, height }"
  ></div>
</template>

<script setup lang="ts">
import ecahrts from '@/components/ECharts'
import { ref, onMounted, onUnmounted } from 'vue'

const props = withDefaults(defineProps<{
  width?: string;
  height?: string;
  data: {name: string; value: number}[];
  colors?: any[];
}>(), {
  width: '100%',
  height: '150px',
})

const chartRef = ref()
let chart: any = null  // 不要设置echarts响应式实例，会导致意外问题，或者用markRaw工具函数去除响应式

const resize = () => {
  if (chart) {
    chart.resize()
  }
}

onMounted(() => {
  chart = ecahrts.init(chartRef.value)
  chart.setOption({
    tooltip: {
      trigger: 'item'
    },
    legend: {
      orient: 'vertical',
      right: '15%',
      bottom: '20%',
      icon: 'circle',
    },
    series: [
      {
        data: props.colors ? props.data.map((item, index) => {
          return {
            ...item,
            itemStyle: {
              color: props.colors?.[index]
            }
          }
        }) : props.data,
        type: 'pie',
        radius: '90%',
        top: '5%',
        right: '25%',
        label: {
          formatter: (params) => {
            return `${params.percent}%`
          },
          position: 'inside',
          color: '#000',
          fontWeight: 'bold'
        },
      }
    ]
  })

  window.addEventListener('resize', resize)
})


onUnmounted(() => {
  window.removeEventListener('resize', resize)
})

</script>