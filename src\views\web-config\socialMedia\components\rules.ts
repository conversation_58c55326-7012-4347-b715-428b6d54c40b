import { reactive } from 'vue'
import type { FormRules } from 'element-plus'

export const formRules = reactive<FormRules>({
  position_id: [
    { required: true, message: '推广位位置不能为空', trigger: 'change' },
  ],
  title: [
    { required: true, message: '标题不能为空', trigger: 'blur' },
  ],
  country: [
    { required: true, message: '应用IP地区不能为空', trigger: 'change' },
  ],
  device: [
    { required: true, message: '应用系统不能为空', trigger: 'change' },
  ],
  scope_type: [
    { required: true, message: '应用范围不能为空', trigger: 'change' },
  ],
  scope_value: [
    { required: true, message: '应用范围值不能为空', trigger: 'change' },
  ],
  pop_time: [
    { required: true, message: '展示不能为空', trigger: 'change' },
  ],
  branch: [
    { required: true, message: '用户随机分流配置不能为空', trigger: 'change' },
  ]
})