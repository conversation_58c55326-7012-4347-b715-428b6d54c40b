<svg width="56" height="56" viewBox="0 0 56 56" fill="none" xmlns="http://www.w3.org/2000/svg">
<g clip-path="url(#clip0_407_7552)">
<rect width="56" height="56" rx="12" fill="url(#paint0_linear_407_7552)"/>
<path d="M56 56V27.5L44.5 13L41 11H16C12.6863 11 10 13.6863 10 17V30.5V42L13 46L24.5 58L56 56Z" fill="url(#paint1_linear_407_7552)"/>
<g filter="url(#filter0_d_407_7552)">
<rect x="10" y="11" width="36" height="36" rx="6" fill="white"/>
<rect x="10" y="11" width="12" height="9" fill="#91DAFF"/>
<rect x="22" y="11" width="12" height="9" fill="#64CBFF"/>
<rect x="34" y="11" width="12" height="9" fill="#34B8FF"/>
<rect x="16" y="33" width="4" height="9" fill="#01BFFF"/>
<path d="M25.5 30H29.5V42H25.5V30Z" fill="#01BFFF"/>
<path d="M35 25H39V42H35V25Z" fill="#01BFFF"/>
</g>
</g>
<defs>
<filter id="filter0_d_407_7552" x="8" y="9" width="44" height="44" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dx="2" dy="2"/>
<feGaussianBlur stdDeviation="2"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0.552789 0 0 0 0 0.845833 0 0 0 0.6 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_407_7552"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_407_7552" result="shape"/>
</filter>
<linearGradient id="paint0_linear_407_7552" x1="41.5" y1="56" x2="41.5" y2="2" gradientUnits="userSpaceOnUse">
<stop stop-color="#00D1FF"/>
<stop offset="1" stop-color="#00A3FF"/>
</linearGradient>
<linearGradient id="paint1_linear_407_7552" x1="27" y1="13.5" x2="42.25" y2="55" gradientUnits="userSpaceOnUse">
<stop stop-color="#3E87EB" stop-opacity="0.76"/>
<stop offset="1" stop-color="#2169C9" stop-opacity="0"/>
</linearGradient>
<clipPath id="clip0_407_7552">
<rect width="56" height="56" rx="12" fill="white"/>
</clipPath>
</defs>
</svg>
