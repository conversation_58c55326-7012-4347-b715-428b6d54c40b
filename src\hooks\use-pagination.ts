import { ref } from 'vue'



export default function usePagination(initial_page = 1, initial_page_size = 10, initial_total = 0) {
  const page_size = window.localStorage.getItem('page_size')
  const initPageSize = page_size ? +page_size : initial_page_size

  const page = ref(initial_page)
  const pageSize = ref(initPageSize > 100 ? 100 : initPageSize)
  const total = ref(initial_total)

  const setPage = (val: number) => {
    page.value = val
  }
  const setPageSize = (val: number) => {
    pageSize.value = val
  }
  const setTotal = (val: number) => {
    total.value = val
  }

  return {
    page,
    pageSize,
    total,
    setPage,
    setPageSize,
    setTotal
  }
}