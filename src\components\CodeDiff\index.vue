<template>
  <div>
    <div style="padding: 10px 0; display: flex; align-items: center;">
      折叠相同部分： 
      <el-checkbox v-model="folding" label="" size="large" />
    </div>
    <Diff 
      :mode="mode"
      :theme="diffTheme"
      :language="language"
      :prev="prev"
      :current="current"
      :folding="folding"
    />
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { useConfigStore } from '@/store/config'

defineOptions( { name: 'CodeDiff' } )

const { theme } = useConfigStore()

const props = withDefaults(defineProps<
{
  mode?: 'split'|'unified';
  language?: string;
  prev: string;
  current: string;
}>(), {
  mode: 'split',
  language: 'html',
})

const diffTheme = ref(theme === 'dark' ? 'dark' : 'light')
const folding = ref(false)

</script>