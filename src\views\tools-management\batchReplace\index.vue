<template>
  <div ref="scrollyRef" class="scroll-y">
    <el-tabs v-model="activeName" @tab-click="handleTabChange">
      <el-tab-pane label="批量替换" name="first">
        <template v-if="channel_item_all.channel_id">
          <div class="step-container" v-show="!uploaded" v-loading="loading">
            <el-steps :active="3" align-center>
              <el-step title="第一步">
                <template #description>
                  <p style="color: var(--el-text-color-primary)">点击下载批量导入模板</p>
                  <el-button type="primary" link size="large" @click="handleDownload('all')"> <u>整站替换模板</u> </el-button>
                  <el-button type="primary" link size="large" @click="handleDownload('field')"> <u>字段替换模板</u> </el-button>
                </template>
              </el-step>
              <el-step title="第二步">
                <template #description>
                  <p style="color: var(--el-text-color-primary)">填写模板信息</p>
                </template>
              </el-step>
              <el-step title="第三步">
                <template #description>
                  <p style="color: var(--el-text-color-primary)">上传文件</p>
                  <el-upload
                    ref="uploadRef"
                    action="string"
                    :auto-upload="false"
                    :show-file-list="false"
                    :limit="1"
                    :before-upload="() => true"
                    :on-change="handleFileChange"
                  >
                    <el-button ref="uploadButtonRef" type="primary" :loading="loading"> 开始上传 </el-button>
                  </el-upload>
                </template>
              </el-step>
            </el-steps>
          </div>
          <div v-show="uploaded" class="show-pagination" :style="{ height, position: 'relative' }">
            <CustomTable 
              ref="tableRef"
              :columns="columns"
              :data="tableData"
              :index-method="(index: number) => index + 1"
              skip-init
              height="100%"
              :table-method="getListMock"
            />
            <div class="fixed-bottom text-center" style="position: absolute;">
              <el-button plain :icon="Back" @click="handlePrev"> 返回上一步 </el-button>
              <el-button type="primary" :icon="Upload" :loading="loading" @click="handleReUpload"> 重新上传 </el-button>
              <el-button type="primary" :icon="Refresh" :loading="loading" @click="handleReplace"> 开始替换 </el-button>
            </div>
          </div>
        </template>
        <template v-else>
          <el-result
            icon="warning"
            title="请先选择具体渠道!" 
            sub-title="您还未选择渠道"
            class="custom-result custom-layout-box"
          />
        </template>
      </el-tab-pane>
      <el-tab-pane label="替换任务记录" name="second">
        <div v-show="channel_item_all.channel_id">
          <div class="show-pagination" :style="{ height, position: 'relative' }" v-loading="loading">
            <CustomTable
              ref="taskTableRef"
              :table-method="getList"
              :columns="taskColumns"
              :data="taskTableData"
              :operate-list="operateList"
              height="100%"
            />
          </div>
        </div>
        <el-result
          v-show="!channel_item_all.channel_id"
          icon="warning"
          title="请先选择具体渠道!" 
          sub-title="您还未选择渠道"
          class="custom-result custom-layout-box"
        />
      </el-tab-pane>
    </el-tabs>
  </div>
</template>

<script setup lang="tsx">
import { ref, onMounted, nextTick } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { ElMessage, ElButton, ElMessageBox, ElDropdown, ElDropdownMenu, ElDropdownItem } from 'element-plus'
import { Back, Refresh, Upload, Delete } from '@element-plus/icons-vue'
import { storeToRefs } from 'pinia'
import { useParamsStore } from '@/store'
import { useBasicStore } from '@/store/basic'
import useLoading from '@/hooks/use-loading'
import useTryCatch from '@/hooks/use-try-catch'
import { getHost } from '@/hooks/use-env'
import { usePageGenerate, useBlockGenerate } from '@/hooks/use-generate'
import { checkExcel, replaceTask, replaceTaskList, replaceReset } from '@/api/tools-management'
import type { replaceListItem, replaceTaskItem } from '@/api/tools-management'
import Listener from '@/utils/site-channel-listeners'
import type { UploadFile, UploadRawFile } from 'element-plus'

import CustomTable from '@/components/CustomTable/index.vue'

defineOptions ( { name: 'BatchReplacement' } )

const route = useRoute()
const router = useRouter()
const { site_id_all, channel_item_all } = storeToRefs(useParamsStore())
const basicStore = useBasicStore()
const { loading, setLoading } = useLoading()

const { name } = route.query as unknown as { name?: string }

const scrollyRef = ref()
const height = ref('0px')
const activeName = ref(name || 'first')
const uploadRef = ref()
const uploadButtonRef = ref()
const uploaded = ref(false)

const tableRef = ref()
let perserveData:replaceListItem[] = []
const tableData = ref<replaceListItem[]>([])
const taskTableRef = ref()
const taskTableData = ref<replaceTaskItem[]>([])
const loadList = ref(false)
const columns = ref([
  {
    title: '来源类型',
    dataKey: 'type',
    width: 100,
  },
  {
    title: '类型ID',
    dataKey: 'type_id',
    width: 100,
  },
  {
    title: '来源字段',
    dataKey: 'type_field',
    width: 100,
  },
  {
    title: '自定义字段名称',
    dataKey: 'type_field_custom',
    minWidth: 120,
  },
  {
    title: '来源内容',
    dataKey: 'source',
    minWidth: 150,
  },
  {
    title: '替换内容',
    dataKey: 'target',
    minWidth: 150,
  },
  {
    title: '检测结果',
    dataKey: 'is_allow',
    minWidth: 100,
    cellRenderer: (scope: { row: replaceListItem }) => (
      <>
        <span style={ { color: scope.row.is_allow === 0 ? 'var(--el-color-success)' : 'var(--el-color-error)' } }>
          { scope.row.is_allow === 0 ? '可以替换' : scope.row.allow_msg }
        </span>
      </>
    )
  },
  {
    title: '操作',
    dataKey: '',
    width: 150,
    cellRenderer: (scope: { row: replaceListItem, $index: number }) => (
      <>
        <el-button link icon={Delete} onClick={() => handleDelete(scope.row, scope.$index)}> 删除 </el-button>
      </>
    )
  },
])
const taskColumns = ref([
  {
    title: '任务ID',
    dataKey: 'id',
    width: 100,
  },
  {
    title: '创建时间',
    dataKey: 'add_time',
    minWidth: 150,
  },
  {
    title: '创建人',
    dataKey: 'add_user',
    minWidth: 100,
  },
  {
    title: '任务状态',
    dataKey: 'status',
    minWidth: 100,
    cellRenderer: (scope: { row: replaceTaskItem }) => (
      <span style={ { color: scope.row.status === 2 ? 'var(--el-color-success)' : scope.row.status === 3 ? 'var(--el-color-error)' : 'inherit' } }> 
        { scope.row.status_name } 
      </span>
    )
  },
  {
    title: '操作',
    dataKey: '',
    width: 220,
    cellRenderer: (scope: { row: replaceTaskItem }) => (
      <>
        <el-button type='primary' plain onClick={() => handleDetail(scope.row)}> 查看详情 </el-button>
        <el-button 
          type='primary' 
          plain 
          disabled={scope.row.status !== 2}
          onClick={() => handleResetTask(scope.row)}
        >  
          还原
        </el-button>
        <ElDropdown trigger='click' style={{ marginLeft: '10px' }}>
          {
            {
              default: () => <ElButton type='primary' link disabled={scope.row.status !== 2 && scope.row.status !== 3}> <strong>...</strong> </ElButton>,
              dropdown: () => 
              <ElDropdownMenu>
                <ElDropdownItem>
                  {
                    scope.row.page_id_str && 
                    <>
                      <el-button 
                        type='success' 
                        plain 
                        onClick={() => handleGenerate(scope.row.page_id_str, 'page')}
                      > 
                        一键发布页面
                      </el-button>
                      <el-button 
                        type='primary' 
                        plain 
                        onClick={() => handleJump(scope.row.page_id_str, 'page')}
                      > 
                        跳转发布页面
                      </el-button>
                    </>
                  }
                  {
                    scope.row.blk_id_str && 
                    <>
                      <el-button 
                        type='success' 
                        plain 
                        onClick={() => handleGenerate(scope.row.blk_id_str, 'block')}
                      > 
                        一键发布块
                      </el-button>
                      <el-button 
                        type='primary' 
                        plain
                        onClick={() => handleJump(scope.row.blk_id_str, 'block')}
                      > 
                        跳转发布块
                      </el-button>
                    </>
                  }
                </ElDropdownItem>
              </ElDropdownMenu>
            }
          }
        </ElDropdown>
      </>
    )
  },
])
const operateList = ref([
  {
    title: '刷新列表',
    icon: 'refresh',
    method: () => {
      getList()
    },
  },
])

const initHeight = () => {
  const scrollyEl = scrollyRef.value as HTMLElement
  if (scrollyEl) {
    const h = scrollyEl.getBoundingClientRect().height
    height.value = `${h - 60}px`
  }
}
const handleTabChange = () => {
  nextTick(() => {
    if (activeName.value === 'second' && taskTableData.value.length === 0) {
      loadList.value = true
      getList()
    }
  })
}
const handleDownload = (type: 'all'|'field') => {
  location.href = `${getHost()}/api/v1/tool/replace/template?type=${type}`
}
const handleFileChange = (file: UploadFile) => {
  uploadRef.value?.clearFiles()
  useTryCatch( async () => {
    setLoading(true)
    const params = {
      site_id: site_id_all.value,
      channel_id: channel_item_all.value.channel_id as number,
      file: file.raw as UploadRawFile
    }
    const res = await checkExcel(params)
    if (res.code === 200) {
      uploaded.value = true
      perserveData = res.data

      const { page, pageSize } = tableRef.value?.getPagination()
      tableRef.value?.setTotal(perserveData.length)

      getListMock( { page, pageSize } )
    } else {
      ElMessage.error(res.msg)
    }
    setLoading(false)
  }, () => setLoading(false) )
}

const handlePrev = () => {
  uploaded.value = false
}
const handleReUpload = () => {
  uploadButtonRef.value?.$el?.click()
}
const handleReplace = () => {
  const data = perserveData.filter(( { is_allow } ) => is_allow === 0)
  if (data.length > 0) {
    ElMessageBox.confirm(
      '请确认是否进行批量替换操作, 仅替换有效数据, 无效数据将自动进行过滤',
      '替换确认',
      {
        type: 'warning',
        confirmButtonText: '确认',
        cancelButtonText: '取消',
        callback: (action: string) => {
          if (action === 'confirm') {
            useTryCatch( async () => {
              setLoading(true)
              
              const params = {
                site_id: site_id_all.value,
                channel_id: channel_item_all.value.channel_id as number,
                content: JSON.stringify(data)
              }
              const res = await replaceTask(params)
              if (res.code === 200) {
                handleRefresh()
                activeName.value = 'second'
              } else {
                ElMessage.error(res.msg)
              }

              setLoading(false)
            }, () => setLoading(false) )
          }
        }
      }
    )
  } else {
    ElMessage.warning('本次替换任务无可有效可替换数据, 请重新上传替换任务')
  }
}
const handleRealDelete = (index: number) => {
  const { page, pageSize } = tableRef.value?.getPagination()
  perserveData.splice(index + (page - 1) * pageSize, 1)
  tableRef.value?.setTotal(perserveData.length)
  getListMock( { page, pageSize } )
}
/* table methods */
const handleDelete = (row: replaceListItem, index: number) => {
  if (row.is_allow !== 0) {
    handleRealDelete(index)
    ElMessage.success('删除成功')
    return 
  }
  ElMessageBox.confirm(
    '请确认是否删除, 此条为有效数据',
    '提示',
    {
      type: 'warning',
      confirmButtonText: '确认',
      cancelButtonText: '取消',
      callback: (action: string) => {
        if (action === 'confirm') {
          handleRealDelete(index)
          ElMessage.success('删除成功')
        }
      }
    }
  )
}
const handleRefresh = () => {
  taskTableRef?.value?.setPage(1)
  loadList.value = true
  getList()
}
const getListMock = ( { page, pageSize } ) => {
  tableData.value = perserveData.slice((page - 1) * pageSize, page * pageSize)
}
const getList = () => {
  if (!loadList.value) {
    return
  }
  if (!channel_item_all.value.channel_id) {
    return
  }
  const { page, pageSize } = taskTableRef?.value?.getPagination()
  useTryCatch( async () => {
    setLoading(true)
    const params = {
      site_id: site_id_all.value,
      channel_id: channel_item_all.value.channel_id,
      page,
      page_size: pageSize
    }

    const res = await replaceTaskList(params)
    if (res.code === 200) {
      taskTableData.value = res.data.list
      taskTableRef.value?.setTotal(res.data.total)
    } else {
      ElMessage.error(res.msg)
    }
    setLoading(false)
  }, () => setLoading(false) )
}
const handleDetail = (row: replaceTaskItem) => {
  router.push({
    name: 'ReplaceDetail',
    query: {
      task_id: row.id
    }
  })
}
const handleResetTask = (row: replaceTaskItem) => {
  ElMessageBox.confirm(
    '请确认是否进行还原操作',
    '提示',
    {
      type: 'warning',
      confirmButtonText: '确认',
      cancelButtonText: '取消',
      callback: (action: string) => {
        if (action === 'confirm') {
          useTryCatch( async () => {
            const res = await replaceReset(row.id)
            if (res.code === 200) {
              ElMessage.success(res.msg)
              loadList.value = true
              getList()
            } else {
              ElMessage.error(res.msg)
            }
          } )
        }
      }
    }
  )
}
const handleJump = (id_str: string, type: 'page'|'block') => {
  const name = type === 'page' ? 'PageManagement' : 'BlockManagement'
  basicStore.delCachedView(name)
  router.push({
    name,
    query: {
      entity_ids: id_str
    }
  })
}
const handleGenerate = (id_str: string, type: 'page'|'block') => {
  if (type === 'page') {
    usePageGenerate({
      tpl_page_ids: id_str,
      site_id: site_id_all.value
    }, getList)
  }
  if (type === 'block') {
    useBlockGenerate({
      blk_ids: id_str,
      site_id: site_id_all.value
    }, getList)
  }
}
/* table methods */

onMounted(() => {
  initHeight()
})

if (activeName.value === 'second') {
  loadList.value = true
  getList()
}

Listener(route, () => {
  if (channel_item_all.value.channel_id) {
    nextTick(() => {
      handleRefresh()
    })
  }
})
</script>

<style lang="scss" scoped>
:deep(.custom-table-container) {
  height: calc(100% - 60px);
}
:deep(.show-pagination) {
  .custom-table-container {
    height: calc(100% - 100px);
  }
}
:deep(.step-container) {
  max-height: 100%;
  height: 600px;
  display: flex;
  flex-direction: column;
  justify-content: center;
}
:deep(.el-step) {
  
  .el-step__description {
    font-size: 24px;
    line-height: 1.25;
  }
  .el-step__head, .el-step__title {
    --el-color-primary: var(--el-text-color-primary);
  }
}
</style>