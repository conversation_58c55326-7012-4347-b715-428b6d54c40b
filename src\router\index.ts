import { createRouter, createWebHistory } from 'vue-router'
import type { RouterTypes } from '~/basic'
import Layout from '@/layout/index.vue'
import { getAppName } from '@/hooks/use-env'
import { addRoutes } from './aysncRoutes'
export { routeMap } from './aysncRoutes'

export const constantRoutes: RouterTypes = [
  {
    path: '/redirect',
    component: Layout,
    hidden: true,
    children: [
      {
        path: '/redirect/:path(.*)',
        component: () => import('@/views/redirect')
      }
    ]
  },
  {
    path: '/',
    component: Layout,
    redirect: '/dashboard',
    children: [
      {
        path: 'dashboard',
        name: 'Dashboard',
        component: () => import('@/views/dashboard/index.vue'),
        //using el svg icon, the elSvgIcon first when at the same time using elSvgIcon and icon
        meta: { title: '首页', icon: 'home', affix: true, cachePage: true, plainLayout: true, }
      },
      
    ]
  },
  {
    path: '/cms40/:chapters*',
    redirect:'/'
  },
  {
    path: '/404',
    component: () => import('@/views/error-page/404.vue'),
    hidden: true,
    meta: { title: '您访问的页面不存在', }
  },
]

//角色和code数组动态路由
export const roleCodeRoutes: RouterTypes = []
/**
 * asyncRoutes
 * the routes that need to be dynamically loaded based on user roles
 */
export const asyncRoutes: RouterTypes = []
export const appendRoutes: RouterTypes = [
  // 404 page must be placed at the end !!!
  { path: '/:catchAll(.*)', name: 'notFound', redirect: '/404', hidden: true }
]

const appName = getAppName()

export const allRoutes = constantRoutes.concat(addRoutes)

const router = createRouter({
  history: createWebHistory('/'),
  scrollBehavior: () => ({ top: 0 }),
  routes: allRoutes
})

export default router

export { addRoutes }
