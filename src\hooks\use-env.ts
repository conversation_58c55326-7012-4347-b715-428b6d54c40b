import { removeLocalToken } from './use-local-auth' // get token from cookie
import packageJson from '../../package.json'

const VUE_APP_ACCOUNT_BASE_API = import.meta.env.VUE_APP_ACCOUNT_BASE_API
const isDev = import.meta.env.VITE_APP_ENV === 'prod' ? false : true

// ---> 该配置保持不变
const wcwUrlMap = {
    dev: 'dev-wcw.300624.cn',
    beta: 'beta-wcw.300624.cn',
    production: 'workspace.300624.cn'
}

// ---> 当前子应用对应不同环境的client id
const clientIdMap = {
    dev: 23,
    beta: 23,
    production: 10
}

// ---> 当前子应用对应不同环境的部署地址
const currentAppDeployUrlMap = {
    dev: 'dev-cms40.wondershare.cn',
    beta: 'beta-cms40.wondershare.cn',
    production: 'cms40.wondershare.cn'
}

// ---> 当前子应用对应不同环境的后台API地址
const currentAppApiUrlMap = {
    dev: 'dev-cms40.wondershare.cn',
    beta: 'beta-cms40.wondershare.cn',
    production: 'cms40.wondershare.cn'
}

// ---> 获取当前子应用的name
export function getAppName() {
    return packageJson.name
}

const APP_NAME = getAppName()

// ---> 根据环境获取当前子应用的client id
export function getClientId() {
    let res: number = 0
    const { host } = window.location
    if (host === wcwUrlMap.dev || host === currentAppDeployUrlMap.dev) {
        res = clientIdMap.dev
    } else if (host === wcwUrlMap.beta || host === currentAppDeployUrlMap.beta) {
        res = clientIdMap.beta
    } else if (host === wcwUrlMap.production || host === currentAppDeployUrlMap.production) {
        res = clientIdMap.production
    }
    return res
}

// ---> 根据环境获取当前子应用的登录地址，点击退出或401跳转到该地址
export function getLoginUrl() {
    // 重定向到认证中心登录页之前，清空当前子系统的token缓存
    removeLocalToken()
    removeLocalToken(`${APP_NAME}IdToken`)
    const { href } = window.location
    // const APP_CLIENT_ID = getClientId()
    // 重定向到登录页面
    // 每个子系统的href替换逻辑会稍微不一样
    const APP_PATH = href.replace('dev-wcw', 'dev-cms40').replace('beta-wcw', 'beta-cms40').replace('workspace', 'cms40')
    const url = `${VUE_APP_ACCOUNT_BASE_API}/oauth/user/logout?redirect_uri=${encodeURIComponent(APP_PATH)}`
    return url
}

// code认证方案，401跳转地址
export function getCodeAuthLoginUrl(path = '') {
    const clientId = getClientId()
    const origin = getOrigin()
    const redirectUri = encodeURIComponent(`${origin}${path}`)
    const url = `${VUE_APP_ACCOUNT_BASE_API}/oauth/authorize?response_type=code&client_id=${clientId}&scope=openid%20profile&state=*********&redirect_uri=${redirectUri}`
    return url
}

// ---> 根据环境获取当前子应用的部署访问地址
export function getOrigin() {
    let res = ''
    const { protocol, host } = window.location
    const originMap = {
        dev: `${protocol}//${currentAppDeployUrlMap.dev}/${APP_NAME}`,
        beta: `${protocol}//${currentAppDeployUrlMap.beta}/${APP_NAME}`,
        production: `${protocol}//${currentAppDeployUrlMap.production}/${APP_NAME}`
    }
    if (host === wcwUrlMap.dev || host === currentAppDeployUrlMap.dev) {
        res = originMap.dev
    } else if (host === wcwUrlMap.beta || host === currentAppDeployUrlMap.beta) {
        res = originMap.beta
    } else if (host === wcwUrlMap.production || host === currentAppDeployUrlMap.production) {
        res = originMap.production
    }
    return res
}

// 根据环境获取当前子应用的接口请求域名
export function getHost() {
    let res = ''
    const { protocol, host } = window.location
    const apiUrlMap = {
        dev: isDev ? `${protocol}//${currentAppApiUrlMap.beta}` : `${protocol}//${currentAppApiUrlMap.dev}`, // 支持本地调试，本地调试后台api地址为beta-pms
        beta: isDev ? `${protocol}//${currentAppApiUrlMap.dev}` : `${protocol}//${currentAppApiUrlMap.beta}`,
        production: `${protocol}//${currentAppApiUrlMap.production}`
    }
    if (host === wcwUrlMap.dev || host === currentAppDeployUrlMap.dev) {
        res = apiUrlMap.dev
    } else if (host === wcwUrlMap.beta || host === currentAppDeployUrlMap.beta) {
        res = apiUrlMap.beta
    } else if (host === wcwUrlMap.production || host === currentAppDeployUrlMap.production) {
        res = apiUrlMap.production
    }
    return res
}

// 根据环境获取当前子应用的独立域名访问重定向url，dev-wcw/pms -> dev-pms/pms
export function getRedirectUrl(path) {
    let res = ''
    const { protocol, host } = window.location
    const redirectUrlMap = {
        dev: `${protocol}//${wcwUrlMap.dev}/${APP_NAME}`,
        beta: `${protocol}//${wcwUrlMap.beta}/${APP_NAME}`,
        production: `${protocol}//${wcwUrlMap.production}/${APP_NAME}`
    }
    if (host === wcwUrlMap.dev || host === currentAppDeployUrlMap.dev) {
        res = redirectUrlMap.dev
    } else if (host === wcwUrlMap.beta || host === currentAppDeployUrlMap.beta) {
        res = redirectUrlMap.beta
    } else if (host === wcwUrlMap.production || host === currentAppDeployUrlMap.production) {
        res = redirectUrlMap.production
    }
    return res + path
}

// 没有token的跳转地址，注意与getLoginUrl区分，认证中心会根据兴云工作台登录态判断是跳回子系统还是跳到登录页
// 如果认证中心判断兴云工作台已登录，则跳回子系统，url携带子系统token
// 如果认证中心判断兴云工作台未登录，则跳到登录页，用户点击登录后再跳回子系统，url携带子系统token
export function getNoTokenRedirectUrl(path) {
    const clientId = getClientId()
    const origin = getOrigin()
    const url = `${VUE_APP_ACCOUNT_BASE_API}/oauth/authorize?response_type=id_token&client_id=${clientId}&scope=openid%20profile&state=*********&redirect_uri=${origin}${path}`
    return url
}
