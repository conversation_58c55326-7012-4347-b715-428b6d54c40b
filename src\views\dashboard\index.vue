<template>
  <div class="dashboard-container" :style="{ paddingLeft: sidebar.opened ? '0' : '16px' }">
    <div class="mb-30px">
      <el-space :size="18">
        <hamburger
          :is-active="sidebar.opened"
          style="--hamburger-width: 32px; --hamburger-height: 32px; transform: translateY(4px);"
          @toggle-click="setToggleSideBar"
        />
      <span style="font-weight: 700; font-size: 28px;">首页</span>
    </el-space>
    </div>
    <div style="display: flex; flex-wrap: nowrap; width: 100%; flex-grow: 1; max-height: calc(100% - 67px);">
      <div class="main">
        <div class="layout-container main-container">
          <el-scrollbar>
            <div style="max-width: 1488px; margin: 0 auto;">
              <div class="title">
                常用功能
                <el-tooltip content="CMS的常用功能模块的快速导航"><SvgIcon icon-class="question" /></el-tooltip>
              </div>
              <div class="mb-48px" style="overflow: hidden;">
                <div class="constant-module-container">
                  <div 
                    v-for="(d, index) in constantModule" 
                    :key="index"
                    class="item"
                    :style="{
                      '--image': `url(${d.image})`
                    }"
                  >
                    <div class="block" @click="handleInnerJump(d.name)">
                      <div class="content">
                        {{ d.title }}
                        <p class="des">{{ d.des }}</p>
                      </div>
                      <SvgIcon :icon-class="d.icon" />
                    </div>
                  </div>
                </div>
              </div>
        
              <div class="title">
                CMS工具
                <el-tooltip content="cms内部便捷操作工具, 用于提升特定业务场景的工作效率"><SvgIcon icon-class="question" /></el-tooltip>
              </div>
              <div class="cms-tools-container mb-48px">
                <el-space :size="100" wrap>
                  <div 
                    v-for="(d, index) in tools" 
                    :key="index"
                    class="item"
                    @click="handleOuterJump('', d.inner, d.name)"
                  >
                    <img :src="d.image">
                    <div>{{ d.title }}</div>
                  </div>
                </el-space>
              </div>
              <div class="title">
                拓展工具
                <el-tooltip content="网站业务其他领域工具(非CMS内容管理), 便于快速导航应用"><SvgIcon icon-class="question" /></el-tooltip>
              </div>
              <div class="cms-tools-container">
                <el-space :size="100" wrap>
                  <div 
                    v-for="(d, index) in externals" 
                    :key="index"
                    class="item"
                    @click="handleOuterJump(d.url)"
                  >
                    <img :src="d.image">
                    <div>{{ d.title }}</div>
                  </div>
                </el-space>
              </div>
            </div>
          </el-scrollbar>
        </div>
      </div>
      <div class="side">
        <div class="layout-container left-container">
          <el-scrollbar>
            <template v-if="showBanner">
              <div class="mb-48px">
                <Banner />
              </div>
            </template>
            <div class="title">待办</div>
            <div class="mb-48px">
              <Todo v-if="site_id_all" />
            </div>
            <div class="title">公告</div>
            <div class="mb-48px">
              <Announce />
            </div>
            <div class="title">系统指南</div>
            <div 
              v-for="(d, index) in guides"
              :key="index"
              class="mb-24px with-hand sys-item"
              @click="handleOuterJump(d.url)"
            >
              <el-space :size="24" nowrap>
                <img :src="d.image">
                <div>
                  {{ d.title }}
                  <br>
                 <span class="date"> {{ d.date }}</span>
                </div>
              </el-space>
            </div>
          </el-scrollbar>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { useRouter } from 'vue-router'
import { storeToRefs } from 'pinia'
import { useParamsStore } from '@/store'
import { useBasicStore } from '@/store/basic'
import { constantModule, tools, guides, externals } from './data'

import Banner from './components/banner.vue'
import Announce from './components/announcement.vue'
import Todo from './components/todo.vue'
import Hamburger from '@/layout/app-main/Hamburger.vue'

import SvgIcon from '@/icons/SvgIcon.vue'
// 保证缓存生效需保证与路由name一致
defineOptions({ name: 'Dashboard' })

const router = useRouter()
const { site_id_all } = storeToRefs(useParamsStore())
const { sidebar, setToggleSideBar, setSidebarOpen } = useBasicStore()

const showBanner = ref(false)

const handleInnerJump = (name: string) => {
  setSidebarOpen(true)
  router.push({ name })
}

const handleOuterJump = (fullPath: string, inner?: boolean, name?: string) => {
  if (inner && name) {
    handleInnerJump(name)
  } else {
    fullPath && window.open(fullPath, '_blank')
  }
}
</script>

<style lang="scss">
.dark .dashboard-container{
  --block-bg-color: #121212;
  --block-color: #fff;
  --item-font-color: #cfd3dc;
  --item-font-gray: #cfd3dc;
  --item-border-color: rgba(255, 255, 255, 0.1);
  --item-color-des: #cfd3dc;
  --main-border-color: rgba(255, 255, 255, 0.08);
  --icon-opacity: .75;
  .constant-module-container {
    .block {
      background-image: none;
      border: 1px solid var(--border-color-layout);
    }
  }
}
.dashboard-container {
  --block-bg-color: #f5faff;
  --block-color: #21398d;
  --item-font-color: rgba(36, 41, 52, 0.7);
  --item-font-gray: rgba(0, 0, 0, 0.32);
  --item-border-color: rgba(0, 0, 0, 0.04);
  --item-color-des: rgba(33, 57, 141, 0.56);
  --main-border-color: rgba(0, 0, 0, 0.08);
  --icon-opacity: 0;
  height: 100%;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  .main {
    width: 100%;
    height: 100%;
    flex: 1 1 auto;
    position: relative;
    padding-right: 16px;
    .main-container {
      padding: 24px;
      height: 100%;
      width: 100%;
    }
  }
  .title {
    font-size: 18px;
    font-weight: 700;
    margin-bottom: 20px;
  }
  .side {
    width: 318px;
    height: 100%;
    overflow: auto;
    flex-shrink: 0;
    transition: width .3s;
    position: relative;
    white-space: nowrap;
    .left-container {
      padding: 24px;
      height: 100%;
    }
    &.hide {
      width: 0;
    }
  }
}
.constant-module-container {
  display: flex;
  flex-wrap: wrap;
  margin-left: -15px;
  margin-right: -15px;
  .item {
    flex: 1 0 auto;
    width: 33.33%;
    padding: 0 15px;
    margin-bottom: 20px;
    min-width: 350px;
  }
  .block {
    background-color: var(--block-bg-color);
    color: var(--block-color);
    border-radius: 12px;
    cursor: pointer;
    font-size: min(20px, max(16px, calc(100vw * 28 / 1920)));
    font-weight: 700;
    max-width: 488px;
    position: relative;
    background-image: var(--image);
    background-size: auto 100%;
    background-position: top right;
    background-repeat: no-repeat;
    .content {
      position: absolute;
      width: 100%;
      padding: 0 40px;
      top: 50%;
      transform: translateY(-50%);
      .des {
        max-width: 65%;
        margin: 12px 0 0;
        font-size: 12px;
        color: var(--item-color-des);
        font-weight: 400;
      }
    }
    &::after {
      content: '';
      display: block;
      width: 100%;
      padding-top: calc(152 / 488 * 100%);
    }
    .svg-icon {
      position: absolute;
      right: 0;
      top: 10%;
      height: 80%;
      transform: translateX(25%);
      width: auto;
      opacity: var(--icon-opacity);
    }
  }
}
.cms-tools-container {
  font-weight: 700;
  font-size: 16px;
  .item {
    display: inline-block;
    text-align: center;
    width: 120px;
    color: var(--item-font-color);
    cursor: pointer;
    img {
      width: 56px;
      margin-bottom: 16px;
    }
  }
}
.sys-item {
  font-weight: 700;
  color: var(--item-font-color);
  .date {
    font-weight: 500;
    padding-top: 6px;
    font-size: 12px;
    color: var(--item-font-gray);
  }
}
.todo-item {
  width: 124px;
  height: 88px;
  border-radius: 6px;
  color: #fff;
  background: var(--gradient);
  font-weight: 700;
  font-size: 12px;
  padding-left: 16px;
  position: relative;
  overflow: hidden;
  .content {
    width: calc(100% - 16px);
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
    z-index: 1;
    .big {
      font-size: 28px;
    }
  }
  &::after {
    content: '';
    position: absolute;
    background-image: var(--image);
    width: 75px;
    height: 60px;
    background-size: auto;
    background-repeat: no-repeat;
    right: 0px;
    bottom: 0px;
  }
}
</style>