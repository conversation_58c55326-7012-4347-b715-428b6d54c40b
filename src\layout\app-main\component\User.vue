<template>
  <el-dropdown trigger="click">
    <Avatar style="width: 22px; height: 23px;" />
    <template #dropdown>
      <el-dropdown-menu>
        <el-dropdown-item @click="handleToWcw"> 兴云工作台 </el-dropdown-item>
        <el-dropdown-item @click="handleLogOut"> 退出登录 </el-dropdown-item>
      </el-dropdown-menu>
    </template>
  </el-dropdown>
</template>

<script setup lang="ts">
import { ElMessageBox } from 'element-plus'
import { Avatar } from '@element-plus/icons-vue'
import { logOut } from '@/api/user'
import { getLoginUrl } from '@/hooks/use-env'
import Cookies from 'js-cookie'

const clearLocal = () => {
  // 清空cookie
  const cookies = Cookies.get()
  Object.keys(cookies).map((item) => {
    Cookies.remove(item)
  })
}
const handleLogOut = () => {
  ElMessageBox.confirm('确认退出登录吗?', '提示', {
    confirmButtonText: '确认',
    cancelButtonText: '取消',
    type: 'warning',
  }).then( async () => {
    try {
      await logOut()
      clearLocal()
      window.location.href = getLoginUrl()
    } catch (error) {
      
    }
  }).catch(() => {

  })
}
const handleToWcw = () => {
  const { protocol, host } = window.location
  const domain = host.startsWith('beta') ? '//beta-wcw.300624.cn' : '//workspace.300624.cn'
  window.open(`${protocol}${domain}`, '_blank')
}
</script>