const article = [
  {
    label: '文章内容页',
    value: 'content'
  },
  {
    label: '文章三级分类页',
    value: 'list'
  },
  {
    label: '文章四级分类页',
    value: 'list_sub'
  },
  {
    label: '文章聚合页',
    value: 'index'
  },
  {
    label: '文章作者页',
    value: 'author'
  },
  {
    label: '其他页',
    value: 'other'
  },
]
const product = [
  {
    label: '单页',
    value: 'index'
  },
  {
    label: '产品overview页',
    value: 'overview'
  },
  {
    label: '产品一级分类页',
    value: 'category_1'
  },
  {
    label: '产品二级分类页',
    value: 'category_2'
  },
  {
    label: '产品一级分类Guide页',
    value: 'one_category_guide'
  },
  {
    label: '产品二级分类Guide页',
    value: 'two_category_guide'
  },
  {
    label: '产品评论页',
    value: 'list_review'
  },
  {
    label: '切换页',
    value: 'switch'
  },
  {
    label: '其他页',
    value: 'other'
  },
]
const store = [
  {
    label: '首页',
    value: 'index'
  },
  {
    label: '购买页',
    value: 'buy'
  },
  {
    label: 'store一级分类',
    value: 'one_category'
  },
  {
    label: 'store二级分类',
    value: 'two_category'
  },
  {
    label: '其他页',
    value: 'other'
  },
]
const other = [
  {
    label: '单页',
    value: 'index'
  },
  {
    label: '其他页',
    value: 'other'
  },
]
export const moduleList = [
  {
    label: '产品模板',
    value: 'product'
  },
  {
    label: '文章模板',
    value: 'article'
  },
  {
    label: '购买页模板',
    value: 'store'
  },
  {
    label: '其他模板',
    value: 'other'
  }
]
export const moduleTypeMap = {
  article,
  product,
  store,
  other
}
export const inputTypes = [
  {
    label: '单行输入框',
    value: 'input'
  },
  {
    label: '下拉框',
    value: 'select'
  },
  {
    label: '多行文本输入框',
    value: 'textarea'
  },{
    label: '富文本框',
    value: 'fulltext'
  }
]
export const fieldTypes = [
  {
    label: 'char',
    value: 'char'
  },
  {
    label: 'varchar',
    value: 'varchar'
  },
  {
    label: 'int',
    value: 'int'
  },

  {
    label: 'tinyint',
    value: 'tinyint'
  },
  {
    label: 'text',
    value: 'text'
  },
  {
    label: 'longtext',
    value: 'longtext'
  },
  {
    label: 'date',
    value: 'date'
  },
  {
    label: 'datetime',
    value: 'datetime'
  },
]

export const pageStateList = [
  {
    label: '未生成',
    value: 'new'
  },
  {
    label: '已修改',
    value: 'modified'
  },
  {
    label: '已生成',
    value: 'created'
  },
  {
    label: '已禁用',
    value: 'disable'
  }
]

export const artPageStateList = [
  {
    label: '未创建',
    value: 'new'
  },
  {
    label: '未生成',
    value: 'no_created'
  },
  {
    label: '已修改',
    value: 'modified'
  },
  {
    label: '已生成',
    value: 'created'
  },
  {
    label: '已禁用',
    value: 'disable'
  }
]

export const page_state_map = {
  'new': {
    color: 'var(--el-color-regular)',
    text: '未生成'
  },
  'created': {
    color: 'var(--el-color-success)',
    text: '已生成'
  },
  'modified': {
    color: 'var(--el-color-primary)',
    text: '已修改'
  },
  'disable': {
    color: 'var(--el-color-error)',
    text: '已禁用'
  },
}

export const blockTypeList = [
  {
    label: '普通块',
    value: 0
  },
  {
    label: '侧边栏目块',
    value: 1
  },
  {
    label: '头部块文件',
    value: 2
  },
  {
    label: '底部块文件',
    value: 3
  },
  {
    label: '浮窗块文件',
    value: 4
  },
  {
    label: 'GTM块文件',
    value: 5
  }
]