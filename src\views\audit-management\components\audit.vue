<template>
  <div class="audit-container" v-loading="loading">
    <el-form
      ref="validateForm"
      :model="checkParams"
      label-width="150px"
    >
      <el-form-item 
        label="审核结果：" 
        prop="operate" 
        :rules="{required: true, message: '审核结果不能为空'}" 
        style="margin-bottom: 20px;"
      >
        <el-radio-group v-model="checkParams.operate">
          <el-radio
            v-for="(d, index) in operateList"
            :key="index"
            :label="d.value"
          >
            {{ d.label }}
          </el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item 
        label="请输入审核意见：" 
        prop="content" 
        :rules="{required: checkParams.operate === 'reject' ? true : false, message: '审核意见不能为空'}"
      >
        <el-input type="textarea" v-model="checkParams.content" :rows="3" style="width: 500px;" />
      </el-form-item>
    </el-form>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive } from 'vue'
import { ElMessage } from 'element-plus'
import type { FormInstance } from 'element-plus'
import useLoading from '@/hooks/use-loading'
import useTryCatch from '@/hooks/use-try-catch'
import { taskUpdate } from '@/api/audit-management'
import emitter from '@/utils/bus'

const props = defineProps<{
  taskId: string;
}>()
const emit = defineEmits(['close', 'success'])

const operateList = [
  {
    label: '通过',
    value: 'pass',
  },
  {
    label: '拒绝',
    value: 'reject',
  },
]

const { loading, setLoading } = useLoading()
const validateForm = ref<FormInstance>()
const checkParams = reactive({
  operate: '',
  content: ''
})

const handleSubmit = async (formEl: FormInstance | undefined) => {
  if (!formEl) return
  let valid = false
  await formEl.validate((isValid) => {
    valid = isValid
  })
  if (!valid) return
  useTryCatch( async () => {
    setLoading(true)
    const params = {
      task_ids: props.taskId,
      ...checkParams
    }
    const res = await taskUpdate(params)
    if (res.code === 200) {
      emit('success')
      formEl.resetFields()
      emitter.emit('noticeUpdate')
    } else {
      ElMessage.error(res.msg)
    }
    setLoading(false)
  }, () => setLoading(false))
}

defineExpose({
  handleSubmit: () => handleSubmit(validateForm.value)
})
</script>