<template>
  <el-dialog
    v-model="show"
    title="编辑同集模板"
    width="60%"
    align-center
    @close="emit('close')"
  >
    <template #default>
      <p class="mb-20px">
        处于同一集群的模板中, 可以选定一个渠道的模板作为来源, 向其他渠道的模板<strong style="color: var(--el-color-primary)">推送来源模板代码内容及自定义页面字段</strong>
        (即选中A渠道模板, A模板代码内容可以批量更新到B, C, D..等模板中)；如果<strong style="color: var(--el-color-danger)">推送的渠道，尚未存在同集模板，则会同步创建对应模板</strong>。
      </p>
      <div class="mb-40px">
        <p>
          Tips: 此功能一般常用于模板的首次复制和二次复用。
        </p>
        <p>
          首次复制场景(例): (1)EN渠道创建新类型的页面模板, 后续其他语言模板需要复用。
        </p>
        <p>
          <div style="display: flex; padding-right: 4px;">
            <div>
              二次复用场景(例): 
            </div>
            <div>
              (1)EN渠道文章模板更新样式后, 其他多语言渠道文章模板, 需要批量更新模板样式;
              <br />
              (2)SEO-JP渠道活动模板上了新活动, PPC-JP需要复用。
            </div>
          </div>      
        </p>
      </div>
      <div class="mb-20px">
        <strong>选中来源的模板渠道(单选): </strong>
        <el-select v-model="sourceChannelId" filterable @change="handleSourceChange">
          <el-option 
            v-for="(d) in channel_list"
            :key="d.channel_id"
            :value="d.channel_id"
            :label="d.channel_code"
          />
        </el-select>
      </div>
      <el-table :data="sourceList" border class="mb-40px">
        <el-table-column prop="channel_name" label="模板渠道" width="100" />
        <el-table-column prop="tpl_id" label="模板ID" />
        <el-table-column prop="name" label="模板名称" />
        <el-table-column prop="edit_time" label="最近更新时间" width="180" />
      </el-table>

      <div class="mb-20px">
        <strong>更新对象的模板渠道(可多选): </strong>
        <el-select v-model="channelIds" filterable multiple collapse-tags @change="handleTargetChange" :disabled="sourceList.length === 0">
          <el-option 
            v-for="(d) in channel_list"
            :key="d.channel_id"
            :value="d.channel_id"
            :label="d.channel_code"
            :disabled="d.channel_id === sourceChannelId"
          />
        </el-select>
      </div>
      <el-table :data="targetList" border>
        <el-table-column prop="channel_name" label="模板渠道" width="100" />
        <el-table-column prop="tpl_id" label="模板ID" >
          <template #default="scope">
            {{ scope.row.tpl_id || '此渠道尚未创建模板，本操作将会创建对应模板' }}
          </template>
        </el-table-column>>
        <el-table-column prop="name" label="模板名称" >
          <template #default="scope">
            {{ scope.row.name || '------' }}
          </template>
        </el-table-column>
        <el-table-column prop="edit_time" label="最近更新时间" width="180">
          <template #default="scope">
            {{ scope.row.edit_time || '------' }}
          </template>
        </el-table-column>
      </el-table>
    </template>
    <template #footer>
      <div class="text-center">
        <el-button @click="handleClose">取消</el-button>
        <el-button type="primary" :loading="loading" :disabled="channelIds.length === 0" @click="handleUpdate"> 更新 </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { ElMessage } from 'element-plus'
import useTryCatch from '@/hooks/use-try-catch'
import useLoading from '@/hooks/use-loading'
import { copyTempGroup, getTempGroupList } from '@/api/template-management'
import type { groupListItem, listItem } from '@/api/template-management'

const props = defineProps<
  {
    channelList?: {channel_id: number; channel_code: string}[];
    tableData?: groupListItem[];
    currentRow?: listItem;
  }
>()
const emit = defineEmits(['close', 'success'])

const dataMap = new Map<number, groupListItem>()
const channelMap = new Map<number, string>()

const channel_list = ref<{channel_id: number; channel_code: string}[]>([])
const table_data = ref<groupListItem[]>([])

const initMap = () => {
  table_data.value.forEach((item) => {
    dataMap.set(item.channel_id, item)
  })
  channel_list.value.forEach((item) => {
    channelMap.set(item.channel_id, item.channel_code)
  })
}

if (props.tableData && props.channelList) {
  channel_list.value = props.channelList
  table_data.value = props.tableData
  initMap()
} else if (props.currentRow) {
  useTryCatch( async () => {
    const res = await getTempGroupList(props.currentRow?.tpl_id as number)
    if (res.code === 200) {
      channel_list.value = res.data.channel_list
      table_data.value = res.data.list
      initMap()
      sourceChannelId.value = props.currentRow?.channel_id as number
      handleSourceChange()
    }
  } )
}

const { loading, setLoading } = useLoading()
const show = ref(true)
const sourceList = ref<groupListItem[]>([])
const targetList = ref<any[]>([])

const sourceChannelId = ref<string|number>('')
const channelIds = ref<number[]>([])

const reset = (all = false) => {
  targetList.value = []
  channelIds.value = []
  if (all) {
    sourceChannelId.value = ''
    sourceList.value = []
  }
}

const handleSourceChange = () => {
  const item = dataMap.get(sourceChannelId.value as number)
  sourceList.value = item ? [ item ] : []
  reset()
}
const handleTargetChange = () => {
  targetList.value = channelIds.value.map((id) => {
    const item = dataMap.get(id) || { channel_name: channelMap.get(id) }
    return item
  })
}

const handleUpdate = () => {
  useTryCatch( async () => {
    setLoading(true)
    const params = {
      tpl_id: sourceList.value[0].tpl_id,
      channel_ids: String(channelIds.value)
    }
    const res = await copyTempGroup(params)
    if (res.code === 200) {
      ElMessage.success(res.msg)
      emit('success')
      reset(true)
      handleClose()
    } else {
      ElMessage.error(res.msg)
    }
    setLoading(false)
  }, () => setLoading(false) )
}

const handleClose = () => {
  show.value = false
}

if (props.currentRow) {
  sourceChannelId.value = props.currentRow.channel_id
  handleSourceChange()
}

defineExpose({
  show: () => show.value = true,
  handleSourceChannel: (channel_id?: number) => {
    sourceChannelId.value = channel_id || ''
    handleSourceChange()
  }
})
</script>