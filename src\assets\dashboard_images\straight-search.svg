<svg width="56" height="56" viewBox="0 0 56 56" fill="none" xmlns="http://www.w3.org/2000/svg">
<g clip-path="url(#clip0_407_7521)">
<rect width="56" height="56" rx="12" fill="url(#paint0_linear_407_7521)"/>
<g filter="url(#filter0_d_407_7521)">
<path d="M56 30.5L39 16.5C41 19.6667 43.4 27.7 37 34.5C30.6 41.3 21 40.3333 17 39L36 55.5H56V30.5Z" fill="url(#paint1_linear_407_7521)"/>
<rect x="32" y="35.8381" width="6" height="15" rx="1.75283" transform="rotate(-45 32 35.8381)" fill="url(#paint2_linear_407_7521)"/>
<circle cx="27.3522" cy="26.8522" r="12.7279" transform="rotate(-45 27.3522 26.8522)" stroke="url(#paint3_linear_407_7521)" stroke-width="6"/>
</g>
</g>
<defs>
<filter id="filter0_d_407_7521" x="1.1073" y="4.11296" width="65.4097" height="65.4097" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="3.50566"/>
<feGaussianBlur stdDeviation="5.25848"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.127778 0 0 0 0 0.423847 0 0 0 0 0.666667 0 0 0 0.26 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_407_7521"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_407_7521" result="shape"/>
</filter>
<linearGradient id="paint0_linear_407_7521" x1="28" y1="0" x2="28" y2="63" gradientUnits="userSpaceOnUse">
<stop stop-color="#0077FF"/>
<stop offset="1" stop-color="#00F0FF"/>
</linearGradient>
<linearGradient id="paint1_linear_407_7521" x1="36.5" y1="16.5" x2="53.5" y2="59" gradientUnits="userSpaceOnUse">
<stop stop-color="#3291E8"/>
<stop offset="1" stop-color="#1278D6" stop-opacity="0"/>
</linearGradient>
<linearGradient id="paint2_linear_407_7521" x1="35" y1="35.8381" x2="35" y2="50.8381" gradientUnits="userSpaceOnUse">
<stop stop-color="#61D3FF"/>
<stop offset="1" stop-color="white"/>
</linearGradient>
<linearGradient id="paint3_linear_407_7521" x1="27.3522" y1="14.1243" x2="27.3522" y2="39.5801" gradientUnits="userSpaceOnUse">
<stop stop-color="#E4F7FF"/>
<stop offset="1" stop-color="white"/>
</linearGradient>
<clipPath id="clip0_407_7521">
<rect width="56" height="56" rx="12" fill="white"/>
</clipPath>
</defs>
</svg>
