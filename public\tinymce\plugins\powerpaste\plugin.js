/* !
 * Tiny PowerPaste plugin
 *
 * Copyright (c) 2023 Ephox Corporation DBA Tiny Technologies, Inc.
 * Licensed under the Tiny commercial license. See https://www.tiny.cloud/legal/
 *
 * Version: 6.2.5-16
 */

!function(){"use strict";const e=e=>t=>(e=>{const t=typeof e;return null===e?"null":"object"===t&&Array.isArray(e)?"array":"object"===t&&((e,t,n)=>{var r,o;return o=e,!!t.prototype.isPrototypeOf(o)||(null===(r=e.constructor)||void 0===r?void 0:r.name)===t.name})(e,String)?"string":t})(t)===e,t=e=>t=>typeof t===e,n=e("string"),r=e("object"),o=e("array"),s=t("boolean"),a=(void 0,e=>undefined===e);const i=e=>null==e,l=e=>!i(e),c=t("function"),u=t("number"),d=()=>{},h=(e,t)=>(...n)=>e(t.apply(null,n)),m=e=>()=>e,p=e=>e,f=(e,t)=>e===t;function g(e,...t){return(...n)=>{const r=t.concat(n);return e.apply(null,r)}}const v=e=>()=>{throw new Error(e)},y=e=>e(),b=m(!1),x=m(!0);class k{constructor(e,t){this.tag=e,this.value=t}static some(e){return new k(!0,e)}static none(){return k.singletonNone}fold(e,t){return this.tag?t(this.value):e()}isSome(){return this.tag}isNone(){return!this.tag}map(e){return this.tag?k.some(e(this.value)):k.none()}bind(e){return this.tag?e(this.value):k.none()}exists(e){return this.tag&&e(this.value)}forall(e){return!this.tag||e(this.value)}filter(e){return!this.tag||e(this.value)?this:k.none()}getOr(e){return this.tag?this.value:e}or(e){return this.tag?this:e}getOrThunk(e){return this.tag?this.value:e()}orThunk(e){return this.tag?this:e()}getOrDie(e){if(this.tag)return this.value;throw new Error(null!=e?e:"Called getOrDie on None")}static from(e){return l(e)?k.some(e):k.none()}getOrNull(){return this.tag?this.value:null}getOrUndefined(){return this.value}each(e){this.tag&&e(this.value)}toArray(){return this.tag?[this.value]:[]}toString(){return this.tag?`some(${this.value})`:"none()"}}k.singletonNone=new k(!1);const w=Array.prototype.slice,S=Array.prototype.indexOf,C=Array.prototype.push,T=(e,t)=>((e,t)=>S.call(e,t))(e,t)>-1,I=(e,t)=>{for(let n=0,r=e.length;n<r;n++)if(t(e[n],n))return!0;return!1},A=(e,t)=>{const n=e.length,r=new Array(n);for(let o=0;o<n;o++){const n=e[o];r[o]=t(n,o)}return r},O=(e,t)=>{for(let n=0,r=e.length;n<r;n++)t(e[n],n)},E=(e,t)=>{const n=[],r=[];for(let o=0,s=e.length;o<s;o++){const s=e[o];(t(s,o)?n:r).push(s)}return{pass:n,fail:r}},L=(e,t)=>{const n=[];for(let r=0,o=e.length;r<o;r++){const o=e[r];t(o,r)&&n.push(o)}return n},N=(e,t,n)=>(O(e,((e,r)=>{n=t(n,e,r)})),n),_=(e,t)=>((e,t,n)=>{for(let r=0,o=e.length;r<o;r++){const o=e[r];if(t(o,r))return k.some(o);if(n(o,r))break}return k.none()})(e,t,b),D=(e,t)=>{for(let n=0,r=e.length;n<r;n++)if(t(e[n],n))return k.some(n);return k.none()},P=e=>{const t=[];for(let n=0,r=e.length;n<r;++n){if(!o(e[n]))throw new Error("Arr.flatten item "+n+" was not an array, input: "+e);C.apply(t,e[n])}return t},R=(e,t)=>P(A(e,t)),M=(e,t)=>{for(let n=0,r=e.length;n<r;++n)if(!0!==t(e[n],n))return!1;return!0},j=(e,t)=>{const n={};for(let r=0,o=e.length;r<o;r++){const o=e[r];n[String(o)]=t(o,r)}return n},F=(e,t)=>{const n=w.call(e,0);return n.sort(t),n},U=e=>((e,t)=>0<e.length?k.some(e[0]):k.none())(e),B=(e,t)=>{for(let n=0;n<e.length;n++){const r=t(e[n],n);if(r.isSome())return r}return k.none()},z=(e,t,n)=>""===t||e.length>=t.length&&e.substr(n,n+t.length)===t,H=(e,t)=>$(e,t)?((e,t)=>e.substring(t))(e,t.length):e,W=(e,t,n=0,r)=>{const o=e.indexOf(t,n);return-1!==o&&(!!a(r)||o+t.length<=r)},$=(e,t)=>z(e,t,0),V=(e,t)=>z(e,t,e.length-t.length),G=(q=/^\s+|\s+$/g,e=>e.replace(q,""));var q;const K=e=>parseInt(e,10),J=(e,t)=>{const n=e-t;return 0===n?0:n>0?1:-1},Y=(e,t,n)=>({major:e,minor:t,patch:n}),X=e=>{const t=/([0-9]+)\.([0-9]+)\.([0-9]+)(?:(\-.+)?)/.exec(e);return t?Y(K(t[1]),K(t[2]),K(t[3])):Y(0,0,0)},Z=e=>t=>t.options.get(e),Q=Z("paste_as_text"),ee=Z("paste_merge_formats"),te=Z("paste_tab_spaces"),ne=Z("smart_paste"),re=Z("cache_suffix"),oe=Z("automatic_uploads"),se=Z("indent_use_margin"),ae=Z("powerpaste_block_drop"),ie=Z("powerpaste_keep_unsupported_src"),le=Z("powerpaste_allow_local_images"),ce=Z("powerpaste_word_import"),ue=Z("powerpaste_googledocs_import"),de=Z("powerpaste_html_import"),he=Z("powerpaste_clean_filtered_inline_elements"),me=Z("link_default_protocol"),pe=e=>{var t;return tinymce.explode(null!==(t=e.options.get("images_file_types"))&&void 0!==t?t:"")},fe=e=>{let t=e;return{get:()=>t,set:e=>{t=e}}},ge=()=>{const e=(e=>{const t=fe(k.none()),n=()=>t.get().each(e);return{clear:()=>{n(),t.set(k.none())},isSet:()=>t.get().isSome(),get:()=>t.get(),set:e=>{n(),t.set(k.some(e))}}})(d);return{...e,on:t=>e.get().each(t)}},ve=(e,t)=>{const n=t=>e(t)?k.from(t.dom.nodeValue):k.none();return{get:r=>{if(!e(r))throw new Error("Can only get "+t+" value of a "+t+" node");return n(r).getOr("")},getOption:n,set:(n,r)=>{if(!e(n))throw new Error("Can only set raw "+t+" value of a "+t+" node");n.dom.nodeValue=r}}},ye="undefined"!=typeof window?window:Function("return this;")(),be=(e,t)=>(void 0!==e[t]&&null!==e[t]||(e[t]={}),e[t]),xe=(e,t)=>((e,t)=>{let n=void 0!==t?t:ye;for(let t=0;t<e.length;++t)n=be(n,e[t]);return n})(e.split("."),t),ke=e=>e.dom.nodeName.toLowerCase(),we=e=>e.dom.nodeType,Se=e=>t=>we(t)===e,Ce=e=>8===we(e)||"#comment"===ke(e),Te=Se(1),Ie=Se(3),Ae=Se(9),Oe=Se(11),Ee=e=>t=>Te(t)&&ke(t)===e,Le=ve(Ce,"comment"),Ne=e=>Le.get(e),_e=e=>{if(null==e)throw new Error("Node cannot be null or undefined");return{dom:e}},De={fromHtml:(e,t)=>{const n=(t||document).createElement("div");if(n.innerHTML=e,!n.hasChildNodes()||n.childNodes.length>1){const t="HTML does not have a single root node";throw console.error(t,e),new Error(t)}return _e(n.childNodes[0])},fromTag:(e,t)=>{const n=(t||document).createElement(e);return _e(n)},fromText:(e,t)=>{const n=(t||document).createTextNode(e);return _e(n)},fromDom:_e,fromPoint:(e,t,n)=>k.from(e.dom.elementFromPoint(t,n)).map(_e)},Pe=Object.keys,Re=Object.hasOwnProperty,Me=(e,t)=>{const n=Pe(e);for(let r=0,o=n.length;r<o;r++){const o=n[r];t(e[o],o)}},je=(e,t)=>Fe(e,((e,n)=>({k:n,v:t(e,n)}))),Fe=(e,t)=>{const n={};return Me(e,((e,r)=>{const o=t(e,r);n[o.k]=o.v})),n},Ue=(e,t)=>{const n={};return((e,t,n,r)=>{Me(e,((e,o)=>{(t(e,o)?n:r)(e,o)}))})(e,t,(e=>(t,n)=>{e[n]=t})(n),d),n},Be=e=>Pe(e).length,ze=(e,t)=>Re.call(e,t),He=e=>{let t,n=!1;return(...r)=>(n||(n=!0,t=e.apply(null,r)),t)},We=()=>$e(0,0),$e=(e,t)=>({major:e,minor:t}),Ve={nu:$e,detect:(e,t)=>{const n=String(t).toLowerCase();return 0===e.length?We():((e,t)=>{const n=((e,t)=>{for(let n=0;n<e.length;n++){const r=e[n];if(r.test(t))return r}})(e,t);if(!n)return{major:0,minor:0};const r=e=>Number(t.replace(n,"$"+e));return $e(r(1),r(2))})(e,n)},unknown:We},Ge=(e,t)=>{const n=String(t).toLowerCase();return _(e,(e=>e.search(n)))},qe=/.*?version\/\ ?([0-9]+)\.([0-9]+).*/,Ke=e=>t=>W(t,e),Je=[{name:"Edge",versionRegexes:[/.*?edge\/ ?([0-9]+)\.([0-9]+)$/],search:e=>W(e,"edge/")&&W(e,"chrome")&&W(e,"safari")&&W(e,"applewebkit")},{name:"Chromium",brand:"Chromium",versionRegexes:[/.*?chrome\/([0-9]+)\.([0-9]+).*/,qe],search:e=>W(e,"chrome")&&!W(e,"chromeframe")},{name:"IE",versionRegexes:[/.*?msie\ ?([0-9]+)\.([0-9]+).*/,/.*?rv:([0-9]+)\.([0-9]+).*/],search:e=>W(e,"msie")||W(e,"trident")},{name:"Opera",versionRegexes:[qe,/.*?opera\/([0-9]+)\.([0-9]+).*/],search:Ke("opera")},{name:"Firefox",versionRegexes:[/.*?firefox\/\ ?([0-9]+)\.([0-9]+).*/],search:Ke("firefox")},{name:"Safari",versionRegexes:[qe,/.*?cpu os ([0-9]+)_([0-9]+).*/],search:e=>(W(e,"safari")||W(e,"mobile/"))&&W(e,"applewebkit")}],Ye=[{name:"Windows",search:Ke("win"),versionRegexes:[/.*?windows\ nt\ ?([0-9]+)\.([0-9]+).*/]},{name:"iOS",search:e=>W(e,"iphone")||W(e,"ipad"),versionRegexes:[/.*?version\/\ ?([0-9]+)\.([0-9]+).*/,/.*cpu os ([0-9]+)_([0-9]+).*/,/.*cpu iphone os ([0-9]+)_([0-9]+).*/]},{name:"Android",search:Ke("android"),versionRegexes:[/.*?android\ ?([0-9]+)\.([0-9]+).*/]},{name:"macOS",search:Ke("mac os x"),versionRegexes:[/.*?mac\ os\ x\ ?([0-9]+)_([0-9]+).*/]},{name:"Linux",search:Ke("linux"),versionRegexes:[]},{name:"Solaris",search:Ke("sunos"),versionRegexes:[]},{name:"FreeBSD",search:Ke("freebsd"),versionRegexes:[]},{name:"ChromeOS",search:Ke("cros"),versionRegexes:[/.*?chrome\/([0-9]+)\.([0-9]+).*/]}],Xe={browsers:m(Je),oses:m(Ye)},Ze="Edge",Qe="Chromium",et="Opera",tt="Firefox",nt="Safari",rt=e=>{const t=e.current,n=e.version,r=e=>()=>t===e;return{current:t,version:n,isEdge:r(Ze),isChromium:r(Qe),isIE:r("IE"),isOpera:r(et),isFirefox:r(tt),isSafari:r(nt)}},ot=()=>rt({current:void 0,version:Ve.unknown()}),st=rt,at=(m(Ze),m(Qe),m("IE"),m(et),m(tt),m(nt),"Windows"),it="Android",lt="Linux",ct="macOS",ut="Solaris",dt="FreeBSD",ht="ChromeOS",mt=e=>{const t=e.current,n=e.version,r=e=>()=>t===e;return{current:t,version:n,isWindows:r(at),isiOS:r("iOS"),isAndroid:r(it),isMacOS:r(ct),isLinux:r(lt),isSolaris:r(ut),isFreeBSD:r(dt),isChromeOS:r(ht)}},pt=()=>mt({current:void 0,version:Ve.unknown()}),ft=mt,gt=(m(at),m("iOS"),m(it),m(lt),m(ct),m(ut),m(dt),m(ht),e=>window.matchMedia(e).matches);let vt=He((()=>((e,t,n)=>{const r=Xe.browsers(),o=Xe.oses(),s=t.bind((e=>((e,t)=>B(t.brands,(t=>{const n=t.brand.toLowerCase();return _(e,(e=>{var t;return n===(null===(t=e.brand)||void 0===t?void 0:t.toLowerCase())})).map((e=>({current:e.name,version:Ve.nu(parseInt(t.version,10),0)})))})))(r,e))).orThunk((()=>((e,t)=>Ge(e,t).map((e=>{const n=Ve.detect(e.versionRegexes,t);return{current:e.name,version:n}})))(r,e))).fold(ot,st),a=((e,t)=>Ge(e,t).map((e=>{const n=Ve.detect(e.versionRegexes,t);return{current:e.name,version:n}})))(o,e).fold(pt,ft),i=((e,t,n,r)=>{const o=e.isiOS()&&!0===/ipad/i.test(n),s=e.isiOS()&&!o,a=e.isiOS()||e.isAndroid(),i=a||r("(pointer:coarse)"),l=o||!s&&a&&r("(min-device-width:768px)"),c=s||a&&!l,u=t.isSafari()&&e.isiOS()&&!1===/safari/i.test(n),d=!c&&!l&&!u;return{isiPad:m(o),isiPhone:m(s),isTablet:m(l),isPhone:m(c),isTouch:m(i),isAndroid:e.isAndroid,isiOS:e.isiOS,isWebView:m(u),isDesktop:m(d)}})(a,s,e,n);return{browser:s,os:a,deviceType:i}})(navigator.userAgent,k.from(navigator.userAgentData),gt)));const yt=()=>vt(),bt=yt(),xt=bt.deviceType.isiOS()||bt.deviceType.isAndroid(),kt=m({isSupported:!1,cleanDocument:()=>Promise.reject("not supported")});var wt=xt?kt:(e,t,n)=>{const r=t+"/wordimport.js"+(e=>k.from(e).filter((e=>0!==e.length)).map((e=>(-1===e.indexOf("?")?"?":"")+e)).getOr(""))(n||"v=6.0.0"),o=e.loadScript("ephox.wimp",r);return o.catch((e=>{console.error("Unable to load word import: ",e)})),{isSupported:!0,cleanDocument:(e,t,n)=>o.then((r=>r.cleanDocument(e,t,n.cleanFilteredInlineElements)))}};const St=e=>{let t=[];return{bind:e=>{if(void 0===e)throw new Error("Event bind error: undefined handler");t.push(e)},unbind:e=>{t=L(t,(t=>t!==e))},trigger:(...n)=>{const r={};O(e,((e,t)=>{r[e]=n[t]})),O(t,(e=>{e(r)}))}}},Ct=e=>{const t=je(e,(e=>({bind:e.bind,unbind:e.unbind}))),n=je(e,(e=>e.trigger));return{registry:t,trigger:n}},Tt=(e,t)=>{const n=e.dom;if(1!==n.nodeType)return!1;{const e=n;if(void 0!==e.matches)return e.matches(t);if(void 0!==e.msMatchesSelector)return e.msMatchesSelector(t);if(void 0!==e.webkitMatchesSelector)return e.webkitMatchesSelector(t);if(void 0!==e.mozMatchesSelector)return e.mozMatchesSelector(t);throw new Error("Browser lacks native selectors")}},It=(e,t)=>{const n=void 0===t?document:t.dom;return 1!==(r=n).nodeType&&9!==r.nodeType&&11!==r.nodeType||0===r.childElementCount?[]:A(n.querySelectorAll(e),De.fromDom);var r},At=(e,t)=>e.dom===t.dom,Ot=Tt,Et=(e,t,n)=>{const r=e.document.createRange();var o;return o=r,t.fold((e=>{o.setStartBefore(e.dom)}),((e,t)=>{o.setStart(e.dom,t)}),(e=>{o.setStartAfter(e.dom)})),((e,t)=>{t.fold((t=>{e.setEndBefore(t.dom)}),((t,n)=>{e.setEnd(t.dom,n)}),(t=>{e.setEndAfter(t.dom)}))})(r,n),r},Lt=(e,t,n,r,o)=>{const s=e.document.createRange();return s.setStart(t.dom,n),s.setEnd(r.dom,o),s},Nt=e=>{if(!o(e))throw new Error("cases must be an array");if(0===e.length)throw new Error("there must be at least one case");const t=[],n={};return O(e,((r,s)=>{const a=Pe(r);if(1!==a.length)throw new Error("one and only one name per case");const i=a[0],l=r[i];if(void 0!==n[i])throw new Error("duplicate key detected:"+i);if("cata"===i)throw new Error("cannot have a case named cata (sorry)");if(!o(l))throw new Error("case arguments must be an array");t.push(i),n[i]=(...n)=>{const r=n.length;if(r!==l.length)throw new Error("Wrong number of arguments to case "+i+". Expected "+l.length+" ("+l+"), got "+r);return{fold:(...t)=>{if(t.length!==e.length)throw new Error("Wrong number of arguments to fold. Expected "+e.length+", got "+t.length);return t[s].apply(null,n)},match:e=>{const r=Pe(e);if(t.length!==r.length)throw new Error("Wrong number of arguments to match. Expected: "+t.join(",")+"\nActual: "+r.join(","));if(!M(t,(e=>T(r,e))))throw new Error("Not all branches were specified when using match. Specified: "+r.join(", ")+"\nRequired: "+t.join(", "));return e[i].apply(null,n)},log:e=>{console.log(e,{constructors:t,constructor:i,params:n})}}}})),n},_t=Nt([{ltr:["start","soffset","finish","foffset"]},{rtl:["start","soffset","finish","foffset"]}]),Dt=(e,t,n)=>t(De.fromDom(n.startContainer),n.startOffset,De.fromDom(n.endContainer),n.endOffset);_t.ltr,_t.rtl;const Pt=(e,t,n,r)=>({start:e,soffset:t,finish:n,foffset:r}),Rt=e=>De.fromDom(e.dom.ownerDocument),Mt=e=>Ae(e)?e:Rt(e),jt=e=>De.fromDom(Mt(e).dom.defaultView),Ft=e=>k.from(e.dom.parentNode).map(De.fromDom),Ut=e=>k.from(e.dom.previousSibling).map(De.fromDom),Bt=e=>k.from(e.dom.nextSibling).map(De.fromDom),zt=e=>(e=>{const t=w.call(e,0);return t.reverse(),t})(((e,t)=>{const n=[],r=e=>(n.push(e),t(e));let o=t(e);do{o=o.bind(r)}while(o.isSome());return n})(e,Ut)),Ht=e=>A(e.dom.childNodes,De.fromDom),Wt=c(Element.prototype.attachShadow)&&c(Node.prototype.getRootNode),$t=m(Wt),Vt=Wt?e=>De.fromDom(e.dom.getRootNode()):Mt,Gt=e=>De.fromDom(e.dom.host),qt=e=>{const t=Ie(e)?e.dom.parentNode:e.dom;if(null==t||null===t.ownerDocument)return!1;const n=t.ownerDocument;return(e=>{const t=Vt(e);return Oe(n=t)&&l(n.dom.host)?k.some(t):k.none();var n})(De.fromDom(t)).fold((()=>n.body.contains(t)),(r=qt,o=Gt,e=>r(o(e))));var r,o},Kt=(e,t)=>{let n=[];return O(Ht(e),(e=>{t(e)&&(n=n.concat([e])),n=n.concat(Kt(e,t))})),n},Jt=(e,t)=>It(t,e),Yt=Nt([{before:["element"]},{on:["element","offset"]},{after:["element"]}]),Xt={before:Yt.before,on:Yt.on,after:Yt.after,cata:(e,t,n,r)=>e.fold(t,n,r),getStart:e=>e.fold(p,p,p)},Zt=Nt([{domRange:["rng"]},{relative:["startSitu","finishSitu"]},{exact:["start","soffset","finish","foffset"]}]),Qt={domRange:Zt.domRange,relative:Zt.relative,exact:Zt.exact,exactFromRange:e=>Zt.exact(e.start,e.soffset,e.finish,e.foffset),getWin:e=>{const t=(e=>e.match({domRange:e=>De.fromDom(e.startContainer),relative:(e,t)=>Xt.getStart(e),exact:(e,t,n,r)=>e}))(e);return jt(t)},range:Pt},en=(e,t)=>{const n=ke(e);return"input"===n?Xt.after(e):T(["br","img"],n)?0===t?Xt.before(e):Xt.after(e):Xt.on(e,t)},tn=(e,t,n,r)=>{const o=Rt(e).dom.createRange();return o.setStart(e.dom,t),o.setEnd(n.dom,r),o},nn=e=>k.from(e.getSelection()),rn=(e,t,n,r,o)=>{((e,t)=>{nn(e).each((e=>{e.removeAllRanges(),e.addRange(t)}))})(e,Lt(e,t,n,r,o))},on=(e,t,n,r,o)=>{const s=((e,t,n,r)=>{const o=en(e,t),s=en(n,r);return Qt.relative(o,s)})(t,n,r,o);((e,t)=>{((e,t)=>{const n=((e,t)=>t.match({domRange:e=>({ltr:m(e),rtl:k.none}),relative:(t,n)=>({ltr:He((()=>Et(e,t,n))),rtl:He((()=>k.some(Et(e,n,t))))}),exact:(t,n,r,o)=>({ltr:He((()=>Lt(e,t,n,r,o))),rtl:He((()=>k.some(Lt(e,r,o,t,n))))})}))(e,t);return((e,t)=>{const n=t.ltr();return n.collapsed?t.rtl().filter((e=>!1===e.collapsed)).map((e=>_t.rtl(De.fromDom(e.endContainer),e.endOffset,De.fromDom(e.startContainer),e.startOffset))).getOrThunk((()=>Dt(0,_t.ltr,n))):Dt(0,_t.ltr,n)})(0,n)})(e,t).match({ltr:(t,n,r,o)=>{rn(e,t,n,r,o)},rtl:(t,n,r,o)=>{nn(e).each((s=>{if(s.setBaseAndExtent)s.setBaseAndExtent(t.dom,n,r.dom,o);else if(s.extend)try{((e,t,n,r,o,s)=>{t.collapse(n.dom,r),t.extend(o.dom,s)})(0,s,t,n,r,o)}catch(s){rn(e,r,o,t,n)}else rn(e,r,o,t,n)}))}})})(e,s)},sn=e=>{if(e.rangeCount>0){const t=e.getRangeAt(0),n=e.getRangeAt(e.rangeCount-1);return k.some(Pt(De.fromDom(t.startContainer),t.startOffset,De.fromDom(n.endContainer),n.endOffset))}return k.none()},an=e=>{if(null===e.anchorNode||null===e.focusNode)return sn(e);{const t=De.fromDom(e.anchorNode),n=De.fromDom(e.focusNode);return((e,t,n,r)=>{const o=tn(e,t,n,r),s=At(e,n)&&t===r;return o.collapsed&&!s})(t,e.anchorOffset,n,e.focusOffset)?k.some(Pt(t,e.anchorOffset,n,e.focusOffset)):sn(e)}},ln=e=>nn(e).filter((e=>e.rangeCount>0)).bind(an),cn=(e,t)=>{Ft(e).each((n=>{n.dom.insertBefore(t.dom,e.dom)}))},un=(e,t)=>{Bt(e).fold((()=>{Ft(e).each((e=>{hn(e,t)}))}),(e=>{cn(e,t)}))},dn=(e,t)=>{(e=>((e,t)=>{const n=e.dom.childNodes;return k.from(n[0]).map(De.fromDom)})(e))(e).fold((()=>{hn(e,t)}),(n=>{e.dom.insertBefore(t.dom,n.dom)}))},hn=(e,t)=>{e.dom.appendChild(t.dom)},mn=(e,t)=>{cn(e,t),hn(t,e)},pn=(e,t)=>{O(t,((n,r)=>{const o=0===r?e:t[r-1];un(o,n)}))},fn=(e,t)=>{O(t,(t=>{hn(e,t)}))},gn=e=>{e.dom.textContent="",O(Ht(e),(e=>{vn(e)}))},vn=e=>{const t=e.dom;null!==t.parentNode&&t.parentNode.removeChild(t)},yn=e=>{const t=Ht(e);t.length>0&&pn(e,t),vn(e)},bn=(e,t,n,r)=>{const o=At(e,n)&&t===r;return{startContainer:m(e),startOffset:m(t),endContainer:m(n),endOffset:m(r),collapsed:m(o)}};let xn=0;const kn=e=>{const t=(new Date).getTime(),n=Math.floor(1e9*Math.random());return xn++,e+"_"+n+xn+String(t)},wn=(e,t,r)=>{if(!(n(r)||s(r)||u(r)))throw console.error("Invalid call to Attribute.set. Key ",t,":: Value ",r,":: Element ",e),new Error("Attribute value was not simple");e.setAttribute(t,r+"")},Sn=(e,t,n)=>{wn(e.dom,t,n)},Cn=(e,t)=>{const n=e.dom;Me(t,((e,t)=>{wn(n,t,e)}))},Tn=(e,t)=>{const n=e.dom.getAttribute(t);return null===n?void 0:n},In=(e,t)=>k.from(Tn(e,t)),An=(e,t)=>{const n=e.dom;return!(!n||!n.hasAttribute)&&n.hasAttribute(t)},On=(e,t)=>{e.dom.removeAttribute(t)},En=(e,t)=>{const n=Tn(e,t);return void 0===n||""===n?[]:n.split(" ")},Ln=e=>void 0!==e.dom.classList,Nn=e=>En(e,"class"),_n=(e,t)=>{Ln(e)?e.dom.classList.add(t):((e,t)=>{((e,t,n)=>{const r=En(e,t).concat([n]);Sn(e,t,r.join(" "))})(e,"class",t)})(e,t)},Dn=(e,t)=>{Ln(e)?e.dom.classList.remove(t):((e,t)=>{((e,t,n)=>{const r=L(En(e,t),(e=>e!==n));r.length>0?Sn(e,t,r.join(" ")):On(e,t)})(e,"class",t)})(e,t),(e=>{0===(Ln(e)?e.dom.classList:Nn(e)).length&&On(e,"class")})(e)},Pn=(e,t)=>Ln(e)&&e.dom.classList.contains(t),Rn=(e,t,n=f)=>e.exists((e=>n(e,t))),Mn=(e,t)=>e?k.some(t):k.none(),jn=e=>void 0!==e.style&&c(e.style.getPropertyValue),Fn=(e,t,r)=>{if(!n(r))throw console.error("Invalid call to CSS.set. Property ",t,":: Value ",r,":: Element ",e),new Error("CSS value must be a string: "+r);jn(e)&&e.style.setProperty(t,r)},Un=(e,t)=>{jn(e)&&e.style.removeProperty(t)},Bn=(e,t,n)=>{const r=e.dom;Fn(r,t,n)},zn=(e,t)=>{const n=e.dom;Me(t,((e,t)=>{Fn(n,t,e)}))},Hn=(e,t)=>{const n=e.dom,r=window.getComputedStyle(n).getPropertyValue(t);return""!==r||qt(e)?r:Wn(n,t)},Wn=(e,t)=>jn(e)?e.style.getPropertyValue(t):"",$n=(e,t)=>{const n=e.dom,r=Wn(n,t);return k.from(r).filter((e=>e.length>0))},Vn=e=>{const t={},n=e.dom;if(jn(n))for(let e=0;e<n.style.length;e++){const r=n.style.item(e);t[r]=n.style[r]}return t},Gn=(e,t)=>{const n=e.dom;Un(n,t),Rn(In(e,"style").map(G),"")&&On(e,"style")},qn=e=>"rtl"===Hn(e,"direction")?"rtl":"ltr",Kn=(e,t)=>{const n=(t||document).createElement("div");return n.innerHTML=e,Ht(De.fromDom(n))},Jn=e=>e.dom.innerHTML,Yn=(e,t,n)=>{let r=e.dom;const o=c(n)?n:b;for(;r.parentNode;){r=r.parentNode;const e=De.fromDom(r);if(t(e))return k.some(e);if(o(e))break}return k.none()},Xn=(e,t)=>_(e.dom.childNodes,(e=>t(De.fromDom(e)))).map(De.fromDom),Zn=(e,t)=>{const n=e=>{for(let r=0;r<e.childNodes.length;r++){const o=De.fromDom(e.childNodes[r]);if(t(o))return k.some(o);const s=n(e.childNodes[r]);if(s.isSome())return s}return k.none()};return n(e.dom)},Qn=(e,t,n)=>Yn(e,(e=>Tt(e,t)),n),er=(e,t,n)=>((e,t,n,r,o)=>((e,t)=>Tt(e,t))(n,r)?k.some(n):c(o)&&o(n)?k.none():t(n,r,o))(0,Qn,e,t,n),tr=e=>{const t=nr(e);return{resolve:e=>{const n=e.split(" ");return A(n,(e=>rr(t,e))).join(" ")}}},nr=e=>e.replace(/\./g,"-"),rr=(e,t)=>e+"-"+t,or=tr("ephox-sloth").resolve,sr=m(or("bin")),ar=["b","i","u","sub","sup","strike"],ir=sr(),lr=ir+kn(""),cr=("-100000px","100000px",e=>"rtl"===qn(e)?"100000px":"-100000px");const ur=(e,t)=>{const n=De.fromTag("div");var r;Cn(n,t),Cn(n,{contenteditable:"true","aria-hidden":"true"}),zn(n,{position:"fixed",top:"0px",width:"100px",height:"100px",overflow:"hidden",opacity:"0"}),r=n,O([ir,lr],(e=>{_n(r,e)}));const o=e=>Pn(e,lr);return{attach:e=>{gn(n),Bn(n,"left",cr(e)),hn(e,n)},focus:()=>{Qn(n,"body").each((t=>{e.toOff(t,n)}))},contents:()=>(((e,t)=>{Bt(e).filter(t).each((t=>{const n=Ht(t);fn(e,n),vn(t)})),((e,t)=>{const n=Ht(e);O(n,(e=>{t(e)&&((e,t)=>{const n=Ht(e),r=De.fromTag("div",Rt(e).dom);fn(r,n),cn(e,r),vn(e)})(e)}))})(e,t),O(Ht(e),(e=>{(e=>Te(e)&&!e.dom.hasChildNodes()&&T(ar,ke(e)))(e)&&vn(e)}))})(n,o),{elements:Ht(n),html:Jn(n),offscreen:n}),container:m(n),detach:()=>{vn(n)}}},dr=e=>{const t=At(e.start,e.finish)&&e.soffset===e.foffset;return{startContainer:m(e.start),startOffset:m(e.soffset),endContainer:m(e.finish),endOffset:m(e.foffset),collapsed:m(t)}},hr={set:(e,t)=>{on(e,t.startContainer(),t.startOffset(),t.endContainer(),t.endOffset())},get:e=>ln(e).map(dr)};var mr=e=>t=>{const n=Ct({after:St(["container"])}),r=(e=>{const t=De.fromTag("br");let n=k.none();const r=e=>jt(e).dom;return{cleanup:()=>{vn(t)},toOn:(t,o)=>{const s=r(o);n.each((n=>{const r=(e=>e.dom.childNodes.length)(t),o=At(t,n.startContainer())&&r<n.startOffset()?r:n.startOffset(),a=At(t,n.endContainer())&&r<n.endOffset()?r:n.endOffset(),i=bn(n.startContainer(),o,n.endContainer(),a);e.set(s,i)}))},toOff:(o,s)=>{const a=r(s);hn(s,t),n=e.get(a),e.set(a,bn(t,0,t,0))}}})(hr),o=((e,t,n)=>{const r=ur(e,n),o=()=>{e.cleanup();const t=r.contents();r.detach(),s.trigger.after(t.elements,t.html,r.container())},s=Ct({before:St([]),after:St(["elements","html","container"])}),a=d;return{instance:m((()=>{s.trigger.before(),r.attach(t),r.focus(),((e,t)=>{((e,t)=>{setTimeout(t,1)})(0,t)})(Rt(t),o)})),destroy:a,events:s.registry}})(r,t,e);return o.events.after.bind((e=>{r.toOn(t,e.container),n.trigger.after(e.container)})),{run:()=>{o.instance()()},events:n.registry}};const pr=tr("ephox-cement").resolve,fr={cacheSuffix:"",pasteBinAttrs:{},keepSrc:!1,sanitizer:{sanitizeHtml:m(""),sanitizeText:m("")},tabSpaces:4,cleanFilteredInlineElements:[],indentUseMargin:!1,defaultProtocol:"https"},gr=m(pr("smartpaste-eph-bin")),vr=e=>{let t=k.none(),n=[];const r=e=>{o()?s(e):n.push(e)},o=()=>t.isSome(),s=e=>{t.each((t=>{setTimeout((()=>{e(t)}),0)}))};return e((e=>{o()||(t=k.some(e),O(n,s),n=[])})),{get:r,map:e=>vr((t=>{r((n=>{t(e(n))}))})),isReady:o}},yr={nu:vr,pure:e=>vr((t=>{t(e)}))},br=e=>{setTimeout((()=>{throw e}),0)},xr=e=>{const t=t=>{e().then(t,br)};return{map:t=>xr((()=>e().then(t))),bind:t=>xr((()=>e().then((e=>t(e).toPromise())))),anonBind:t=>xr((()=>e().then((()=>t.toPromise())))),toLazy:()=>yr.nu(t),toCached:()=>{let t=null;return xr((()=>(null===t&&(t=e()),t)))},toPromise:e,get:t}},kr=e=>xr((()=>new Promise(e))),wr=e=>((e,t)=>t((t=>{const n=[];let r=0;0===e.length?t([]):O(e,((o,s)=>{o.get((o=>s=>{n[o]=s,r++,r>=e.length&&t(n)})(s))}))})))(e,kr),Sr=()=>{const e={};return{getOrSetIndexed:(t,n)=>(t=>void 0!==e[t])(t)?e[t]:((t,n)=>(e[t]=n,n))(t,n()),waitForLoad:()=>{const t=((e,t)=>{const n=[];return Me(e,((e,r)=>{n.push(t(e,r))})),n})(e,p);return wr(t)}}},Cr=e=>{const t=t=>t(e),n=m(e),r=()=>o,o={tag:!0,inner:e,fold:(t,n)=>n(e),isValue:x,isError:b,map:t=>Ir.value(t(e)),mapError:r,bind:t,exists:t,forall:t,getOr:n,or:r,getOrThunk:n,orThunk:r,getOrDie:n,each:t=>{t(e)},toOptional:()=>k.some(e)};return o},Tr=e=>{const t=()=>n,n={tag:!1,inner:e,fold:(t,n)=>t(e),isValue:b,isError:x,map:t,mapError:t=>Ir.error(t(e)),bind:t,exists:b,forall:x,getOr:p,or:p,getOrThunk:y,orThunk:y,getOrDie:v(String(e)),each:d,toOptional:k.none};return n},Ir={value:Cr,error:Tr,fromOption:(e,t)=>e.fold((()=>Tr(t)),Cr)},Ar=e=>{const t=De.fromDom((e=>{if($t()&&l(e.target)){const t=De.fromDom(e.target);if(Te(t)&&l(t.dom.shadowRoot)&&e.composed&&e.composedPath){const t=e.composedPath();if(t)return U(t)}}return k.from(e.target)})(e).getOr(e.target)),n=()=>e.stopPropagation(),r=()=>e.preventDefault(),o=h(r,n);return((e,t,n,r,o,s,a)=>({target:e,x:t,y:n,stop:r,prevent:o,kill:s,raw:a}))(t,e.clientX,e.clientY,n,r,o,e)},Or=(e,t,n,r)=>{e.dom.removeEventListener(t,n,r)},Er=x,Lr=(e,t,n)=>((e,t,n,r)=>((e,t,n,r,o)=>{const s=((e,t)=>n=>{e(n)&&t(Ar(n))})(n,r);return e.dom.addEventListener(t,s,o),{unbind:g(Or,e,t,s,o)}})(e,t,n,r,!1))(e,t,Er,n),Nr=(e=>{const t=g(xe,e);xe("callbacks",t());const n=(n,o)=>{const s=t(),a=(e=>{const t=void 0===e.count?0:e.count,n="callback_"+t;return e.count=t+1,n})(s);return s.callbacks[a]=(...e)=>{o||r(a),n(...e)},(t=>e+".callbacks."+t)(a)},r=e=>{const n=e.substring(e.lastIndexOf(".")+1),r=t();void 0!==r.callbacks[n]&&delete r.callbacks[n]};return{ephemeral:e=>n(e,!1),permanent:e=>n(e,!0),unregister:r}})("ephox.henchman.features"),_r=(e,t)=>yr.nu((n=>{const o=t=>{O(a,(e=>{e.unbind()})),n(t.fold((t=>Ir.error(t+'Unable to download editor stylesheets from "'+e+'"')),Ir.value))},s=((e,t)=>{const n=De.fromDom(document),r=De.fromTag("link",n.dom);return Cn(r,{rel:"stylesheet",type:"text/css",href:e}),((e,t)=>{const n=(e=>{const t=e.dom.head;if(null==t)throw new Error("Head is not available yet");return De.fromDom(t)})(e);hn(n,t)})(n,r),r})(e),a=[Lr(s,"load",(e=>{(e=>{var t;try{const n=null===(t=e.target.dom.sheet)||void 0===t?void 0:t.cssRules;return r(n)&&0===n.length}catch(e){}return!1})(e)?o(Ir.error("")):t(o)})),Lr(s,"error",g(o,Ir.error("")))]})),Dr=(()=>{const e=Sr(),t=()=>e.waitForLoad();return{preload:()=>{t().get(p)},addStylesheet:(t,n)=>e.getOrSetIndexed(t,(()=>_r(t,n))),addScript:(t,n)=>e.getOrSetIndexed(t,(()=>(e=>yr.nu((t=>{const n=()=>{o.unbind(),s.unbind()},r=De.fromTag("script");Sn(r,"src",e),Sn(r,"type","text/javascript"),Sn(r,"async","async"),Sn(r,"data-main",Nr.ephemeral((e=>{t(Ir.value(e))})));const o=Lr(r,"error",(()=>{n(),t(Ir.error("Error loading external script tag "+e))})),s=Lr(r,"load",n);hn(De.fromDom(document.head),r)})))(t).map(n))),waitForLoad:t}})(),Pr={loadScript:(e,t)=>new Promise(((e,n)=>{((e,t)=>Dr.addScript(e,t))(t,p).get((t=>{t.fold(n,e)}))}))},Rr=Nt([{error:["message"]},{paste:["elements","correlated"]},{cancel:[]},{incomplete:["elements","correlated","message"]}]),Mr=(e,t,n,r,o)=>e.fold(t,n,r,o),jr=Rr.error,Fr=Rr.paste,Ur=Rr.cancel,Br=Rr.incomplete,zr=["officeStyles","htmlStyles","gdocsStyles","isWord","isGoogleDocs","proxyBin","isInternal"],Hr=(e,t)=>{const n={};return O(zr,(r=>{t[r].or(e[r]).each((e=>{n[r]=e}))})),Wr(n)},Wr=e=>j(zr,(t=>k.from(e[t]))),$r=e=>({response:jr(e),bundle:Wr({})}),Vr=e=>Promise.resolve($r(e)),Gr={response:Ur(),bundle:Wr({})},qr=e=>l(e.then),Kr=(e,t,n,r)=>({steps:e,input:t,label:n,capture:r}),Jr=(e,t)=>{const n=Hr(e.bundle,t.bundle),r=((e,t)=>Mr(e,k.none,k.none,k.none,((e,n,r)=>Mr(t,k.none,((e,t)=>k.some(Rr.incomplete(e,t,r))),k.none,k.none))).getOr(t))(e.response,t.response);return{response:r,bundle:n}},Yr=(e,t,n)=>{const r=()=>e,o=()=>{const r=n(t,e);return qr(r)?r.then((t=>Jr(e,t))):Jr(e,r)};return Mr(e.response,r,o,r,o)},Xr=(Zr=(e,t)=>t,(...e)=>{if(0===e.length)throw new Error("Can't merge zero objects");const t={};for(let n=0;n<e.length;n++){const r=e[n];for(const e in r)ze(r,e)&&(t[e]=Zr(t[e],r[e]))}return t});var Zr;const Qr=Nt([{starts:["value","f"]},{pattern:["regex","f"]},{contains:["value","f"]},{exact:["value","f"]},{all:[]},{not:["stringMatch"]}]),eo=p,to=(e,t)=>e.fold(((e,n)=>0===n(t).indexOf(n(e))),((e,n)=>e.test(n(t))),((e,n)=>n(t).indexOf(n(e))>=0),((e,n)=>n(t)===n(e)),x,(e=>!to(e,t))),no={starts:Qr.starts,pattern:Qr.pattern,contains:Qr.contains,exact:Qr.exact,all:Qr.all,not:Qr.not,cata:(e,t,n,r,o,s,a)=>e.fold(t,n,r,o,s,a),matches:to,caseSensitive:eo,caseInsensitive:e=>e.toLowerCase()},ro=(e,t)=>{const n=ke(e),r=t.name,o=void 0!==t.condition?t.condition:x;return no.matches(r,n)&&o(e)},oo=(e,t,n)=>{const r=(e=>{const t={},n=l(e)?e.split(";"):[];return O(n,(e=>{const n=e.split(":");2===n.length&&(t[G(n[0])]=G(n[1]))})),t})(e.dom.getAttribute("style")),o={};return O(t,(e=>{const t=r[e];void 0===t||n(t,e)||(o[e]=t)})),o},so=e=>{const t=Pe(e);return A(t,(t=>t+": "+e[t])).join("; ")},ao=["mso-list"],io=(e,t)=>{const n=oo(e,ao,t),r=((e,t)=>{const n=e.dom.style,r=i(n)?[]:n,o={};return O(r,(n=>{$n(e,n).each((e=>{t(e,n)||(o[n]=e)}))})),o})(e,t);((e,t,n)=>{Sn(e,"style","");const r=Be(t),o=Be(n);if(0===r&&0===o)On(e,"style");else if(0===r)Sn(e,"style",so(n));else{Me(t,((t,n)=>{Bn(e,n,t)}));const r=Tn(e,"style"),s=o>0?so(n)+"; ":"";Sn(e,"style",s+r)}})(e,r,n)},lo=(e,t)=>{const n=((e,t)=>{const n={};return O(e.dom.attributes,(e=>{t(e.value,e.name)||(n[e.name]=e.value)})),n})(e,t);((e,t)=>{const n=A(e.dom.attributes,(e=>e.name));Be(t)!==n.length&&((e,t,n)=>{O(t,(t=>{On(e,t)})),Me(n,((t,n)=>{Sn(e,n,t)}))})(e,n,t)})(e,n)},co=(e,t)=>{io(De.fromDom(e),t)},uo=(e,t,n)=>{e(n,((e,r)=>I(t,(t=>((e,t,n,r)=>{const o=r.name,s=void 0!==r.condition?r.condition:x,a=void 0!==r.value?r.value:no.all();return no.matches(o,n)&&no.matches(a,t)&&s(e)})(n,e,r,t)))))},ho="startElement",mo="endElement",po="text",fo="comment",go=(e,t,n)=>{let r,o,s;const a=De.fromDom(e);switch(e.nodeType){case 1:t?r=mo:(r=ho,zn(a,n||{}));const i=e;o="HTML"!==i.scopeName&&i.scopeName&&i.tagName&&i.tagName.indexOf(":")<=0?(i.scopeName+":"+i.tagName).toLowerCase():i.tagName.toLowerCase();break;case 3:r=po,s=e.nodeValue;break;case 8:r=fo,s=e.nodeValue;break;default:console.log("WARNING: Unsupported node type encountered: "+e.nodeType)}return{getNode:m(e),tag:()=>o,type:()=>r,text:()=>s}},vo=(e,t,n,r)=>{const o=r.createElement(e);return Me(t,((e,t)=>{o.setAttribute(t,e+"")})),go(o,!1,n)},yo=(e,t)=>go(t.createElement(e),!0),bo=yo("html",window.document),xo=e=>{const t=e.createDocumentFragment();let n=t;const r=e=>{n.appendChild(e)};return{dom:t,receive:o=>{switch(o.type()){case ho:s=o.getNode().cloneNode(!1),r(s),n=s;break;case po:(t=>{const n=e.createTextNode(t.text());r(n)})(o);break;case mo:(()=>{const e=n.parentNode;n=null===e?t:e})();break;case fo:break;default:throw new Error("Unsupported token type: "+o.type())}var s},label:"SERIALISER"}},ko=e=>t=>{((e,t)=>{const n=Xr({styles:[],attributes:[],classes:[],tags:[]},t),r=Jt(e,"*");O(r,(e=>{uo(io,n.styles,e),uo(lo,n.attributes,e),O(n.classes,(t=>{const n=An(e,"class")?(e=>Ln(e)?(e=>{const t=e.dom.classList,n=new Array(t.length);for(let e=0;e<t.length;e++){const r=t.item(e);null!==r&&(n[e]=r)}return n})(e):Nn(e))(e):[];O(n,(n=>{no.matches(t.name,n)&&Dn(e,n)}))}))}));const o=Jt(e,"*");O(o,(e=>{I(n.tags,g(ro,e))&&vn(e)}))})(t,e)},wo=e=>t=>{((e,t)=>{const n=Xr({tags:[]},t),r=Jt(e,"*");O(r,(e=>{I(n.tags,g(ro,e))&&yn(e)}))})(t,e)},So=e=>t=>{((e,t)=>{const n=Xr({tags:[]},t),r=Jt(e,"*");O(r,(e=>{_(n.tags,g(ro,e)).each((t=>{t.mutate(e)}))}))})(t,e)},Co=e=>t=>{const n=Jn(t),r=((e,t,n)=>{const r=xo(e),o=((e,t=window.document)=>{const n=t.createElement("div");t.body.appendChild(n),n.style.position="absolute",n.style.left="-10000px",n.innerHTML=e;let r=n.firstChild||bo;const o=[];let s=!1;return{hasNext:()=>void 0!==r,next:()=>{const e=r,a=r,i=s;return!s&&e.firstChild?(o.push(e),r=e.firstChild):s||1!==e.nodeType?e.nextSibling?(r=e.nextSibling,s=!1):(r=o.pop(),s=!0):s=!0,a===bo||r||(t.body.removeChild(n),r=bo),(l=a)===bo?l:l?go(l,i):void 0;var l}}})(t,e),s=((e,t,n)=>{let r=n;for(let n=t.length-1;n>=0;n--)r=t[n](r,{},e);return r})(e,n,r);for(;o.hasNext();){const e=o.next();s.receive(e)}return r.dom})(Rt(t).dom,n,e);gn(t),t.dom.appendChild(r)},To=(e,t,n)=>{const r=De.fromTag("div",e.dom);zn(r,{position:"fixed",left:"-100000px",top:"0px"}),hn((e=>{const t=e.dom.body;if(null==t)throw new Error("Body is not available yet");return De.fromDom(t)})(e),r),r.dom.innerHTML=t,O(n,(e=>{e(r)}));const o=Jn(r);return vn(r),o},Io=(e,t)=>n=>{const r=e=>{n.receive(e)},o=(e,t,n)=>(n=void 0!==n?n:e.type()===mo,go(t,n,{})),s={emit:r,emitTokens:e=>{O(e,r)},receive:t=>{e(s,t,o)},document:window.document};return t(s),s},Ao=(e,t)=>{if(void 0===e||void 0===t)throw console.trace(),new Error("brick");e.nextFilter.set(t)},Oo=(e,t,n=!1)=>e===t||l(e)&&l(t)&&e.tag===t.tag&&e.type===t.type&&(n||e.variant===t.variant),Eo=(e,t)=>An(De.fromDom(t.getNode()),"data-list-level"),Lo=(e,t,n,r)=>{const o=n.getCurrentListType(),s=n.getCurrentLevel()==r.level()?o:null;return(a=r.emblems(),i=s,_(a,(e=>"ul"===e.tag||l(i)&&Oo(e,i,!0))).orThunk((()=>U(a)))).filter((e=>!("ol"===e.tag&&(e=>{if(T(["p"],e.tag())){const t=((e,t)=>{const n=De.fromDom(e.getNode());return Tn(n,"class")})(e);return l(t)&&/^MsoHeading/.test(t)}return!0})(t))));var a,i},No=e=>(t,n,r)=>{const o=r,s=(e=>{const t=parseInt(Tn(e,"data-list-level"),10),n=Tn(e,"data-list-emblems"),r=l(n)?JSON.parse(n):[];return On(e,"data-list-level"),On(e,"data-list-emblems"),{level:m(t),emblems:m(r)}})(De.fromDom(o.getNode()));n.originalToken.set(o);const a=((e,t,n)=>(Lo(n.listType.get(),e,n.emitter,t).each(n.listType.set),((e,t,n)=>({level:m(e),token:m(t),type:m(n)}))(t.level(),n.originalToken.get(),n.listType.get())))(o,s,n);n.emitter.openItem(a.level(),a.token(),a.type()),Ao(n,e.inside())},_o=(e,t,n)=>({pred:e,action:t,label:m(n)});var Do=(e,t,n)=>{const r=(e,r,o)=>{_(t,(e=>e.pred(r,o))).fold(m(n),(e=>e.action))(e,r,o)};return r.toString=()=>"Handlers for "+e,r};const Po=(e,t)=>({state:m(e),result:m(t)}),Ro=(e,t)=>({state:m(e),value:m(t)}),Mo=(e,t,n,r)=>({level:m(e),type:m(t),types:m(n),items:m(r)}),jo=e=>{const t=e.items().slice(0);if(t.length>0&&"p"!==t[t.length-1]){const n=t[t.length-1];t[t.length-1]="p";const r=Mo(e.level(),e.type(),e.types(),t);return Ro(r,k.some(n))}return Ro(e,k.none())},Fo=(e,t,n)=>{let r=[],o=e;for(;t(o);){const e=n(o);o=e.state(),r=r.concat(e.result())}return Po(o,r)},Uo=(e,t,n)=>{const r=t.start&&t.start>1?{start:t.start}:{},o=e.level()+1,s=t,a=e.types().concat([t]),i=[g(vo,t.tag,r,n)],l=Mo(o,s,a,e.items());return Po(l,i)},Bo=e=>{const t=e.types().slice(0),n=[g(yo,t.pop().tag)],r=e.level()-1,o=t[t.length-1],s=Mo(r,o,t,e.items());return Po(s,n)},zo=(e,t,n)=>{const r=(c=t)?(e=>{const t=((e,t)=>{const n=De.fromDom(e.getNode());return Hn(n,"margin-left")})(e);return l(t)&&"0px"!==t?{"margin-left":t}:{}})(c):{"list-style-type":"none"},o=e.type()&&!Oo(e.type(),n)?((e,t)=>{const n=Bo(e),r=Uo(n.state(),t,t.type?{"list-style-type":t.type}:{});return Po(r.state(),n.result().concat(r.result()))})(e,n):Po(e,[]),s=[g(vo,"li",{},r)],a=((e,t)=>{const n=e.items().slice(0),r=void 0!==t&&"p"!==t?k.some(t):k.none();r.fold((()=>{n.push("p")}),(e=>{n.push(e)}));const o=Mo(e.level(),e.type(),e.types(),n);return Ro(o,r)})(o.state(),t&&t.tag()),i=a.value().map((e=>{const n=t;return co(n.getNode(),x),[m(n)]})).getOr([]);var c;return Po(a.state(),o.result().concat(s).concat(i))},Ho=e=>{const t=g(yo,"li"),n=jo(e),r=n.value().fold((()=>[t]),(e=>[g(yo,e),t]));return Po(n.state(),r)},Wo=e=>{if(0===e.length)throw new Error("Compose must have at least one element in the list");const t=e[e.length-1],n=R(e,(e=>e.result()));return Po(t.state(),n)},$o=e=>{const t=Ho(e),n=Bo(t.state());return Wo([t,n])},Vo=(e,t)=>((e,t,n)=>Fo(e,(e=>e.level()>t),n))(e,t,$o),Go=(e,t,n,r)=>{const o=e.level()>t?Vo(e,t):Po(e,[]),s=o.state().level()===t?((e,t,n)=>{const r=e.level()>0?Ho(e):Po(e,[]),o=zo(r.state(),n,t);return Wo([r,o])})(o.state(),r,n):((e,t,n,r)=>{const o=n>1?jo(e):Ro(e,k.none()),s=o.value().map((e=>[g(yo,e)])).getOr([]),a=((e,t,n,r)=>((e,t,n)=>Fo(e,(e=>e.level()<t),n))(e,n,(e=>((e,t,n,r)=>{const o=e.level()===n-1&&t.type?{"list-style-type":t.type}:{},s=Uo(e,t,o),a=zo(s.state(),s.state().level()==n?r:void 0,t);return Wo([s,a])})(e,t,n,r))))(o.state(),t,n,r);return Po(a.state(),s.concat(a.result()))})(o.state(),r,t,n);return Wo([o,s])},qo=Vo,Ko=["disc","circle","square"],Jo={getCurrentListType:()=>Yo().getCurrentListType(),getCurrentLevel:()=>Yo().getCurrentLevel(),closeAllLists:()=>Yo().closeAllLists(),openItem:(e,t,n)=>Yo().openItem(e,t,n)};let Yo=()=>({getCurrentListType:m({}),getCurrentLevel:m(1),closeAllLists:d,openItem:p});const Xo={inside:()=>Qo,outside:()=>es},Zo=(()=>{let e=!1;return{check:t=>!(!e||t.type()!==po)||(t.type()===ho&&"style"===t.tag()?(e=!0,!0):t.type()===mo&&"style"===t.tag()&&(e=!1,!0))}})(),Qo=(e=>Do("Inside.List.Item",[_o(((e,t)=>{const n=e.originalToken.get();return t.type()===mo&&null!==n&&t.tag()===n.tag()}),((t,n)=>{Ao(n,e.outside())}),"Closing open tag")],((e,t,n)=>{e.emit(n)})))(Xo),es=(e=>Do("Outside.List.Item",[_o(Eo,No(e),"Data List ****"),_o(((e,t)=>t.type()===po&&(e=>e.type()===po&&/^[\s\u00A0]*$/.test(e.text()))(t)),((e,t,n)=>{e.emit(n)}),"Whitespace")],((t,n,r)=>{n.emitter.closeAllLists(),t.emit(r),Ao(n,e.outside())})))(Xo),ts=(e=>{const t=fe(e),n=fe(null),r=fe(null);return{reset:o=>{t.set(e),n.set(null),r.set(null),Yo=m(((e,t)=>{let n=Mo(0,void 0,[],[]);const r=n=>{O(n.result(),(n=>{const r=n(t);e.emit(r)}))};return{closeAllLists:()=>{const e=qo(n,0);n=e.state(),r(e)},openItem:(e,t,o)=>{if(!o)return;const s=((e,t)=>"ul"===e.tag&&Ko[t-1]===e.type?{tag:"ul"}:e)(o,e),a=Go(n,e,t,s);n=a.state(),r(a)},getCurrentListType:()=>n.type(),getCurrentLevel:()=>n.level()}})(o,o.document))},nextFilter:t,originalToken:n,listType:r,emitter:Jo}})(es);var ns=Io(((e,t,n)=>{Zo.check(t)||((e,t,n)=>{t.nextFilter.get()(e,t,n)})(e,ts,t)}),ts.reset);const rs=e=>e.dom.textContent,os=[{regex:/^\(?[dc][\.\)]$/,type:{tag:"ol",type:"lower-alpha"}},{regex:/^\(?[DC][\.\)]$/,type:{tag:"ol",type:"upper-alpha"}},{regex:/^\(?M*(CM|CD|D?C{0,3})(XC|XL|L?X{0,3})(IX|IV|V?I{0,3})[\.\)]$/,type:{tag:"ol",type:"upper-roman"}},{regex:/^\(?m*(cm|cd|d?c{0,3})(xc|xl|l?x{0,3})(ix|iv|v?i{0,3})[\.\)]$/,type:{tag:"ol",type:"lower-roman"}},{regex:/^\(?[0-9]+[\.\)]$/,type:{tag:"ol"}},{regex:/^([0-9]+\.)*[0-9]+\.?$/,type:{tag:"ol",variant:"outline"}},{regex:/^\(?[a-z]+[\.\)]$/,type:{tag:"ol",type:"lower-alpha"}},{regex:/^\(?[A-Z]+[\.\)]$/,type:{tag:"ol",type:"upper-alpha"}}],ss={"\u2022":{tag:"ul",type:"disc"},"\xb7":{tag:"ul",type:"disc"},"\xa7":{tag:"ul",type:"square"}},as={o:{tag:"ul",type:"circle"},"-":{tag:"ul",type:"disc"},"\u25cf":{tag:"ul",type:"disc"},"\ufffd":{tag:"ul",type:"circle"}},is=(e,t)=>a(e.variant)?"("===t.charAt(0)?"()":")"===t.charAt(t.length-1)?")":".":e.variant,ls=e=>{const t=e.split("."),n=(()=>{if(0===t.length)return e;const n=t[t.length-1];return 0===n.length&&t.length>1?t[t.length-2]:n})(),r=parseInt(n,10);return isNaN(r)?{}:{start:r}},cs=e=>{const t=(e=>oo(e,["mso-list"],b)["mso-list"])(e),n=l(t)&&/ level([0-9]+)/.exec(t);return n&&n[1]?k.some(parseInt(n[1],10)):k.none()},us=(e,t)=>{const n=((e,t)=>{const n=as[e]?[as[e]]:[],r=((e,t)=>t&&ss[e]?[ss[e]]:t?[{tag:"ul",variant:e}]:[])(e,t),o=R(os,(t=>t.regex.test(e)?[Xr(t.type,ls(e),{variant:is(t.type,e)})]:[])),s=n.concat(r).concat(o);return A(s,(t=>void 0!==t.variant?t:Xr(t,{variant:e})))})(rs(e).trim(),t);return n.length>0?k.some(n):k.none()},ds=e=>Xn(e,Ce).bind(Bt).filter(Ee("span")),hs=e=>Zn(e,(e=>!!(Te(e)?oo(e,["mso-list"],b):{})["mso-list"])),ms=e=>Te(e)&&$n(e,"font-family").exists((e=>T(["wingdings","symbol"],e.toLowerCase()))),ps=(e,t,n,r)=>{((e,t,n)=>{Sn(e,"data-list-level",t);const r=JSON.stringify(n);Sn(e,"data-list-emblems",r)})(e,t,n),(e=>{const t=((e,t)=>{const n=t.getOr(x);return(e=>{const t=[];for(;null!==e.nextNode();)t.push(De.fromDom(e.currentNode));return t})(document.createTreeWalker(e.dom,NodeFilter.SHOW_COMMENT,{acceptNode:e=>n(e.nodeValue)?NodeFilter.FILTER_ACCEPT:NodeFilter.FILTER_REJECT}))})(e,k.none());O(t,vn)})(e),O(r,vn),On(e,"style"),On(e,"class")},fs=e=>(e=>cs(e).bind((t=>(e=>Xn(e,ms))(e).bind((n=>us(n,!0).map((r=>({mutate:()=>{ps(e,t,r,[n])}}))))))))(e).orThunk((()=>(e=>cs(e).bind((t=>ds(e).bind((n=>us(n,ms(n)).map((r=>({mutate:()=>{ps(e,t,r,[n])}}))))))))(e))).orThunk((()=>(e=>cs(e).bind((t=>ds(e).bind((n=>us(n,ms(n)).map((r=>({mutate:()=>{ps(e,t,r,[n])}}))))))))(e))).orThunk((()=>(e=>"p"!==ke(e)?k.none():cs(e).bind((t=>hs(e).bind((n=>us(n,!1).map((r=>({mutate:()=>{ps(e,t,r,[Ft(n).getOr(n)])}}))))))))(e))).orThunk((()=>(e=>"p"!==ke(e)?k.none():hs(e).bind((t=>{const n=Ft(t).getOr(t),r=ms(n);return us(t,r).bind((t=>(e=>$n(e,"margin-left").bind((e=>{const t=parseInt(e,10);return isNaN(t)?k.none():k.some(Math.max(1,Math.ceil(t/18)))})))(e).map((r=>({mutate:()=>{ps(e,r,t,[n])}})))))})))(e))),gs=So({tags:[{name:no.pattern(/^(p|h\d+)$/,no.caseInsensitive),mutate:e=>{fs(e).each((e=>{e.mutate()}))}}]}),vs=ns,ys=e=>{return(o=e,Ce(o)?(t=o,n="v:shape",k.from((r=t,r.dom.nodeValue)).bind((e=>{const t=e.indexOf("]>"),r=(e=>{try{return(new DOMParser).parseFromString(e,"text/html").body}catch(t){const n=document.implementation.createHTMLDocument("").body;return n.innerHTML=e,n}})(`<div>${e.slice(t+2,e.lastIndexOf("<!["))}</div>`);return Zn(De.fromDom(r),(e=>ke(e)===n))}))):k.none()).map((e=>{const t=Tn(e,"o:spid"),n=void 0===t?In(e,"id").getOr(""):t,r=De.fromTag("img");return _n(r,"rtf-data-image"),Sn(r,"data-image-id",n.substr(7)),Sn(r,"data-image-type","code"),((e,t)=>{const n=e.dom;Me(t,((e,t)=>{e.fold((()=>{Un(n,t)}),(e=>{Fn(n,t,e)}))}))})(r,{width:$n(e,"width"),height:$n(e,"height")}),r}));var t,n,r,o},bs=e=>{if(Ee("img")(e)){const t=Tn(e,"src");if(null!=t&&$(t,"file://")){const n=(e=>((e,t)=>De.fromDom(e.dom.cloneNode(!1)))(e))(e),r=t.split(/[\/\\]/),o=r[r.length-1];return Sn(n,"data-image-id",o),On(n,"src"),Sn(n,"data-image-type","local"),_n(n,"rtf-data-image"),k.some(n)}return k.none()}return k.none()};var xs;!function(e){e[e.Word=0]="Word",e[e.GoogleDocs=1]="GoogleDocs",e[e.Html=2]="Html"}(xs||(xs={}));const ks=e=>t=>{In(t,e.attrName).each((n=>{const r=l(e.styleName)?e.styleName:e.attrName;if($n(t,r).isNone()){const o=e.mapValue(n);Bn(t,r,o)}On(t,e.attrName)}))},ws=e=>{const t=ke(e);return"td"===t||"tr"===t||"col"===t||"th"===t},Ss=/^#?([a-f\d])([a-f\d])([a-f\d])$/i,Cs=/^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i,Ts=e=>H(e,"#").toUpperCase(),Is=e=>{const t=e.toString(16);return(1===t.length?"0"+t:t).toUpperCase()},As=e=>(e=>({value:Ts(e)}))(Is(e.red)+Is(e.green)+Is(e.blue)),Os=/^\s*rgb\s*\(\s*(\d+)\s*,\s*(\d+)\s*,\s*(\d+)\s*\)\s*$/i,Es=/^\s*rgba\s*\(\s*(\d+)\s*,\s*(\d+)\s*,\s*(\d+)\s*,\s*(\d?(?:\.\d+)?)\s*\)\s*$/i,Ls=(e,t,n,r)=>({red:e,green:t,blue:n,alpha:r}),Ns=(e,t,n,r)=>{const o=parseInt(e,10),s=parseInt(t,10),a=parseInt(n,10),i=parseFloat(r);return Ls(o,s,a,i)},_s=["background-repeat-x","background-repeat-y"],Ds=e=>{return"currentcolor"===e||"transparent"===e?e:"#"+(t=H(e,"#").toUpperCase(),(e=>Ss.test(e)||Cs.test(e))(t)?k.some({value:Ts(t)}):k.none()).orThunk((()=>(e=>{if("transparent"===e)return k.some(Ls(0,0,0,0));const t=Os.exec(e);if(null!==t)return k.some(Ns(t[1],t[2],t[3],"1"));const n=Es.exec(e);return null!==n?k.some(Ns(n[1],n[2],n[3],n[4])):k.none()})(e).map(As))).getOrThunk((()=>{const t=document.createElement("canvas");t.height=1,t.width=1;const n=t.getContext("2d");n.clearRect(0,0,t.width,t.height),n.fillStyle="#FFFFFF",n.fillStyle=e,n.fillRect(0,0,1,1);const r=n.getImageData(0,0,1,1).data,o=r[0],s=r[1],a=r[2],i=r[3];return As(Ls(o,s,a,i))})).value;var t},Ps=(e,t)=>{if(a(e))return"";switch(t){case"color":return Ds(e);case"font-family":return e.replace(/['"]/g,"");case"font-weight":return(e=>{switch(e){case"bold":return"700";case"normal":return"400";default:return e}})(e);default:return V(t,"-color")?Ds(e):(e=>e.replace(/^0(pt|px|pc|in|cm|mm|Q|cap|ch|ic|em|ex|lh|rlh|rem|vw|vh|vb|vi|vmax|vmin|%)$/,"0"))(e)}},Rs=So({tags:[{name:no.pattern(/^(p|div)$/,no.caseInsensitive),mutate:e=>{const t="ltr"===qn(e),n=t?"margin-left":"margin-right",r=t?"padding-left":"padding-right";$n(e,n).each((()=>{const t=Hn(e,n);Bn(e,r,t),Gn(e,n)}))}}]}),Ms=wo({tags:[{name:no.exact("b",no.caseInsensitive),condition:e=>In(e,"id").exists((e=>$(e,"docs-internal-guid")))}]}),js=ko({attributes:[{name:no.exact("id",no.caseInsensitive),value:no.starts("docs-internal-guid",no.caseInsensitive)}]}),Fs=[So({tags:[{name:no.exact("col",no.caseInsensitive),mutate:ks({attrName:"width",mapValue:e=>e.replace(/^(\d+)$/,"$1px")})}]})],Us=e=>So({tags:[{name:no.exact(e.matchTag,no.caseInsensitive),mutate:t=>{var n,r;(n=Vn(t),r=e.key,ze(n,r)?k.from(n[r]):k.none()).exists((t=>T(e.values,t)))&&(mn(t,De.fromTag(e.newTag)),Gn(t,e.key),o(e.removeExtra)&&O(e.removeExtra,(e=>Gn(t,e))))}}]}),Bs=[Us({matchTag:"span",key:"font-weight",values:["700","bold"],newTag:"strong"}),Us({matchTag:"span",key:"font-style",values:["italic"],newTag:"em"}),Us({matchTag:"span",key:"vertical-align",values:["sub"],newTag:"sub",removeExtra:["font-size"]}),Us({matchTag:"span",key:"vertical-align",values:["super"],newTag:"sup",removeExtra:["font-size"]})],zs=e=>t=>{const n=[],r={border:e.browser.isFirefox()?"medium none":"none","text-decoration":"none"},o=(e,t)=>{if(!a(t)){const r=De.fromTag(ke(e));hn(t,r),n.push({me:e,fake:r})}const r=L(Ht(e),Te);O(r,(t=>o(t,e)))};o(t);const s=A(n,(e=>{const{fake:t,me:n}=e,o=Vn(n),s=Ue(o,((e,r)=>{const o=((e,t)=>T(_s,t)?$n(e,"background-repeat"):k.none())(n,r).getOr(e),s=((e,t)=>T(_s,t)?Hn(e,"background-repeat"):Hn(e,t))(t,r);return Ps(o,r)===Ps(s,r)})),a=Ue(r,((e,t)=>Rn($n(n,t),e)));return{fake:t,me:n,toRemove:s,toPreserve:a}}));O(s,(e=>{const{me:t,toRemove:n,toPreserve:r,fake:o}=e;Me(n,((e,n)=>{Gn(t,n)})),Me(r,((e,n)=>{Bn(t,n,e)})),vn(o)}))},Hs=["p","div","article","aside","details","dt","figcaption","footer","form","fieldset","header","hgroup","html","main","nav","section","summary","body","dl","multicol","dd","figure","address","center","blockquote","h1","h2","h3","h4","h5","h6","listing","xmp","pre","plaintext","menu","dir","ul","ol","li","hr","table","tbody","thead","tfoot","th","tr","td","caption"],Ws=ko({styles:[{name:no.exact("background-color",no.caseInsensitive),value:no.exact("transparent",no.caseInsensitive)},{name:no.exact("white-space",no.caseInsensitive),value:no.starts("pre",no.caseInsensitive)},{name:no.exact("white-space-collapse",no.caseInsensitive),value:no.starts("preserve",no.caseInsensitive)},{name:no.exact("text-wrap",no.caseInsensitive),value:no.not(no.exact("wrap",no.caseInsensitive))},{name:no.pattern(/^overflow(-[xy])?$/,no.caseInsensitive),condition:e=>ws(e)&&Rn($n(e,"overflow"),"hidden")},{name:no.exact("overflow-wrap",no.caseInsensitive),condition:ws},{name:no.exact("table-layout",no.caseInsensitive),value:no.exact("fixed",no.caseInsensitive),condition:Ee("table")},{name:no.exact("line-height",no.caseInsensitive),value:no.exact("1.38",no.caseInsensitive)},{name:no.exact("vertical-align",no.caseInsensitive),value:no.exact("baseline",no.caseInsensitive)},{name:no.exact("font-style",no.caseInsensitive),value:no.exact("normal",no.caseInsensitive)},{name:no.exact("font-variant",no.caseInsensitive),value:no.exact("normal",no.caseInsensitive)},{name:no.exact("background-color",no.caseInsensitive),value:no.exact("transparent",no.caseInsensitive)},{name:no.starts("padding",no.caseInsensitive),condition:ws},{name:no.pattern(/^text-decoration(-(line|thickness|style|color))?$/,no.caseInsensitive),condition:e=>!Ee("a")(e)&&Rn($n(e,"text-decoration"),"none")}],attributes:[{name:no.exact("aria-level",no.caseInsensitive),condition:Ee("li")},{name:no.exact("dir",no.caseInsensitive),value:no.exact("ltr",no.caseInsensitive),condition:e=>T(Hs,ke(e))},{name:no.exact("role",no.caseInsensitive),value:no.exact("presentation",no.caseInsensitive),condition:e=>Ee("p")(e)&&Ft(e).exists(Ee("li"))}]}),$s=ko({styles:[{name:no.exact("text-align",no.caseInsensitive),value:no.exact("right",no.caseInsensitive),condition:e=>"rtl"===qn(e)}]}),Vs=So({tags:[{name:no.exact("p",no.caseInsensitive),condition:e=>{const t=t=>$n(e,t).map((e=>parseInt(e,10))).filter((e=>!isNaN(e))).getOr(0),n=qn(e);return t("text-indent")+t("rtl"===n?"padding-right":"padding-left")===0},mutate:e=>{const t=qn(e);Gn(e,"text-indent"),Gn(e,"rtl"===t?"padding-right":"padding-left")}}]}),Gs=e=>t=>Pn(t,e),qs=ve(Ie,"text"),Ks=e=>qs.get(e),Js=e=>{const t=e.dom.attributes;return null==t||0===t.length||1===t.length&&"style"===t[0].name},Ys=(e,t)=>{const n=De.fromTag(e);cn(t,n);const r=t.dom.attributes;O(r,(e=>{n.dom.setAttribute(e.name,e.value)}));const o=Ht(t);return fn(n,o),vn(t),n},Xs=Ee("li"),Zs=e=>Ut(e).bind((e=>Ie(e)&&0===Ks(e).trim().length?Zs(e):Xs(e)?k.some(e):k.none())),Qs=e=>{const t=Ys("span",e),n={"font-size":{1:"8pt",2:"10pt",3:"12pt",4:"14pt",5:"18pt",6:"24pt",7:"36pt"}};Me({face:"font-family",size:"font-size",color:"color"},((e,r)=>{In(t,r).each((o=>{const s=n[e],a=void 0!==s&&void 0!==s[o]?s[o]:o;Bn(t,e,a),On(t,r)}))}))},ea=ko({tags:[{name:no.exact("script",no.caseInsensitive)},{name:no.exact("link",no.caseInsensitive)},{name:no.exact("style",no.caseInsensitive),condition:e=>0===Jn(e).length}],attributes:[{name:no.starts("on",no.caseInsensitive)},{name:no.exact('"',no.caseInsensitive)},{name:no.exact("lang",no.caseInsensitive)},{name:no.exact("language",no.caseInsensitive)}],styles:[{name:no.all(),value:no.pattern(/OLE_LINK/i,no.caseInsensitive)}]}),ta=ko({tags:[{name:no.exact("meta",no.caseInsensitive)}]}),na=ko({tags:[{name:no.exact("style",no.caseInsensitive)}]}),ra=ko({styles:[{name:no.not(no.pattern(/^(width|height|list-style-type)$/,no.caseInsensitive)),condition:e=>!Pn(e,"ephox-limbo-transform")},{name:no.pattern(/^(width|height)$/,no.caseInsensitive),condition:e=>{return(e=>"img"!==ke(e))(e)&&!("table"===ke(t=e)||ws(t));var t}}]}),oa=(e=[])=>{const t=A(e,(e=>({name:no.exact(e,no.caseInsensitive)})));return wo({tags:t})},sa=ko({classes:[{name:no.not(no.exact("rtf-data-image",no.caseInsensitive))}]}),aa=ko({styles:[{name:no.pattern(/^(mso-.*|tab-stops|tab-interval|language|text-underline|text-effect|text-line-through|font-color|horiz-align|list-image-[0-9]+|separator-image|table-border-color-(dark|light)|vert-align|vnd\..*)$/,no.caseInsensitive)}]}),ia=ko({classes:[{name:no.pattern(/mso/i,no.caseInsensitive)}]}),la=wo({tags:[{name:no.exact("img",no.caseInsensitive),condition:e=>{const t=Tn(e,"src");return n(t)&&/^file:/.test(t)}}]}),ca=wo({tags:[{name:no.exact("a",no.caseInsensitive),condition:Js}]}),ua=ko({attributes:[{name:no.exact("style",no.caseInsensitive),value:no.exact("",no.caseInsensitive)}]}),da=ko({attributes:[{name:no.exact("class",no.caseInsensitive),value:no.exact("",no.caseInsensitive)}]}),ha=wo({tags:[{name:no.pattern(/^(font|em|strong|samp|acronym|cite|code|dfn|kbd|tt|b|i|u|s|sub|sup|ins|del|var|span)$/,no.caseInsensitive),condition:(ma=e=>!Js(e)||(e=>{const t=e.dom.attributes,n=null!=t&&t.length>0;return"span"!==ke(e)||n})(e)&&Zn(e,(e=>{const t=!Js(e),n=!T(["font","em","strong","samp","acronym","cite","code","dfn","kbd","tt","b","i","u","s","sub","sup","ins","del","var","span"],ke(e));return Ie(e)||t||n})).isSome(),e=>!ma(e))}]});var ma;const pa=So({tags:[{name:no.exact("p",no.caseInsensitive),mutate:e=>{0===Jn(e).length&&hn(e,De.fromTag("br"))}}]}),fa=e=>{const t=Ys("span",e);_n(t,"ephox-limbo-transform"),Bn(t,"text-decoration","underline")},ga=So({tags:[{name:no.pattern(/ol|ul/,no.caseInsensitive),mutate:e=>{Ft(e).each((t=>{const n=ke(t);T(["ol","ul"],n)&&Zs(e).fold((()=>{const t=De.fromTag("li");Bn(t,"list-style-type","none"),mn(e,t)}),(t=>{hn(t,e)}))}))}}]}),va=e=>{const t=[{name:"b",transform:{mutate:g(Ys,"strong")}},{name:"i",transform:{mutate:g(Ys,"em")}},{name:"u",transform:{mutate:fa}},{name:"s",transform:{mutate:g(Ys,"strike")}},{name:"font",transform:{mutate:Qs,debug:!0}}],n=L(t,(t=>!T(e,t.name))).map((e=>({name:no.exact(e.name,no.caseInsensitive),...e.transform})));return So({tags:n})},ya=ko({classes:[{name:no.exact("ephox-limbo-transform",no.caseInsensitive)}]}),ba=ko({tags:[{name:no.exact("br",no.caseInsensitive),condition:Gs("Apple-interchange-newline")}]}),xa=ko({styles:[{name:no.pattern(/^-/,no.caseInsensitive)},{name:no.all(),value:no.exact("initial",no.caseInsensitive)},{name:no.exact("background-color",no.caseInsensitive),value:no.exact("transparent",no.caseInsensitive)},{name:no.exact("font-style",no.caseInsensitive),value:no.exact("normal",no.caseInsensitive)},{name:no.pattern(/font-variant.*/,no.caseInsensitive)},{name:no.exact("letter-spacing",no.caseInsensitive)},{name:no.exact("font-weight",no.caseInsensitive),value:no.pattern(/400|normal/,no.caseInsensitive)},{name:no.exact("orphans",no.caseInsensitive)},{name:no.exact("text-decoration",no.caseInsensitive),value:no.exact("none",no.caseInsensitive)},{name:no.exact("text-size-adjust",no.caseInsensitive)},{name:no.exact("text-indent",no.caseInsensitive),value:no.exact("0px",no.caseInsensitive)},{name:no.exact("text-transform",no.caseInsensitive),value:no.exact("none",no.caseInsensitive)},{name:no.exact("white-space",no.caseInsensitive),value:no.exact("normal",no.caseInsensitive)},{name:no.exact("widows",no.caseInsensitive)},{name:no.exact("word-spacing",no.caseInsensitive),value:no.exact("0px",no.caseInsensitive)},{name:no.exact("text-align",no.caseInsensitive),value:no.pattern(/start|end/,no.caseInsensitive)},{name:no.exact("font-weight",no.caseInsensitive),value:no.pattern(/700|bold/,no.caseInsensitive),condition:e=>/^h\d$/.test(ke(e))}]}),ka=(()=>{const e=(e,t)=>n=>e(n).filter((e=>Ie(n)&&t(rs(e)||""," "))).isSome(),t=e(Ut,V),n=e(Bt,$);return So({tags:[{name:no.exact("span",no.caseInsensitive),condition:Gs("Apple-converted-space"),mutate:e=>{"\xa0"===rs(e)&&(t(e)||n(e)?yn(e):(cn(e,De.fromText(" ")),vn(e)))}}]})})(),wa=(()=>{const e=/^file:\/\/\/[^#]+(#[^#]+)$/;return So({tags:[{name:no.exact("a",no.caseInsensitive),condition:t=>{const n=Tn(t,"href");return!!n&&e.test(n)},mutate:t=>{In(t,"href").each((n=>{Sn(t,"href",n.replace(e,"$1"))}))}}]})})(),Sa=ko({attributes:[{name:no.exact("href",no.caseInsensitive),value:no.starts("file:///",no.caseInsensitive)}]}),Ca=(()=>{const e=(e,t,n)=>({name:no.exact(e,no.caseInsensitive),condition:e=>An(e,t),mutate:e=>{In(e,t).each((r=>{Sn(e,n,r),On(e,t)}))}});return So({tags:[e("a","data-ephox-href","href"),e("img","data-ephox-src","src")]})})(),Ta=e=>{const t=["table","thead","tbody","tfoot","th","tr","td","ul","ol","li"],n=Kt(e,Ce),r=_(n,(e=>W(Ne(e),"StartFragment"))),o=_(n,(e=>W(Ne(e),"EndFragment")));r.each((n=>{o.each((r=>{let o=n;const s=[];let a=((e,t,n,r)=>{const o=tn(e,0,n,0);return De.fromDom(o.commonAncestorContainer)})(n,0,r);for(;void 0!==a&&!At(a,e);)T(t,ke(a))?o=a:s.push(a),a=Ft(a).getOrUndefined();O(s,yn),O(zt(o),vn)})),vn(n)})),o.each(vn)},Ia=So({tags:[{name:no.pattern(/^(img|table)$/,no.caseInsensitive),mutate:e=>{$n(e,"margin-left").exists((e=>$(e,"-")))&&Gn(e,"margin-left"),$(Hn(e,"margin-left"),"-")&&(Bn(e,"margin-top",Hn(e,"margin-top")),Bn(e,"margin-bottom",Hn(e,"margin-bottom")),Bn(e,"margin-right",Hn(e,"margin-right")),Gn(e,"margin"))}}]}),Aa=So({tags:[{name:no.exact("p",no.caseInsensitive),mutate:ks({attrName:"align",styleName:"text-align",mapValue:p})}]}),Oa=(e,t)=>{return(n=e,qs.getOption(n)).exists((e=>0===t(e).length));var n},Ea=ko({tags:[{name:no.exact("font",no.caseInsensitive),condition:e=>{const t=Ht(e),n=e=>e.replace(/[ \r\n\uFEFF]+/g,"");return 0===t.length||M(t,(e=>Oa(e,n)))}}]}),La=e=>O(Ht(e),(e=>{Oa(e,G)&&vn(e)})),Na=So({tags:[{name:no.exact("ol",no.caseInsensitive),mutate:La},{name:no.exact("ul",no.caseInsensitive),mutate:La}]}),_a=wo({tags:[{name:no.pattern(/^([OVWXP]|U[0-9]+|ST[0-9]+):/i,no.caseInsensitive)}]}),Da=[Co([vs])],Pa=ko({attributes:[{name:no.exact("height",no.caseInsensitive),condition:Ee("table")}]}),Ra=ko({attributes:[{name:no.pattern(/^(width|height)$/,no.caseInsensitive),condition:ws}]}),Ma=So({tags:[{name:no.exact("table",no.caseInsensitive),mutate:ks({attrName:"width",mapValue:e=>e.replace(/^(\d+)$/,"$1px")})}]}),ja=ko({styles:[{name:no.exact("height",no.caseInsensitive),condition:Ee("td")},{name:no.exact("width",no.caseInsensitive),condition:Ee("tr")},{name:no.exact("height",no.caseInsensitive),condition:Ee("col")}]}),Fa=ko({attributes:[{name:no.pattern(/^v:/,no.caseInsensitive)},{name:no.exact("href",no.caseInsensitive),value:no.contains("#_toc",no.caseInsensitive)},{name:no.exact("href",no.caseInsensitive),value:no.contains("#_mso",no.caseInsensitive)},{name:no.pattern(/^xmlns(:|$)/,no.caseInsensitive)},{name:no.exact("type",no.caseInsensitive),condition:e=>"ol"===ke(e)||"ul"===ke(e)}]});wo({tags:[{name:no.exact("p",no.caseInsensitive),condition:("li",e=>Ft(e).exists((e=>"li"===ke(e)&&1===Ht(e).length)))}]}),So({tags:[{name:no.pattern(/^(img|table)$/,no.caseInsensitive),mutate:e=>{$n(e,"margin-left").exists((e=>$(e,"-")))&&Gn(e,"margin-left"),$(Hn(e,"margin-left"),"-")&&(Bn(e,"margin-top",Hn(e,"margin-top")),Bn(e,"margin-bottom",Hn(e,"margin-bottom")),Bn(e,"margin-right",Hn(e,"margin-right")),Gn(e,"margin"))}}]});const Ua=e=>{const t=[Aa,aa,ia],n=[Aa,ra,oa(e.cleanFilteredInlineElements),sa];return e.merge?t:n},Ba=e=>e.type===xs.Word?[gs]:[],za=e=>e.type===xs.Word?[_a,...Da,Fa]:[],Ha=e=>e.type===xs.GoogleDocs?[Ms]:[],Wa=e=>e.type!==xs.GoogleDocs||e.indentUseMargin?[]:[Rs],$a=(e,t)=>e.type===xs.GoogleDocs?[Ws,...Bs,...Fs,zs(t),$s,Vs,...Wa(e)]:[],Va=e=>e.type===xs.Html&&e.merge?[xa]:[],Ga=e=>e.type===xs.Word?[ja,Ma,Ra,Pa]:[];Nt([{bothErrors:["error1","error2"]},{firstError:["error1","value2"]},{secondError:["value1","error2"]},{bothValues:["value1","value2"]}]);const qa=(e,t)=>Ya(document.createElement("canvas"),e,t),Ka=e=>{const t=qa(e.width,e.height);return Ja(t).drawImage(e,0,0),t},Ja=e=>e.getContext("2d"),Ya=(e,t,n)=>(e.width=t,e.height=n,e),Xa=e=>e.naturalWidth||e.width,Za=e=>e.naturalHeight||e.height,Qa=e=>fetch(e).then((e=>e.ok?e.blob():Promise.reject(new Error("Error "+e.status+" downloading image"))),(()=>Promise.reject((()=>{const e=new Error("No access to download image");return e.code=18,e.name="SecurityError",e})()))),ei=e=>{const t=e.split(","),n=/data:([^;]+)/.exec(t[0]);if(!n)return k.none();const r=n[1],o=t[1],s=1024,a=atob(o),i=a.length,l=Math.ceil(i/s),c=new Array(l);for(let e=0;e<l;++e){const t=e*s,n=Math.min(t+s,i),r=new Array(n-t);for(let e=t,o=0;e<n;++o,++e)r[o]=a[e].charCodeAt(0);c[e]=new Uint8Array(r)}return k.some(new Blob(c,{type:r}))},ti=e=>new Promise(((t,n)=>{ei(e).fold((()=>{n("uri is not base64: "+e)}),t)})),ni=(e,t,n)=>(t=t||"image/png",c(HTMLCanvasElement.prototype.toBlob)?new Promise(((r,o)=>{e.toBlob((e=>{e?r(e):o()}),t,n)})):ti(e.toDataURL(t,n))),ri=e=>(e=>{const t=URL.createObjectURL(e),n=new Image;return n.src=t,(e=>new Promise(((t,n)=>{const r=()=>{s(),t(e)},o=[Lr(e,"load",r),Lr(e,"error",(()=>{s(),n("Unable to load data from image: "+e.dom.src)}))],s=()=>O(o,(e=>e.unbind()));e.dom.complete&&r()})))(De.fromDom(n)).then((e=>e.dom))})(e).then((e=>{si(e);const t=qa(Xa(e),Za(e));return Ja(t).drawImage(e,0,0),t})),oi=e=>new Promise((t=>{const n=new FileReader;n.onloadend=()=>{t(n.result)},n.readAsDataURL(e)})),si=e=>{URL.revokeObjectURL(e.src)},ai=(e,t,n)=>{const r=t.type,o=m(r),s=m(n),a=(t,n)=>e.then((e=>((e,t,n)=>(t=t||"image/png",e.toDataURL(t,n)))(e,t,n)));return{getType:o,toBlob:()=>Promise.resolve(t),toDataURL:s,toBase64:()=>n.split(",")[1],toAdjustedBlob:(t,n)=>e.then((e=>ni(e,t,n))),toAdjustedDataURL:a,toAdjustedBase64:(e,t)=>a(e,t).then((e=>e.split(",")[1])),toCanvas:()=>e.then(Ka)}},ii=(e,t)=>ni(e,t).then((t=>ai(Promise.resolve(e),t,e.toDataURL()))),li=(e,t,n)=>{const r=Xa(e),o=Za(e);let s=t/r,a=n/o,i=!1;(s<.5||s>2)&&(s=s<.5?.5:2,i=!0),(a<.5||a>2)&&(a=a<.5?.5:2,i=!0);const l=ci(e,s,a);return i?l.then((e=>li(e,t,n))):l},ci=(e,t,n)=>new Promise((r=>{const o=Xa(e),s=Za(e),a=Math.floor(o*t),i=Math.floor(s*n),l=qa(a,i);Ja(l).drawImage(e,0,0,o,s,0,0,a,i),r(l)})),ui=(e,t=2)=>{const n=Math.pow(10,t),r=Math.round(e*n);return Math.ceil(r/n)},di=(e,t,n,r,o)=>((e,t,n,r,o)=>e.toCanvas().then((s=>((e,t,n,r,o,s)=>{const a=qa(o,s);return Ja(a).drawImage(e,-n,-r),ii(a,t)})(s,e.getType(),t,n,r,o))))(e,t,n,r,o),hi=(e,t)=>((e,t)=>e.toCanvas().then((n=>((e,t,n)=>{const r=(n<0?360+n:n)*Math.PI/180,o=e.width,s=e.height,a=Math.sin(r),i=Math.cos(r),l=ui(Math.abs(o*i)+Math.abs(s*a)),c=ui(Math.abs(o*a)+Math.abs(s*i)),u=qa(l,c),d=Ja(u);return d.translate(l/2,c/2),d.rotate(r),d.drawImage(e,-o/2,-s/2),ii(u,t)})(n,e.getType(),t))))(e,t),mi=(e,t)=>((e,t)=>ai(ri(e),e,t))(e,t),pi=e=>(e=>(e=>{const t=e.src;return 0===t.indexOf("data:")?ti(t):Qa(t)})(e).then((e=>(e=>oi(e).then((t=>ai(ri(e),e,t))))(e))))(e),fi=e=>e.toBlob(),gi=e=>e.toDataURL(),vi=e=>parseInt(e,10),yi=e=>e.isPx&&(e.cropWidth!==e.width||e.cropHeight!==e.height),bi=/rotate\((\d\.\d+)rad\)/,xi=(e,t,n)=>pi(e.dom).then((e=>t=>((e,t,n)=>((e,t,n)=>e.toCanvas().then((r=>li(r,t,n).then((t=>ii(t,e.getType()))))))(e,t,n))(t,e.width,e.height))(t)).then(((e,t)=>n=>{if(yi(t)){const r=-1*vi(Hn(e,"margin-top")),o=-1*vi(Hn(e,"margin-left"));return di(n,o,r,t.cropWidth,t.cropHeight).then((n=>(Cn(e,{width:t.cropWidth,height:t.cropHeight}),n)))}return Promise.resolve(n)})(e,t)).then(((e,t)=>n=>(e=>{const t=Hn(e,"transform");return k.from(bi.exec(t)).map((e=>Math.round(parseFloat(e[1])*(180/Math.PI))))})(t).fold((()=>Promise.resolve(n)),(r=>hi(n,r).then((n=>(Gn(t,"transform"),On(e,"width"),On(e,"height"),n))))))(e,n)).then((t=>{const n=gi(t);return Sn(e,"src",n),Promise.resolve()})),ki=e=>Ft(e).filter(Ee("span")).map((t=>{const n=()=>((e,t)=>{var n,r,o;Gn(e,"margin-top"),Gn(e,"margin-left"),Gn(t,"width"),Gn(t,"height"),Gn(t,"overflow"),Gn(t,"display"),r=e,o=["transform"],Te(n=t)&&Te(r)&&O(o,(e=>{((e,t,n)=>{$n(e,n).each((e=>{$n(t,n).isNone()&&Bn(t,n,e)}))})(n,r,e)})),Gn(t,"transform")})(e,t),r=((e,t)=>{const n=(e,t)=>In(e,t).map(vi).filter((e=>!isNaN(e))).getOr(0),r=Hn(t,"width"),o=Hn(t,"height"),s=n(e,"width"),a=n(e,"height"),i=/^\d+px$/;return{isPx:i.test(r)&&i.test(o),cropWidth:vi(r),cropHeight:vi(o),width:s,height:a}})(e,t);return(yi(r)||bi.test(Hn(t,"transform"))?xi(e,r,t):Promise.resolve()).then(n,n)})).getOrThunk((()=>Promise.resolve()));var wi=["body","p","div","article","aside","figcaption","figure","footer","header","nav","section","ol","ul","li","table","thead","tbody","tfoot","caption","tr","td","th","h1","h2","h3","h4","h5","h6","blockquote","pre","address"];const Si=(e,t)=>D(e,(e=>e.start===t)),Ci=(e,t,n=0)=>N(e,((e,n)=>t(n,e.len).fold(m(e),(t=>({len:t.finish,list:e.list.concat([t])})))),{len:n,list:[]}).list,Ti=(e,t,n)=>0===t.length?e:R(e,(e=>{const r=R(t,(t=>((e,t)=>t>=e.start&&t<=e.finish)(e,t)?[t-e.start]:[]));return r.length>0?((e,t,n)=>((e,t)=>A(e,(e=>({...e,start:e.start+t,finish:e.finish+t}))))(n(e,t),e.start))(e,r,n):[e]})),Ii=(e,t)=>{const n=R(t,(t=>{const n=((e,t)=>{const n=t.term(),r=[];let o=n.exec(e);for(;o;){const s=o.index+t.prefix(o),a=o[0].length-t.prefix(o)-t.suffix(o);r.push({start:s,finish:s+a}),n.lastIndex=s+a,o=n.exec(e)}return r})(e,t.pattern);return A(n,(e=>({...t,...e})))}));return(e=>{const t=(e=>F(e,((e,t)=>e.start-t.start)))(e);return N(t,((e,t)=>{const n=I(e,(e=>t.start>=e.start&&t.finish<=e.finish));return D(e,(e=>t.start===e.start)).fold((()=>n?e:[...e,t]),(n=>t.finish>e[n].finish?[...e.slice(0,n),t]:e))}),[])})(n)},Ai=(e,t,n)=>({element:e,start:t,finish:n}),Oi=Nt([{include:["item"]},{excludeWith:["item"]},{excludeWithout:["item"]}]),Ei={include:Oi.include,excludeWith:Oi.excludeWith,excludeWithout:Oi.excludeWithout,cata:(e,t,n,r)=>e.fold(t,n,r)},Li=Nt([{boundary:["item","universe"]},{empty:["item","universe"]},{text:["item","universe"]},{nonEditable:["item","universe"]}]),Ni=b,_i=x,Di=m(0),Pi=m(1),Ri=e=>({...e,isBoundary:()=>e.fold(_i,Ni,Ni,Ni),toText:()=>e.fold(k.none,k.none,(e=>k.some(e)),k.none),is:t=>e.fold(Ni,Ni,((e,n)=>n.eq(e,t)),Ni),len:()=>e.fold(Di,Pi,((e,t)=>t.property().getText(e).length),Pi)}),Mi={text:h(Ri,Li.text),boundary:h(Ri,Li.boundary),empty:h(Ri,Li.empty),nonEditable:h(Ri,Li.empty),cata:(e,t,n,r,o)=>e.fold(t,n,r,o)},ji=m([]),Fi=(e,t,n)=>{if(e.property().isText(t))return[Mi.text(t,e)];if(e.property().isEmptyTag(t))return[Mi.empty(t,e)];if(e.property().isNonEditable(t))return[];if(e.property().isElement(t)){const r=e.property().children(t),o=e.property().isBoundary(t)?[Mi.boundary(t,e)]:[],s=void 0!==n&&n(t)?[]:R(r,(t=>Fi(e,t,n)));return o.concat(s).concat(o)}return[]},Ui=Fi,Bi=(e,t,n)=>{const r=((e,t)=>{const n=[];let r=[];return O(e,(e=>{const o=t(e);Ei.cata(o,(()=>{r.push(e)}),(()=>{r.length>0&&n.push(r),n.push([e]),r=[]}),(()=>{r.length>0&&n.push(r),r=[]}))})),r.length>0&&n.push(r),n})(R(t,(t=>Ui(e,t,n))),(e=>e.match({boundary:()=>Ei.excludeWithout(e),empty:()=>Ei.excludeWith(e),text:()=>Ei.include(e),nonEditable:()=>Ei.excludeWithout(e)})));return L(r,(e=>e.length>0))},zi=(e,t,n)=>{const r=R(n,(e=>[e.start,e.finish])),o=Ti(t,r,((t,n)=>((e,t,n)=>{const r=e.property().getText(t),o=L(((e,t)=>{if(0===t.length)return[e];const n=N(t,((t,n)=>{if(0===n)return t;const r=e.substring(t.prev,n);return{prev:n,values:t.values.concat([r])}}),{prev:0,values:[]}),r=t[t.length-1];return r<e.length?n.values.concat(e.substring(r)):n.values})(r,n),(e=>e.length>0));if(o.length<=1)return[Ai(t,0,r.length)];e.property().setText(t,o[0]);const s=Ci(o.slice(1),((t,n)=>{const r=e.create().text(t),o=Ai(r,n,n+t.length);return k.some(o)}),o[0].length),a=A(s,(e=>e.element));return e.insert().afterAll(t,a),[Ai(t,0,o[0].length)].concat(s)})(e,t.element,n)));return A(n,(t=>{const n=((e,t,n)=>{const r=Si(e,t),o=Si(e,n);return r.bind((t=>{const r=o.getOr(((e,t)=>e[e.length-1]&&e[e.length-1].finish===t?e.length+1:-1)(e,n));return r>-1?k.some(e.slice(t,r)):k.none()})).getOr([])})(o,t.start,t.finish),r=A(n,(e=>e.element)),s=A(r,e.property().getText).join("");return{elements:r,word:t.word,exact:s}}))},Hi={up:m({selector:Qn,closest:er,predicate:Yn,all:(e,t)=>{const n=c(t)?t:b;let r=e.dom;const o=[];for(;null!==r.parentNode&&void 0!==r.parentNode;){const e=r.parentNode,t=De.fromDom(e);if(o.push(t),!0===n(t))break;r=e}return o}}),down:m({selector:Jt,predicate:Kt}),styles:m({get:Hn,getRaw:$n,set:Bn,remove:Gn}),attrs:m({get:Tn,set:Sn,remove:On,copyTo:(e,t)=>{const n=N(e.dom.attributes,((e,t)=>(e[t.name]=t.value,e)),{});Cn(t,n)}}),insert:m({before:cn,after:un,afterAll:pn,append:hn,appendAll:fn,prepend:dn,wrap:mn}),remove:m({unwrap:yn,remove:vn}),create:m({nu:De.fromTag,clone:e=>De.fromDom(e.dom.cloneNode(!1)),text:De.fromText}),query:m({comparePosition:(e,t)=>e.dom.compareDocumentPosition(t.dom),prevSibling:Ut,nextSibling:Bt}),property:m({children:Ht,name:ke,parent:Ft,document:e=>Mt(e).dom,isText:Ie,isComment:Ce,isElement:Te,isSpecial:e=>{const t=ke(e);return T(["script","noscript","iframe","noframes","noembed","title","style","textarea","xmp"],t)},getLanguage:e=>Te(e)?In(e,"lang"):k.none(),getText:Ks,setText:(e,t)=>qs.set(e,t),isBoundary:e=>!!Te(e)&&("body"===ke(e)||T(wi,ke(e))),isEmptyTag:e=>!!Te(e)&&T(["br","img","hr","input"],ke(e)),isNonEditable:e=>Te(e)&&"false"===Tn(e,"contenteditable")}),eq:At,is:Ot},Wi=/(?:[A-Za-z][A-Za-z\d.+-]{0,14}:\/\/(?:[-.~*+=!&;:'%@?^${}(),\w]+@)?|www\.|[-;:&=+$,.\w]+@)[A-Za-z\d-]+(?:\.[A-Za-z\d-]+)*(?::\d+)?(?:\/(?:[-.~*+=!;:'%@$(),\/\w]*[-~*+=%@$()\/\w])?)?(?:\?(?:[-.~*+=!&;:'%@?^${}(),\/\w]+))?(?:#(?:[-.~*+=!&;:'%@?^${}(),\/\w]+))?/g.source,$i=e=>{return((e,t,n)=>((e,t,n,r)=>{const o=Bi(e,t,r),s=R(o,(t=>{const r=R(t,(e=>e.fold(ji,ji,(e=>[e]),ji))),o=A(r,e.property().getText).join(""),s=Ii(o,n),a=((e,t)=>Ci(t,((t,n)=>{const r=n+e.property().getText(t).length;return k.from(Ai(t,n,r))})))(e,r);return zi(e,a,s)}));return s})(Hi,e,t,void 0))(e,[{word:"__INTERNAL__",pattern:(t=Wi,((e,t,n,r)=>({term:()=>new RegExp(e,r.getOr("g")),prefix:t,suffix:n}))(t,m(0),m(0),k.none()))}]);var t},Vi=e=>!er(e,"a",undefined).isSome(),Gi=(e,t)=>{const n=$i(e);O(n,(e=>{const n=e.exact;if(n.indexOf("@")<0||qi(n)){const r=W(n,"://")?n:`${t.defaultProtocol}://${n}`;((e,t)=>{k.from(e[0]).filter(Vi).map((n=>{const r=De.fromTag("a");return cn(n,r),fn(r,e),Sn(r,"href",t),r}))})(e.elements,r)}}))},qi=e=>{const t=e.indexOf("://");return t>=3&&t<=9},Ki=(e,t)=>{O(e,(e=>{Te(e)&&$n(e,"position").isSome()&&Gn(e,"position")}))},Ji=(e,t)=>{const n=L(e,Ee("li"));if(n.length>0){const t=zt(n[0]),r=De.fromTag("ul");if(cn(e[0],r),t.length>0){const e=De.fromTag("li");hn(r,e),fn(e,t)}fn(r,n)}},Yi=(e,t)=>{const n=Ht(e);O([Gi,Ki,Ji],(e=>{e(n,t)}))},Xi={disabled:()=>({discriminator:"disabled",data:{}}),fromClipboard:e=>({discriminator:"fromClipboard",data:{rtf:e}})},Zi=Pe(Xi),Qi=Xi.disabled,el=Xi.fromClipboard,tl=e=>void 0!==e&&void 0!==e.types&&null!==e.types,nl=(e,t)=>{const n=new RegExp(t,"i");return B(e,(e=>Mn(null!==n.exec(e),{type:e,flavor:t})))};function rl(e){return rl="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},rl(e)}function ol(e,t){return ol=Object.setPrototypeOf||function(e,t){return e.__proto__=t,e},ol(e,t)}function sl(e,t,n){return sl=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(e){return!1}}()?Reflect.construct:function(e,t,n){var r=[null];r.push.apply(r,t);var o=new(Function.bind.apply(e,r));return n&&ol(o,n.prototype),o},sl.apply(null,arguments)}function al(e){return function(e){if(Array.isArray(e))return il(e)}(e)||function(e){if("undefined"!=typeof Symbol&&null!=e[Symbol.iterator]||null!=e["@@iterator"])return Array.from(e)}(e)||function(e,t){if(e){if("string"==typeof e)return il(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);return"Object"===n&&e.constructor&&(n=e.constructor.name),"Map"===n||"Set"===n?Array.from(e):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?il(e,t):void 0}}(e)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function il(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=new Array(t);n<t;n++)r[n]=e[n];return r}var ll=Object.hasOwnProperty,cl=Object.setPrototypeOf,ul=Object.isFrozen,dl=Object.getPrototypeOf,hl=Object.getOwnPropertyDescriptor,ml=Object.freeze,pl=Object.seal,fl=Object.create,gl="undefined"!=typeof Reflect&&Reflect,vl=gl.apply,yl=gl.construct;vl||(vl=function(e,t,n){return e.apply(t,n)}),ml||(ml=function(e){return e}),pl||(pl=function(e){return e}),yl||(yl=function(e,t){return sl(e,al(t))});var bl,xl=Nl(Array.prototype.forEach),kl=Nl(Array.prototype.pop),wl=Nl(Array.prototype.push),Sl=Nl(String.prototype.toLowerCase),Cl=Nl(String.prototype.toString),Tl=Nl(String.prototype.match),Il=Nl(String.prototype.replace),Al=Nl(String.prototype.indexOf),Ol=Nl(String.prototype.trim),El=Nl(RegExp.prototype.test),Ll=(bl=TypeError,function(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];return yl(bl,t)});function Nl(e){return function(t){for(var n=arguments.length,r=new Array(n>1?n-1:0),o=1;o<n;o++)r[o-1]=arguments[o];return vl(e,t,r)}}function _l(e,t,n){var r;n=null!==(r=n)&&void 0!==r?r:Sl,cl&&cl(e,null);for(var o=t.length;o--;){var s=t[o];if("string"==typeof s){var a=n(s);a!==s&&(ul(t)||(t[o]=a),s=a)}e[s]=!0}return e}function Dl(e){var t,n=fl(null);for(t in e)!0===vl(ll,e,[t])&&(n[t]=e[t]);return n}function Pl(e,t){for(;null!==e;){var n=hl(e,t);if(n){if(n.get)return Nl(n.get);if("function"==typeof n.value)return Nl(n.value)}e=dl(e)}return function(e){return console.warn("fallback value for",e),null}}var Rl=ml(["a","abbr","acronym","address","area","article","aside","audio","b","bdi","bdo","big","blink","blockquote","body","br","button","canvas","caption","center","cite","code","col","colgroup","content","data","datalist","dd","decorator","del","details","dfn","dialog","dir","div","dl","dt","element","em","fieldset","figcaption","figure","font","footer","form","h1","h2","h3","h4","h5","h6","head","header","hgroup","hr","html","i","img","input","ins","kbd","label","legend","li","main","map","mark","marquee","menu","menuitem","meter","nav","nobr","ol","optgroup","option","output","p","picture","pre","progress","q","rp","rt","ruby","s","samp","section","select","shadow","small","source","spacer","span","strike","strong","style","sub","summary","sup","table","tbody","td","template","textarea","tfoot","th","thead","time","tr","track","tt","u","ul","var","video","wbr"]),Ml=ml(["svg","a","altglyph","altglyphdef","altglyphitem","animatecolor","animatemotion","animatetransform","circle","clippath","defs","desc","ellipse","filter","font","g","glyph","glyphref","hkern","image","line","lineargradient","marker","mask","metadata","mpath","path","pattern","polygon","polyline","radialgradient","rect","stop","style","switch","symbol","text","textpath","title","tref","tspan","view","vkern"]),jl=ml(["feBlend","feColorMatrix","feComponentTransfer","feComposite","feConvolveMatrix","feDiffuseLighting","feDisplacementMap","feDistantLight","feFlood","feFuncA","feFuncB","feFuncG","feFuncR","feGaussianBlur","feImage","feMerge","feMergeNode","feMorphology","feOffset","fePointLight","feSpecularLighting","feSpotLight","feTile","feTurbulence"]),Fl=ml(["animate","color-profile","cursor","discard","fedropshadow","font-face","font-face-format","font-face-name","font-face-src","font-face-uri","foreignobject","hatch","hatchpath","mesh","meshgradient","meshpatch","meshrow","missing-glyph","script","set","solidcolor","unknown","use"]),Ul=ml(["math","menclose","merror","mfenced","mfrac","mglyph","mi","mlabeledtr","mmultiscripts","mn","mo","mover","mpadded","mphantom","mroot","mrow","ms","mspace","msqrt","mstyle","msub","msup","msubsup","mtable","mtd","mtext","mtr","munder","munderover"]),Bl=ml(["maction","maligngroup","malignmark","mlongdiv","mscarries","mscarry","msgroup","mstack","msline","msrow","semantics","annotation","annotation-xml","mprescripts","none"]),zl=ml(["#text"]),Hl=ml(["accept","action","align","alt","autocapitalize","autocomplete","autopictureinpicture","autoplay","background","bgcolor","border","capture","cellpadding","cellspacing","checked","cite","class","clear","color","cols","colspan","controls","controlslist","coords","crossorigin","datetime","decoding","default","dir","disabled","disablepictureinpicture","disableremoteplayback","download","draggable","enctype","enterkeyhint","face","for","headers","height","hidden","high","href","hreflang","id","inputmode","integrity","ismap","kind","label","lang","list","loading","loop","low","max","maxlength","media","method","min","minlength","multiple","muted","name","nonce","noshade","novalidate","nowrap","open","optimum","pattern","placeholder","playsinline","poster","preload","pubdate","radiogroup","readonly","rel","required","rev","reversed","role","rows","rowspan","spellcheck","scope","selected","shape","size","sizes","span","srclang","start","src","srcset","step","style","summary","tabindex","title","translate","type","usemap","valign","value","width","xmlns","slot"]),Wl=ml(["accent-height","accumulate","additive","alignment-baseline","ascent","attributename","attributetype","azimuth","basefrequency","baseline-shift","begin","bias","by","class","clip","clippathunits","clip-path","clip-rule","color","color-interpolation","color-interpolation-filters","color-profile","color-rendering","cx","cy","d","dx","dy","diffuseconstant","direction","display","divisor","dur","edgemode","elevation","end","fill","fill-opacity","fill-rule","filter","filterunits","flood-color","flood-opacity","font-family","font-size","font-size-adjust","font-stretch","font-style","font-variant","font-weight","fx","fy","g1","g2","glyph-name","glyphref","gradientunits","gradienttransform","height","href","id","image-rendering","in","in2","k","k1","k2","k3","k4","kerning","keypoints","keysplines","keytimes","lang","lengthadjust","letter-spacing","kernelmatrix","kernelunitlength","lighting-color","local","marker-end","marker-mid","marker-start","markerheight","markerunits","markerwidth","maskcontentunits","maskunits","max","mask","media","method","mode","min","name","numoctaves","offset","operator","opacity","order","orient","orientation","origin","overflow","paint-order","path","pathlength","patterncontentunits","patterntransform","patternunits","points","preservealpha","preserveaspectratio","primitiveunits","r","rx","ry","radius","refx","refy","repeatcount","repeatdur","restart","result","rotate","scale","seed","shape-rendering","specularconstant","specularexponent","spreadmethod","startoffset","stddeviation","stitchtiles","stop-color","stop-opacity","stroke-dasharray","stroke-dashoffset","stroke-linecap","stroke-linejoin","stroke-miterlimit","stroke-opacity","stroke","stroke-width","style","surfacescale","systemlanguage","tabindex","targetx","targety","transform","transform-origin","text-anchor","text-decoration","text-rendering","textlength","type","u1","u2","unicode","values","viewbox","visibility","version","vert-adv-y","vert-origin-x","vert-origin-y","width","word-spacing","wrap","writing-mode","xchannelselector","ychannelselector","x","x1","x2","xmlns","y","y1","y2","z","zoomandpan"]),$l=ml(["accent","accentunder","align","bevelled","close","columnsalign","columnlines","columnspan","denomalign","depth","dir","display","displaystyle","encoding","fence","frame","height","href","id","largeop","length","linethickness","lspace","lquote","mathbackground","mathcolor","mathsize","mathvariant","maxsize","minsize","movablelimits","notation","numalign","open","rowalign","rowlines","rowspacing","rowspan","rspace","rquote","scriptlevel","scriptminsize","scriptsizemultiplier","selection","separator","separators","stretchy","subscriptshift","supscriptshift","symmetric","voffset","width","xmlns"]),Vl=ml(["xlink:href","xml:id","xlink:title","xml:space","xmlns:xlink"]),Gl=pl(/\{\{[\w\W]*|[\w\W]*\}\}/gm),ql=pl(/<%[\w\W]*|[\w\W]*%>/gm),Kl=pl(/\${[\w\W]*}/gm),Jl=pl(/^data-[\-\w.\u00B7-\uFFFF]/),Yl=pl(/^aria-[\-\w]+$/),Xl=pl(/^(?:(?:(?:f|ht)tps?|mailto|tel|callto|cid|xmpp):|[^a-z]|[a-z+.\-]+(?:[^a-z+.\-:]|$))/i),Zl=pl(/^(?:\w+script|data):/i),Ql=pl(/[\u0000-\u0020\u00A0\u1680\u180E\u2000-\u2029\u205F\u3000]/g),ec=pl(/^html$/i),tc=function(){return"undefined"==typeof window?null:window},nc=function e(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:tc(),n=function(t){return e(t)};if(n.version="2.4.7",n.removed=[],!t||!t.document||9!==t.document.nodeType)return n.isSupported=!1,n;var r=t.document,o=t.document,s=t.DocumentFragment,a=t.HTMLTemplateElement,i=t.Node,l=t.Element,c=t.NodeFilter,u=t.NamedNodeMap,d=void 0===u?t.NamedNodeMap||t.MozNamedAttrMap:u,h=t.HTMLFormElement,m=t.DOMParser,p=t.trustedTypes,f=l.prototype,g=Pl(f,"cloneNode"),v=Pl(f,"nextSibling"),y=Pl(f,"childNodes"),b=Pl(f,"parentNode");if("function"==typeof a){var x=o.createElement("template");x.content&&x.content.ownerDocument&&(o=x.content.ownerDocument)}var k=function(e,t){if("object"!==rl(e)||"function"!=typeof e.createPolicy)return null;var n=null,r="data-tt-policy-suffix";t.currentScript&&t.currentScript.hasAttribute(r)&&(n=t.currentScript.getAttribute(r));var o="dompurify"+(n?"#"+n:"");try{return e.createPolicy(o,{createHTML:function(e){return e},createScriptURL:function(e){return e}})}catch(e){return console.warn("TrustedTypes policy "+o+" could not be created."),null}}(p,r),w=k?k.createHTML(""):"",S=o,C=S.implementation,T=S.createNodeIterator,I=S.createDocumentFragment,A=S.getElementsByTagName,O=r.importNode,E={};try{E=Dl(o).documentMode?o.documentMode:{}}catch(e){}var L={};n.isSupported="function"==typeof b&&C&&void 0!==C.createHTMLDocument&&9!==E;var N,_,D=Gl,P=ql,R=Kl,M=Jl,j=Yl,F=Zl,U=Ql,B=Xl,z=null,H=_l({},[].concat(al(Rl),al(Ml),al(jl),al(Ul),al(zl))),W=null,$=_l({},[].concat(al(Hl),al(Wl),al($l),al(Vl))),V=Object.seal(Object.create(null,{tagNameCheck:{writable:!0,configurable:!1,enumerable:!0,value:null},attributeNameCheck:{writable:!0,configurable:!1,enumerable:!0,value:null},allowCustomizedBuiltInElements:{writable:!0,configurable:!1,enumerable:!0,value:!1}})),G=null,q=null,K=!0,J=!0,Y=!1,X=!0,Z=!1,Q=!1,ee=!1,te=!1,ne=!1,re=!1,oe=!1,se=!0,ae=!1,ie=!0,le=!1,ce={},ue=null,de=_l({},["annotation-xml","audio","colgroup","desc","foreignobject","head","iframe","math","mi","mn","mo","ms","mtext","noembed","noframes","noscript","plaintext","script","style","svg","template","thead","title","video","xmp"]),he=null,me=_l({},["audio","video","img","source","image","track"]),pe=null,fe=_l({},["alt","class","for","id","label","name","pattern","placeholder","role","summary","title","value","style","xmlns"]),ge="http://www.w3.org/1998/Math/MathML",ve="http://www.w3.org/2000/svg",ye="http://www.w3.org/1999/xhtml",be=ye,xe=!1,ke=null,we=_l({},[ge,ve,ye],Cl),Se=["application/xhtml+xml","text/html"],Ce=null,Te=o.createElement("form"),Ie=function(e){return e instanceof RegExp||e instanceof Function},Ae=function(e){Ce&&Ce===e||(e&&"object"===rl(e)||(e={}),e=Dl(e),N=N=-1===Se.indexOf(e.PARSER_MEDIA_TYPE)?"text/html":e.PARSER_MEDIA_TYPE,_="application/xhtml+xml"===N?Cl:Sl,z="ALLOWED_TAGS"in e?_l({},e.ALLOWED_TAGS,_):H,W="ALLOWED_ATTR"in e?_l({},e.ALLOWED_ATTR,_):$,ke="ALLOWED_NAMESPACES"in e?_l({},e.ALLOWED_NAMESPACES,Cl):we,pe="ADD_URI_SAFE_ATTR"in e?_l(Dl(fe),e.ADD_URI_SAFE_ATTR,_):fe,he="ADD_DATA_URI_TAGS"in e?_l(Dl(me),e.ADD_DATA_URI_TAGS,_):me,ue="FORBID_CONTENTS"in e?_l({},e.FORBID_CONTENTS,_):de,G="FORBID_TAGS"in e?_l({},e.FORBID_TAGS,_):{},q="FORBID_ATTR"in e?_l({},e.FORBID_ATTR,_):{},ce="USE_PROFILES"in e&&e.USE_PROFILES,K=!1!==e.ALLOW_ARIA_ATTR,J=!1!==e.ALLOW_DATA_ATTR,Y=e.ALLOW_UNKNOWN_PROTOCOLS||!1,X=!1!==e.ALLOW_SELF_CLOSE_IN_ATTR,Z=e.SAFE_FOR_TEMPLATES||!1,Q=e.WHOLE_DOCUMENT||!1,ne=e.RETURN_DOM||!1,re=e.RETURN_DOM_FRAGMENT||!1,oe=e.RETURN_TRUSTED_TYPE||!1,te=e.FORCE_BODY||!1,se=!1!==e.SANITIZE_DOM,ae=e.SANITIZE_NAMED_PROPS||!1,ie=!1!==e.KEEP_CONTENT,le=e.IN_PLACE||!1,B=e.ALLOWED_URI_REGEXP||B,be=e.NAMESPACE||ye,V=e.CUSTOM_ELEMENT_HANDLING||{},e.CUSTOM_ELEMENT_HANDLING&&Ie(e.CUSTOM_ELEMENT_HANDLING.tagNameCheck)&&(V.tagNameCheck=e.CUSTOM_ELEMENT_HANDLING.tagNameCheck),e.CUSTOM_ELEMENT_HANDLING&&Ie(e.CUSTOM_ELEMENT_HANDLING.attributeNameCheck)&&(V.attributeNameCheck=e.CUSTOM_ELEMENT_HANDLING.attributeNameCheck),e.CUSTOM_ELEMENT_HANDLING&&"boolean"==typeof e.CUSTOM_ELEMENT_HANDLING.allowCustomizedBuiltInElements&&(V.allowCustomizedBuiltInElements=e.CUSTOM_ELEMENT_HANDLING.allowCustomizedBuiltInElements),Z&&(J=!1),re&&(ne=!0),ce&&(z=_l({},al(zl)),W=[],!0===ce.html&&(_l(z,Rl),_l(W,Hl)),!0===ce.svg&&(_l(z,Ml),_l(W,Wl),_l(W,Vl)),!0===ce.svgFilters&&(_l(z,jl),_l(W,Wl),_l(W,Vl)),!0===ce.mathMl&&(_l(z,Ul),_l(W,$l),_l(W,Vl))),e.ADD_TAGS&&(z===H&&(z=Dl(z)),_l(z,e.ADD_TAGS,_)),e.ADD_ATTR&&(W===$&&(W=Dl(W)),_l(W,e.ADD_ATTR,_)),e.ADD_URI_SAFE_ATTR&&_l(pe,e.ADD_URI_SAFE_ATTR,_),e.FORBID_CONTENTS&&(ue===de&&(ue=Dl(ue)),_l(ue,e.FORBID_CONTENTS,_)),ie&&(z["#text"]=!0),Q&&_l(z,["html","head","body"]),z.table&&(_l(z,["tbody"]),delete G.tbody),ml&&ml(e),Ce=e)},Oe=_l({},["mi","mo","mn","ms","mtext"]),Ee=_l({},["foreignobject","desc","title","annotation-xml"]),Le=_l({},["title","style","font","a","script"]),Ne=_l({},Ml);_l(Ne,jl),_l(Ne,Fl);var _e=_l({},Ul);_l(_e,Bl);var De=function(e){wl(n.removed,{element:e});try{e.parentNode.removeChild(e)}catch(t){try{e.outerHTML=w}catch(t){e.remove()}}},Pe=function(e,t){try{wl(n.removed,{attribute:t.getAttributeNode(e),from:t})}catch(e){wl(n.removed,{attribute:null,from:t})}if(t.removeAttribute(e),"is"===e&&!W[e])if(ne||re)try{De(t)}catch(e){}else try{t.setAttribute(e,"")}catch(e){}},Re=function(e){var t,n;if(te)e="<remove></remove>"+e;else{var r=Tl(e,/^[\r\n\t ]+/);n=r&&r[0]}"application/xhtml+xml"===N&&be===ye&&(e='<html xmlns="http://www.w3.org/1999/xhtml"><head></head><body>'+e+"</body></html>");var s=k?k.createHTML(e):e;if(be===ye)try{t=(new m).parseFromString(s,N)}catch(e){}if(!t||!t.documentElement){t=C.createDocument(be,"template",null);try{t.documentElement.innerHTML=xe?w:s}catch(e){}}var a=t.body||t.documentElement;return e&&n&&a.insertBefore(o.createTextNode(n),a.childNodes[0]||null),be===ye?A.call(t,Q?"html":"body")[0]:Q?t.documentElement:a},Me=function(e){return T.call(e.ownerDocument||e,e,c.SHOW_ELEMENT|c.SHOW_COMMENT|c.SHOW_TEXT,null,!1)},je=function(e){return"object"===rl(i)?e instanceof i:e&&"object"===rl(e)&&"number"==typeof e.nodeType&&"string"==typeof e.nodeName},Fe=function(e,t,r){L[e]&&xl(L[e],(function(e){e.call(n,t,r,Ce)}))},Ue=function(e){var t,r;if(Fe("beforeSanitizeElements",e,null),(r=e)instanceof h&&("string"!=typeof r.nodeName||"string"!=typeof r.textContent||"function"!=typeof r.removeChild||!(r.attributes instanceof d)||"function"!=typeof r.removeAttribute||"function"!=typeof r.setAttribute||"string"!=typeof r.namespaceURI||"function"!=typeof r.insertBefore||"function"!=typeof r.hasChildNodes))return De(e),!0;if(El(/[\u0080-\uFFFF]/,e.nodeName))return De(e),!0;var o=_(e.nodeName);if(Fe("uponSanitizeElement",e,{tagName:o,allowedTags:z}),e.hasChildNodes()&&!je(e.firstElementChild)&&(!je(e.content)||!je(e.content.firstElementChild))&&El(/<[/\w]/g,e.innerHTML)&&El(/<[/\w]/g,e.textContent))return De(e),!0;if("select"===o&&El(/<template/i,e.innerHTML))return De(e),!0;if(!z[o]||G[o]){if(!G[o]&&ze(o)){if(V.tagNameCheck instanceof RegExp&&El(V.tagNameCheck,o))return!1;if(V.tagNameCheck instanceof Function&&V.tagNameCheck(o))return!1}if(ie&&!ue[o]){var s=b(e)||e.parentNode,a=y(e)||e.childNodes;if(a&&s)for(var i=a.length-1;i>=0;--i)s.insertBefore(g(a[i],!0),v(e))}return De(e),!0}return e instanceof l&&!function(e){var t=b(e);t&&t.tagName||(t={namespaceURI:be,tagName:"template"});var n=Sl(e.tagName),r=Sl(t.tagName);return!!ke[e.namespaceURI]&&(e.namespaceURI===ve?t.namespaceURI===ye?"svg"===n:t.namespaceURI===ge?"svg"===n&&("annotation-xml"===r||Oe[r]):Boolean(Ne[n]):e.namespaceURI===ge?t.namespaceURI===ye?"math"===n:t.namespaceURI===ve?"math"===n&&Ee[r]:Boolean(_e[n]):e.namespaceURI===ye?!(t.namespaceURI===ve&&!Ee[r])&&!(t.namespaceURI===ge&&!Oe[r])&&!_e[n]&&(Le[n]||!Ne[n]):!("application/xhtml+xml"!==N||!ke[e.namespaceURI]))}(e)?(De(e),!0):"noscript"!==o&&"noembed"!==o&&"noframes"!==o||!El(/<\/no(script|embed|frames)/i,e.innerHTML)?(Z&&3===e.nodeType&&(t=e.textContent,t=Il(t,D," "),t=Il(t,P," "),t=Il(t,R," "),e.textContent!==t&&(wl(n.removed,{element:e.cloneNode()}),e.textContent=t)),Fe("afterSanitizeElements",e,null),!1):(De(e),!0)},Be=function(e,t,n){if(se&&("id"===t||"name"===t)&&(n in o||n in Te))return!1;if(J&&!q[t]&&El(M,t));else if(K&&El(j,t));else if(!W[t]||q[t]){if(!(ze(e)&&(V.tagNameCheck instanceof RegExp&&El(V.tagNameCheck,e)||V.tagNameCheck instanceof Function&&V.tagNameCheck(e))&&(V.attributeNameCheck instanceof RegExp&&El(V.attributeNameCheck,t)||V.attributeNameCheck instanceof Function&&V.attributeNameCheck(t))||"is"===t&&V.allowCustomizedBuiltInElements&&(V.tagNameCheck instanceof RegExp&&El(V.tagNameCheck,n)||V.tagNameCheck instanceof Function&&V.tagNameCheck(n))))return!1}else if(pe[t]);else if(El(B,Il(n,U,"")));else if("src"!==t&&"xlink:href"!==t&&"href"!==t||"script"===e||0!==Al(n,"data:")||!he[e])if(Y&&!El(F,Il(n,U,"")));else if(n)return!1;return!0},ze=function(e){return e.indexOf("-")>0},He=function(e){var t,r,o,s;Fe("beforeSanitizeAttributes",e,null);var a=e.attributes;if(a){var i={attrName:"",attrValue:"",keepAttr:!0,allowedAttributes:W};for(s=a.length;s--;){var l=t=a[s],c=l.name,u=l.namespaceURI;if(r="value"===c?t.value:Ol(t.value),o=_(c),i.attrName=o,i.attrValue=r,i.keepAttr=!0,i.forceKeepAttr=void 0,Fe("uponSanitizeAttribute",e,i),r=i.attrValue,!i.forceKeepAttr&&(Pe(c,e),i.keepAttr))if(X||!El(/\/>/i,r)){Z&&(r=Il(r,D," "),r=Il(r,P," "),r=Il(r,R," "));var d=_(e.nodeName);if(Be(d,o,r)){if(!ae||"id"!==o&&"name"!==o||(Pe(c,e),r="user-content-"+r),k&&"object"===rl(p)&&"function"==typeof p.getAttributeType)if(u);else switch(p.getAttributeType(d,o)){case"TrustedHTML":r=k.createHTML(r);break;case"TrustedScriptURL":r=k.createScriptURL(r)}try{u?e.setAttributeNS(u,c,r):e.setAttribute(c,r),kl(n.removed)}catch(e){}}}else Pe(c,e)}Fe("afterSanitizeAttributes",e,null)}},We=function e(t){var n,r=Me(t);for(Fe("beforeSanitizeShadowDOM",t,null);n=r.nextNode();)Fe("uponSanitizeShadowNode",n,null),Ue(n)||(n.content instanceof s&&e(n.content),He(n));Fe("afterSanitizeShadowDOM",t,null)};return n.sanitize=function(e){var o,a,l,c,u,d=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};if((xe=!e)&&(e="\x3c!--\x3e"),"string"!=typeof e&&!je(e)){if("function"!=typeof e.toString)throw Ll("toString is not a function");if("string"!=typeof(e=e.toString()))throw Ll("dirty is not a string, aborting")}if(!n.isSupported){if("object"===rl(t.toStaticHTML)||"function"==typeof t.toStaticHTML){if("string"==typeof e)return t.toStaticHTML(e);if(je(e))return t.toStaticHTML(e.outerHTML)}return e}if(ee||Ae(d),n.removed=[],"string"==typeof e&&(le=!1),le){if(e.nodeName){var h=_(e.nodeName);if(!z[h]||G[h])throw Ll("root node is forbidden and cannot be sanitized in-place")}}else if(e instanceof i)1===(a=(o=Re("\x3c!----\x3e")).ownerDocument.importNode(e,!0)).nodeType&&"BODY"===a.nodeName||"HTML"===a.nodeName?o=a:o.appendChild(a);else{if(!ne&&!Z&&!Q&&-1===e.indexOf("<"))return k&&oe?k.createHTML(e):e;if(!(o=Re(e)))return ne?null:oe?w:""}o&&te&&De(o.firstChild);for(var m=Me(le?e:o);l=m.nextNode();)3===l.nodeType&&l===c||Ue(l)||(l.content instanceof s&&We(l.content),He(l),c=l);if(c=null,le)return e;if(ne){if(re)for(u=I.call(o.ownerDocument);o.firstChild;)u.appendChild(o.firstChild);else u=o;return(W.shadowroot||W.shadowrootmod)&&(u=O.call(r,u,!0)),u}var p=Q?o.outerHTML:o.innerHTML;return Q&&z["!doctype"]&&o.ownerDocument&&o.ownerDocument.doctype&&o.ownerDocument.doctype.name&&El(ec,o.ownerDocument.doctype.name)&&(p="<!DOCTYPE "+o.ownerDocument.doctype.name+">\n"+p),Z&&(p=Il(p,D," "),p=Il(p,P," "),p=Il(p,R," ")),k&&oe?k.createHTML(p):p},n.setConfig=function(e){Ae(e),ee=!0},n.clearConfig=function(){Ce=null,ee=!1},n.isValidAttribute=function(e,t,n){Ce||Ae({});var r=_(e),o=_(t);return Be(r,o,n)},n.addHook=function(e,t){"function"==typeof t&&(L[e]=L[e]||[],wl(L[e],t))},n.removeHook=function(e){if(L[e])return kl(L[e])},n.removeHooks=function(e){L[e]&&(L[e]=[])},n.removeAllHooks=function(){L={}},n}();const rc=["script","svg"],oc=e=>-1!==e.lastIndexOf("</html>"),sc=e=>({sanitizeHtml:(t,n)=>e(t)?t:((e,t)=>{nc.addHook("uponSanitizeElement",((e,t)=>{T(rc,t.tagName)||ze(t.allowedTags,t.tagName)||(t.allowedTags[t.tagName]=!0)})),nc.addHook("uponSanitizeAttribute",((e,t)=>{0===t.attrName.indexOf("on")||ze(t.allowedAttributes,t.attrName)||(t.allowedAttributes[t.attrName]=!0),t.attrValue&&-1!==t.attrValue.indexOf("\n")&&(t.attrValue=t.attrValue.replace(/\r?\n/g,""))}));const n=(e=>$(G(e),"<!"))(e),r=n?`<body>${e}</body>`:(e=>e.replace(/^[\S\s]*?(<!DOCTYPE|<html)/i,"$1"))(e),o=nc.sanitize(r,{ALLOW_UNKNOWN_PROTOCOLS:!0,FORBID_TAGS:rc,WHOLE_DOCUMENT:oc(e)});return nc.removeHook("uponSanitizeElement"),nc.removeHook("uponSanitizeAttribute"),n?(s=H(o,"<body>"),V(s,"</body>")?((e,t)=>e.substring(0,e.length-7))(s):s):o;var s})(t),sanitizeText:p}),ac={sanitizeHtml:p,sanitizeText:p},ic=e=>t=>({discriminator:e,data:t}),lc=e=>t=>t.discriminator===e?k.some(t.data):k.none(),cc=ic("event"),uc=ic("html"),dc=ic("images"),hc=ic("word"),mc=ic("text"),pc=ic("void"),fc=lc("html"),gc=lc("images"),vc=lc("word"),yc=lc("text"),bc=["^image/","file"],xc=e=>(e=>W(e,"<html")&&(W(e,'xmlns:o="urn:schemas-microsoft-com:office:office"')||W(e,'xmlns:x="urn:schemas-microsoft-com:office:excel"')))(e)||(e=>W(e,'meta name="ProgId" content="Word.Document"'))(e),kc=e=>W(e,'id="docs-internal-guid-'),wc=e=>e.length>0,Sc=(e,t)=>nl(e.types,t).map((t=>e.getData(t.type))).filter(wc),Cc=e=>Sc(e,"html"),Tc=e=>Cc(e).filter(kc),Ic=e=>k.from(e.clipboardData).filter(tl),Ac=e=>{const t=De.fromTag("div"),n=((e,t)=>To(e,t,[Ta]))(Rt(t),e);return((e,t)=>{const n=Rt(e).dom,r=De.fromDom(n.createDocumentFragment()),o=Kn(t,n);fn(r,o),gn(e),hn(e,r)})(t,n),uc({container:t})},Oc={native:"Outside of Textbox.io pasting HTML5 API (could be internal)",fallback:"Outside of Textbox.io pasting offscreen (could be internal)",msoffice:"Word Import pasting",googledocs:"Google Docs pasting",image:"Image pasting",plaintext:"Only plain text is available to paste",text:"Plain text pasting",none:"There is no valid way to paste",discard:"There is no valid way to paste, discarding content"},Ec={getLabelForApi:e=>{const t=Pe(Oc);return _(t,(t=>Oc[t]===e)).fold(m("unknown"),(e=>{switch(e){case"native":case"fallback":return"html";case"none":case"discard":return"invalid";default:return e}}))},...Oc},Lc=e=>A(e,(e=>e.asset)),Nc=(e,t,n)=>{const r=Ct({cancel:St([]),error:St(["message"]),insert:St(["elements","assets","correlated","isInternal","source","mode"]),block:St(["state"])});let o=!1;r.registry.block.bind((e=>o=e.state));const s=(n,o)=>{r.trigger.block(!0);const s=((e,t,n)=>{const r=((e,t)=>B(e,(e=>e.getAvailable(t).map((t=>Kr(e.steps,t,e.label,e.capture()))))))(e,n);return r.getOrThunk((()=>{const e=t.getAvailable(n);return Kr(t.steps,e,t.label,t.capture())}))})(e,t,n);s.capture&&o();const a=((e,t)=>{const n={response:Fr([],[]),bundle:Wr({})},r=N(e,((e,n)=>qr(e)?e.then((e=>Yr(e,t,n))):Yr(e,t,n)),n);return qr(r)?r:Promise.resolve(r)})(s.steps,s.input),i=Ec.getLabelForApi(s.label);a.then((e=>{const t=e.bundle.isInternal.getOr(!1),n=e.bundle.officeStyles.fold(m("auto"),(e=>e?"merge":"clean"));r.trigger.block(!1),Mr(e.response,(e=>{r.trigger.error(e)}),((e,o)=>{r.trigger.insert(e,Lc(o),o,t,i,n)}),(()=>{r.trigger.cancel()}),((e,o,s)=>{r.trigger.insert(e,Lc(o),o,t,i,n),r.trigger.error(s)}))}))};return{paste:e=>{const t=jt(De.fromDom(e.target));ln(t.dom).each((t=>{if(!Pn(t.start,sr())){const t=((e,t=ac)=>{const n=e=>{return void 0===e.items?k.none():(t=bc,n=e.types,B(t,(e=>nl(n,e)))).map((t=>{const n=[];for(let t=0;t<e.items.length;t++)n.push(e.items[t]);return dc({images:n})}));var t,n},r=e=>B(e.types,(n=>"text/plain"===n?k.some(e.getData(n)).map((e=>mc({text:t.sanitizeText(e)}))):k.none()));return{getWordData:()=>Ic(e).bind((e=>(e=>Cc(e).filter(xc))(e).map((t=>{const n=(e=>Sc(e,"rtf"))(e);return hc({html:t,rtf:n.fold((()=>Qi()),(e=>el(e)))})})))),getGoogleDocsData:()=>Ic(e).bind(Tc).map((e=>t.sanitizeHtml(e,"googledocs"))).map(Ac),getImage:()=>Ic(e).bind(n),getText:()=>Ic(e).bind(r),getHtml:()=>Ic(e).bind(Cc).map(t.sanitizeHtml).map(Ac),getOnlyText:()=>Ic(e).bind((e=>{return 1===(t=e.types).length&&"text/plain"===t[0]?r(e):k.none();var t})),getNative:()=>cc({nativeEvent:e}),getVoid:()=>pc({})}})(e,n);s(t,(()=>{e.preventDefault()}))}}))},pasteCustom:(e,t=d)=>{s(e,t)},isBlocked:()=>o,destroy:d,events:r.registry}},_c=e=>oi(e);var Dc={cata:(e,t,n)=>e.fold(t,n),...Nt([{blob:["id","imageresult","objurl"]},{url:["id","url","raw"]}])};const Pc=e=>{const t=URL.createObjectURL(e);return Rc(e,t)},Rc=(e,t)=>_c(e).then((n=>{const r=mi(e,n),o=kn("image");return Dc.blob(o,r,t)})),Mc=e=>Promise.all(A(e,Pc)),jc=(e,t)=>({asset:e,image:t}),Fc=(e,t)=>Dc.cata(e,((e,n,r)=>(Sn(t,"src",r),!0)),b),Uc=(e,t)=>{const n=[];return O(e,((e,r)=>{const o=t[r];Fc(e,o)&&n.push(jc(e,o))})),n},Bc=(e,t)=>({asyncAsset:e.then(Ir.value,Ir.error),image:t}),zc=e=>{const t=De.fromTag("div");return fn(t,e),Jt(t,"img[src]")},Hc=e=>0===e.indexOf("data:")&&e.indexOf("base64")>-1,Wc=e=>0===e.indexOf("blob:"),$c=e=>In(e,"src").exists((e=>Hc(e)||Wc(e))),Vc=e=>R(zc(e),(e=>{const t=In(e,"src").getOr("");return Hc(t)?((e,t)=>{return(n=t,ei(n)).map((t=>Bc(Pc(t),e)));var n})(e,t).toArray():Wc(t)?((e,t)=>{return(n=t,k.from(0===(r=n).indexOf("blob:")?Qa(r):0===r.indexOf("data:")?ti(r):null)).map((t=>{const n=t.then(Pc);return Bc(n,e)}));var n,r})(e,t).toArray():[]})),Gc=e=>{const t=L(e,(e=>!Ee("img")(e)||!$c(e)));return Br(t,[],"errors.local.images.disallowed")};var qc=e=>(t,n)=>{const r=()=>Promise.resolve(n),o=(t,o)=>!1===e.allowLocalImages?(e=>{const t=L(zc(e),$c);return O(t,vn),Promise.resolve({response:t.length>0?Gc(e):n.response,bundle:n.bundle})})(t):0===o.length?(e=>{const t=Vc(e),r=Promise.all(A(t,(e=>e.asyncAsset))),o=A(t,(e=>e.image));return r.then((t=>{const r=(e=>{const t=[],n=[];return O(e,(e=>{e.fold((e=>{t.push(e)}),(e=>{n.push(e)}))})),{errors:t,values:n}})(t),s=Uc(r.values,o);return{response:r.errors.length>0?Br(e,s,"errors.imageimport.failed"):Fr(e,s),bundle:n.bundle}}))})(t):r();return Mr(n.response,Vr,o,r,o)};const Kc=(e,t,n=!1)=>fetch(e,{credentials:n?"include":"same-origin",headers:t}).then((async e=>{const t=await e.blob();return{ok:e.ok,status:e.status,blob:t}}),(()=>({ok:!1,status:0}))),Jc=[{code:404,message:"Could not find Image Proxy"},{code:403,message:"Rejected request"},{code:0,message:"Incorrect Image Proxy URL"}],Yc=[{type:"not_found",message:"Failed to load image."},{type:"key_missing",message:"The request did not include an api key."},{type:"key_not_found",message:"The provided api key could not be found."},{type:"domain_not_trusted",message:"The api key is not valid for the request origins."}],Xc=e=>{const t=(e=>{const t=_(Jc,(t=>e===t.code)).fold(m("Unknown ImageProxy error"),(e=>e.message));return"ImageProxy HTTP error: "+t})(e);return Promise.reject(t)},Zc=e=>_(Yc,(t=>t.type===e)).fold(m("Unknown service error"),(e=>e.message)),Qc=e=>(e=>new Promise(((t,n)=>{const r=new FileReader;r.onload=()=>{t(r.result)},r.onerror=e=>{n(e)},r.readAsText(e)})))(e).then((e=>{const t=(e=>{const t=(e=>{try{return k.some(JSON.parse(e))}catch(e){return k.none()}})(e),n=t.bind((e=>((e,t)=>{const n=N(["error","type"],((e,t)=>l(e)?e[t]:void 0),e);return k.from(n)})(e).map(Zc))).getOr("Invalid JSON in service error message");return"ImageProxy Service error: "+n})(e);return Promise.reject(t)})),eu=(e,t,n=!0)=>t?((e,t)=>{const n={"Content-Type":"application/json;charset=UTF-8","tiny-api-key":t};return Kc(((e,t)=>{const n=-1===e.indexOf("?")?"?":"&";return/[?&]apiKey=/.test(e)?e:e+n+"apiKey="+encodeURIComponent(t)})(e,t),n).then((e=>{return e.ok?Promise.resolve(e.blob):((e,t)=>"application/json"===(null==t?void 0:t.type)&&(400===e||403===e||404===e||500===e))(t=e.status,n=e.blob)?Qc(n):Xc(t);var t,n}))})(e,t):((e,t)=>Kc(e,{},t).then((e=>e.ok?Promise.resolve(e.blob):Xc(e.status))))(e,n),tu=/^(?:([^:\/?#]+):)?(?:\/\/((?:(([^:@\/]*)(?::([^:@\/]*))?)?@)?([^:\/?#]*)(?::(\d*))?))?((((?:[^?#\/]*\/)*)([^?#]*))(?:\?([^#]*))?(?:#(.*))?)/,nu=/^(?:(?![^:@\/]+:[^:@\/]*@)([^:\/?#.]+):)?(?:\/\/)?((?:(([^:@\/]*)(?::([^:@\/]*))?)?@)?([^:\/?#]*)(?::(\d*))?)(((\/(?:[^?#](?![^?#\/]*\.[^?#\/.]+(?:[?#]|$)))*\/?)?([^?#\/]*))(?:\?([^#]*))?(?:#(.*))?)/,ru=/(?:^|&)([^&=]+)=?([^&]*)/g,ou=e=>{const t={};for(;;){const n=ru.exec(e);if(null===n)return t;t[n[1]]=n[2]}},su=(e,t)=>{const n=()=>Promise.resolve({response:t.response,bundle:t.bundle}),r=e=>_c(e).then((t=>{const n=kn("image"),r=mi(e,t),o=URL.createObjectURL(e);return Dc.blob(n,r,o)})),o=(e,t)=>Dc.url(kn("image"),t,e),s=(e,n)=>{let s=!1;const a=R(e,(e=>Jt(e,"img")));return Promise.all(A(a,(e=>{const t=e.dom.src;return(e=>{const t=((e,t={})=>{var n;return((e,t)=>{const n=(t?tu:nu).exec(e),r=j(["source","protocol","authority","userInfo","user","password","host","port","relative","path","directory","file","query","anchor"],((e,t)=>{var r;return null!==(r=n[t])&&void 0!==r?r:""}));return{...r,queryKey:ou(r.query)}})(e,null!==(n=t.strictMode)&&void 0!==n&&n)})(e);return W(t.host,"google")&&!$(t.path,"/drawings/")})(t)?eu(e.dom.src,void 0,!0).then(r,(()=>(s=!0,o(e,t)))):o(e,t)}))).then((r=>{const o=n.concat(Uc(r,a));return{response:s?Br(e,o,"errors.imageimport.failed"):Fr(e,o),bundle:t.bundle}}),(()=>({response:jr("errors.imageimport.invalid"),bundle:t.bundle})))};return Mr(t.response,n,s,n,s)},au=e=>{const t=cu(e);return t&&iu(e)||!t&&lu(e)},iu=e=>e.officeStyles.getOr(!0),lu=e=>e.htmlStyles.getOr(!1),cu=e=>e.isWord.getOr(!1),uu=e=>e.isInternal.getOr(!1),du=e=>cu(e)?xs.Word:(e=>e.isGoogleDocs.getOr(!1))(e)?xs.GoogleDocs:xs.Html,hu=(e,t)=>{const n=((e,t)=>{const n=t.translations,r=e=>k.some(Xr(t,{officeStyles:e,gdocsStyles:e,htmlStyles:e}));return{get:o=>{const s=(e=>{switch(e){case xs.Word:return"officeStyles";case xs.GoogleDocs:return"gdocsStyles";default:return"htmlStyles"}})(o),a=t[s];return c(a)?a().then((e=>r("merge"===e)),(e=>(console.error(e),r(!1)))):"clean"===a?Promise.resolve(r(!1)):"merge"===a?Promise.resolve(r(!0)):new Promise((t=>{const o=De.fromTag("div");_n(o,pr("styles-dialog-content"));const s=De.fromTag("p"),a=Kn(n("cement.dialog.paste.instructions"));fn(s,a),hn(o,s);const i={text:n("cement.dialog.paste.clean"),tabindex:0,className:pr("clean-styles"),click:()=>{c(),t(r(!1))}},l={text:n("cement.dialog.paste.merge"),tabindex:1,className:pr("merge-styles"),click:()=>{c(),t(r(!0))}},c=()=>{u.destroy()},u=e();u.setTitle(n("cement.dialog.paste.title")),u.setContent(o),u.setButtons([i,l]),u.events.close.bind((()=>{c(),t(k.none())})),u.show()}))},destroy:d}})(e,t);return(e,t)=>{const r=t.bundle,o=t.response;return n.get(du(r)).then((e=>e.fold((()=>({response:Ur(),bundle:t.bundle})),(e=>({response:o,bundle:Wr({officeStyles:e.officeStyles,gdocsStyles:e.gdocsStyles,htmlStyles:e.htmlStyles})})))))}},mu=(e,t)=>(n,r)=>uu(r.bundle)?(e=>Promise.resolve({response:r.response,bundle:Wr({officeStyles:e,gdocsStyles:e,htmlStyles:e})}))(!0):hu(e,t)(n,r),pu=(e,t)=>{if(!qt(e))throw new Error("Internal error: attempted to write to an iframe that is not in the DOM");const n=(e=>(e=>{const t=e.dom;try{return((e,t)=>null!=e?k.some(t(e)):k.none())(t.contentWindow?t.contentWindow.document:t.contentDocument,De.fromDom)}catch(e){return console.log("Error reading iframe: ",t),console.log("Error was: "+e),k.none()}})(e).getOrThunk((()=>Rt(e))))(e),r=n.dom;r.open("text/html","replace"),r.write(t),r.close()};var fu=Object.create,gu=Object.defineProperty,vu=Object.getOwnPropertyDescriptor,yu=Object.getOwnPropertyNames,bu=Object.getPrototypeOf,xu=Object.prototype.hasOwnProperty,ku=(e,t)=>()=>(t||e((t={exports:{}}).exports,t),t.exports),wu=(e,t)=>{for(var n in t)gu(e,n,{get:t[n],enumerable:!0})},Su=ku((e=>{var t="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/".split("");e.encode=function(e){if(0<=e&&e<t.length)return t[e];throw new TypeError("Must be between 0 and 63: "+e)},e.decode=function(e){return 65<=e&&e<=90?e-65:97<=e&&e<=122?e-97+26:48<=e&&e<=57?e-48+52:43==e?62:47==e?63:-1}})),Cu=ku((e=>{var t=Su();e.encode=function(e){var n,r,o="",s=(r=e)<0?1+(-r<<1):0+(r<<1);do{n=31&s,(s>>>=5)>0&&(n|=32),o+=t.encode(n)}while(s>0);return o},e.decode=function(e,n,r){var o,s,a=e.length,i=0,l=0;do{if(n>=a)throw new Error("Expected more digits in base 64 VLQ value.");if(-1===(s=t.decode(e.charCodeAt(n++))))throw new Error("Invalid base64 digit: "+e.charAt(n-1));o=!!(32&s),i+=(s&=31)<<l,l+=5}while(o);r.value=function(e){var t=e>>1;return 1==(1&e)?-t:t}(i),r.rest=n}})),Tu=ku((e=>{e.getArg=function(e,t,n){if(t in e)return e[t];if(3===arguments.length)return n;throw new Error('"'+t+'" is a required argument.')};var t=/^(?:([\w+\-.]+):)?\/\/(?:(\w+:\w+)@)?([\w.-]*)(?::(\d+))?(.*)$/,n=/^data:.+\,.+$/;function r(e){var n=e.match(t);return n?{scheme:n[1],auth:n[2],host:n[3],port:n[4],path:n[5]}:null}function o(e){var t="";return e.scheme&&(t+=e.scheme+":"),t+="//",e.auth&&(t+=e.auth+"@"),e.host&&(t+=e.host),e.port&&(t+=":"+e.port),e.path&&(t+=e.path),t}e.urlParse=r,e.urlGenerate=o;var s=function(t){var n=[];return function(t){for(var s=0;s<n.length;s++)if(n[s].input===t){var a=n[0];return n[0]=n[s],n[s]=a,n[0].result}var i=function(t){var n=t,s=r(t);if(s){if(!s.path)return t;n=s.path}for(var a=e.isAbsolute(n),i=[],l=0,c=0;;){if(l=c,-1===(c=n.indexOf("/",l))){i.push(n.slice(l));break}for(i.push(n.slice(l,c));c<n.length&&"/"===n[c];)c++}var u,d=0;for(c=i.length-1;c>=0;c--)"."===(u=i[c])?i.splice(c,1):".."===u?d++:d>0&&(""===u?(i.splice(c+1,d),d=0):(i.splice(c,2),d--));return""===(n=i.join("/"))&&(n=a?"/":"."),s?(s.path=n,o(s)):n}(t);return n.unshift({input:t,result:i}),n.length>32&&n.pop(),i}}();function a(e,t){""===e&&(e="."),""===t&&(t=".");var a=r(t),i=r(e);if(i&&(e=i.path||"/"),a&&!a.scheme)return i&&(a.scheme=i.scheme),o(a);if(a||t.match(n))return t;if(i&&!i.host&&!i.path)return i.host=t,o(i);var l="/"===t.charAt(0)?t:s(e.replace(/\/+$/,"")+"/"+t);return i?(i.path=l,o(i)):l}e.normalize=s,e.join=a,e.isAbsolute=function(e){return"/"===e.charAt(0)||t.test(e)},e.relative=function(e,t){""===e&&(e="."),e=e.replace(/\/$/,"");for(var n=0;0!==t.indexOf(e+"/");){var r=e.lastIndexOf("/");if(r<0||(e=e.slice(0,r)).match(/^([^\/]+:\/)?\/*$/))return t;++n}return Array(n+1).join("../")+t.substr(e.length+1)};var i=!("__proto__"in Object.create(null));function l(e){return e}function c(e){if(!e)return!1;var t=e.length;if(t<9||95!==e.charCodeAt(t-1)||95!==e.charCodeAt(t-2)||111!==e.charCodeAt(t-3)||116!==e.charCodeAt(t-4)||111!==e.charCodeAt(t-5)||114!==e.charCodeAt(t-6)||112!==e.charCodeAt(t-7)||95!==e.charCodeAt(t-8)||95!==e.charCodeAt(t-9))return!1;for(var n=t-10;n>=0;n--)if(36!==e.charCodeAt(n))return!1;return!0}function u(e,t){return e===t?0:null===e?1:null===t?-1:e>t?1:-1}e.toSetString=i?l:function(e){return c(e)?"$"+e:e},e.fromSetString=i?l:function(e){return c(e)?e.slice(1):e},e.compareByOriginalPositions=function(e,t,n){var r=u(e.source,t.source);return 0!==r||0!=(r=e.originalLine-t.originalLine)||0!=(r=e.originalColumn-t.originalColumn)||n||0!=(r=e.generatedColumn-t.generatedColumn)||0!=(r=e.generatedLine-t.generatedLine)?r:u(e.name,t.name)},e.compareByOriginalPositionsNoSource=function(e,t,n){var r;return 0!=(r=e.originalLine-t.originalLine)||0!=(r=e.originalColumn-t.originalColumn)||n||0!=(r=e.generatedColumn-t.generatedColumn)||0!=(r=e.generatedLine-t.generatedLine)?r:u(e.name,t.name)},e.compareByGeneratedPositionsDeflated=function(e,t,n){var r=e.generatedLine-t.generatedLine;return 0!==r||0!=(r=e.generatedColumn-t.generatedColumn)||n||0!==(r=u(e.source,t.source))||0!=(r=e.originalLine-t.originalLine)||0!=(r=e.originalColumn-t.originalColumn)?r:u(e.name,t.name)},e.compareByGeneratedPositionsDeflatedNoLine=function(e,t,n){var r=e.generatedColumn-t.generatedColumn;return 0!==r||n||0!==(r=u(e.source,t.source))||0!=(r=e.originalLine-t.originalLine)||0!=(r=e.originalColumn-t.originalColumn)?r:u(e.name,t.name)},e.compareByGeneratedPositionsInflated=function(e,t){var n=e.generatedLine-t.generatedLine;return 0!==n||0!=(n=e.generatedColumn-t.generatedColumn)||0!==(n=u(e.source,t.source))||0!=(n=e.originalLine-t.originalLine)||0!=(n=e.originalColumn-t.originalColumn)?n:u(e.name,t.name)},e.parseSourceMapInput=function(e){return JSON.parse(e.replace(/^\)]}'[^\n]*\n/,""))},e.computeSourceURL=function(e,t,n){if(t=t||"",e&&("/"!==e[e.length-1]&&"/"!==t[0]&&(e+="/"),t=e+t),n){var i=r(n);if(!i)throw new Error("sourceMapURL could not be parsed");if(i.path){var l=i.path.lastIndexOf("/");l>=0&&(i.path=i.path.substring(0,l+1))}t=a(o(i),t)}return s(t)}})),Iu=ku((e=>{var t=Tu(),n=Object.prototype.hasOwnProperty,r=typeof Map<"u";function o(){this._array=[],this._set=r?new Map:Object.create(null)}o.fromArray=function(e,t){for(var n=new o,r=0,s=e.length;r<s;r++)n.add(e[r],t);return n},o.prototype.size=function(){return r?this._set.size:Object.getOwnPropertyNames(this._set).length},o.prototype.add=function(e,o){var s=r?e:t.toSetString(e),a=r?this.has(e):n.call(this._set,s),i=this._array.length;(!a||o)&&this._array.push(e),a||(r?this._set.set(e,i):this._set[s]=i)},o.prototype.has=function(e){if(r)return this._set.has(e);var o=t.toSetString(e);return n.call(this._set,o)},o.prototype.indexOf=function(e){if(r){var o=this._set.get(e);if(o>=0)return o}else{var s=t.toSetString(e);if(n.call(this._set,s))return this._set[s]}throw new Error('"'+e+'" is not in the set.')},o.prototype.at=function(e){if(e>=0&&e<this._array.length)return this._array[e];throw new Error("No element indexed by "+e)},o.prototype.toArray=function(){return this._array.slice()},e.ArraySet=o})),Au=ku((e=>{var t=Tu();function n(){this._array=[],this._sorted=!0,this._last={generatedLine:-1,generatedColumn:0}}n.prototype.unsortedForEach=function(e,t){this._array.forEach(e,t)},n.prototype.add=function(e){!function(e,n){var r=e.generatedLine,o=n.generatedLine,s=e.generatedColumn,a=n.generatedColumn;return o>r||o==r&&a>=s||t.compareByGeneratedPositionsInflated(e,n)<=0}(this._last,e)?(this._sorted=!1,this._array.push(e)):(this._last=e,this._array.push(e))},n.prototype.toArray=function(){return this._sorted||(this._array.sort(t.compareByGeneratedPositionsInflated),this._sorted=!0),this._array},e.MappingList=n})),Ou=ku((e=>{var t=Cu(),n=Tu(),r=Iu().ArraySet,o=Au().MappingList;function s(e){e||(e={}),this._file=n.getArg(e,"file",null),this._sourceRoot=n.getArg(e,"sourceRoot",null),this._skipValidation=n.getArg(e,"skipValidation",!1),this._sources=new r,this._names=new r,this._mappings=new o,this._sourcesContents=null}s.prototype._version=3,s.fromSourceMap=function(e){var t=e.sourceRoot,r=new s({file:e.file,sourceRoot:t});return e.eachMapping((function(e){var o={generated:{line:e.generatedLine,column:e.generatedColumn}};null!=e.source&&(o.source=e.source,null!=t&&(o.source=n.relative(t,o.source)),o.original={line:e.originalLine,column:e.originalColumn},null!=e.name&&(o.name=e.name)),r.addMapping(o)})),e.sources.forEach((function(o){var s=o;null!==t&&(s=n.relative(t,o)),r._sources.has(s)||r._sources.add(s);var a=e.sourceContentFor(o);null!=a&&r.setSourceContent(o,a)})),r},s.prototype.addMapping=function(e){var t=n.getArg(e,"generated"),r=n.getArg(e,"original",null),o=n.getArg(e,"source",null),s=n.getArg(e,"name",null);this._skipValidation||this._validateMapping(t,r,o,s),null!=o&&(o=String(o),this._sources.has(o)||this._sources.add(o)),null!=s&&(s=String(s),this._names.has(s)||this._names.add(s)),this._mappings.add({generatedLine:t.line,generatedColumn:t.column,originalLine:null!=r&&r.line,originalColumn:null!=r&&r.column,source:o,name:s})},s.prototype.setSourceContent=function(e,t){var r=e;null!=this._sourceRoot&&(r=n.relative(this._sourceRoot,r)),null!=t?(this._sourcesContents||(this._sourcesContents=Object.create(null)),this._sourcesContents[n.toSetString(r)]=t):this._sourcesContents&&(delete this._sourcesContents[n.toSetString(r)],0===Object.keys(this._sourcesContents).length&&(this._sourcesContents=null))},s.prototype.applySourceMap=function(e,t,o){var s=t;if(null==t){if(null==e.file)throw new Error('SourceMapGenerator.prototype.applySourceMap requires either an explicit source file, or the source map\'s "file" property. Both were omitted.');s=e.file}var a=this._sourceRoot;null!=a&&(s=n.relative(a,s));var i=new r,l=new r;this._mappings.unsortedForEach((function(t){if(t.source===s&&null!=t.originalLine){var r=e.originalPositionFor({line:t.originalLine,column:t.originalColumn});null!=r.source&&(t.source=r.source,null!=o&&(t.source=n.join(o,t.source)),null!=a&&(t.source=n.relative(a,t.source)),t.originalLine=r.line,t.originalColumn=r.column,null!=r.name&&(t.name=r.name))}var c=t.source;null!=c&&!i.has(c)&&i.add(c);var u=t.name;null!=u&&!l.has(u)&&l.add(u)}),this),this._sources=i,this._names=l,e.sources.forEach((function(t){var r=e.sourceContentFor(t);null!=r&&(null!=o&&(t=n.join(o,t)),null!=a&&(t=n.relative(a,t)),this.setSourceContent(t,r))}),this)},s.prototype._validateMapping=function(e,t,n,r){if(t&&"number"!=typeof t.line&&"number"!=typeof t.column)throw new Error("original.line and original.column are not numbers -- you probably meant to omit the original mapping entirely and only map the generated position. If so, pass null for the original mapping instead of an object with empty or null values.");if(!(e&&"line"in e&&"column"in e&&e.line>0&&e.column>=0)||t||n||r){if(e&&"line"in e&&"column"in e&&t&&"line"in t&&"column"in t&&e.line>0&&e.column>=0&&t.line>0&&t.column>=0&&n)return;throw new Error("Invalid mapping: "+JSON.stringify({generated:e,source:n,original:t,name:r}))}},s.prototype._serializeMappings=function(){for(var e,r,o,s,a=0,i=1,l=0,c=0,u=0,d=0,h="",m=this._mappings.toArray(),p=0,f=m.length;p<f;p++){if(e="",(r=m[p]).generatedLine!==i)for(a=0;r.generatedLine!==i;)e+=";",i++;else if(p>0){if(!n.compareByGeneratedPositionsInflated(r,m[p-1]))continue;e+=","}e+=t.encode(r.generatedColumn-a),a=r.generatedColumn,null!=r.source&&(s=this._sources.indexOf(r.source),e+=t.encode(s-d),d=s,e+=t.encode(r.originalLine-1-c),c=r.originalLine-1,e+=t.encode(r.originalColumn-l),l=r.originalColumn,null!=r.name&&(o=this._names.indexOf(r.name),e+=t.encode(o-u),u=o)),h+=e}return h},s.prototype._generateSourcesContent=function(e,t){return e.map((function(e){if(!this._sourcesContents)return null;null!=t&&(e=n.relative(t,e));var r=n.toSetString(e);return Object.prototype.hasOwnProperty.call(this._sourcesContents,r)?this._sourcesContents[r]:null}),this)},s.prototype.toJSON=function(){var e={version:this._version,sources:this._sources.toArray(),names:this._names.toArray(),mappings:this._serializeMappings()};return null!=this._file&&(e.file=this._file),null!=this._sourceRoot&&(e.sourceRoot=this._sourceRoot),this._sourcesContents&&(e.sourcesContent=this._generateSourcesContent(e.sources,e.sourceRoot)),e},s.prototype.toString=function(){return JSON.stringify(this.toJSON())},e.SourceMapGenerator=s}));function Eu(e){return e>=48&&e<=57}function Lu(e){return Eu(e)||e>=65&&e<=70||e>=97&&e<=102}function Nu(e){return e>=65&&e<=90}function _u(e){return function(e){return Nu(e)||function(e){return e>=97&&e<=122}(e)}(e)||function(e){return e>=128}(e)||95===e}function Du(e){return _u(e)||Eu(e)||45===e}function Pu(e){return e>=0&&e<=8||11===e||e>=14&&e<=31||127===e}function Ru(e){return 10===e||13===e||12===e}function Mu(e){return Ru(e)||32===e||9===e}function ju(e,t){return!(92!==e||Ru(t)||0===t)}function Fu(e,t,n){return 45===e?_u(t)||45===t||ju(t,n):!!_u(e)||92===e&&ju(e,t)}function Uu(e,t,n){return 43===e||45===e?Eu(t)?2:46===t&&Eu(n)?3:0:46===e?Eu(t)?2:0:Eu(e)?1:0}function Bu(e){return 65279===e||65534===e?1:0}var zu=new Array(128),Hu=130,Wu=131,$u=132,Vu=133;for(let e=0;e<zu.length;e++)zu[e]=Mu(e)&&Hu||Eu(e)&&Wu||_u(e)&&$u||Pu(e)&&Vu||e||128;function Gu(e){return e<128?zu[e]:$u}function qu(e,t){return t<e.length?e.charCodeAt(t):0}function Ku(e,t,n){return 13===n&&10===qu(e,t+1)?2:1}function Ju(e,t,n){let r=e.charCodeAt(t);return Nu(r)&&(r|=32),r===n}function Yu(e,t,n,r){if(n-t!==r.length||t<0||n>e.length)return!1;for(let o=t;o<n;o++){let n=r.charCodeAt(o-t),s=e.charCodeAt(o);if(Nu(s)&&(s|=32),s!==n)return!1}return!0}function Xu(e,t){for(;t<e.length&&Mu(e.charCodeAt(t));t++);return t}function Zu(e,t){for(;t<e.length&&Eu(e.charCodeAt(t));t++);return t}function Qu(e,t){if(Lu(qu(e,(t+=2)-1))){for(let n=Math.min(e.length,t+5);t<n&&Lu(qu(e,t));t++);let n=qu(e,t);Mu(n)&&(t+=Ku(e,t,n))}return t}function ed(e,t){for(;t<e.length;t++){let n=e.charCodeAt(t);if(!Du(n)){if(ju(n,qu(e,t+1))){t=Qu(e,t)-1;continue}break}}return t}function td(e,t){let n=e.charCodeAt(t);if((43===n||45===n)&&(n=e.charCodeAt(t+=1)),Eu(n)&&(t=Zu(e,t+1),n=e.charCodeAt(t)),46===n&&Eu(e.charCodeAt(t+1))&&(t=Zu(e,t+=2)),Ju(e,t,101)){let r=0;n=e.charCodeAt(t+1),(45===n||43===n)&&(r=1,n=e.charCodeAt(t+2)),Eu(n)&&(t=Zu(e,t+1+r+1))}return t}function nd(e,t){for(;t<e.length;t++){let n=e.charCodeAt(t);if(41===n){t++;break}ju(n,qu(e,t+1))&&(t=Qu(e,t))}return t}function rd(e){if(1===e.length&&!Lu(e.charCodeAt(0)))return e[0];let t=parseInt(e,16);return(0===t||t>=55296&&t<=57343||t>1114111)&&(t=65533),String.fromCodePoint(t)}var od=["EOF-token","ident-token","function-token","at-keyword-token","hash-token","string-token","bad-string-token","url-token","bad-url-token","delim-token","number-token","percentage-token","dimension-token","whitespace-token","CDO-token","CDC-token","colon-token","semicolon-token","comma-token","[-token","]-token","(-token",")-token","{-token","}-token"];function sd(e=null,t){return null===e||e.length<t?new Uint32Array(Math.max(t+1024,16384)):e}function ad(e){let t=e.source,n=t.length,r=t.length>0?Bu(t.charCodeAt(0)):0,o=sd(e.lines,n),s=sd(e.columns,n),a=e.startLine,i=e.startColumn;for(let e=r;e<n;e++){let r=t.charCodeAt(e);o[e]=a,s[e]=i++,(10===r||13===r||12===r)&&(13===r&&e+1<n&&10===t.charCodeAt(e+1)&&(e++,o[e]=a,s[e]=i),a++,i=1)}o[n]=a,s[n]=i,e.lines=o,e.columns=s,e.computed=!0}var id=class{constructor(){this.lines=null,this.columns=null,this.computed=!1}setSource(e,t=0,n=1,r=1){this.source=e,this.startOffset=t,this.startLine=n,this.startColumn=r,this.computed=!1}getLocation(e,t){return this.computed||ad(this),{source:t,offset:this.startOffset+e,line:this.lines[e],column:this.columns[e]}}getLocationRange(e,t,n){return this.computed||ad(this),{source:n,start:{offset:this.startOffset+e,line:this.lines[e],column:this.columns[e]},end:{offset:this.startOffset+t,line:this.lines[t],column:this.columns[t]}}}},ld=16777215,cd=24,ud=new Map([[2,22],[21,22],[19,20],[23,24]]),dd=class{constructor(e,t){this.setSource(e,t)}reset(){this.eof=!1,this.tokenIndex=-1,this.tokenType=0,this.tokenStart=this.firstCharOffset,this.tokenEnd=this.firstCharOffset}setSource(e="",t=(()=>{})){let n=(e=String(e||"")).length,r=sd(this.offsetAndType,e.length+1),o=sd(this.balance,e.length+1),s=0,a=0,i=0,l=-1;for(this.offsetAndType=null,this.balance=null,t(e,((e,t,c)=>{switch(e){default:o[s]=n;break;case a:{let e=i&ld;for(i=o[e],a=i>>cd,o[s]=e,o[e++]=s;e<s;e++)o[e]===n&&(o[e]=s);break}case 21:case 2:case 19:case 23:o[s]=i,a=ud.get(e),i=a<<cd|s}r[s++]=e<<cd|c,-1===l&&(l=t)})),r[s]=0|n,o[s]=n,o[n]=n;0!==i;){let e=i&ld;i=o[e],o[e]=n}this.source=e,this.firstCharOffset=-1===l?0:l,this.tokenCount=s,this.offsetAndType=r,this.balance=o,this.reset(),this.next()}lookupType(e){return(e+=this.tokenIndex)<this.tokenCount?this.offsetAndType[e]>>cd:0}lookupOffset(e){return(e+=this.tokenIndex)<this.tokenCount?this.offsetAndType[e-1]&ld:this.source.length}lookupValue(e,t){return(e+=this.tokenIndex)<this.tokenCount&&Yu(this.source,this.offsetAndType[e-1]&ld,this.offsetAndType[e]&ld,t)}getTokenStart(e){return e===this.tokenIndex?this.tokenStart:e>0?e<this.tokenCount?this.offsetAndType[e-1]&ld:this.offsetAndType[this.tokenCount]&ld:this.firstCharOffset}substrToCursor(e){return this.source.substring(e,this.tokenStart)}isBalanceEdge(e){return this.balance[this.tokenIndex]<e}isDelim(e,t){return t?9===this.lookupType(t)&&this.source.charCodeAt(this.lookupOffset(t))===e:9===this.tokenType&&this.source.charCodeAt(this.tokenStart)===e}skip(e){let t=this.tokenIndex+e;t<this.tokenCount?(this.tokenIndex=t,this.tokenStart=this.offsetAndType[t-1]&ld,t=this.offsetAndType[t],this.tokenType=t>>cd,this.tokenEnd=t&ld):(this.tokenIndex=this.tokenCount,this.next())}next(){let e=this.tokenIndex+1;e<this.tokenCount?(this.tokenIndex=e,this.tokenStart=this.tokenEnd,e=this.offsetAndType[e],this.tokenType=e>>cd,this.tokenEnd=e&ld):(this.eof=!0,this.tokenIndex=this.tokenCount,this.tokenType=0,this.tokenStart=this.tokenEnd=this.source.length)}skipSC(){for(;13===this.tokenType||25===this.tokenType;)this.next()}skipUntilBalanced(e,t){let n,r,o=e;e:for(;o<this.tokenCount&&(n=this.balance[o],!(n<e));o++)switch(r=o>0?this.offsetAndType[o-1]&ld:this.firstCharOffset,t(this.source.charCodeAt(r))){case 1:break e;case 2:o++;break e;default:this.balance[n]===o&&(o=n)}this.skip(o-this.tokenIndex)}forEachToken(e){for(let t=0,n=this.firstCharOffset;t<this.tokenCount;t++){let r=n,o=this.offsetAndType[t],s=o&ld;n=s,e(o>>cd,r,s,t)}}dump(){let e=new Array(this.tokenCount);return this.forEachToken(((t,n,r,o)=>{e[o]={idx:o,type:od[t],chunk:this.source.substring(n,r),balance:this.balance[o]}})),e}};function hd(e,t){function n(t){return t<i?e.charCodeAt(t):0}function r(){return c=td(e,c),Fu(n(c),n(c+1),n(c+2))?(a=12,void(c=ed(e,c))):37===n(c)?(a=11,void c++):void(a=10)}function o(){let t=c;return c=ed(e,c),Yu(e,t,c,"url")&&40===n(c)?(c=Xu(e,c+1),34===n(c)||39===n(c)?(a=2,void(c=t+4)):void function(){for(a=7,c=Xu(e,c);c<e.length;c++){let t=e.charCodeAt(c);switch(Gu(t)){case 41:return void c++;case Hu:return c=Xu(e,c),41===n(c)||c>=e.length?void(c<e.length&&c++):(c=nd(e,c),void(a=8));case 34:case 39:case 40:case Vu:return c=nd(e,c),void(a=8);case 92:if(ju(t,n(c+1))){c=Qu(e,c)-1;break}return c=nd(e,c),void(a=8)}}}()):40===n(c)?(a=2,void c++):void(a=1)}function s(t){for(t||(t=n(c++)),a=5;c<e.length;c++){let r=e.charCodeAt(c);switch(Gu(r)){case t:return void c++;case Hu:if(Ru(r))return c+=Ku(e,c,r),void(a=6);break;case 92:if(c===e.length-1)break;let o=n(c+1);Ru(o)?c+=Ku(e,c+1,o):ju(r,o)&&(c=Qu(e,c)-1)}}}let a,i=(e=String(e||"")).length,l=Bu(n(0)),c=l;for(;c<i;){let i=e.charCodeAt(c);switch(Gu(i)){case Hu:a=13,c=Xu(e,c+1);break;case 34:s();break;case 35:Du(n(c+1))||ju(n(c+1),n(c+2))?(a=4,c=ed(e,c+1)):(a=9,c++);break;case 39:s();break;case 40:a=21,c++;break;case 41:a=22,c++;break;case 43:Uu(i,n(c+1),n(c+2))?r():(a=9,c++);break;case 44:a=18,c++;break;case 45:Uu(i,n(c+1),n(c+2))?r():45===n(c+1)&&62===n(c+2)?(a=15,c+=3):Fu(i,n(c+1),n(c+2))?o():(a=9,c++);break;case 46:Uu(i,n(c+1),n(c+2))?r():(a=9,c++);break;case 47:42===n(c+1)?(a=25,c=e.indexOf("*/",c+2),c=-1===c?e.length:c+2):(a=9,c++);break;case 58:a=16,c++;break;case 59:a=17,c++;break;case 60:33===n(c+1)&&45===n(c+2)&&45===n(c+3)?(a=14,c+=4):(a=9,c++);break;case 64:Fu(n(c+1),n(c+2),n(c+3))?(a=3,c=ed(e,c+1)):(a=9,c++);break;case 91:a=19,c++;break;case 92:ju(i,n(c+1))?o():(a=9,c++);break;case 93:a=20,c++;break;case 123:a=23,c++;break;case 125:a=24,c++;break;case Wu:r();break;case $u:o();break;default:a=9,c++}t(a,l,l=c)}}var md,pd=(md=Ou(),((e,t,n,r)=>{if(t&&"object"==typeof t||"function"==typeof t)for(let n of yu(t))!xu.call(e,n)&&"default"!==n&&gu(e,n,{get:()=>t[n],enumerable:!(r=vu(t,n))||r.enumerable});return e})((e=>gu(e,"__esModule",{value:!0}))(gu(null!=md?fu(bu(md)):{},"default",{value:md,enumerable:!0})),md)),fd=new Set(["Atrule","Selector","Declaration"]),gd={};wu(gd,{safe:()=>wd,spec:()=>kd});var vd=(e,t)=>{if(9===e&&(e=t),"string"==typeof e){let t=e.charCodeAt(0);return t>127?32768:t<<8}return e},yd=[[1,1],[1,2],[1,7],[1,8],[1,"-"],[1,10],[1,11],[1,12],[1,15],[1,21],[3,1],[3,2],[3,7],[3,8],[3,"-"],[3,10],[3,11],[3,12],[3,15],[4,1],[4,2],[4,7],[4,8],[4,"-"],[4,10],[4,11],[4,12],[4,15],[12,1],[12,2],[12,7],[12,8],[12,"-"],[12,10],[12,11],[12,12],[12,15],["#",1],["#",2],["#",7],["#",8],["#","-"],["#",10],["#",11],["#",12],["#",15],["-",1],["-",2],["-",7],["-",8],["-","-"],["-",10],["-",11],["-",12],["-",15],[10,1],[10,2],[10,7],[10,8],[10,10],[10,11],[10,12],[10,"%"],[10,15],["@",1],["@",2],["@",7],["@",8],["@","-"],["@",15],[".",10],[".",11],[".",12],["+",10],["+",11],["+",12],["/","*"]],bd=yd.concat([[1,4],[12,4],[4,4],[3,21],[3,5],[3,16],[11,11],[11,12],[11,2],[11,"-"],[22,1],[22,2],[22,11],[22,12],[22,4],[22,"-"]]);function xd(e){let t=new Set(e.map((([e,t])=>vd(e)<<16|vd(t))));return function(e,n,r){let o=vd(n,r),s=r.charCodeAt(0);return(45===s&&1!==n&&2!==n&&15!==n||43===s?t.has(e<<16|s<<8):t.has(e<<16|o))&&this.emit(" ",13,!0),o}}var kd=xd(yd),wd=xd(bd);function Sd(e,t){if("function"!=typeof t)e.children.forEach(this.node,this);else{let n=null;e.children.forEach((e=>{null!==n&&t.call(this,n),this.node(e),n=e}))}}function Cd(e){hd(e,((t,n,r)=>{this.token(t,e.slice(n,r))}))}var Td={};wu(Td,{AnPlusB:()=>Rd,Atrule:()=>Md,AtrulePrelude:()=>jd,AttributeSelector:()=>qd,Block:()=>Kd,Brackets:()=>Jd,CDC:()=>Yd,CDO:()=>Xd,ClassSelector:()=>Qd,Combinator:()=>th,Comment:()=>nh,Declaration:()=>rh,DeclarationList:()=>oh,Dimension:()=>sh,Function:()=>ah,Hash:()=>ih,IdSelector:()=>dh,Identifier:()=>ch,MediaFeature:()=>hh,MediaQuery:()=>mh,MediaQueryList:()=>ph,NestingSelector:()=>gh,Nth:()=>yh,Number:()=>bh,Operator:()=>xh,Parentheses:()=>kh,Percentage:()=>Sh,PseudoClassSelector:()=>Th,PseudoElementSelector:()=>Ah,Ratio:()=>Oh,Raw:()=>Nh,Rule:()=>_h,Selector:()=>Ph,SelectorList:()=>Mh,String:()=>Hh,StyleSheet:()=>Wh,TypeSelector:()=>qh,UnicodeRange:()=>Kh,Url:()=>tm,Value:()=>nm,WhiteSpace:()=>rm});var Id=43,Ad=45,Od=110,Ed=!0;function Ld(e,t){let n=this.tokenStart+e,r=this.charCodeAt(n);for((r===Id||r===Ad)&&(t&&this.error("Number sign is not allowed"),n++);n<this.tokenEnd;n++)Eu(this.charCodeAt(n))||this.error("Integer is expected",n)}function Nd(e){return Ld.call(this,0,e)}function _d(e,t){if(!this.cmpChar(this.tokenStart+e,t)){let n="";switch(t){case Od:n="N is expected";break;case Ad:n="HyphenMinus is expected"}this.error(n,this.tokenStart+e)}}function Dd(){let e=0,t=0,n=this.tokenType;for(;13===n||25===n;)n=this.lookupType(++e);if(10!==n){if(!this.isDelim(Id,e)&&!this.isDelim(Ad,e))return null;t=this.isDelim(Id,e)?Id:Ad;do{n=this.lookupType(++e)}while(13===n||25===n);10!==n&&(this.skip(e),Nd.call(this,Ed))}return e>0&&this.skip(e),0===t&&(n=this.charCodeAt(this.tokenStart),n!==Id&&n!==Ad&&this.error("Number sign is expected")),Nd.call(this,0!==t),t===Ad?"-"+this.consume(10):this.consume(10)}function Pd(){let e=this.tokenStart,t=null,n=null;if(10===this.tokenType)Nd.call(this,!1),n=this.consume(10);else if(1===this.tokenType&&this.cmpChar(this.tokenStart,Ad))switch(t="-1",_d.call(this,1,Od),this.tokenEnd-this.tokenStart){case 2:this.next(),n=Dd.call(this);break;case 3:_d.call(this,2,Ad),this.next(),this.skipSC(),Nd.call(this,Ed),n="-"+this.consume(10);break;default:_d.call(this,2,Ad),Ld.call(this,3,Ed),this.next(),n=this.substrToCursor(e+2)}else if(1===this.tokenType||this.isDelim(Id)&&1===this.lookupType(1)){let r=0;switch(t="1",this.isDelim(Id)&&(r=1,this.next()),_d.call(this,0,Od),this.tokenEnd-this.tokenStart){case 1:this.next(),n=Dd.call(this);break;case 2:_d.call(this,1,Ad),this.next(),this.skipSC(),Nd.call(this,Ed),n="-"+this.consume(10);break;default:_d.call(this,1,Ad),Ld.call(this,2,Ed),this.next(),n=this.substrToCursor(e+r+1)}}else if(12===this.tokenType){let r=this.charCodeAt(this.tokenStart),o=r===Id||r===Ad,s=this.tokenStart+o;for(;s<this.tokenEnd&&Eu(this.charCodeAt(s));s++);s===this.tokenStart+o&&this.error("Integer is expected",this.tokenStart+o),_d.call(this,s-this.tokenStart,Od),t=this.substring(e,s),s+1===this.tokenEnd?(this.next(),n=Dd.call(this)):(_d.call(this,s-this.tokenStart+1,Ad),s+2===this.tokenEnd?(this.next(),this.skipSC(),Nd.call(this,Ed),n="-"+this.consume(10)):(Ld.call(this,s-this.tokenStart+2,Ed),this.next(),n=this.substrToCursor(s+1)))}else this.error();return null!==t&&t.charCodeAt(0)===Id&&(t=t.substr(1)),null!==n&&n.charCodeAt(0)===Id&&(n=n.substr(1)),{type:"AnPlusB",loc:this.getLocation(e,this.tokenStart),a:t,b:n}}function Rd(e){if(e.a){let t=("+1"===e.a||"1"===e.a?"n":"-1"===e.a&&"-n")||e.a+"n";if(e.b){let n="-"===e.b[0]||"+"===e.b[0]?e.b:"+"+e.b;this.tokenize(t+n)}else this.tokenize(t)}else this.tokenize(e.b)}function Md(e){this.token(3,"@"+e.name),null!==e.prelude&&this.node(e.prelude),e.block?this.node(e.block):this.token(17,";")}function jd(e){this.children(e)}var Fd=36,Ud=42,Bd=61,zd=94,Hd=124,Wd=126;function $d(){this.eof&&this.error("Unexpected end of input");let e=this.tokenStart,t=!1;return this.isDelim(Ud)?(t=!0,this.next()):this.isDelim(Hd)||this.eat(1),this.isDelim(Hd)?this.charCodeAt(this.tokenStart+1)!==Bd?(this.next(),this.eat(1)):t&&this.error("Identifier is expected",this.tokenEnd):t&&this.error("Vertical line is expected"),{type:"Identifier",loc:this.getLocation(e,this.tokenStart),name:this.substrToCursor(e)}}function Vd(){let e=this.tokenStart,t=this.charCodeAt(e);return t!==Bd&&t!==Wd&&t!==zd&&t!==Fd&&t!==Ud&&t!==Hd&&this.error("Attribute selector (=, ~=, ^=, $=, *=, |=) is expected"),this.next(),t!==Bd&&(this.isDelim(Bd)||this.error("Equal sign is expected"),this.next()),this.substrToCursor(e)}function Gd(){let e,t=this.tokenStart,n=null,r=null,o=null;return this.eat(19),this.skipSC(),e=$d.call(this),this.skipSC(),20!==this.tokenType&&(1!==this.tokenType&&(n=Vd.call(this),this.skipSC(),r=5===this.tokenType?this.String():this.Identifier(),this.skipSC()),1===this.tokenType&&(o=this.consume(1),this.skipSC())),this.eat(20),{type:"AttributeSelector",loc:this.getLocation(t,this.tokenStart),name:e,matcher:n,value:r,flags:o}}function qd(e){this.token(9,"["),this.node(e.name),null!==e.matcher&&(this.tokenize(e.matcher),this.node(e.value)),null!==e.flags&&this.token(1,e.flags),this.token(9,"]")}function Kd(e){this.token(23,"{"),this.children(e,(e=>{"Declaration"===e.type&&this.token(17,";")})),this.token(24,"}")}function Jd(e){this.token(9,"["),this.children(e),this.token(9,"]")}function Yd(){this.token(15,"--\x3e")}function Xd(){this.token(14,"\x3c!--")}function Zd(){return this.eatDelim(46),{type:"ClassSelector",loc:this.getLocation(this.tokenStart-1,this.tokenEnd),name:this.consume(1)}}function Qd(e){this.token(9,"."),this.token(1,e.name)}function eh(){let e,t=this.tokenStart;switch(this.tokenType){case 13:e=" ";break;case 9:switch(this.charCodeAt(this.tokenStart)){case 62:case 43:case 126:this.next();break;case 47:this.next(),this.eatIdent("deep"),this.eatDelim(47);break;default:this.error("Combinator is expected")}e=this.substrToCursor(t)}return{type:"Combinator",loc:this.getLocation(t,this.tokenStart),name:e}}function th(e){this.tokenize(e.name)}function nh(e){this.token(25,"/*"+e.value+"*/")}function rh(e){this.token(1,e.property),this.token(16,":"),this.node(e.value),e.important&&(this.token(9,"!"),this.token(1,!0===e.important?"important":e.important))}function oh(e){this.children(e,(e=>{"Declaration"===e.type&&this.token(17,";")}))}function sh(e){this.token(12,e.value+e.unit)}function ah(e){this.token(2,e.name+"("),this.children(e),this.token(22,")")}function ih(e){this.token(4,"#"+e.value)}function lh(){return{type:"Identifier",loc:this.getLocation(this.tokenStart,this.tokenEnd),name:this.consume(1)}}function ch(e){this.token(1,e.name)}function uh(){let e=this.tokenStart;return this.eat(4),{type:"IdSelector",loc:this.getLocation(e,this.tokenStart),name:this.substrToCursor(e+1)}}function dh(e){this.token(9,"#"+e.name)}function hh(e){this.token(21,"("),this.token(1,e.name),null!==e.value&&(this.token(16,":"),this.node(e.value)),this.token(22,")")}function mh(e){this.children(e)}function ph(e){this.children(e,(()=>this.token(18,",")))}function fh(){let e=this.tokenStart;return this.eatDelim(38),{type:"NestingSelector",loc:this.getLocation(e,this.tokenStart)}}function gh(){this.token(9,"&")}function vh(){this.skipSC();let e,t=this.tokenStart,n=t,r=null;return e=this.lookupValue(0,"odd")||this.lookupValue(0,"even")?this.Identifier():this.AnPlusB(),n=this.tokenStart,this.skipSC(),this.lookupValue(0,"of")&&(this.next(),r=this.SelectorList(),n=this.tokenStart),{type:"Nth",loc:this.getLocation(t,n),nth:e,selector:r}}function yh(e){this.node(e.nth),null!==e.selector&&(this.token(1,"of"),this.node(e.selector))}function bh(e){this.token(10,e.value)}function xh(e){this.tokenize(e.value)}function kh(e){this.token(21,"("),this.children(e),this.token(22,")")}function wh(){return{type:"Percentage",loc:this.getLocation(this.tokenStart,this.tokenEnd),value:this.consumeNumber(11)}}function Sh(e){this.token(11,e.value+"%")}function Ch(){let e,t,n=this.tokenStart,r=null;return this.eat(16),2===this.tokenType?(e=this.consumeFunctionName(),t=e.toLowerCase(),hasOwnProperty.call(this.pseudo,t)?(this.skipSC(),r=this.pseudo[t].call(this),this.skipSC()):(r=this.createList(),r.push(this.Raw(this.tokenIndex,null,!1))),this.eat(22)):e=this.consume(1),{type:"PseudoClassSelector",loc:this.getLocation(n,this.tokenStart),name:e,children:r}}function Th(e){this.token(16,":"),null===e.children?this.token(1,e.name):(this.token(2,e.name+"("),this.children(e),this.token(22,")"))}function Ih(){let e,t,n=this.tokenStart,r=null;return this.eat(16),this.eat(16),2===this.tokenType?(e=this.consumeFunctionName(),t=e.toLowerCase(),hasOwnProperty.call(this.pseudo,t)?(this.skipSC(),r=this.pseudo[t].call(this),this.skipSC()):(r=this.createList(),r.push(this.Raw(this.tokenIndex,null,!1))),this.eat(22)):e=this.consume(1),{type:"PseudoElementSelector",loc:this.getLocation(n,this.tokenStart),name:e,children:r}}function Ah(e){this.token(16,":"),this.token(16,":"),null===e.children?this.token(1,e.name):(this.token(2,e.name+"("),this.children(e),this.token(22,")"))}function Oh(e){this.token(10,e.left),this.token(9,"/"),this.token(10,e.right)}function Eh(){return this.tokenIndex>0&&13===this.lookupType(-1)?this.tokenIndex>1?this.getTokenStart(this.tokenIndex-1):this.firstCharOffset:this.tokenStart}function Lh(e,t,n){let r,o=this.getTokenStart(e);return this.skipUntilBalanced(e,t||this.consumeUntilBalanceEnd),r=n&&this.tokenStart>o?Eh.call(this):this.tokenStart,{type:"Raw",loc:this.getLocation(o,r),value:this.substring(o,r)}}function Nh(e){this.tokenize(e.value)}function _h(e){this.node(e.prelude),this.node(e.block)}function Dh(){let e=this.readSequence(this.scope.Selector);return null===this.getFirstListNode(e)&&this.error("Selector is expected"),{type:"Selector",loc:this.getLocationFromList(e),children:e}}function Ph(e){this.children(e)}function Rh(){let e=this.createList();for(;!this.eof&&(e.push(this.Selector()),18===this.tokenType);)this.next();return{type:"SelectorList",loc:this.getLocationFromList(e),children:e}}function Mh(e){this.children(e,(()=>this.token(18,",")))}var jh=92,Fh=34,Uh=39;function Bh(e){let t=e.length,n=e.charCodeAt(0),r=n===Fh||n===Uh?1:0,o=1===r&&t>1&&e.charCodeAt(t-1)===n?t-2:t-1,s="";for(let n=r;n<=o;n++){let r=e.charCodeAt(n);if(r===jh){if(n===o){n!==t-1&&(s=e.substr(n+1));break}if(r=e.charCodeAt(++n),ju(jh,r)){let t=n-1,r=Qu(e,t);n=r-1,s+=rd(e.substring(t+1,r))}else 13===r&&10===e.charCodeAt(n+1)&&n++}else s+=e[n]}return s}function zh(){return{type:"String",loc:this.getLocation(this.tokenStart,this.tokenEnd),value:Bh(this.consume(5))}}function Hh(e){this.token(5,function(e,t){let n=Fh,r="",o=!1;for(let t=0;t<e.length;t++){let s=e.charCodeAt(t);0!==s?s<=31||127===s?(r+="\\"+s.toString(16),o=!0):s===n||s===jh?(r+="\\"+e.charAt(t),o=!1):(o&&(Lu(s)||Mu(s))&&(r+=" "),r+=e.charAt(t),o=!1):r+="\ufffd"}return'"'+r+'"'}(e.value))}function Wh(e){this.children(e)}var $h=42;function Vh(){1!==this.tokenType&&!1===this.isDelim($h)&&this.error("Identifier or asterisk is expected"),this.next()}function Gh(){let e=this.tokenStart;return this.isDelim(124)?(this.next(),Vh.call(this)):(Vh.call(this),this.isDelim(124)&&(this.next(),Vh.call(this))),{type:"TypeSelector",loc:this.getLocation(e,this.tokenStart),name:this.substrToCursor(e)}}function qh(e){this.tokenize(e.name)}function Kh(e){this.tokenize(e.value)}var Jh=32,Yh=92,Xh=34,Zh=39,Qh=40,em=41;function tm(e){this.token(7,function(e){let t="",n=!1;for(let r=0;r<e.length;r++){let o=e.charCodeAt(r);0!==o?o<=31||127===o?(t+="\\"+o.toString(16),n=!0):o===Jh||o===Yh||o===Xh||o===Zh||o===Qh||o===em?(t+="\\"+e.charAt(r),n=!1):(n&&Lu(o)&&(t+=" "),t+=e.charAt(r),n=!1):t+="\ufffd"}return"url("+t+")"}(e.value))}function nm(e){this.children(e)}function rm(e){this.token(13,e.value)}var om=function(e){let t=new Map;for(let n in e.node){let r=e.node[n];"function"==typeof(r.generate||r)&&t.set(n,r.generate||r)}return function(e,n){let r="",o=0,s={node(e){if(!t.has(e.type))throw new Error("Unknown node type: "+e.type);t.get(e.type).call(a,e)},tokenBefore:wd,token(e,t){o=this.tokenBefore(o,e,t),this.emit(t,e,!1),9===e&&92===t.charCodeAt(0)&&this.emit("\n",13,!0)},emit(e){r+=e},result:()=>r};n&&("function"==typeof n.decorator&&(s=n.decorator(s)),n.sourceMap&&(s=function(e){let t=new pd.SourceMapGenerator,n={line:1,column:0},r={line:0,column:0},o={line:1,column:0},s={generated:o},a=1,i=0,l=!1,c=e.node;e.node=function(e){if(e.loc&&e.loc.start&&fd.has(e.type)){let c=e.loc.start.line,u=e.loc.start.column-1;(r.line!==c||r.column!==u)&&(r.line=c,r.column=u,n.line=a,n.column=i,l&&(l=!1,(n.line!==o.line||n.column!==o.column)&&t.addMapping(s)),l=!0,t.addMapping({source:e.loc.source,original:r,generated:n}))}c.call(this,e),l&&fd.has(e.type)&&(o.line=a,o.column=i)};let u=e.emit;e.emit=function(e,t,n){for(let t=0;t<e.length;t++)10===e.charCodeAt(t)?(a++,i=0):i++;u(e,t,n)};let d=e.result;return e.result=function(){return l&&t.addMapping(s),{css:d(),map:t}},e}(s)),n.mode in gd&&(s.tokenBefore=gd[n.mode]));let a={node:e=>s.node(e),children:Sd,token:(e,t)=>s.token(e,t),tokenize:Cd};return s.node(e),s.result()}}({node:Td}),sm=null,am=class{static createItem(e){return{prev:null,next:null,data:e}}constructor(){this.head=null,this.tail=null,this.cursor=null}createItem(e){return am.createItem(e)}allocateCursor(e,t){let n;return null!==sm?(n=sm,sm=sm.cursor,n.prev=e,n.next=t,n.cursor=this.cursor):n={prev:e,next:t,cursor:this.cursor},this.cursor=n,n}releaseCursor(){let{cursor:e}=this;this.cursor=e.cursor,e.prev=null,e.next=null,e.cursor=sm,sm=e}updateCursors(e,t,n,r){let{cursor:o}=this;for(;null!==o;)o.prev===e&&(o.prev=t),o.next===n&&(o.next=r),o=o.cursor}*[Symbol.iterator](){for(let e=this.head;null!==e;e=e.next)yield e.data}get size(){let e=0;for(let t=this.head;null!==t;t=t.next)e++;return e}get isEmpty(){return null===this.head}get first(){return this.head&&this.head.data}get last(){return this.tail&&this.tail.data}fromArray(e){let t=null;this.head=null;for(let n of e){let e=am.createItem(n);null!==t?t.next=e:this.head=e,e.prev=t,t=e}return this.tail=t,this}toArray(){return[...this]}toJSON(){return[...this]}forEach(e,t=this){let n=this.allocateCursor(null,this.head);for(;null!==n.next;){let r=n.next;n.next=r.next,e.call(t,r.data,r,this)}this.releaseCursor()}forEachRight(e,t=this){let n=this.allocateCursor(this.tail,null);for(;null!==n.prev;){let r=n.prev;n.prev=r.prev,e.call(t,r.data,r,this)}this.releaseCursor()}reduce(e,t,n=this){let r,o=this.allocateCursor(null,this.head),s=t;for(;null!==o.next;)r=o.next,o.next=r.next,s=e.call(n,s,r.data,r,this);return this.releaseCursor(),s}reduceRight(e,t,n=this){let r,o=this.allocateCursor(this.tail,null),s=t;for(;null!==o.prev;)r=o.prev,o.prev=r.prev,s=e.call(n,s,r.data,r,this);return this.releaseCursor(),s}some(e,t=this){for(let n=this.head;null!==n;n=n.next)if(e.call(t,n.data,n,this))return!0;return!1}map(e,t=this){let n=new am;for(let r=this.head;null!==r;r=r.next)n.appendData(e.call(t,r.data,r,this));return n}filter(e,t=this){let n=new am;for(let r=this.head;null!==r;r=r.next)e.call(t,r.data,r,this)&&n.appendData(r.data);return n}nextUntil(e,t,n=this){if(null===e)return;let r=this.allocateCursor(null,e);for(;null!==r.next;){let e=r.next;if(r.next=e.next,t.call(n,e.data,e,this))break}this.releaseCursor()}prevUntil(e,t,n=this){if(null===e)return;let r=this.allocateCursor(e,null);for(;null!==r.prev;){let e=r.prev;if(r.prev=e.prev,t.call(n,e.data,e,this))break}this.releaseCursor()}clear(){this.head=null,this.tail=null}copy(){let e=new am;for(let t of this)e.appendData(t);return e}prepend(e){return this.updateCursors(null,e,this.head,e),null!==this.head?(this.head.prev=e,e.next=this.head):this.tail=e,this.head=e,this}prependData(e){return this.prepend(am.createItem(e))}append(e){return this.insert(e)}appendData(e){return this.insert(am.createItem(e))}insert(e,t=null){if(null!==t)if(this.updateCursors(t.prev,e,t,e),null===t.prev){if(this.head!==t)throw new Error("before doesn't belong to list");this.head=e,t.prev=e,e.next=t,this.updateCursors(null,e)}else t.prev.next=e,e.prev=t.prev,t.prev=e,e.next=t;else this.updateCursors(this.tail,e,null,e),null!==this.tail?(this.tail.next=e,e.prev=this.tail):this.head=e,this.tail=e;return this}insertData(e,t){return this.insert(am.createItem(e),t)}remove(e){if(this.updateCursors(e,e.prev,e,e.next),null!==e.prev)e.prev.next=e.next;else{if(this.head!==e)throw new Error("item doesn't belong to list");this.head=e.next}if(null!==e.next)e.next.prev=e.prev;else{if(this.tail!==e)throw new Error("item doesn't belong to list");this.tail=e.prev}return e.prev=null,e.next=null,e}push(e){this.insert(am.createItem(e))}pop(){return null!==this.tail?this.remove(this.tail):null}unshift(e){this.prepend(am.createItem(e))}shift(){return null!==this.head?this.remove(this.head):null}prependList(e){return this.insertList(e,this.head)}appendList(e){return this.insertList(e)}insertList(e,t){return null===e.head||(null!=t?(this.updateCursors(t.prev,e.tail,t,e.head),null!==t.prev?(t.prev.next=e.head,e.head.prev=t.prev):this.head=e.head,t.prev=e.tail,e.tail.next=t):(this.updateCursors(this.tail,e.tail,null,e.head),null!==this.tail?(this.tail.next=e.head,e.head.prev=this.tail):this.head=e.head,this.tail=e.tail),e.head=null,e.tail=null),this}replace(e,t){"head"in t?this.insertList(t,e):this.insert(t,e),this.remove(e)}},im=100,lm=60,cm="    ";function um({source:e,line:t,column:n},r){function o(e,t){return s.slice(e,t).map(((t,n)=>String(e+n+1).padStart(l)+" |"+t)).join("\n")}let s=e.split(/\r\n?|\n|\f/),a=Math.max(1,t-r)-1,i=Math.min(t+r,s.length+1),l=Math.max(4,String(i).length)+1,c=0;(n+=(cm.length-1)*(s[t-1].substr(0,n-1).match(/\t/g)||[]).length)>im&&(c=n-lm+3,n=lm-2);for(let e=a;e<=i;e++)e>=0&&e<s.length&&(s[e]=s[e].replace(/\t/g,cm),s[e]=(c>0&&s[e].length>c?"\u2026":"")+s[e].substr(c,im-2)+(s[e].length>c+im-1?"\u2026":""));return[o(a,t),new Array(n+l+2).join("-")+"^",o(t,i)].filter(Boolean).join("\n")}function dm(e,t,n,r,o){return Object.assign(function(e,t){let n=Object.create(SyntaxError.prototype),r=new Error;return Object.assign(n,{name:e,message:t,get stack(){return(r.stack||"").replace(/^(.+\n){1,3}/,`${e}: ${t}\n`)}})}("SyntaxError",e),{source:t,offset:n,line:r,column:o,sourceFragment:e=>um({source:t,line:r,column:o},isNaN(e)?0:e),get formattedMessage(){return`Parse error: ${e}\n`+um({source:t,line:r,column:o},2)}})}function hm(e){let t=this.createList(),n=!1,r={recognizer:e};for(;!this.eof;){switch(this.tokenType){case 25:this.next();continue;case 13:n=!0,this.next();continue}let o=e.getNode.call(this,r);if(void 0===o)break;n&&(e.onWhiteSpace&&e.onWhiteSpace.call(this,o,t,r),n=!1),t.push(o)}return n&&e.onWhiteSpace&&e.onWhiteSpace.call(this,null,t,r),t}var mm=()=>{};function pm(e){return function(){return this[e]()}}function fm(e){let t=Object.create(null);for(let n in e){let r=e[n],o=r.parse||r;o&&(t[n]=o)}return t}var gm={parse(){return this.createSingleNodeList(this.SelectorList())}},vm={parse(){return this.createSingleNodeList(this.Selector())}},ym={parse(){return this.createSingleNodeList(this.Identifier())}},bm={parse(){return this.createSingleNodeList(this.Nth())}},xm={dir:ym,has:gm,lang:ym,matches:gm,is:gm,"-moz-any":gm,"-webkit-any":gm,where:gm,not:gm,"nth-child":bm,"nth-last-child":bm,"nth-last-of-type":bm,"nth-of-type":bm,slotted:vm,host:vm,"host-context":vm},km={};wu(km,{AnPlusB:()=>Pd,AttributeSelector:()=>Gd,ClassSelector:()=>Zd,Combinator:()=>eh,IdSelector:()=>uh,Identifier:()=>lh,NestingSelector:()=>fh,Nth:()=>vh,Percentage:()=>wh,PseudoClassSelector:()=>Ch,PseudoElementSelector:()=>Ih,Raw:()=>Lh,Selector:()=>Dh,SelectorList:()=>Rh,String:()=>zh,TypeSelector:()=>Gh});var wm=function(e){let t="",n="<unknown>",r=!1,o=mm,s=!1,a=new id,i=Object.assign(new dd,function(e){let t={context:Object.create(null),scope:Object.assign(Object.create(null),e.scope),atrule:fm(e.atrule),pseudo:fm(e.pseudo),node:fm(e.node)};for(let n in e.parseContext)switch(typeof e.parseContext[n]){case"function":t.context[n]=e.parseContext[n];break;case"string":t.context[n]=pm(e.parseContext[n])}return{config:t,...t,...t.node}}(e||{}),{parseAtrulePrelude:!0,parseRulePrelude:!0,parseValue:!0,parseCustomProperty:!1,readSequence:hm,consumeUntilBalanceEnd:()=>0,consumeUntilLeftCurlyBracket:e=>123===e?1:0,consumeUntilLeftCurlyBracketOrSemicolon:e=>123===e||59===e?1:0,consumeUntilExclamationMarkOrSemicolon:e=>33===e||59===e?1:0,consumeUntilSemicolonIncluded:e=>59===e?2:0,createList:()=>new am,createSingleNodeList:e=>(new am).appendData(e),getFirstListNode:e=>e&&e.first,getLastListNode:e=>e&&e.last,parseWithFallback(e,t){let n=this.tokenIndex;try{return e.call(this)}catch(e){if(s)throw e;let r=t.call(this,n);return s=!0,o(e,r),s=!1,r}},lookupNonWSType(e){let t;do{if(t=this.lookupType(e++),13!==t)return t}while(0!==t);return 0},charCodeAt:e=>e>=0&&e<t.length?t.charCodeAt(e):0,substring:(e,n)=>t.substring(e,n),substrToCursor(e){return this.source.substring(e,this.tokenStart)},cmpChar:(e,n)=>Ju(t,e,n),cmpStr:(e,n,r)=>Yu(t,e,n,r),consume(e){let t=this.tokenStart;return this.eat(e),this.substrToCursor(t)},consumeFunctionName(){let e=t.substring(this.tokenStart,this.tokenEnd-1);return this.eat(2),e},consumeNumber(e){let n=t.substring(this.tokenStart,td(t,this.tokenStart));return this.eat(e),n},eat(e){if(this.tokenType!==e){let t=od[e].slice(0,-6).replace(/-/g," ").replace(/^./,(e=>e.toUpperCase())),n=`${/[[\](){}]/.test(t)?`"${t}"`:t} is expected`,r=this.tokenStart;switch(e){case 1:2===this.tokenType||7===this.tokenType?(r=this.tokenEnd-1,n="Identifier is expected but function found"):n="Identifier is expected";break;case 4:this.isDelim(35)&&(this.next(),r++,n="Name is expected");break;case 11:10===this.tokenType&&(r=this.tokenEnd,n="Percent sign is expected")}this.error(n,r)}this.next()},eatIdent(e){(1!==this.tokenType||!1===this.lookupValue(0,e))&&this.error(`Identifier "${e}" is expected`),this.next()},eatDelim(e){this.isDelim(e)||this.error(`Delim "${String.fromCharCode(e)}" is expected`),this.next()},getLocation:(e,t)=>r?a.getLocationRange(e,t,n):null,getLocationFromList(e){if(r){let t=this.getFirstListNode(e),r=this.getLastListNode(e);return a.getLocationRange(null!==t?t.loc.start.offset-a.startOffset:this.tokenStart,null!==r?r.loc.end.offset-a.startOffset:this.tokenStart,n)}return null},error(e,n){let r=typeof n<"u"&&n<t.length?a.getLocation(n):this.eof?a.getLocation(function(e,t){for(;t>=0&&Mu(e.charCodeAt(t));t--);return t+1}(t,t.length-1)):a.getLocation(this.tokenStart);throw new dm(e||"Unexpected input",t,r.offset,r.line,r.column)}});return Object.assign((function(e,l){t=e,l=l||{},i.setSource(t,hd),a.setSource(t,l.offset,l.line,l.column),n=l.filename||"<unknown>",r=Boolean(l.positions),o="function"==typeof l.onParseError?l.onParseError:mm,s=!1,i.parseAtrulePrelude=!("parseAtrulePrelude"in l)||Boolean(l.parseAtrulePrelude),i.parseRulePrelude=!("parseRulePrelude"in l)||Boolean(l.parseRulePrelude),i.parseValue=!("parseValue"in l)||Boolean(l.parseValue),i.parseCustomProperty="parseCustomProperty"in l&&Boolean(l.parseCustomProperty);let{context:c="default",onComment:u}=l;if(!(c in i.context))throw new Error("Unknown context `"+c+"`");"function"==typeof u&&i.forEachToken(((e,n,r)=>{if(25===e){let e=i.getLocation(n,r),o=Yu(t,r-2,r,"*/")?t.slice(n+2,r-2):t.slice(n+2,r);u(o,e)}}));let d=i.context[c].call(i,l);return i.eof||i.error(),d}),{SyntaxError:dm,config:i.config})}({parseContext:{default:"SelectorList",selectorList:"SelectorList",selector:"Selector"},scope:{Selector:{onWhiteSpace:function(e,t){null!==t.last&&"Combinator"!==t.last.type&&null!==e&&"Combinator"!==e.type&&t.push({type:"Combinator",loc:null,name:" "})},getNode:function(){switch(this.tokenType){case 19:return this.AttributeSelector();case 4:return this.IdSelector();case 16:return 16===this.lookupType(1)?this.PseudoElementSelector():this.PseudoClassSelector();case 1:return this.TypeSelector();case 10:case 11:return this.Percentage();case 12:46===this.charCodeAt(this.tokenStart)&&this.error("Identifier is expected",this.tokenStart+1);break;case 9:switch(this.charCodeAt(this.tokenStart)){case 43:case 62:case 126:case 47:return this.Combinator();case 46:return this.ClassSelector();case 42:case 124:return this.TypeSelector();case 35:return this.IdSelector();case 38:return this.NestingSelector()}}}}},atrule:{},pseudo:xm,node:km}),Sm=(e,t)=>e.a===t.a?e.b===t.b?e.c-t.c:e.b-t.b:e.a-t.a,Cm=(e,t)=>0===Sm(e,t),Tm=(e,t)=>Sm(e,t)>0,Im=(e,t)=>Sm(e,t)<0,Am=(e,t="ASC")=>{let n=e.sort(Sm);return"DESC"===t?n.reverse():n},Om=(...e)=>Am(e,"ASC"),Em=(...e)=>Am(e,"DESC"),Lm=(...e)=>Em(...e)[0],Nm=e=>{let t={a:0,b:0,c:0};return e.children.forEach((e=>{switch(e.type){case"IdSelector":t.a+=1;break;case"AttributeSelector":case"ClassSelector":t.b+=1;break;case"PseudoClassSelector":switch(e.name.toLowerCase()){case"where":break;case"is":case"matches":case"-webkit-any":case"-moz-any":case"any":case"not":case"has":if(e.children){let n=Lm(..._m(e.children.first));t.a+=n.a,t.b+=n.b,t.c+=n.c}break;case"nth-child":case"nth-last-child":if(t.b+=1,e.children.first.selector){let n=Lm(..._m(e.children.first.selector));t.a+=n.a,t.b+=n.b,t.c+=n.c}break;case"host-context":case"host":if(t.b+=1,e.children){let n={type:"Selector",children:[]},r=!1;e.children.first.children.forEach((e=>!r&&("Combinator"===e.type?(r=!0,!1):void n.children.push(e))));let o=_m(n)[0];t.a+=o.a,t.b+=o.b,t.c+=o.c}break;case"after":case"before":case"first-letter":case"first-line":t.c+=1;break;default:t.b+=1}break;case"PseudoElementSelector":switch(e.name){case"slotted":if(t.c+=1,e.children){let n={type:"Selector",children:[]},r=!1;e.children.first.children.forEach((e=>!r&&("Combinator"===e.type?(r=!0,!1):void n.children.push(e))));let o=_m(n)[0];t.a+=o.a,t.b+=o.b,t.c+=o.c}break;case"view-transition-group":case"view-transition-image-pair":case"view-transition-old":case"view-transition-new":if(e.children&&"*"===e.children.first.value)break;t.c+=1;break;default:t.c+=1}break;case"TypeSelector":let n=e.name;n.includes("|")&&(n=n.split("|")[1]),"*"!==n&&(t.c+=1)}})),new Dm(t,e)},_m=e=>{if(!e)return[];let t=(e=>{if("string"==typeof e||e instanceof String)try{return wm(e,{context:"selectorList"})}catch(t){throw new TypeError(`Could not convert passed in source '${e}' to SelectorList: ${t.message}`)}if(e instanceof Object){if(e.type&&["Selector","SelectorList"].includes(e.type))return e;if(e.type&&"Raw"===e.type)try{return wm(e.value,{context:"selectorList"})}catch(e){throw new TypeError(`Could not convert passed in source to SelectorList: ${e.message}`)}throw new TypeError("Passed in source is an Object but no AST / AST of the type Selector or SelectorList")}throw new TypeError("Passed in source is not a String nor an Object. I don't know what to do with it.")})(e);if("Selector"===t.type)return[Nm(e)];if("SelectorList"===t.type){let e=[];return t.children.forEach((t=>{let n=Nm(t);e.push(n)})),e}},Dm=class{constructor(e,t=null){this.value=e,this.selector=t}get a(){return this.value.a}set a(e){throw new Error("Manipulating the port of the specificity directly is not allowed. Instead, directly set a new value")}get b(){return this.value.b}set b(e){throw new Error("Manipulating the port of the specificity directly is not allowed. Instead, directly set a new value")}get c(){return this.value.c}set c(e){throw new Error("Manipulating the port of the specificity directly is not allowed. Instead, directly set a new value")}selectorString(){return"string"==typeof this.selector||this.selector instanceof String?this.selector:this.selector instanceof Object&&"Selector"===this.selector.type?om(this.selector):""}toObject(){return this.value}toArray(){return[this.value.a,this.value.b,this.value.c]}toString(){return`(${this.value.a},${this.value.b},${this.value.c})`}toJSON(){return{selector:this.selectorString(),asObject:this.toObject(),asArray:this.toArray(),asString:this.toString()}}isEqualTo(e){return Cm(this,e)}isGreaterThan(e){return Tm(this,e)}isLessThan(e){return Im(this,e)}static calculate(e){return _m(e)}static compare(e,t){return Sm(e,t)}static equals(e,t){return Cm(e,t)}static lessThan(e,t){return Im(e,t)}static greaterThan(e,t){return Tm(e,t)}static min(...e){return((...e)=>Om(...e)[0])(...e)}static max(...e){return Lm(...e)}static sortAsc(...e){return Om(...e)}static sortDesc(...e){return Em(...e)}};const Pm=e=>(e=>{const t=new Map,n=e=>{const n=t.get(e);if(l(n))return n;{const n=Dm.calculate(e)[0];return t.set(e,n),n}};return F(e,((e,t)=>{const r=n(e.selector),o=n(t.selector);return Dm.compare(r,o)}))})((e=>R(e,(e=>(e=>-1!==e.selector.indexOf(","))(e)?(e=>{const t=e.selector.split(/,(?![^(]*\))/g);return A(t,(t=>{const n=t.trim();return{...e,selector:n}}))})(e):[e])))(e)),Rm=e=>{const t={};return O(e,(r=>{const o=e.getPropertyValue(r);n(o)&&(t[r]=o)})),t},Mm=e=>({selector:e.selectorText,styles:Rm(e.style)}),jm=e=>{const t=e.cssRules;return R(t,(e=>(e=>e.type===window.CSSRule.IMPORT_RULE)(e)?jm(e.styleSheet):(e=>e.type===window.CSSRule.STYLE_RULE)(e)?[Mm(e)]:[]))},Fm=(e,t)=>{const n=N(t,((e,t)=>({...e,...t.styles})),{});return Ue(n,((t,n)=>!T(e.dom.style,n)))};const Um=e=>{return t=(e=>{const t=e.dom.styleSheets;return Array.prototype.slice.call(t)})(e),R(t,jm);var t},Bm=(!0,(e,t,n)=>((e,t,r,o)=>{var s,a;((e,t,n)=>{((e,t,n)=>{const r=[],o=document.createTreeWalker(e.dom,NodeFilter.SHOW_ELEMENT);for(;l(o.nextNode());){const e=De.fromDom(o.currentNode),n=L(t,(t=>Tt(e,t.selector)));if(n.length>0){const t=Fm(e,n);zn(e,t),r.push(e)}}n&&O(r,(e=>On(e,"class")))})(t,Pm(e),n)})((s=Um(t),a=n,A(s,(e=>{const t=e.selector,n=a.hasOwnProperty(t)?a[t]:t;return{...e,selector:n}}))),r,true)})(0,e,t));const zm={p:"p, li[data-converted-paragraph]"},Hm=(e,t)=>{const n=Jt(e,"li[data-converted-paragraph]");if(O(n,(e=>{On(e,"data-converted-paragraph")})),t){const t=Jt(e,"li");O(t,(t=>{const n=(e=>{const t=(()=>{const t=De.fromTag("spn");return dn(e,t),t})();return{convertToPx:e=>{return Bn(t,"margin-left",e),n=Hn(t,"margin-left"),parseFloat(/-?\d+\.?\d*/.exec(n)[0]);var n},destroy:()=>vn(t)}})(e),r=((e,t)=>{const n=In(e,"data-tab-interval").getOr("36pt");return t.convertToPx(n)})(e,n),o=Wm(t,r,n).getOr({});(e=>{On(e,"data-list-level"),On(e,"data-text-indent-alt"),On(e,"data-border-margin"),Gn(e,"margin-left"),Gn(e,"text-indent"),Me(Vn(e),((t,n)=>{!$(n,"border")||"border-image"!==n&&"none"!==t.trim()&&"initial"!==t.trim()||Gn(e,n)}))})(t),n.destroy(),zn(t,o)}));const n=Jt(e,"ol,ul");O(n,(e=>{const t=Jt(e,"li");$n(e,"margin-top").isNone()&&k.from(t[0]).each((t=>{Bn(e,"margin-top",Hn(t,"margin-top"))})),$n(e,"margin-bottom").isNone()&&k.from(t[t.length-1]).each((t=>{Bn(e,"margin-bottom",Hn(t,"margin-bottom"))}))}))}On(e,"data-tab-interval")},Wm=(e,t,n)=>{const r=e=>In(e,"data-list-level").map((e=>parseInt(e,10))).getOr(1);return $n(e,"text-indent").bind((o=>$n(e,"margin-left").map((s=>{const a=$n(e,"list-style").exists((e=>W(e,"none"))),i=In(e,"data-border-margin").getOr("0px"),l=a?r(e)+1:r(e),c=n.convertToPx(s)+n.convertToPx(i),u=t*l,d=In(e,"data-text-indent-alt").getOr(o),h=n.convertToPx(d),m={},p=t/2*-1-h;p>0&&(m["text-indent"]=p+"px");const f=c-u-p;return m["margin-left"]=f>0?f+"px":"0px",m}))))},$m=e=>{const t=(n=De.fromDom(document.body),{play:(e,t,r)=>{const o=De.fromTag("div"),s=De.fromTag("iframe");zn(o,{display:"none"});const a=Lr(s,"load",(()=>{var n;a.unbind(),pu(s,e);const i=null===(n=s.dom.contentWindow)||void 0===n?void 0:n.document;if(void 0===i)throw new Error("sandbox iframe load event did not fire correctly");const l=De.fromDom(i),c=l.dom.body;if(void 0===c)throw new Error("sandbox iframe does not have a body");const u=De.fromDom(c),d=t(l,u);vn(o),setTimeout(g(r,d),0)}));hn(o,s),hn(n,o)}});var n;return n=>new Promise((r=>{t.play(n,((t,n)=>(((e,t,n)=>{const r=n.mergeInline();r&&(Bm(e,t,zm),(e=>{Ia(e)})(t)),Hm(t,r)})(t,n,{mergeInline:m(e)}),Jn(n))),r)}))},Vm=(e,t,n,r)=>{const o=e.html;return n.cleanDocument(o,t,r).then((e=>{if(null==(n=e)||0===n.length)return{response:Fr([],[]),bundle:Wr({})};{const n=void 0===r.sanitizer?sc(r.intraFlag.isMarked):r.sanitizer;return((e,t,n,r)=>{const o=e=>({response:e,bundle:Wr({})}),s=r.sanitizeHtml(t,"word");return $m(e)(s).then((e=>{const t=Kn(e),r=e=>o(Fr(t,e)),s=De.fromTag("div");fn(s,t);const a=L(It("img[src]",s),(e=>In(e,"src").exists((e=>$(e,"blob:")||$(e,"data:"))))),i=It("img[data-image-src]",s);if(0===a.length&&0===i.length)return r([]);if(n)return O(a,(e=>On(e,"id"))),(e=>Promise.all(A(e,(e=>{const t=e.dom;return pi(t).then((n=>n.toBlob().then((r=>((n,r)=>{const o=$(t.src,"blob:")?t.src:URL.createObjectURL(r),s=kn("image"),a=Dc.blob(s,n,o);return jc(a,e)})(n,r)))))}))))(a).then(r);{O(a,vn),O(i,vn);const e=Ht(s);return o(Br(e,[],"errors.local.images.disallowed"))}}))})(t,e,r.allowLocalImages,n)}var n}),(e=>(console.error("PowerPaste error code: WIM01"),{response:jr("errors.paste.process.failure"),bundle:Wr({})})))},Gm=yt(),qm=e=>{try{const t=e(),n=null!=t&&t.length>0?Kn(t):[];return Ir.value(n)}catch(e){return console.error("PowerPaste error code: PT01. Message: ",e),Ir.error("errors.paste.process.failure")}},Km=e=>e.fold($r,(e=>({response:Fr(e,[]),bundle:Wr({})}))),Jm=(e,t,n,r,o)=>qm((()=>{const s={type:r,merge:n,cleanFilteredInlineElements:o.cleanFilteredInlineElements,indentUseMargin:o.indentUseMargin,preprocessor:{defaultProtocol:o.defaultProtocol}};return((e,t,n,r)=>{Yi(n,r.preprocessor);const o=Jn(n),s=((e,t)=>{const n=t.merge,r=((e,t)=>{const n=t.browser.isFirefox(),r=Co([(s=n?bs:ys,a=!n,Io(((e,t)=>{const n=((e,t,n)=>t(De.fromDom(e.getNode())).fold((()=>[e]),(t=>{const r=e.type()===mo,o=[go(t.dom,r)];return!r&&n&&o.push(go(t.dom,!0)),o})))(t,s,a);e.emitTokens(n)}),d))]),o=n?d:la;var s,a;return{annotate:[e.type===xs.Word?r:d],local:[o]}})(t,e);return P([r.local,Ba(t),Ha(t),r.annotate,(o=n?[]:t.cleanFilteredInlineElements,[Ea,Na,va(o)]),za(t),$a(t,e),[js],[ga],[ea],[ta],Ua(t),[wa,Sa,ca,Ca],[ua],[da],[ha],[ba],Va(t),[ka],[pa],[Ia],Ga(t),[ya],[na]]);var o})(t,r);return To(e,o,s)})(e,Gm,t,s)})),Ym=(e,t)=>{const n=qm((()=>((e,t)=>((e,t)=>To(e,t,[ta,Ca]))(e,Jn(t)))(e,t)));return Km(n)},Xm=(e,t,n,r,o)=>Jm(e,t,r,n,o).fold(Vr,(e=>Promise.resolve({response:Fr(e,[]),bundle:Wr({})}))),Zm=e=>"\n"===e||"\r"===e,Qm=(e,t)=>{const n=(e=>{const t=De.fromTag("div");return((e,t)=>{e.dom.textContent=t})(t,e),Jn(t)})(e),r=((e,t)=>{const n=((e,t)=>{const n=e.replace(/\t/g,(r=t)<=0?"":new Array(r+1).join(" "));var r;const o=N(n,((e,t)=>(e=>-1!==" \f\t\v".indexOf(e))(t)||"\xa0"===t?e.pcIsSpace||""===e.str||e.str.length===n.length-1||((e,t)=>t<e.length&&t>=0&&Zm(e[t]))(n,e.str.length+1)?{pcIsSpace:!1,str:e.str+"\xa0"}:{pcIsSpace:!0,str:e.str+" "}:{pcIsSpace:Zm(t),str:e.str+t}),{pcIsSpace:!1,str:""});return o.str})(e,t).replace(/^[\r\n]*|[\r\n]*$/g,"").split(/(?:\r?\n){2}/),r=A(n,(e=>e.split(/\n|\r\n/).join("<br />")));return 1===r.length?r[0]:A(r,(e=>"<p>"+e+"</p>")).join("")})(n,t),o=Kn(r);return Fr(o,[])},ep=e=>(t,n)=>((e,t)=>{const n=yc(e).getOrDie("Required text input for Text Handler");return{response:n.text.length>0?Qm(n.text,t):Ur(),bundle:Wr({})}})(t,e),tp=e=>(t,n)=>{const r=(t,r)=>{const o=De.fromTag("div");fn(o,t),Yi(o,e);const s=Ht(o);return{response:Fr(s,r),bundle:n.bundle}},o=m(n);return Mr(n.response,o,r,o,r)},np=(e,t,n)=>(r,o)=>{const s=fc(r).getOrDie("Wrong input type for HTML handler").container,a=Rt(t),i=o.bundle;return uu(i)?Ym(a,s):(e(s),((e,t,n,r,o)=>{const s=Jm(e,t,n,r,o);return Km(s)})(a,s,au(i),du(i),n))},rp=(e,t,n)=>(r,o)=>{const s=o.bundle;return(e=>({handle:(t,n)=>e.proxyBin.fold((()=>(console.error(t),Promise.resolve({response:Ur(),bundle:Wr({})}))),n)}))(s).handle("There was no proxy bin setup. Ensure you have run proxyStep first.",(r=>{const o=au(s),a=du(s),i=uu(s),l=Rt(e);return i?((e,t,n,r)=>{const o=xs.Html,s=t.findClipboardTags(Ht(n)).getOr([]);return O(s,vn),Xm(e,n,o,!0,r)})(l,t,r,n):((e,t,n,r,o)=>Xm(e,t,r,n,o))(l,r,o,a,n)}))},op=(e,t)=>(n,r)=>{const o=vc(n).getOrDie("Wrong input type for Word Import handler"),s=iu(r.bundle);return Vm(o,s,e,t)},sp=(e,t)=>Gr,ap=e=>(t,n)=>{const r=Hr(n.bundle,Wr(e));return{response:n.response,bundle:r}},ip=(e,t)=>(e=>{const t=e=>({response:Fr([e],[]),bundle:Wr({})});return(e=>(e=>{const t=Jt(e,"img");return Promise.all(A(t,ki)).then((()=>e))})(e))(e).then(t).catch((()=>t(e)))})(fc(e).getOrDie("Wrong input type for HTML handler").container);yt();var lp=(e,t,n,r)=>(o,s)=>{const a=s.response;return new Promise(((o,s)=>{const i=e(n);i.events.after.bind((e=>{const n=e.container;t(n),_n(n,gr());const s=(u=Jn(n)).indexOf("<o:p>")>=0||u.indexOf("mso-list")>=0||u.indexOf("p.MsoNormal, li.MsoNormal, div.MsoNormal")>=0||u.indexOf("MsoListParagraphCxSpFirst")>=0||u.indexOf("<w:WordDocument>")>=0,i=((e,t)=>{const n=Jt(t,"*[id]");return I(n,(e=>In(e,"id").exists((e=>$(e,"docs-internal-guid-")))))})(0,n),l=Ht(n),c=r.findClipboardTags(l).isSome();var u;o({response:a,bundle:Wr({isWord:s,isGoogleDocs:i,isInternal:c,proxyBin:n})})})),i.run()}))};const cp=Nt([{unsupported:["id","message","isEquation","attrs"]},{supported:["id","contentType","blob","isEquation","attrs"]}]),up={unsupported:cp.unsupported,supported:cp.supported,cata:(e,t,n)=>e.fold(t,n)},dp=(e,t,n)=>t.indexOf(e,n),hp=(e,t,n,r,o,s,a)=>-1===e||-1===t?k.none():k.some({start:e,end:t,bower:n,regex:r,idRef:o,isEquation:s,attrs:a}),mp=(e,t,n)=>e.substring(t,n),pp=(e,t)=>{if(-1===t)return t;let n=0;const r=e.length;do{const o=e.indexOf("{",t),s=e.indexOf("}",t);if(s>o&&-1!==o?(t=o+1,++n):(o>s||o<0)&&-1!==s&&(t=s+1,--n),t>r||-1===s)return-1}while(n>0);return t},fp=(e,t,n,r,o)=>{const s=mp(e,n,r),a=((e,t)=>{const n=dp("\\picscalex",e,t),r=dp("\\bliptag",e,n);return n>-1&&n<r?k.from(e.substring(n,r)):k.none()})(e,n);return hp(n,r,s,/[^a-fA-F0-9]([a-fA-F0-9]+)\}$/,"i",o,a)},gp=(e,t,n,r,o)=>{const s=mp(e,n,r);return hp(n,r,s,/([a-fA-F0-9]{64,})(?:\}.*)/,"s",o,k.none())},vp=(e,t)=>((e,t)=>{const n=dp("{\\pict{",e,t),r=pp(e,n),o=dp("{\\shp{",e,t),s=pp(e,o),a=dp("{\\mmathPict{",e,t),i=pp(e,a),l=-1!==a&&a<n&&i>r,c=g(gp,e,t,o,s,-1!==a&&a<o&&i>s),u=g(fp,e,t,n,r,l);return-1===n&&-1===o?k.none():-1===n?c():-1===o||o<n&&s>r?u():n<o&&r>s?c():n<o?u():o<n?c():k.none()})(e,t),yp=e=>{let t=[];const n=()=>e.length,r=e=>{const n=(e=>{const t=e.bower,n=e.regex,r=e.isEquation,o=e.attrs;return(e=>{const t=/\\shplid(\d+)/.exec(e);return null!==t?k.some(t[1]):k.none()})(t).map((s=>{const a=e.idRef+s;return(e=>e.indexOf("\\pngblip")>=0?Ir.value("image/png"):e.indexOf("\\jpegblip")>=0?Ir.value("image/jpeg"):Ir.error("errors.imageimport.unsupported"))(t).fold((e=>up.unsupported(a,e,r,o)),(e=>((e,t)=>{const n=e.match(t);return n&&n[1]&&n[1].length%2==0?Ir.value(n[1]):Ir.error("errors.imageimport.invalid")})(t,n).fold((e=>up.unsupported(a,e,r,o)),(t=>up.supported(a,e,((e,t)=>{if(0===e.length)throw new Error("Zero length content passed to Hex conversion");const n=(e=>{const t=new Array(e.length/2);for(let n=0;n<e.length;n+=2){const r=e.substr(n,2);t[Math.floor(n/2)]=parseInt(r,16)}return t})(e),r=new Uint8Array(n);return new Blob([r],{type:t})})(t,e),r,o)))))}))})(e);return t=t.concat(n.toArray()),e.end};let o=0;for(;o<e.length;)o=vp(e,o).fold(n,r);return t},bp=e=>up.cata(e,((e,t,n)=>e),((e,t,n,r,o)=>e)),xp=e=>up.cata(e,((e,t,n)=>n),((e,t,n,r,o)=>r)),kp=e=>up.cata(e,((e,t,n)=>Ir.error(t)),((e,t,n,r,o)=>Ir.value(n))),wp=(e,t)=>{const n=new RegExp("\\\\pic"+t+"(\\-?\\d+)\\\\").exec(e)[1];return parseInt(n,10)},Sp=(e,t,n,r,o)=>{const s=[],a=[];let i=!1;const l=R(e,((e,l)=>{const c=Tn(e,"data-image-id");return On(e,"rtf-data-image"),On(e,"data-image-id"),On(e,"data-ms-equation"),o||On(e,"data-image-src"),"unsupported"===c?(i=!0,Sn(e,"alt",n("errors.imageimport.unsupported")),[]):_(t,((e,t)=>r(e,t,c,l))).fold((()=>(console.log("WARNING: unable to find data for image ",e.dom),i=!0,Sn(e,"alt",n("errors.imageimport.unsupported")),[])),(t=>kp(t).fold((t=>(i=!0,console.error("PowerPaste error code: RTF04"),Sn(e,"alt",n(t)),[])),(n=>{var r;return s.push(e),a.push((r=t,up.cata(r,((e,t,n)=>k.none()),((e,t,n,r,o)=>o)))),o&&On(e,"data-image-src"),[n]}))))}));return{blobs:l,filteredImages:s,imageAttrs:a,failedImage:i}},Cp=(e,t,n,r,o)=>{const s=(e=>N(e,((e,t)=>{const n=bp(t),r=xp(t);return D(e,(e=>!(r||xp(e))&&bp(e)===n)).fold((()=>e.concat([t])),(n=>kp(e[n]).isValue()?e:e.slice(0,n).concat(e.slice(n+1)).concat([t])))}),[]))(t),{pass:a,fail:i}=E(s,(e=>!xp(e))),{pass:l,fail:c}=E(e,(e=>!(e=>"true"===Tn(e,"data-ms-equation"))(e))),u=Sp(l,a,r,((e,t,n,r)=>bp(e)===n),o.keepSrc),d=Sp(c,i,r,((e,t,n,r)=>t===r),o.keepSrc),h=u.filteredImages.concat(d.filteredImages),m=u.imageAttrs.concat(d.imageAttrs),p=u.blobs.concat(d.blobs),f=u.failedImage||d.failedImage;Mc(p).then((e=>{((e,t)=>e.length===t.length?Promise.all(A(e,((e,n)=>((e,t)=>t.fold((()=>Promise.resolve(e)),(t=>Dc.cata(e,((n,r,o)=>r.toCanvas().then((s=>{const a=De.fromDom(s),i=In(a,"width").map((e=>parseInt(e,10))).getOr(1),l=In(a,"height").map((e=>parseInt(e,10))).getOr(1),c=((e,t,n)=>{const r=g(wp,e),o=r("wgoal"),s=r("hgoal"),a=o/t,i=s/n,l=r("cropl"),c=r("cropt");return{cropl:l/a,cropt:c/i,cropw:(o-l-r("cropr"))/a,croph:(s-c-r("cropb"))/i}})(t,i,l);return i===c.cropw&&l===c.croph&&0===c.cropl&&0===c.cropt?Promise.resolve(e):di(r,c.cropl,c.cropt,c.cropw,c.croph).then((e=>e.toBlob().then((t=>{URL.revokeObjectURL(o);const r=URL.createObjectURL(t);return Dc.blob(n,e,r)}))))}))),((t,n,r)=>Promise.resolve(e))))))(e,t[n])))):Promise.resolve(e))(e,m).then((e=>{const t=Uc(e,h);n(t,f)}))}))},Tp=e=>Jt(e,"[rtf-data-image]"),Ip=e=>{const t=e.translations,n=Ct({insert:St(["elements","correlated"]),incomplete:St(["elements","correlated","message"])});return{events:n.registry,processRtf:(e,r,o,s)=>{const a=(e=>{const t=e.replace(/\r/g,"").replace(/\n/g,"");return yp(t)})(o),i=Tp(e);Cp(i,a,((t,o)=>{const s=Ht(e),a=t.concat(r);o?(console.error("PowerPaste error code: RTF01"),n.trigger.incomplete(s,a,"errors.imageimport.failed")):n.trigger.insert(s,a)}),t,s)}}};const Ap=e=>{const t=()=>Promise.resolve(e);return Dc.cata(e.asset,((n,r,o)=>/(tiff|pdf)$/.test(r.getType())?(e=>((e,t,n)=>e.toAdjustedBlob(t,n))(e,"image/png").then(Pc).then(k.some).catch((e=>(console.warn(e),k.none()))))(r).then((t=>t.map((t=>{const n=e.image;return URL.revokeObjectURL(o),Fc(t,n),jc(t,n)})).getOr(e))):t()),t)};var Op=(e,t)=>{const n=(e,n)=>Promise.all(A(e,Ap)).then((e=>({response:n(e),bundle:t.bundle})));return Mr(t.response,Vr,((e,t)=>n(t,(t=>Fr(e,t)))),(()=>Promise.resolve(t)),((e,t,r)=>n(t,(t=>(console.error("PowerPaste error code:  IMG01"),Br(e,t,r))))))};const Ep=(e,t)=>e.isSupported?t.getWordData():k.none(),Lp=e=>e.getNative(),Np=e=>e.getImage(),_p=e=>e.getHtml(),Dp=e=>e.getText(),Pp=e=>e.getOnlyText(),Rp=e=>e.getGoogleDocsData(),Mp=e=>e.getVoid(),jp=(e,t,n,r)=>({label:e,getAvailable:t,steps:n,capture:m(r)}),Fp=(e,t,n,r)=>({label:e,getAvailable:t,steps:n,capture:m(r)}),Up=(e,t,n,r)=>{return jp(Ec.native,_p,[(o=t.intraFlag,(e,t)=>{const n=fc(e).getOrDie("Wrong input type for HTML handler"),r=o.findClipboardTags(Ht(n.container));r.each((e=>{O(e,vn)}));const s=r.isSome();return{response:t.response,bundle:Wr({isInternal:s})}}),mu(e,t),np(n,r,t),qc(t),Op],!0);var o},Bp=(e,t,n)=>{return jp(Ec.msoffice,g(Ep,e),[ap({isWord:!0}),hu(t,n),op(e,n),(r=n,(e,t)=>new Promise(((n,o)=>{const s=Ip(r),a=e=>n({response:e,bundle:Wr({})});s.events.insert.bind((e=>{a(Fr(e.elements,e.correlated))})),s.events.incomplete.bind((e=>{console.error("PowerPaste error code: RTF02"),a(Br(e.elements,e.correlated,e.message))}));const i=vc(e).getOrDie("Word input required for rtf data"),l=e=>{const n=()=>Promise.resolve(t),o=(t,n)=>{const o=De.fromTag("div");return fn(o,t),e.fold((()=>{const e=Tp(o);return e.length>0?(e=>{O(e,vn);const t=Ht(o);return console.error("PowerPaste error code: RTF03"),a(Br(t,n,"errors.imageimport.failed"))})(e):(()=>{const e=Ht(o);return a(Fr(e,n))})()}),(e=>{s.processRtf(o,n,e,r)}))};return Mr(t.response,n,o,n,o)};((e,t)=>{const n=Pe(t);if(n.length!==Zi.length)throw new Error("Partial match");B(n,(n=>Mn(e.discriminator===n,t[n]))).getOrDie("Must find branch for constructor: "+e.discriminator)(e.data)})(i.rtf,{disabled:()=>{l(k.none())},fromClipboard:e=>{l(!0===r.allowLocalImages?k.some(e.rtf):k.none())}})}))),Op],!0);var r},zp=(e,t,n,r)=>jp(Ec.googledocs,Rp,[ap({isGoogleDocs:!0}),hu(e,t),ip,np(n,r,t),su,qc(t),Op],!0),Hp=e=>jp(Ec.image,Np,[!1===e.allowLocalImages?(e,t)=>Vr("errors.local.images.disallowed"):(e,t)=>(e=>{const t=L(e,(e=>"file"===e.kind&&/image/.test(e.type))),n=N(t,((e,t)=>{const n=t.getAsFile();return(e=>null!==e)(n)?e.concat(n):e}),[]);return Mc(n).then((e=>{const t=(e=>{const t=[],n=[];return O(e,(e=>Dc.cata(e,((r,o,s)=>{const a=De.fromTag("img");Sn(a,"src",s),t.push(a),n.push(jc(e,a))}),((e,t,n)=>{console.error("Internal error: Paste operation produced an image URL instead of a Data URI: ",t)})))),Fr(t,n)})(e);return{response:t,bundle:Wr({})}}))})(gc(e).getOrDie("Must have image data for images handler").images),Op],!0),Wp=e=>jp(Ec.plaintext,Pp,[ep(e.tabSpaces),tp({defaultProtocol:e.defaultProtocol})],!0),$p=(e,t)=>jp(Ec.text,Dp,[ep(e),tp({defaultProtocol:t})],!0);var Vp=Object.freeze({__proto__:null,loadScript:(e,t)=>tinymce.Resource.load(e,t)});const Gp={"cement.dialog.paste.title":"Paste Formatting Options","cement.dialog.paste.instructions":"Choose to keep or remove formatting in the pasted content.","cement.dialog.paste.merge":"Keep formatting","cement.dialog.paste.clean":"Remove formatting","error.code.images.not.found":"The images service was not found: (","error.imageupload":"Image failed to upload: (","error.full.stop":").","errors.local.images.disallowed":"Local image paste has been disabled. Local images have been removed from pasted content.","errors.imageimport.failed":"Some images failed to import.","errors.imageimport.unsupported":"Unsupported image type.","errors.imageimport.invalid":"Image is invalid."},qp=e=>tinymce.translate((e=>Gp[e])(e)),Kp=e=>{const t=document.createElement("div");return t.appendChild(e.cloneNode(!0)),t.innerHTML},Jp="x-tinymce/html",Yp=m(Jp),Xp="\x3c!-- "+Jp+" --\x3e",Zp=e=>-1!==e.indexOf(Xp),Qp=e=>/^https?:\/\/[\w\-\/+=.,!;:&%@^~(){}?#]+$/i.test(e),ef=e=>{const t=/^<a href="([^"]+)">([^<]+)<\/a>$/.exec(e);return k.from(t).bind((t=>{const n={url:t[1],html:e};return Mn(t[1]===t[2],n)}))},tf=(e,t,n)=>(e=>"extra"in e.undoManager)(e)?(e.undoManager.extra((()=>{of(e,t)}),n),k.some(!0)):k.none(),nf=(e,t)=>ef(t).bind((t=>!1===e.selection.isCollapsed()&&Qp(t.url)?((e,t)=>tf(e,t.html,(()=>{e.execCommand("mceInsertLink",!1,t.url)})))(e,t):k.none())),rf=(e,t)=>ef(t).bind((t=>((e,t)=>{const n=e.toLowerCase();return Qp(n)&&I(t,(e=>V(n,`.${e.toLowerCase()}`)))})(t.url,pe(e))?((e,t)=>tf(e,t.html,(()=>{e.insertContent('<img src="'+t.url+'">')})))(e,t):k.none())),of=(e,t)=>(e.insertContent(t,{merge:ee(e),paste:!0}),k.some(!0)),sf=(e,t)=>e.hasEventListeners(t),af=(e,t,n,r,o)=>{const s=(e=>e.replace(Xp,""))(t),a=((e,t,n,r,o)=>sf(e,"PastePreProcess")?((e,t,n,r,o)=>{const s=((e,t)=>e.dispatch("PastePreProcess",t))(e,{internal:n,content:t,source:r,mode:o}),a=s.isDefaultPrevented();return a?{cancelled:a}:{cancelled:a,content:s.content}})(e,t,n,r,o):{cancelled:!1,content:t})(e,s,n,r,o);return a.cancelled?a:((e,t,n,r,o)=>sf(e,"PastePostProcess")?((e,t,n,r,o)=>{const s=e.dom.add(e.getBody(),"div",{style:"display:none"},t),a=((e,t)=>e.dispatch("PastePostProcess",t))(e,{internal:n,node:s,source:r,mode:o}),i=a.isDefaultPrevented();if(i)return{cancelled:i};const l=a.node.innerHTML;return e.dom.remove(s),{cancelled:i,content:l}})(e,t,n,r,o):{content:t,cancelled:!1})(e,a.content,n,r,o)},lf=(e,t,n,r,o,s)=>{const a=ge(),i=(null==r?void 0:r.jsUrl)?r.jsUrl:("/js",n.replace(/\/$/,"")+"/"+"/js".replace(/^\//,""));const c=(e,t)=>(e.undoManager.transact((()=>{var n;of(e,t),n=e.getBody(),O(A(n.getElementsByTagName("*"),De.fromDom),(e=>{An(e,"data-mce-style")&&!An(e,"style")&&In(e,"data-mce-style").each((t=>Sn(e,"style",t)))}))})),k.some(!0)),u=()=>{a.on((t=>e.selection.moveToBookmark(t))),a.clear()},h=te(e),m={baseUrl:i,cacheSuffix:re(e),officeStyles:ce(e),htmlStyles:de(e),gdocsStyles:ue(e),translations:qp,allowLocalImages:le(e),pasteBinAttrs:{"data-mce-bogus":"all",class:"mce-pastebin"},intraFlag:{isMarked:Zp,findClipboardTags:e=>{const t=L(e,(e=>Ce(e)&&W(Ne(e),Yp())));return t.length?k.some(t):k.none()}},keepSrc:ie(e),cleanFilteredInlineElements:he(e),indentUseMargin:se(e),sanitizer:o,tabSpaces:h,defaultProtocol:me(e)},p=((e,t,n,r,o=Pr)=>{const s={...{...fr,sanitizer:sc(r.intraFlag.isMarked)},...Ue(r,l)},a=wt(o,s.baseUrl,s.cacheSuffix),i=mr(s.pasteBinAttrs),c=[Wp(s),Bp(a,t,s),zp(t,s,n,e),Up(t,s,n,e),Hp(s),$p(s.tabSpaces,s.defaultProtocol)],u=((e,t,n,r,o)=>Fp(Ec.fallback,Lp,[lp(r,n,o,t.intraFlag),mu(e,t),rp(o,t.intraFlag,t),qc(t),Op],!1))(t,s,n,i,e);return Nc(c,u,s.sanitizer)})(De.fromDom(e.getBody()),(e=>({createDialog:()=>{let t="";const n=ge(),r=(()=>{const e=fe([{text:"Close",name:"close",type:"custom",primary:!0}]),t=fe({});return{setButtons:n=>{const r={},o=A(n,(e=>{const t=e.text;return r[t.toLowerCase()]=e.click,{text:t,name:t.toLowerCase(),type:"custom"}}));t.set(r),e.set(o)},getButtons:e.get,getAction:e=>{const n=t.get();return ze(n,e)?k.some(n[e]):k.none()}}})(),o=Ct({close:St([])}),s=()=>{o.trigger.close()};return{events:o.registry,setTitle:e=>t=e,setContent:e=>n.set(e),setButtons:e=>{r.setButtons(e)},show:()=>{n.on((n=>{const o=Kp(n.dom),a={title:t,body:{type:"panel",items:[{type:"htmlpanel",html:o}]},initialData:{},buttons:r.getButtons(),onCancel:s,onAction:(e,t)=>{r.getAction(t.name).each(y),e.close()}};e.windowManager.open(a)}))},hide:d,destroy:()=>{n.clear()},reflow:d}}}))(e).createDialog,d,m,Vp),f=((e=ac,t=fr.tabSpaces)=>Nc([$p(t,fr.defaultProtocol)],Fp(Ec.discard,Mp,[sp],!0),e))(o,h);return O([p,f],(t=>{t.events.cancel.bind((()=>{u()})),t.events.error.bind((t=>{u(),e.notificationManager?e.notificationManager.open({text:qp(t.message),type:"error"}):((e,t)=>{const n={title:"Error",body:{type:"panel",items:[{type:"htmlpanel",html:t}]},initialData:{},buttons:[{text:"OK",type:"cancel",name:"ok",primary:!0}]};e.windowManager.open(n)})(e,qp(t.message))})),t.events.insert.bind((t=>{const n=A(t.elements,(e=>Kp(e.dom))).join("");e.focus(),s.importImages(t.assets).get((()=>{u();const r=af(e,n,t.isInternal,t.source,t.mode);r.cancelled||(((e,t)=>{((e,t,n)=>{B(n,(n=>n(e,t)))})(e,t,(ne(e)?[nf,rf]:[]).concat([c]))})(e,r.content),oe(e)&&s.uploadImages(t.assets))}))})),t.events.block.bind((t=>{e.setProgressState(t.state)}))})),{pasteHtml:e=>p.pasteCustom(((e,t=ac)=>({getWordData:()=>k.from(e).filter(xc).map((e=>hc({html:e,rtf:Qi()}))),getGoogleDocsData:()=>k.from(e).filter(kc).map((e=>t.sanitizeHtml(e,"googledocs"))).map(Ac),getImage:k.none,getHtml:()=>k.some(Ac(t.sanitizeHtml(e))),getText:k.none,getNative:v("There is no native event"),getOnlyText:k.none,getVoid:v("There is no paste event")}))(e,o)),pasteText:e=>f.pasteCustom(((e,t=ac)=>({getWordData:k.none,getGoogleDocsData:k.none,getImage:k.none,getHtml:k.none,getText:()=>k.some(mc({text:t.sanitizeText(e)})),getNative:v("There is no native event"),getOnlyText:k.none,getVoid:v("There is no paste event")}))(e,o)),pasteEvent:n=>{(e=>{return(t=e,k.from(t.clipboardData).bind((e=>k.from(e.getData("text/html"))))).bind((e=>(e=>W(e,"<google-sheets-html-origin"))(e)?k.some("googlesheets"):(e=>W(e," data-ccp-props=")&&W(e," paraid=")&&/font-family:.+?_MSFontService(&quot;)?[,;]/.test(e))(e)?k.some("mswordonline"):(e=>W(e,"<meta name=ProgId content=Excel.Sheet>")&&!W(e,'="urn:schemas-microsoft-com:office:'))(e)?k.some("msexcelonline"):k.none()));var t})(n).each((t=>{((e,t)=>{e.dispatch("PowerPasteTempStats",{source:t})})(e,t)})),a.isSet()||a.set(e.selection.getBookmark(1)),(t.isText()?f:p).paste(n),t.reset(),n.stopImmediatePropagation()}}},cf=(e,t)=>{const n=tinymce.html.DomParser({},e.schema).parse(t,{forced_root_block:!1,isRootContent:!0});return tinymce.html.Serializer({validate:!0},e.schema).serialize(n)},uf=(e,t)=>{e.dom.bind(t,"drop dragstart dragend dragover dragenter dragleave dragdrop draggesture",(e=>{e.preventDefault(),e.stopImmediatePropagation()}))},df=e=>{var t,n;return I(null!==(n=null===(t=e.dataTransfer)||void 0===t?void 0:t.items)&&void 0!==n?n:[],(e=>$(e.type,"image/")))},hf=(e,t,n,r)=>{const o=tinymce.dom.RangeUtils;let s;const a=t=>{var n,r;const s=o.getCaretRangeFromPoint(null!==(n=t.clientX)&&void 0!==n?n:0,null!==(r=t.clientY)&&void 0!==r?r:0,e.getDoc());e.focus(),l(s)&&e.selection.setRng(s)};e.on("dragstart dragend",(e=>{s="dragstart"===e.type})),e.on("dragover dragend dragleave",(e=>{s||df(e)||(e.preventDefault(),a(e))}));const i=(e,t)=>t in e&&e[t].length>0;e.on("drop",(t=>{if(!s&&!df(t)){a(t);const o=(t=>{var n,r,o;const s=null!==(o=null!==(n=t.target.files)&&void 0!==n?n:null===(r=t.dataTransfer)||void 0===r?void 0:r.files)&&void 0!==o?o:[],a=pe(e);return L(s,(e=>$(e.type,"image/")&&I(a,(t=>(e=>{const t=e.toLowerCase(),n={jpg:"jpeg",jpe:"jpeg",jfi:"jpeg",jif:"jpeg",jfif:"jpeg",pjpeg:"jpeg",pjp:"jpeg",svg:"svg+xml"};return ze(n,t)?"image/"+n[t]:"image/"+t})(t)===e.type))))})(t);if(o.length>0)return(t=>{Mc(t).then((t=>{const n=(e=>A(e,(e=>{const t=De.fromTag("img"),n=Dc.cata(e,r.getLocalURL,((e,t,n)=>t));return Sn(t,"src",n),t.dom.outerHTML})).join(""))(t),o=af(e,n,!1,"imagedrop","auto");o.cancelled||(e.insertContent(o.content,{merge:ee(e)}),oe(e)&&r.uploadImages(t))}))})(o),void t.preventDefault();const s=(e=>{const t={};if(e&&e.types)for(let n=0;n<e.types.length;n++){const r=e.types[n];t[r]=e.getData(r)}return t})(t.dataTransfer);i(s,"text/html")?(n.pasteHtml(s["text/html"]),t.preventDefault()):i(s,"text/plain")&&!(e=>{const t=e["text/plain"];return!!t&&0===t.indexOf("file://")})(s)&&(n.pasteText(s["text/plain"]),t.preventDefault())}}))};tinymce.PluginManager.requireLangPack("powerpaste","ar,bg_BG,ca,cs,da,de,el,es,eu,fa,fi,fr_FR,he_IL,hi,hr,hu_HU,id,it,ja,kk,ko_KR,ms,nb_NO,nl,pl,pt_BR,pt_PT,ro,ru,sk,sl_SI,sv_SE,th_TH,tr,uk,vi,zh_CN,zh_TW"),tinymce.PluginManager.add("powerpaste",(e=>(t,r)=>{if(((e,t)=>!!e&&-1===((e,t)=>{const n=J(e.major,t.major);if(0!==n)return n;const r=J(e.minor,t.minor);if(0!==r)return r;const o=J(e.patch,t.patch);return 0!==o?o:0})((e=>X((e=>[e.majorVersion,e.minorVersion].join(".").split(".").slice(0,3).join("."))(e)))(e),X(t)))(tinymce,"6.0.0"))return void console.error('The "powerpaste" plugin requires at least version 6.0.0 of TinyMCE.');(e=>{const t=e.options.register,r=e=>{const t=c(e)||(e=>n(e)&&T(["clean","merge","prompt"],e))(e);return t?{value:e,valid:t}:{valid:!1,message:"Must be prompt, clean or merge."}};t("powerpaste_block_drop",{processor:"boolean",default:!1}),t("powerpaste_keep_unsupported_src",{processor:"boolean",default:!1}),t("powerpaste_allow_local_images",{processor:"boolean",default:!0}),t("powerpaste_word_import",{processor:r,default:"prompt"}),t("powerpaste_googledocs_import",{processor:r,default:"prompt"}),t("powerpaste_html_import",{processor:r,default:"clean"}),t("powerpaste_clean_filtered_inline_elements",{processor:"string[]",default:[]}),e.options.isRegistered("link_default_protocol")||t("link_default_protocol",{processor:"string",default:"https"})})(t),(e=>{const t=e.options.set;t("paste_block_drop",!1),t("paste_remove_styles_if_webkit",!1)})(t);const o=(e=>{const t=fe(Q(e)),n=fe(!1);return e.on("keydown",(e=>{(e=>tinymce.util.VK.metaKeyPressed(e)&&86===e.keyCode&&e.shiftKey)(e)&&n.set(!0)})),e.on("PastePlainTextToggle",(e=>{t.set(e.state)})),{reset:()=>{n.set(!1)},isText:()=>n.get()||t.get()}})(t),s=(e=>{const t=(e,t)=>m(e+"."+(e=>{const t=e.toLowerCase(),n={"image/jpeg":"jpg","image/jpg":"jpg","image/gif":"gif","image/png":"png","image/apng":"apng","image/avif":"avif","image/svg+xml":"svg","image/webp":"webp","image/bmp":"bmp","image/tiff":"tiff"};return ze(n,t)?n[t]:"dat"})(t)),n=(n,r,o,s)=>kr((a=>{fi(r).then((r=>{e.editorUpload.blobCache.add({id:m(n),name:m(n),filename:t(n,r.type),blob:m(r),base64:m(o.split(",")[1]),blobUri:m(s),uri:m(void 0)}),a(r)}))}));return{importImages:e=>{const t=R(e,(e=>Dc.cata(e,((e,t,r)=>{const o=gi(t);return[n(e,t,o,r)]}),m([]))));return wr(t)},uploadImages:()=>{e.uploadImages()},getLocalURL:(e,t,n)=>gi(t)}})(t),a=(e=>{const t=sc(Zp),n=(e=>({sanitizeHtml:g(cf,e),sanitizeText:p}))(e);return{sanitizeText:t.sanitizeText,sanitizeHtml:(e,r)=>(Zp(e)?n:t).sanitizeHtml(e,r)}})(t);t.on("PreInit",(()=>{if(t.removed)return;const i=lf(t,o,r,e,a,s);((e,t)=>{e.addCommand("mceInsertClipboardContent",((e,r)=>{n(r.html)?t.pasteHtml(r.html):n(r.text)&&t.pasteText(r.text)}))})(t,i),((e,t)=>{e.on("paste",(e=>{e.isDefaultPrevented()||(e=>{var t,n;const r=null!==(n=null===(t=e.clipboardData)||void 0===t?void 0:t.items)&&void 0!==n?n:[],o=L(r,(e=>$(e.type,"image/")));return o.length>0&&o.length===r.length})(e)||t.pasteEvent(e)}))})(t,i),ae(t)?(e=>{uf(e,e.getBody()),e.inline||uf(e,e.getDoc())})(t):hf(t,0,i,s)}))})(undefined))}();