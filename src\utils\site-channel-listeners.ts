import { onActivated } from 'vue'
import type { RouteLocationNormalized } from 'vue-router'
import emitter from './bus'
import { useParamsStore } from '@/store'

const { siteChannelListeners } = useParamsStore()

/**
 * @param route 
 * @param handler 
 * 
 * 减少刷新操作，全局站点切换及渠道切换交互改为发布订阅模式管理，router文件按需引入(基于路由管理)
 */

// useRoute 不能脱离setup作用域使用，需作为参数传入
const Listener = (route: RouteLocationNormalized, handler = () => {}) => {

  if (route.name) {
    siteChannelListeners.set(route.name, false)
    emitter.on(route.name, () => {
      handler()
    })
  }

  onActivated(() => {
    if (route.name && siteChannelListeners.get(route.name)) {
      siteChannelListeners.set(route.name, false)
      handler()
    }
  })

}

export default Listener