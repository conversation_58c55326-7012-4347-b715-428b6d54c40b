export type siteItem = {
  id: number;
  name: string;
  cms_site_name: string;
  cms_site_id: number;
}

export type user = {
  email: string;
  phone: string;
  real_name: string;
  type: number;
  user_id: number;
  user_name: string;
  wsId: string;
}

export type channelItem = {
  channel_id?: number;
  channel_code?: string;
  host?: string;
  name?: string;
  type?: string;
}

export type manage_template = {
  name: string;
  template_id: number;
}

export type siteDetail = {
  site_id: number;
  name: string;
  host: string;
  html_path: string;
  channel_id: number;
  channel_code: string;
  image_path: string;
  video_path: string;
  secondary_dir: string;
  host_js: string;
  host_css: string;
  host_image: string;
  host_video: string;
  brand: string;
  main_currency: string;
  language: string;
  leads_mail: string;
  syn_mail: string;
  user_id_a: number;
  user_id_e: number;
  user_name: string;
  add_time: string;
  edit_time: string;
  state: string;
  site_file_limits: string;
  can_use: string;
  examiner: string;
}

export type templateModuleType = {
  module: string;
  module_name: string;
  type: string;
  type_name: string;
  module_type: string;
  module_type_name: string;
}