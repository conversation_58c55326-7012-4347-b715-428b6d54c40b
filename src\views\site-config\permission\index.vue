<template>
  <div class="scroll-y with-flex">
    <QueryForm
      :form-list="formList"
      :loading="loading"
      @search="handleSearch" 
      @reset="reset"
      hide-reset
    />
    <div v-loading="loading" class="sticky-table">
      <CustomTable
        ref="tableRef"
        :columns="columns"
        :data="tableData"
        :operate-list="operateList"
        :table-method="getList"
      />
    </div>

    <RolesModal 
      v-if="loadRolesModal"
      :row="currentRow"
      @close="loadRolesModal = false"
    />
  </div>
</template>

<script setup lang="tsx">
import { ref, reactive, toRef, defineAsyncComponent, h } from 'vue'
import { ElButton, ElMessage, ElMessageBox, ElFormItem, ElRadioGroup, ElRadio, ElSelect, ElOption } from 'element-plus'
import { storeToRefs } from 'pinia'
import { useParamsStore } from '@/store'
import useTryCatch from '@/hooks/use-try-catch'
import useLoading from '@/hooks/use-loading'
import { permissionList, syncWSUsers, editUserInfo } from '@/api/site-config'
import type { permissionQueryParams, permissionListItem } from '@/api/site-config'

import QueryForm from '@/components/QueryForm/index.vue'
import CustomTable from '@/components/CustomTable/index.vue'
const RolesModal = defineAsyncComponent( () =>  import('./components/rolesModal.vue') )

defineOptions( { name: 'SitePermission' } )

const { webList } = storeToRefs(useParamsStore())
const { loading, setLoading } = useLoading()

const queryParams_raw = {
  site_id: '',
  role_id: '',
  real_name: '',
  wsid: '',
  state: '',
  department: '',
  email: '',
  phone: '',
}
const queryParams = reactive<permissionQueryParams>( {
  ...queryParams_raw,
  state: 'able'
} )

const formList = ref([
  {
    label: '工号',
    placeholder: '工号',
    value: toRef(queryParams, 'wsid'),
    component: 'input',
  },
  {
    label: '姓名',
    placeholder: '姓名',
    value: toRef(queryParams, 'real_name'),
    component: 'input',
  },
  {
    label: '权限站点',
    placeholder: '权限站点',
    value: toRef(queryParams, 'site_id'),
    component: 'select',
    selections: webList,
    labelKey: 'cms_site_name',
    valueKey: 'cms_site_id',
  },
  {
    label: '状态',
    placeholder: '状态',
    value: toRef(queryParams, 'state'),
    component: 'select',
    selections: [
      {
        label: '启用',
        value: 'able'
      },
      {
        label: '禁用',
        value: 'disable'
      }
    ]
  },
  {
    label: '邮箱',
    placeholder: '邮箱',
    value: toRef(queryParams, 'email'),
    component: 'input',
  },
])

const currentRow = ref<permissionListItem|null>(null)
const loadRolesModal = ref(false)
const tableRef = ref()
const tableData = ref<permissionListItem[]>([])
const columns = ref([
  {
    title: '工号',
    dataKey: 'wsId',
    width: 150
  },
  {
    title: '姓名',
    dataKey: 'real_name',
    width: 150
  },
  {
    title: '邮箱',
    dataKey: 'email',
    width: 150
  },
  {
    title: '手机号',
    dataKey: 'phone',
    width: 150,
    cellRenderer: ( scope: { row: permissionListItem } ) => (
      <span> { scope.row.phone ? scope.row.phone.replace(/(\d{3})\d{4}(\d{4})/, '$1****$2') : '' } </span>
    )
  },
  {
    title: '部门',
    dataKey: 'department',
    minWidth: 100
  },
  {
    title: '状态',
    dataKey: 'state',
    width: 100,
    cellRenderer: ( scope: { row: permissionListItem } ) => (
      <span style= { { color: scope.row.state === 'able' ? 'var(--el-color-success)' : 'var(--el-color-error)' } }> 
        { scope.row.state === 'able' ? '启用' : '禁用' } 
      </span>
    )
  },
  {
    title: '最近操作时间',
    dataKey: 'edit_time',
    width: 180
  },
  {
    title: '系统角色',
    dataKey: 'role_name',
    minWidth: 150,
    cellRenderer: ( scope: { row: permissionListItem } ) => (
      <> 
        { scope.row.role_name ? scope.row.role_name.replace(/\,$/, '') : '/' }
      </>
    )
  },
  {
    title: '操作',
    dataKey: '',
    width: 180,
    fixed: 'right',
    cellRenderer: ( scope: { row: permissionListItem } ) => (
      <> 
        <ElButton type='success' plain onClick={() => handleEdit(scope.row)}> 编辑 </ElButton>
        <ElButton type='primary' plain onClick={() => handleView(scope.row)}>查看</ElButton>
      </>
    )
  }
])
const operateList = ref([
  {
    title: '兴云数据同步',
    icon: 'refresh',
    method: () => {
      useTryCatch( async () => {
        setLoading(true)

        const res = await syncWSUsers()
        if (res.code === 200) {
          reset()
          handleSearch()
        } else {
          ElMessage.error(res.msg)
        }

        setLoading(false)
      }, () => setLoading(false) )
    }
  },
  {
    title: '角色权限配置',
    button: true,
    append: true,
    method: () => {
      currentRow.value = null
      loadRolesModal.value = true
    }
  }
])

const getList = ( { page, pageSize } ) => {
  useTryCatch( async () => {
    setLoading(true)
    const params = {
      ...queryParams,
      page,
      page_size: pageSize
    }
    const res = await permissionList( params )
    if (res.code === 200) {
      tableData.value = res.data.list
      tableRef.value?.setTotal(res.data.total)
    } else {
      ElMessage.error(res.msg)
    }
    setLoading(false)
  }, () => setLoading(false) )
}

const reset = () => {
  Object.entries(queryParams_raw).forEach(([ key, value ]) => {
    queryParams[key] = value
  })
}

const handleSearch = () => {
  tableRef.value?.setPage(1)
  const { page, pageSize } = tableRef.value?.getPagination()
  getList({ page, pageSize })
}

const handleView = (row: permissionListItem) => {
  currentRow.value = row
  loadRolesModal.value = true
}

const handleEdit = (row: permissionListItem) => {
  const state = ref(row.state)
  const user_parent_id = ref( row.user_parent_id ? row.user_parent_id.split(',') : [])
  ElMessageBox({
    title: '编辑用户信息',
    showCancelButton: false,
    showConfirmButton: false,
    closeOnClickModal: false,
    closeOnPressEscape: false,
    callback: () => {},
    message: () => h('div', null, [
      h(ElFormItem, { label: '用户状态：' }, () => h(ElRadioGroup, {
        modelValue: state.value,
        onChange: ( val: any ) => state.value = val
      }, () => [
        h(ElRadio, { label: 'able' }, () => '启用'),
        h(ElRadio, { label: 'disable' }, () => '禁用'),
      ])),
      h(ElFormItem, { label: '特殊权限：' }, () => h(ElSelect, {
        multiple: true,
        collapseTags: true,
        collapseTagsTooltip: true,
        modelValue: user_parent_id.value,
        onChange: ( val: any ) => user_parent_id.value = val
      }, () => [
        h(ElOption, { label: '购买页编辑权限', value: '2' }),
        h(ElOption, { label: '翻译任务提交权限', value: '1' }),
      ])),
      h('div', { style: { marginTop: '30px', textAlign: 'center' }, }, h(ElButton, {
        type: 'primary',
        onClick: () => {
          useTryCatch( async () => {
            const params = {
              user_id: row.user_id,
              state: state.value,
              user_parent_id: user_parent_id.value.join(',')
            }
            const res = await editUserInfo( params )
            if (res.code === 200) {
              ElMessage.success('修改成功')
              handleSearch()
              ElMessageBox.close()
            } else {
              ElMessage.error(res.msg)
            }
          } )
        }
      }, () => '确认'))
    ])
  })
}
</script>