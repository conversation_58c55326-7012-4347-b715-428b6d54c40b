<template>
  <el-dialog
    v-model="show"
    title="购买页信息配置"
    width="90%"
    append-to-body
    align-center
    :close-on-click-modal="false"
    @closed="() => emit('close')"
  >
    <el-form
      ref="formRef"
      label-suffix=":"
      label-width="140px"
    >
      <el-form-item label="卡片数">
        <el-select v-model="buyInfo.card_num" @change="handleCardNumChange">
          <el-option v-for="n in card_num" :key="n" :value="n" :label="n" />
        </el-select>
      </el-form-item>
      <el-form-item label="拓展卡片判别">
        <el-checkbox-group v-model="ip_option" @change="handleOptionChange">
          <el-checkbox label="default" disabled>基础判别</el-checkbox>
          <el-checkbox label="ip_option" @change="hanleIp_optionChange">ip判别</el-checkbox>
          <el-checkbox label="tab_switch" @change="handleTab_switchChange">tab切换</el-checkbox>
        </el-checkbox-group>
      </el-form-item>

      <div v-for="(card, key, index) in buyInfo.card_detail" :key="key"
        class="mb-20px"
        style="border-radius: 10px; border: 1px solid var(--el-border-color); padding: 16px;"
        v-show="buyInfo.card_num > index"
      >
        <h2 class="mb-20px">卡片{{ index + 1 }}</h2>
        <div class="mb-15px rowBC">
          <h3>基础购买链接组合-<span style="color: var(--el-color-primary);">基础判别</span></h3>
          <el-button @click="handleResolve(key as string, 'default')">批量贴入</el-button>
        </div>
        <el-table
          :data="card.default"
          border
        >
          <el-table-column label="Sku id" prop="sku_id" />
          <el-table-column label="Sku名称" prop="sku_name" />
          <el-table-column label="销售价格" prop="" />
          <el-table-column label="额外参数" prop="" />
          <el-table-column label="活动ID" prop="activity_id" />
          <el-table-column label="捆绑产品sku" prop="additional_sku" />
          <el-table-column label="是否ab test" prop="url_ab_testing" />
          <el-table-column label="购买链接" prop="url" width="500" />
        </el-table>
        <div v-show="buyInfo.ip_option.indexOf('ip_option') > -1">
          <div style="padding-top: 20px;" class="mb-15px rowBC">
            <h3>拓展判别组-<span style="color: var(--el-color-danger);">ip判别</span></h3>
            <el-button @click="handleResolve(key as string, 'ip_option')">批量贴入</el-button>
          </div>
          <el-table
            :data="card.ip_option"
            border
          >
            <el-table-column label="Sku id" prop="sku_id" />
            <el-table-column label="Sku名称" prop="sku_name" />
            <el-table-column label="销售价格" prop="" />
            <el-table-column label="额外参数" prop="" />
            <el-table-column label="活动ID" prop="activity_id" />
            <el-table-column label="捆绑产品sku" prop="additional_sku" />
            <el-table-column label="是否ab test" prop="url_ab_testing" />
            <el-table-column label="购买链接" prop="url" width="500" />
          </el-table>
        </div>
        <div v-show="buyInfo.ip_option.indexOf('tab_switch') > -1">
          <div style="padding-top: 20px;" class="mb-15px rowBC">
            <h3>拓展判别组-<span style="color: var(--el-color-danger);">tab切换</span></h3>
            <el-button @click="handleResolve(key as string, 'tab_switch')">批量贴入</el-button>
          </div>
          <el-table
            :data="card.tab_switch"
            border
          >
            <el-table-column label="Sku id" prop="sku_id" />
            <el-table-column label="Sku名称" prop="sku_name" />
            <el-table-column label="销售价格" prop="" />
            <el-table-column label="额外参数" prop="" />
            <el-table-column label="活动ID" prop="activity_id" />
            <el-table-column label="捆绑产品sku" prop="additional_sku" />
            <el-table-column label="是否ab test" prop="url_ab_testing" />
            <el-table-column label="购买链接" prop="url" width="500" />
          </el-table>
        </div>
      </div>
    </el-form>
    <template #footer>
      <div class="text-center">
        <el-button @click="show = false">取消</el-button>
        <el-button type="primary" @click="handleSave">保存</el-button>
      </div>
    </template>

  </el-dialog>
</template>

<script setup lang="ts">
import { ref, reactive, h } from 'vue'
import { ElMessage, ElMessageBox, ElInput, ElButton } from 'element-plus'
import useTryCatch from '@/hooks/use-try-catch'
import useLoading from '@/hooks/use-loading'
import { getBuyUrl } from '@/api/buy-management'
import type { buyInfo } from '@/api/buy-management'

const show = ref(true)

const emit = defineEmits(['save', 'close'])
const props = defineProps<{
  buyInfo?: buyInfo|null;
}>()

const { loading, setLoading } = useLoading()

const ip_option = ref<string[]>([])

const buyInfo_raw = {
  card_num: 1,
  card_type: '',
  ip_option: 'default',
  card_detail: {
    card_1: {
      default: [],
      ip_option: [],
      tab_switch: [],
    }
  }
}

const card_num = ref(10)
const buyInfo = reactive<buyInfo>({
  ...props.buyInfo ? JSON.parse(JSON.stringify(props.buyInfo)) : buyInfo_raw
})

ip_option.value = buyInfo.ip_option.split(',')

const handleOptionChange = () => {
  buyInfo.ip_option = String(ip_option.value)
}

const hanleIp_optionChange = (val: boolean) => {
  const label = 'ip_option'
  !val && handleConfirmChange(label, 'ip判别')
}

const handleTab_switchChange = (val: boolean) => {
  const label = 'tab_switch'
  !val && handleConfirmChange(label, 'tab切换')
}

const handleConfirmChange = (label: string, name: string) => {
  ElMessageBox.confirm(`请确认是否取消${name}拓展卡片判别项`, '提示', {
    confirmButtonText: '确认',
    cancelButtonText: '取消',
    type: 'warning',
    callback: (action: string) => {
      if (action === 'confirm') {
        const cards = Object.keys(buyInfo.card_detail)
        cards.forEach(item => {
          buyInfo.card_detail[item][label] = []
        })
      } else {
        ip_option.value.push(label)
        handleOptionChange()
      }
    }
  })
}

const handleCardNumChange = () => {
  const cards = Object.keys(buyInfo.card_detail)
  const start = cards.length + 1
  if (buyInfo.card_num > cards.length) {
    for (let i = start; i <= buyInfo.card_num; i++) {
      buyInfo.card_detail[`card_${i}`] = {
        default: [],
        ip_option: [],
        tab_switch: [],
      }
    }
  }
}

const handleResolve = (card_key: string, type: 'default' | 'ip_option' | 'tab_switch') => {
  const list = buyInfo.card_detail[card_key][type]
  const urlList:string[] = []
  if (list) {
    list.forEach(item => {
      urlList.push(item.url)
    })
  }

  const urls = ref(urlList.join('\n'))
  ElMessageBox({
    title: '批量编辑购买链接',
    customClass: 'dialog-button-center',
    customStyle: {
      maxWidth: 'none',
      width: '1200px',
    },
    showCancelButton: false,
    showConfirmButton: false,
    cancelButtonText: '取消',
    confirmButtonText: '确认',
    callback: (action: string) => {

    },
    message: () => h('div', null, [
      h(ElInput, {
        type: 'textarea',
        placeholder: '请粘入复制的链接，多个链接用换行符隔开',
        rows: 20,
        modelValue: urls.value,
        onInput: (value) => urls.value = value.trim(),
      }),
      h('div', { style: { marginTop: '30px' }, class: 'text-center' }, h(ElButton, {
        type: 'primary',
        loading: loading.value,
        onClick: () => {
            useTryCatch(async () => {
            setLoading(true)
            const res = await getBuyUrl(urls.value)
            if (res.code === 200) {
              buyInfo.card_detail[card_key][type] = res.data
            } else {
              ElMessage.error(res.msg)
            }
            setLoading(false)
          }, () => setLoading(false))
          ElMessageBox.close()
        }
      }, () => '确认') )
  ]) 
  })
}

const handleSave = () => {
  const cards = {} as buyInfo
  const num = buyInfo.card_num
  const ip_option = buyInfo.ip_option
  cards.card_num = num
  cards.card_type = buyInfo.card_type
  cards.ip_option = String(ip_option)
  cards.card_detail = {}
  for (let i = 1; i <= num; i++) {
    cards.card_detail[`card_${i}`] = {
      default: [],
      ip_option: [],
      tab_switch: [],
    }
    cards.card_detail[`card_${i}`].default = [...buyInfo.card_detail[`card_${i}`].default]
    cards.card_detail[`card_${i}`].ip_option = []
    cards.card_detail[`card_${i}`].tab_switch = []
    if (ip_option) {
      cards.card_detail[`card_${i}`].ip_option = [...buyInfo.card_detail[`card_${i}`].ip_option]
      const tab_switch = buyInfo.card_detail[`card_${i}`].tab_switch
      cards.card_detail[`card_${i}`].tab_switch = [...tab_switch ? tab_switch : []]
    }
  }
  show.value = false
  emit('save', cards)
}

</script>