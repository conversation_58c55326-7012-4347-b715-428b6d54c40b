<template>
  <div class="scroll-y with-flex">
    <QueryForm hide-reset :form-list="formList" @search="handleSearch" />

    <div v-loading="loading" class="sticky-table">
      <CustomTable
        ref="myTable"
        :data="tableData"
        :columns="columns"
        :table-method="getList"
        :row-class-name="filterRowClassName"
      />
    </div>

    <AnnounceModal v-if="detailInfo" :detail-info="detailInfo" />
  </div>
</template>
<script setup lang="tsx">
import { ref, reactive, toRef } from 'vue'
import { useRouter } from 'vue-router'
import { ElButton, ElMessage } from 'element-plus'
import { useBasicStore } from '@/store/basic'
import useLoading from '@/hooks/use-loading'
import useTryCatch from '@/hooks/use-try-catch'
import QueryForm from '@/components/QueryForm/index.vue'
import CustomTable from '@/components/CustomTable/index.vue'
import AnnounceModal from './components/announce-modal.vue'
import { getAnnounceList, setAnnounceReaded } from '@/api/announcement'
import type { announceListItem, queryParams } from '@/api/announcement'

defineOptions({ name: 'AnnounceList' })

const router = useRouter()
const basicStore = useBasicStore()
const { loading, setLoading } = useLoading()

const queryParams_raw: queryParams = {
  state: 'able',
  is_read: '',
}
const queryParams = reactive<queryParams>({...queryParams_raw})
const myTable = ref()
const detailInfo = ref<announceListItem>()

const formList = ref([
  {
    label: '是否已读',
    clearable: true,
    placeholder: '是否已读',
    value: toRef(queryParams, 'is_read'),
    component: 'select',
    selections: [
      {
        label: '是',
        value: '1'
      },
      {
        label: '否',
        value: '2'
      }
    ]
  },
])

const columns = ref([
  {
    key: 'is_top_name',
    title: '列表状态展示',
    dataKey: 'is_top_name',
    width: 150,
  },
  {
    key: 'header',
    title: '公告标题',
    dataKey: 'title',
    showOverflowTooltip: true,
  },
  {
    key: 'publish_time',
    title: '发布时间',
    dataKey: 'publish_time',
    width: 200,
  },
  {
    title: '操作',
    dataKey: '',
    width: 150,
    cellRenderer: (scope: { row: announceListItem }) => {
      return (
        <ElButton
          type='primary'
          plain
          onClick={() => {
            router.push({
              name: 'AnnounceUserDetail',
              query: {
                announcement_id: scope.row.announcement_id
              }
            })
          }}
        >
          查看详情
        </ElButton>
      )
    }
  }
])

const tableData = ref<announceListItem[]>([])

const getList = ( { page, pageSize } ) => {
  useTryCatch(
    async () => {
      setLoading(true)
      const params = {
      S: { ...queryParams },
        page,
        page_size: pageSize
      }
      const res = await getAnnounceList(params)
      if (res.code === 200) {
        const items = res.data.items as announceListItem[]
        tableData.value = items
        myTable.value.setTotal(res.data.total)
      } else {
        ElMessage.error(res.msg)
      }
      setLoading(false)
    },
    () => setLoading(false)
  )
}

const getModalDetail = () => {
  useTryCatch(
    async () => {
      const params = {
        S: {
          is_read: '2',
          type: 'A',
          state: 'able',
          is_top: '1'
        }
      }
      const res = await getAnnounceList(params)
      if (res.code === 200) {
        const items = res.data.items as announceListItem[]
        if (items.length > 0) {
          detailInfo.value = items[0]
          setAnnounceReaded(items[0].announcement_id)
        }
      }
    }
  )
}

const resetState = () => {
  Object.entries(queryParams_raw).forEach(([ key, value ]) => {
    queryParams[key] = value
  })
}

const resetPageAndGetList = () => {
  myTable.value.setPage(1)
  const { page, pageSize } = myTable.value
  getList( { page, pageSize } )
}

const handleSearch = () => {
  resetPageAndGetList()
}

const filterRowClassName = ({ row, rowIndex }) => {
  return row.is_read === 1 ? '' : 'bg-gray'
}

onActivated(() => {
  if (basicStore.refresh) {
    basicStore.setRefresh(false)
    resetState()
    resetPageAndGetList()
  }
})

getModalDetail()
</script>
