import { defineStore } from 'pinia'
import type { RouterTypes } from '~/basic'
import defaultSettings from '@/settings'
import router, { constantRoutes, appendRoutes, allRoutes, routeMap } from '@/router'
import { allRouteTree, userRoleList } from '@/api/site-config'
import type { menuData } from '@/api/site-config'
export const useBasicStore = defineStore('basic', {
  state: () => {
    return {
      //router
      allRoutes: allRoutes as RouterTypes,
      buttonCodes: [],
      filterAsyncRoutes: [],
      roles: [] as Array<string>,
      codes: [] as Array<number>,
      //keep-alive
      cachedViews: [] as Array<string>,
      cachedViewsDeep: [] as Array<string>,
      //other
      sidebar: { opened: true },
      settings: defaultSettings,
      refresh: false, // 全局缓存刷新提示，刷新完毕后重置为false
      menuList: [] as menuData[], // 后台菜单
      roleList: [] as { val: string; key: number }[], // 角色列表
    }
  },
  persist: {
    storage: localStorage,
    paths: ['token']
  },
  actions: {
    setRefresh(value: boolean) {
      this.$patch((state) => {
        state.refresh = value
      })
    },
    setFilterAsyncRoutes(routes) {
      this.$patch((state) => {
        state.filterAsyncRoutes = routes
        state.allRoutes = constantRoutes.concat(routes)
      })
    },
    setSidebarOpen(data: boolean) {
      this.$patch((state) => {
        state.sidebar.opened = data
      })
    },
    setToggleSideBar() {
      this.$patch((state) => {
        state.sidebar.opened = !state.sidebar.opened
      })
    },

    /*keepAlive缓存*/
    addCachedView(view) {
      this.$patch((state) => {
        if (state.cachedViews.includes(view)) return
        state.cachedViews.push(view)
      })
    },

    delCachedView(view) {
      this.$patch((state) => {
        const index = state.cachedViews.indexOf(view)
        index > -1 && state.cachedViews.splice(index, 1)
      })
    },
    /*third  keepAlive*/
    addCachedViewDeep(view) {
      this.$patch((state) => {
        if (state.cachedViewsDeep.includes(view)) return
        state.cachedViewsDeep.push(view)
      })
    },
    setCacheViewDeep(view) {
      this.$patch((state) => {
        const index = state.cachedViewsDeep.indexOf(view)
        index > -1 && state.cachedViewsDeep.splice(index, 1)
      })
    },
    setAllRoutes(userMenu: menuData[]) {
      this.$patch((state) => {
        const allRoutesMap = new Map()
        state.allRoutes.forEach( ( route ) => allRoutesMap.set( route.path, route ) )
        userMenu.forEach( (menu) => {
          if (routeMap.has(menu.route)) {
            const route = routeMap.get(menu.route)
            const m_children = menu.children || []
            const current = allRoutesMap.get(menu.route)
            // 无子目录则不进行后续操作
            if (m_children.length > 0) {
              const childrenSet = new Set<string>()
              const appendChild = <any>[]
              m_children.forEach( (child) => {
                childrenSet.add(child.route)
              } )

              const children = route?.children || []
              children.forEach( ( child ) => {
                if ( childrenSet.has(child.path) ) {
                  router.addRoute(route?.name as string, child)
                  appendChild.push(child)
                }
              } )

              // 子菜单添加
              current.children = appendChild
              current.hidden = false
            }
          }
        } )

        appendRoutes.forEach((item) => router.addRoute(item))
      })
    },
    async getMenuList() {
      try {
        const res = await allRouteTree()
        const { code, data, msg } = res
        if (code === 200) {
          this.menuList = data
        } else {
          Promise.reject(msg)
        }
      } catch (error) {
        
      }
    },
    async getRoleList() {
      try {
        const res = await userRoleList()
        const { code, data, msg } = res
        if (code === 200) {
          this.roleList = data.role_list
        } else {
          Promise.reject(msg)
        }
      } catch (error) {
        
      }
    }
  }
})
