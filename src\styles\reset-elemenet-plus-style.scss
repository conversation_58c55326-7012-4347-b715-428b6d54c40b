$button-types: primary, danger, warning, info, success;

.el-pagination {
  padding-top: 20px;
  &.right {
    justify-content: flex-end;
  }
  &.center {
    justify-content: center;
  }
}
.el-dialog__title {
  font-weight: 700;
}
%border-transparent {
  border-color: transparent;
}

@each $type in $button-types {
  .el-button.el-button--#{$type} {
    @extend %border-transparent
  }
}
.el-form-item__error {
  z-index: 1;
}

// 覆盖table样式
.el-table--border .el-table__cell { 
  border-right: none !important;
}
.el-table--border::before, .el-table--border::after, .el-table__border-left-patch {
  opacity: 0;
}
