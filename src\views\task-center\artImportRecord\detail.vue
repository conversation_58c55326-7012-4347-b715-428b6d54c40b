<template>
  <div class="scroll-y with-flex">
    <div v-loading="loading" class="sticky-table">
      <CustomTable 
        ref="tableRef"
        :data="tableData"
        :columns="columns"
        :table-method="getList"
        hide-pagination
      />
    </div>

    <div style="display: none;">
      <el-upload
        action="string"
        accept=".docx"
        :auto-upload="false"
        :on-change="handleChange"
      >
        <el-button type="primary" ref="uploadRef">上传</el-button>
    </el-upload>
    </div>
  </div>
</template>

<script setup lang="tsx">
import { ref } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { ElMessage, ElButton } from 'element-plus'
import { useBasicStore } from '@/store/basic'
import useTryCatch from '@/hooks/use-try-catch'
import useLoading from '@/hooks/use-loading'
import { getHost } from '@/hooks/use-env'
import { getArtImportTaskDetailList,  wordUpload } from '@/api/tools-management'
import type { artImportTaskDetailItem } from '@/api/tools-management'
import type { UploadFile, UploadRawFile } from 'element-plus'

import CustomTable from '@/components/CustomTable/index.vue'

defineOptions( { name: 'ArticleImportDetail' } )

const statusMap = {
  1: '待处理',
  2: '处理中',
  3: '已处理',
  4: '任务失败',
  5: '图片待发布'
}

const route = useRoute()
const router = useRouter()
const basicStore = useBasicStore()
const { loading, setLoading } = useLoading()
const job_id = route.query.job_id as string

const uploadRef = ref()
const currentId = ref(0)
const tableRef = ref()
const tableData = ref<artImportTaskDetailItem[]>([])
const columns = ref([
  {
    title: '文档ID',
    dataKey: 'id',
    width: 100,
  },
  {
    title: '文档名称',
    dataKey: 'file_name',
    minWidth: 150,
  },
  {
    title: '文章title',
    dataKey: 'art_name',
    minWidth: 100,
  },
  {
    title: '转码状态',
    dataKey: 'status',
    width: 100,
    cellRenderer: ( scope: { row: artImportTaskDetailItem } ) => (
      <span style={ { 
        color: scope.row.status === 2 ? 'var(--el-coolor-primary)' 
        : scope.row.status === 3 ? 'var(--el-coolor-success)' 
        : scope.row.status === 4 ? 'var(--el-color-danger)' : '' 
      } }>
        { statusMap[ scope.row.status ] }
      </span>
    )
  },
  {
    title: '错误信息',
    dataKey: 'msg',
    minWidth: 150,
  },
  {
    title: '录入文章模版ID',
    dataKey: 'tpl_id',
    width: 150,
  },
  {
    title: '转码完成时间',
    dataKey: 'edit_time',
    width: 180,
  },
  {
    title: '操作',
    dataKey: '',
    fixed: 'right',
    width: 340,
    cellRenderer: ( scope: { row: artImportTaskDetailItem } ) => (
      <>
        <ElButton type='success' plain disabled={ scope.row.status === 1 || scope.row.status === 2 } onClick={() => handleUpload(scope.row)}> 
          重新上传
        </ElButton>
        <ElButton type='primary' plain disabled={ scope.row.status === 1 || scope.row.status === 2 || scope.row.status === 4 } onClick={() => handleToArticle(scope.row)}> 
          跳转文章 
        </ElButton>
        <ElButton type='primary' plain onClick={() => handleDownloadWord(scope.row.id)}> 
          查看word 
        </ElButton>
      </>
    )
  }
])

const getList = ( ) => {
  useTryCatch( async () => {
    setLoading(true)

    const res = await getArtImportTaskDetailList( job_id )
    if (res.code === 200) {
      tableData.value = res.data.list
      tableRef.value?.setTotal(res.data.total)
    } else {
      ElMessage.error( res.msg )
    }

    setLoading(false)
  }, () => setLoading(false) )
}

const handleToArticle = (row: artImportTaskDetailItem) => {
  const name = 'ArticleManageOperate'
  basicStore.delCachedView(name)
    router.push({
    name: 'ArticleManageOperate',
    query: {
      type: 'edit',
      art_id: row.art_id,
      channel_id: row.channel_id,
      page_preview_url: row.page_preview_url
    }
  })
}

const handleDownloadWord = (id: number) => {
  location.href = `${getHost()}/api/v1/doc/download?id=${id}`
}

const handleChange = (file: UploadFile, list: UploadFile[]) => {
  useTryCatch( async () => {
    setLoading(true)

    const res = await wordUpload( { file: file.raw as UploadRawFile, id: currentId.value } )
    if (res.code === 200) {
      getList()
      ElMessage.success('上传成功')
    } else {
      ElMessage.error( res.msg )
    }

    setLoading(false)
  }, () => setLoading(false) )
}

const handleUpload = (row: artImportTaskDetailItem) => {
  setLoading(true)
  currentId.value = row.id
  uploadRef.value.$el.click()
  setTimeout(() => {
    setLoading(false)
  }, 1000)
}
</script>