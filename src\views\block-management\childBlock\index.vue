<template>
  <div class="scroll-y">
    <el-card shadow="never">
      <template #header>
        <div class="rowBC">
          <div><strong style="font-size: 1.25em;">编辑块关系</strong></div>
          <div>
            <el-button type="primary" plain @click="handleAdd"> 添加子块 </el-button>
          </div>
        </div>
      </template>
      <template #default>
        <el-table
          border
          highlight-current-row
          max-height="800"
          :data="tableData"
        >
          <el-table-column prop="blk_id" label="块ID" width="100" />
          <el-table-column prop="blk_type_name" label="块类型" min-width="150" />
          <el-table-column prop="blk_name" label="块名称" min-width="150" />
          <el-table-column label="块渠道" width="120">
            <template #default="{ row }: { row: listItem }">
              <span :style=" { color: row.color } "> {{ row.channel_name }} </span>
            </template>
          </el-table-column>
          <el-table-column label="生成状态" width="100">
            <template #default="{ row }: { row: listItem }">
              <span :style=" { color: row.created_state === 'created' ? 'var(--el-color-success)' : 'inherit' } "> {{ row.created_state_name }} </span>
            </template>
          </el-table-column>
          <el-table-column prop="user_name" label="更新人" width="100" />
          <el-table-column prop="edit_time" label="更新时间" width="180" />
          <el-table-column label="操作" width="100">
            <template #default="{ row }: { row: listItem }">
              <el-button type="primary" plain @click="handleEdit(row)"> 编辑 </el-button>
            </template>
          </el-table-column>
        </el-table>
      </template>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import { useBasicStore } from '@/store/basic'
import useTryCatch from '@/hooks/use-try-catch'
import { getChildList } from '@/api/block-management'
import type { listItem } from '@/api/block-management'

const route = useRoute()
const router = useRouter()
const basicStore = useBasicStore()
const { blk_id } = route.query as unknown as { blk_id: number }

const tableData = ref<listItem[]>([])

const getList = () => {
  useTryCatch( async () => {
    const res = await getChildList(blk_id)
    if (res.code === 200) {
      tableData.value = res.data
    } else {
      ElMessage.error(res.msg)
    }
  } )
}

const handleDeleteCache = () => {
  const name = 'BlockManagementOperate'
  if (basicStore.cachedViews.indexOf(name) > -1) {
    basicStore.delCachedView(name)
  }
}

const handleAdd = () => {
  handleDeleteCache()
  router.push({
    name: 'BlockManagementOperate',
    query: {
      type: 'add',
      parent_id: blk_id
    }
  })
}

const handleEdit = (row: listItem) => {
  handleDeleteCache()
  router.push({
    name: 'BlockManagementOperate',
    query: {
      type: 'edit',
      blk_id: row.blk_id,
      parent_id: blk_id
    }
  })
}

getList()
</script>