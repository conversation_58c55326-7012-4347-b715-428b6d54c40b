<template>
  <el-dialog
    title="文章历史对比"
    v-model="show"
    top="3vh"
    width="95%"
    append-to-body
    @closed="emit('close')"
  >
    <template #default>
      <div style="overflow: auto; max-height: calc(100vh - 15vh);">
        <el-form label-suffix=":" label-width="120px" style="overflow: hidden">
          <el-row :gutter="20">
            <el-col :span="12">
              <el-card shadow="never" style="height: 100%;">
                <template #header>
                  文章分类-{{ cat_id }} V{{ prev?.version }} 基础信息
                </template>
                <template #default>
                  <el-form-item label="分类名称">
                    {{ prev?.name }}
                  </el-form-item>
                  <el-form-item label="父级分类">
                    {{ prev?.parent_id }}
                  </el-form-item>
                  <el-form-item label="分类页面URL">
                    {{ prev?.page_url }}
                  </el-form-item>
                  <el-form-item label="排序">
                    {{ prev?.sort }}
                  </el-form-item>
                  <el-form-item label="是否展示">
                    {{ prev?.is_display === 0 ? '否' : '是' }}
                  </el-form-item>
                  <el-form-item label="是否热门">
                    {{ prev?.is_hot === '0' ? '否' : '是' }}
                  </el-form-item>
                  <el-form-item label="是否推荐">
                    {{ prev?.is_recom === '0' ? '否' : '是' }}
                  </el-form-item>
                  <el-form-item label="文章图片URL">
                    {{ prev?.image_url }}
                  </el-form-item>
                  <el-form-item label="文章缩略图URL">
                    {{ prev?.thumb_url }}
                  </el-form-item>
                  <el-form-item label="分类简介">
                    {{ prev?.summary }}
                  </el-form-item>
                  <el-form-item label="备份说明">
                    {{ prev?.remark }}
                  </el-form-item>
                </template>
              </el-card>
            </el-col>
            <el-col :span="12">
              <el-card shadow="never" style="height: 100%;">
                <template #header>
                  文章分类-{{ cat_id }} V{{ current?.version }} 基础信息
                </template>
                <template #default>
                  <el-form-item label="分类名称">
                    {{ current?.name }}
                  </el-form-item>
                  <el-form-item label="父级分类">
                    {{ current?.parent_id }}
                  </el-form-item>
                  <el-form-item label="分类页面URL">
                    {{ current?.page_url }}
                  </el-form-item>
                  <el-form-item label="排序">
                    {{ current?.sort }}
                  </el-form-item>
                  <el-form-item label="是否展示">
                    {{ current?.is_display === 0 ? '否' : '是' }}
                  </el-form-item>
                  <el-form-item label="是否热门">
                    {{ current?.is_hot === '0' ? '否' : '是' }}
                  </el-form-item>
                  <el-form-item label="是否推荐">
                    {{ current?.is_recom === '0' ? '否' : '是' }}
                  </el-form-item>
                  <el-form-item label="文章图片URL">
                    {{ current?.image_url }}
                  </el-form-item>
                  <el-form-item label="文章缩略图URL">
                    {{ current?.thumb_url }}
                  </el-form-item>
                  <el-form-item label="分类简介">
                    {{ current?.summary }}
                  </el-form-item>
                  <el-form-item label="备份说明">
                    {{ current?.remark }}
                  </el-form-item>
                </template>
              </el-card>
            </el-col>
          </el-row>
        </el-form>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { ElMessage } from 'element-plus'
import useParamsStore from '@/store/modules/params'
import useTryCatch from '@/hooks/use-try-catch'
import { historyClassifyCompare } from '@/api/article-management'

const props = defineProps<{
  cat_id: number|string;
  version_ids: string;
}>()
const emit = defineEmits(['close'])

const { site_id_all } = useParamsStore()
const show = ref(true)

const prev = ref<any>()
const current = ref<any>()

const getDetail = () => {
  useTryCatch( async () => {
    const { cat_id, version_ids } = props
    const res = await historyClassifyCompare(site_id_all, cat_id as number, version_ids)
    if (res.code === 200) {
      const data = Object.values(res.data)
      prev.value = data[0] as any
      current.value = data[1] as any
    } else {
      ElMessage.error(res.msg)
    }
  } )
}

getDetail()
</script>