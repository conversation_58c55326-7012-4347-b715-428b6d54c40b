<template>
  <el-dialog
    v-model="show"
    :title="`${ row ? disabled ? '查看' : '编辑' : '添加' }配置信息`"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    append-to-body
    align-center
    width="1000px"
    @closed="emit('close')"
  >
    <template #default>
      <el-form
        ref="formRef"
        :model="formData"
        :rules="formRules"
        label-suffix=":"
        label-width="150"
      >
        <el-form-item label="标题" prop="title">
          <el-input v-model="formData.title" placeholder="请输入标题" :disabled="disabled" />
        </el-form-item>
        <el-form-item label="推广位位置" prop="position_id">
          <el-cascader 
            v-model="cascader"
            plcaeholder="选择推广位位置"
            :options="adList"
            :props="{ expandTrigger: 'hover', value: 'id', label: 'name', children: 'child' }"
            @change="handleCascaderChange"
            :disabled="disabled"
          />
        </el-form-item>
        <el-form-item label="应用IP地区" prop="country">
          <el-select 
            v-model="formData.country"
            placeholder="请选择国家/地区"
            filterable
            clearable
            :disabled="disabled"
          >
            <el-option v-for="d in countryList" :label="d.label" :value="d.value" />
          </el-select>
        </el-form-item>
        <el-form-item label="应用系统" prop="device">
          <el-radio-group v-model="formData.device" :disabled="disabled">
            <el-radio v-for="d in deviceList" :label="d.value">{{ d.label }}</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="应用范围" prop="scope_type">
          <el-radio-group v-model="formData.scope_type" @change="formData.scope_value = ''" :disabled="disabled">
            <el-radio v-for="d in scopeTypeList" :label="d.value">{{ d.label }}</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item prop="scope_value" v-if="formData.scope_type === 2 || formData.scope_type === 3">
          <el-input 
            type="textarea" 
            v-model="formData.scope_value" 
            :placeholder="`请输入${formData.scope_type === 2 ? '模板' : '页面'}id,多个以英文逗号分隔`" 
            style="width: 400px"
            :disabled="disabled"
          />
        </el-form-item>
        <el-form-item label="展示规则" prop="pop_time">
          <el-select v-model="formData.pop_time" placeholder="请选择展示规则" :disabled="disabled">
            <el-option v-for="d in popTimeList" :label="d.label" :value="d.value" />
          </el-select>
        </el-form-item>
        <el-form-item label="用户随机分流" prop="branch">
          <el-switch v-model="formData.branch" :active-value="2" :inactive-value="1" active-text="是" inactive-text="否"  :disabled="disabled" />
        </el-form-item>
        <div class="text-right" style="margin-bottom: 15px;">
          <el-button type="primary" :icon="Plus" @click="handleAddContent" :disabled="disabled"></el-button>
        </div>
        <div v-for="(item, idx) in formData.content" class="content-item">
          <Delete v-if="formData.content.length > 1" width="20" class="delete-icon" @click="handDeleteContent(idx)" 
          />
          <el-form-item label="用户分流占比配置(%)">
            <el-input-number 
              v-model="item.user_percent" 
              :min="0" :max="100" :step="1" 
              controls-position="right" 
              step-strictly 
              :disabled="disabled"
            >
              <template #suffix>
                <span>%</span>
              </template>
            </el-input-number>
          </el-form-item>
          <template v-for="(d, index) in item.image_info">
            <div style="padding: 15px; margin-bottom: 20px; border: 1px solid var(--el-border-color);">
              <el-row :gutter="15">
                <el-col :span="6" class="text-center">
                  <el-form-item label-width="0" :prop="`content.${idx}.image_info.${index}.image_url`" :rules="[{ required: true, message: '请上传图片', trigger: 'change' }]">
                    <div class="bg-image" :style="{ backgroundImage: `url(${d.image_url})` }" @click="handleImgPreview(d.image_url)"></div>
                    <el-input v-model="d.image_url" v-show="false" />
                  </el-form-item>
                  <el-button @click="handleUploadImg(d)" :disabled="disabled">上传图片</el-button>
                </el-col>
                <el-col :span="18">
                  <el-form-item label="跳转链接" :prop="`content.${idx}.image_info.${index}.click_url`">
                    <el-input v-model="d.click_url" placeholder="请输入跳转链接" :disabled="disabled" />
                  </el-form-item>
                  <el-form-item label="背景颜色" :prop="`content.${idx}.image_info.${index}.bg_color`">
                    <el-color-picker v-model="d.bg_color" show-alpha color-format="hex" :disabled="disabled" />
                  </el-form-item>
                  <el-form-item label="文本内容" :prop="`content.${idx}.image_info.${index}.text_content`" :rules="[{ required: true, message: '请输入文本内容', trigger: 'blur' }]">
                    <el-input type="textarea" v-model="d.text_content" :rows="5" placeholder="请输入文本内容" :disabled="disabled" />
                  </el-form-item>
                </el-col>
              </el-row>
              <div class="text-center" style="padding-top: 15px;">
                <el-button size="small" :icon="Minus" v-if="item.image_info.length > 1" @click="handleDeleteImage(idx, index)"></el-button>
                <el-button type="primary" size="small" :icon="Plus" @click="handleAddImage(idx)"></el-button>
              </div>
            </div>
          </template>
        </div>
      </el-form>
    </template>
    <template #footer>
      <div class="text-center">
        <el-button @click="show = false"> 取消 </el-button>
        <el-button v-if="!disabled" type="primary" :loading="loading" @click="handleSave(formRef)"> 保存配置 </el-button>
      </div>
    </template>
  </el-dialog>

  <el-dialog
    v-model="resourceDialog"
    :title="`选择您需要插入的图片`"
    width="90%"
    top="5vh"
    class="resource-dialog"
    append-to-body
  >
    <ResourceCenter
      v-if="loadResourceCenter"
      :file-type="1"
      show-bottom
      @confirm="handleConfirm"
      @cancel="() => resourceDialog = false"
    />
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, reactive, defineAsyncComponent, h } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Plus, Delete, Minus } from '@element-plus/icons-vue'
import { useParamsStore } from '@/store'
import useTryCatch from '@/hooks/use-try-catch'
import useLoading from '@/hooks/use-loading'
import { addCustom, editCustom } from '@/api/web-config'
import type { customData, customListItem } from '@/api/web-config'
import type { FormInstance } from 'element-plus'
import { formRules } from './rules'

const ResourceCenter = defineAsyncComponent(() => import('@/components/ResourceCenter/index.vue'))

type contentItem = {
  image_url: string;
  click_url: string;
  bg_color: string;
  text_content: string;
}

const props = defineProps<{
  row: customListItem|null;
  readonly?: boolean;
}>()

const disabled = props.readonly || false
const emit = defineEmits(['close', 'save'])

const adList = JSON.parse(sessionStorage.getItem('adList') as string) || []
const countryList = JSON.parse(sessionStorage.getItem('countryList') as string) || []
const deviceList = JSON.parse(sessionStorage.getItem('deviceList') as string) || []
const scopeTypeList = JSON.parse(sessionStorage.getItem('scopeTypeList') as string) || []
const popTimeList = JSON.parse(sessionStorage.getItem('popTimeList') as string) || []

scopeTypeList.forEach( ( item ) => { item.value = Number(item.value) } )
popTimeList.forEach( ( item ) => { item.value = Number(item.value) } )

const { channel_item_all } = useParamsStore()
const { loading, setLoading } = useLoading()
const resourceDialog = ref(false)
const loadResourceCenter = ref(false)
const show = ref(true)
const formRef = ref()
const currentContent = ref()
const cascader = ref<number[]>([])

const formData_raw = {
  position_id: '',
  title: '',
  country: '',
  device: '',
  scope_type: '',
  scope_value: '',
  channel_id: '',
  pop_time: '',
  branch: 1,
  content: [
    {
      user_percent: 0,
      image_info: [
        {
          image_url: '',
          click_url: '',
          bg_color: '',
          text_content: ''
        }
      ]
    }
  ]
}
const formData = reactive<customData>({
  ...formData_raw,
})

const handleSave = async ( formEl: FormInstance | undefined ) => {
  if (!formEl) {
    return
  }
  await formEl.validate( ( valid ) => {
    if (valid) {
      useTryCatch( async () => {
        setLoading(true)

        const api = props.row ? editCustom : addCustom
        const params = {
          ...formData,
          country: String(formData.country),
          device: formData.device,
          channel_id: channel_item_all.channel_id as number,
          scope_type: formData.scope_type,
          scope_value: formData.scope_value || null,
          pop_time: formData.pop_time,
          ...props.row ? { id: props.row.id } : {}
        }

        const res = await api( params )
        if (res.code === 200) {
          ElMessage.success(res.msg)
          emit('save')
          show.value = false
        } else {
          ElMessage.error(res.msg)
        }

        setLoading(false)
      }, () => setLoading(false) )
    }
  } )
}

const handleCascaderChange = ( val: number[] ) => {
  formData.position_id = val[1]
}

const handleUploadImg = ( item: contentItem ) => {
  currentContent.value = item
  resourceDialog.value = true
  loadResourceCenter.value = true
}

const handleConfirm = ( { url } ) => {
  currentContent.value.image_url = url
  resourceDialog.value = false
}

const handleAddContent = () => {
  const item = {
    user_percent: 0,
    image_info: [
      {
        image_url: '',
        click_url: '',
        bg_color: '',
        text_content: ''
      }
    ]
  }
  formData.content.push( item )
}

const handDeleteContent = ( index: number )  => {
  formData.content.splice( index, 1 )
}

const handleAddImage = ( index: number ) => {
  const item = {
    image_url: '',
    click_url: '',
    bg_color: '',
    text_content: ''
  }
  formData.content[index].image_info.push( item )
}

const handleDeleteImage = ( index: number, index2: number ) => {
  formData.content[index].image_info.splice( index2, 1 )
}

const handleImgPreview = ( url: string ) => {
  if (url) {
    ElMessageBox({
      title: '图片预览',
      message: h('img', {
        style: {
          'max-width': '100%',
          'max-height': '85vh',
        },
        src: url
      }),
      customClass: 'img-preview-dialog',
      callback: () => {}
    })
  }
}

const findParent = ( child_id: number ) => {
  for ( const item of adList ) {
    for ( const item2 of item.child ) {
      if (item2.id === child_id) {
        return item.id
      }
    }
  }
  return 0
}

if (props.row) {
  Object.keys( formData_raw ).forEach( key => {
    if (key === 'content') {
      formData[key] = JSON.parse( props.row?.[key] as string )
    } else {
      formData[key] = props.row?.[key]
    }
  })

  cascader.value = [ findParent( formData.position_id as number ), formData.position_id ]
}

</script>

<style lang="scss" scoped>
.content-item {
  --gap: 150px;
  padding: 30px 10px;
  border: 1px solid var(--el-border-color);
  border-radius: 4px;
  width: calc(100% - var(--gap));
  margin-left: var(--gap);
  margin-bottom: 8px;
  position: relative;
  .delete-icon {
    position: absolute;
    right: 4px;
    top: 4px;
    cursor: pointer;
    &:hover {
      color: var(--el-color-danger);
    }
  }
}
.bg-image{
  width: 200px;
  height: 200px;
  background-size: contain;
  background-position: center center;
  background-repeat: no-repeat;
  margin-bottom: 10px;
  border: 1px solid var(--el-border-color);
  border-radius: 2px;
  cursor: pointer;
}
</style>