<template>
  <div 
    class="app-main" 
    :class="{ 'show-tag-view': settings.showTagsView }"
    :style="{ 
      height: `calc(100vh - ${ route.name === 'Dashboard' 
      ? 'var(--layout-gap) * 2 - var(--nav-bar-height)' 
      : 'var(--nav-bar-height) - var(--subnav-bar-height) - var(--tag-view-height) - var(--layout-gap) * 5' })`,
    }"
  >
    <div class="layout-container layout-radius" :class="{ plain: route.meta.plainLayout }">
      <router-view v-slot="{ Component }">
        <!--has transition  setting by settings.mainNeedAnimation-->
        <transition v-if="settings.mainNeedAnimation" name="fade-transform" mode="out-in">
          <keep-alive :include="cachedViewsAll">
            <component :is="Component" :key="key" />
          </keep-alive>
        </transition>
        <!-- no transition -->
        <keep-alive v-else :include="cachedViewsAll">
          <component :is="Component" :key="key" />
        </keep-alive>
      </router-view>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed, watch } from 'vue'
import { storeToRefs } from 'pinia/dist/pinia'
import { useRoute } from 'vue-router'
import type { RouteLocationMatched } from 'vue-router'
import type { rawConfig } from '~/basic'
import { useBasicStore } from '@/store/basic'
import { cloneDeep } from '@/hooks/use-common'
import { toggleBodyClass } from '@/utils/common'
const { settings, cachedViews, cachedViewsDeep } = storeToRefs(useBasicStore())
const route = useRoute()
const key = computed(() => route.meta.queryRequired ? route.fullPath : route.path)
const cachedViewsAll = computed(() => cachedViews.value.concat(cachedViewsDeep.value))
/*listen the component name changing, then to keep-alive the page*/
// cachePage: is true, keep-alive this Page
// leaveRmCachePage: is true, keep-alive remote when page leave
let oldRoute: rawConfig = {}
let deepOldRouter: RouteLocationMatched | null = null
const basicStore = useBasicStore()
const removeDeepChildren = (deepOldRouter) => {
  deepOldRouter.children?.forEach((fItem) => {
    basicStore.setCacheViewDeep(fItem.name)
  })
}
watch(
  () => route.name,
  () => {
    toggleBodyClass(route.name === 'Dashboard' ? 'home' : '')
    const routerLevel = route.matched.length
    //二级路由处理
    if (routerLevel === 2) {
      if (deepOldRouter?.name) {
        if (deepOldRouter.meta?.leaveRmCachePage && deepOldRouter.meta?.cachePage) {
          basicStore.delCachedView(deepOldRouter.name)
          //remove the deepOldRouter‘s children component
          removeDeepChildren(deepOldRouter)
        }
      } else {
        if (oldRoute?.name) {
          if (oldRoute.meta?.leaveRmCachePage && oldRoute.meta?.cachePage) {
            basicStore.delCachedView(oldRoute.name)
          }
        }
      }

      if (route.name) {
        if (route.meta?.cachePage) {
          basicStore.addCachedView(route.name)
        }
      }
      deepOldRouter = null
    }

    //三级路由处理
    if (routerLevel === 3) {
      //三级时存储当前路由对象的上一级
      const parentRoute = route.matched[1]
      //deepOldRouter不为空，且deepOldRouter不是当前路由的父对象，则需要清除deepOldRouter缓存
      //一般为三级路由跳转三级路由的情况
      if (deepOldRouter?.name && deepOldRouter.name !== parentRoute.name) {
        if (deepOldRouter.meta?.leaveRmCachePage && deepOldRouter.meta?.cachePage) {
          basicStore.delCachedView(deepOldRouter.name)
          //remove the deepOldRouter‘s children component
          removeDeepChildren(deepOldRouter)
        }
      } else {
        //否则走正常两级路由处理流程
        if (oldRoute?.name) {
          if (oldRoute.meta?.leaveRmCachePage && oldRoute.meta?.cachePage) {
            basicStore.setCacheViewDeep(oldRoute.name)
          }
        }
      }
      //取的是第二级的name
      if (parentRoute.name && parentRoute.meta?.cachePage) {
        deepOldRouter = parentRoute
        basicStore.addCachedView(deepOldRouter.name)
        if (route.name) {
          if (route.meta?.cachePage) {
            //和第三级的name进行缓存
            basicStore.addCachedViewDeep(route.name)
          }
        }
      }
    }
    oldRoute = cloneDeep({ name: route.name, meta: route.meta })
  },
  { immediate: true }
)
</script>

<style scoped lang="scss">
.app-main {
  padding: 0 var(--app-main-padding) var(--app-main-padding) 0;
  // padding: var(--app-main-padding);
  /*50 = navbar  */
  position: relative;
  overflow: hidden;
  z-index: 2;
  .layout-container {
    padding: var(--app-main-padding);
    height: 100%;
    &.plain {
      background-color: transparent;
      padding: 0;
    }
  }
}
.home {
  .layout-container {
    &.plain {
      border-color: transparent;
    }
  }
}
.fixed-header + .app-main {
  padding-top: calc( #{var(--nav-bar-height)} + #{var(--tag-view-height)} );
}
</style>
