<template>
  <div 
    ref="chartRef"
    :style="{ width, height }"
  ></div>
</template>

<script setup lang="ts">
import ecahrts from '@/components/ECharts'
import { ref, onMounted, onUnmounted } from 'vue'

const props = withDefaults(defineProps<{
  width?: string;
  height?: string;
  data: number[];
  xAxis: string[];
  color?: string;
}>(), {
  width: '100%',
  height: '150px',
  color: '#5c7bd9'
})

const chartRef = ref()
let chart: any = null  // 不要设置echarts响应式实例，会导致意外问题，或者用markRaw工具函数去除响应式

const resize = () => {
  if (chart) {
    chart.resize()
  }
}

onMounted(() => {
  chart = ecahrts.init(chartRef.value)
  chart.setOption({
    tooltip: {
      trigger: 'axis'
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '0%',
      top: '3%',
      containLabel: true
    },
    xAxis: {
      type: 'category',
      boundaryGap: false,
      data: props.xAxis,
      show: false
    },
    yAxis: {
      type: 'value'
    },
    series: [
      {
        data: props.data,
        type: 'line',
        showSymbol: false,
        smooth: false,
        color: props.color
      }
    ]
  })

  window.addEventListener('resize', resize)
})


onUnmounted(() => {
  window.removeEventListener('resize', resize)
})

</script>