<template>
  <el-dialog
    v-model="show"
    title="生成对账确认单"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    append-to-body
    align-center
    width="500"
    @closed="emit('close')"
  >
    <template #default>
      <el-form label-width="100">
        <el-form-item label="时间" class="is-required">
          <el-radio-group v-model="formData.time_type" @change="handleTypeChange">
            <el-radio label="1">按月份</el-radio>
            <el-radio label="2">按日期</el-radio>
          </el-radio-group>

          <template v-if="formData.time_type === '1'">
            <el-date-picker 
              type="month"
              v-model="formData.date_time"
              placeholder="请选择月份"
              value-format="YYYY-MM"
              @change="handleMonthChange"
            />
          </template>
          <template v-if="formData.time_type === '2'">
            <el-date-picker 
              v-model="timeRange"
              type="daterange"
              unlink-panels
              clearable
              value-format="YYYY-MM-DD"
              range-separator="至"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              @change="handleDateChange"
            />
          </template>
        </el-form-item>
        <el-form-item label="人员" class="is-required">
          <el-select 
            v-model="formData.job_users" 
            placeholder="请选择人员"
            multiple
            collapse-tags
            collapse-tags-tooltip
          >
            <el-option 
              v-for="d in users"
              :key="d.user_id_a"
              :label="d.add_user"
              :value="d.user_id_a"
            />
          </el-select>
        </el-form-item>
      </el-form>
    </template>
    <template #footer>
      <div class="text-center">
        <el-button @click="show = false">取消</el-button>
        <el-button type="primary" :loading="loading" @click="handleSubmit">生成确认单</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, reactive } from 'vue'
import { ElMessage } from 'element-plus'
import useTryCatch from '@/hooks/use-try-catch'
import useLoading from '@/hooks/use-loading'
import { addUserList, createBill } from '@/api/translate-management'

const emit = defineEmits(['close', 'success'])

const { loading, setLoading } = useLoading()
const show = ref(true)
const users = ref<{ user_id_a: number; add_user: string; }[]>([])

const formData_raw = {
  date_time: '',
  start_time: '',
  end_time: '',
  job_users: '',
}

const formData = reactive<{
  time_type: string;
  date_time: string;
  start_time: string;
  end_time: string;
  job_users: string|number[];
}>({
  time_type: '',
  ...formData_raw
})
const timeRange = ref('')

const reset = () => {
  Object.keys(formData_raw).forEach( (key) => {
    formData[key] = formData_raw[key]
  } )
}

const handleTypeChange = () => {
  reset()
  users.value = []
}

const handleMonthChange = () => {
  formData.job_users = ''
  getAddUserList()
}

const handleDateChange = (val: [string,string]|null) => {
  if (val) {
    formData.start_time = val[0]
    formData.end_time = val[1]
  } else {
    formData.start_time = ''
    formData.end_time = ''
  }
  formData.job_users = ''
  getAddUserList()
}

const handleSubmit = () => {
  if (!formData.time_type) {
    return ElMessage.warning('请选择时间类型')
  }
  if (!formData.job_users) {
    return ElMessage.warning('请选择人员')
  }
  useTryCatch( async () => {
    setLoading(true)

    const prarms = {
      ...formData,
      job_users: String(formData.job_users)
    }
    const res = await createBill(prarms)
    if (res.code === 200) {
      ElMessage.success('对账确认单生成成功')
      show.value = false
      emit('success')
    } else {
      ElMessage.error(res.msg)
    }

    setLoading(false)
  }, () => setLoading(false) )
}

const getAddUserList = () => {
  useTryCatch( async () => {
    const params = {
      date_time: formData.date_time,
      start_time: formData.start_time,
      end_time: formData.end_time,
      time_type: formData.time_type,
    }
    const res = await addUserList(params)
    if (res.code === 200) {
      const list = res.data
      users.value = list
      const job_users = new Set<number>()
      list.forEach((item: {user_id_a: number; add_user: string;}) => {
        job_users.add(item.user_id_a)
      })

      formData.job_users = [...job_users]
    }
  } )
}
</script>