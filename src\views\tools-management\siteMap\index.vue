<template>
  <div class="scroll-y with-flex">
    <QueryForm
      :form-list="formList"
      :loading="loading"
      @search="handleSearch"
      hide-reset
    />
    <div v-loading="loading" class="sticky-table">
      <CustomTable 
        ref="tableRef"
        :data="tableData"
        :columns="columns"
        :operate-list="operateList"
        :table-method="getList"
        selectable
        @selection-change="handleSelectionChange"
      />
    </div>

    <el-result
      v-show="!channel_item_all.channel_id"
      icon="warning"
      title="请先选择具体渠道!" 
      sub-title="您还未选择渠道"
      style="position: absolute; bottom: 0; width: 100%; height: 100%; background-color: var(--bg-layout); z-index: 19"
    />
  </div>
</template>

<script setup lang="tsx">
import { reactive, ref, toRef, computed, h } from 'vue'
import { useRoute } from 'vue-router'
import { ElMessage, ElMessageBox, ElButton, ElSelect, ElOption } from 'element-plus'
import { storeToRefs } from 'pinia'
import { useParamsStore } from '@/store'
import useTryCatch from '@/hooks/use-try-catch'
import useLoading from '@/hooks/use-loading'
import { getHost } from '@/hooks/use-env'
import Listener from '@/utils/site-channel-listeners'
import { sitemapList, editCycleTime, editWeightValue } from '@/api/tools-management'
import type { sitemapListItem } from '@/api/tools-management'

import QueryForm from '@/components/QueryForm/index.vue'
import CustomTable from '@/components/CustomTable/index.vue'

defineOptions( { name: 'SitemapManagement' } )

const route = useRoute()
const { moduleList, moduleTypeMap, channel_item_all, site_id_all } = storeToRefs(useParamsStore())
const { loading, setLoading } = useLoading()

const typeEntries = {}
Object.values(moduleTypeMap.value).forEach(( values ) => {
  values.forEach( ( { label, value } ) => typeEntries[value] = label )
})

const frequencyList = ['always', 'hourly', 'daily', 'weekly', 'monthly', 'never']

const queryParams = reactive({
  tpl_ids: '',
  name: '',
  module: '',
  type: '',
})

const formList = ref([
  {
    label: '模板id',
    placeholder: '模板id',
    value: toRef(queryParams, 'tpl_ids'),
    component: 'input',
  },
  {
    label: '模板名称',
    placeholder: '模板名称',
    value: toRef(queryParams, 'name'),
    component: 'input',
  },
  {
    label: '模板类别',
    placeholder: '模板类别',
    value: toRef(queryParams, 'module'),
    component: 'select',
    selections: moduleList,
    handleChange: () => {
      queryParams.type = ''
    }
  },
  {
    label: '模板类型',
    placeholder: '模板类型',
    value: toRef(queryParams, 'type'),
    component: 'select',
    selections: computed(() => moduleTypeMap.value[queryParams.module] || [])
  }
])

const tableRef = ref()
const tableData = ref<sitemapListItem[]>([])
const tableSelections = ref<sitemapListItem[]>([])
const columns = ref([
  {
    title: '模板ID',
    dataKey: 'tpl_id',
    width: 100,
  },
  {
    title: '模板名称',
    dataKey: 'name',
    minWidth: 150,
  },
  {
    title: '模板类型',
    dataKey: 'type',
    minWidth: 100,
    cellRenderer: ( scope: { row: sitemapListItem } ) => (
      <>
        { typeEntries[scope.row.type] || '其他' }
      </>
    )
  },
  {
    title: '模板等级',
    dataKey: 'level',
    width: 100,
  },
  {
    title: '更新频率',
    dataKey: 'cycle_time',
    minWidth: 100,
    cellRenderer: ( scope: { row: sitemapListItem } ) => (
      <>
        {
          scope.row.cycle_time
          ? scope.row.cycle_time
          : <span style={ { color: 'var(--el-color-info-light-3)' } }>
            未配置, <br/>
            当前默认频率: { scope.row.default_cycle_time }
          </span>
        }
      </>
    )
  },
  {
    title: '权重',
    dataKey: 'weight_value',
    minWidth: 100,
    cellRenderer: ( scope: { row: sitemapListItem } ) => (
      <>
        {
          scope.row.weight_value
          ? scope.row.weight_value
          : <span style={ { color: 'var(--el-color-info-light-3)' } }>
            未配置, <br/>
            当前默认频率: { scope.row.default_weight_value }
          </span>
        }
      </>
    )
  },
  {
    title: '操作',
    dataKey: '',
    fixed: 'right',
    width: 220,
    cellRenderer: ( scope: { row: sitemapListItem } ) => (
      <>
        <ElButton 
          type='primary' 
          plain 
          onClick={() => handleEditCycleTime(`${scope.row.tpl_id}`, false, scope.row.cycle_time || scope.row.default_cycle_time)}
        > 修改频率 </ElButton>
        <ElButton 
          type='primary' 
          plain
          onClick={() => handleEditWeightValue(`${scope.row.tpl_id}`, false, scope.row.weight_value || scope.row.default_weight_value)}
          > 修改权重 </ElButton>
      </>
    )
  }
])
const operateList = ref([
  {
    title: '批量修改频率',
    method: () => {
      const tpl_ids = tableSelections.value.map(item => item.tpl_id).join(',')
      handleEditCycleTime(tpl_ids, true)
    },
    disabled: computed(() => tableSelections.value.length > 0 ? false : true)
  },
  {
    title: '批量修改权重',
    method: () => {
      const tpl_ids = tableSelections.value.map(item => item.tpl_id).join(',')
      handleEditWeightValue(tpl_ids, true)
    },
    disabled: computed(() => tableSelections.value.length > 0 ? false : true)
  },
  {
    title: '生成sitemap文件',
    method: () => {
      location.href = `${getHost()}/api/v1/sitemap/file/xml?channel_id=${channel_item_all.value.channel_id}`
    },
  }
])

const handleSearch = () => {
  tableRef.value.setPage(1)
  const { page, pageSize } = tableRef?.value?.getPagination()
  getList( { page, pageSize } )
}

const getList = ( { page, pageSize } ) => {
  if (!channel_item_all.value.channel_id) {
    return
  }
  useTryCatch( async () => {
    setLoading(true)

    const params = {
      ...queryParams,
      site_id: site_id_all.value,
      channel_id: channel_item_all.value.channel_id as number,
      page,
      page_size: pageSize
    }

    const res = await sitemapList(params)
    if (res.code === 200) {
      tableData.value = res.data.items
      tableRef.value?.setTotal(res.data.total)
    } else {
      ElMessage.error(res.msg)
    }

    setLoading(false)
  }, () => setLoading(false) )
}
const handleSelectionChange = (selections: sitemapListItem[]) => {
  tableSelections.value = selections
}

const handleEditCycleTime = (tpl_ids: string, is_batch = false, default_value = '') => {
  const cycle_time = ref(default_value)
  ElMessageBox({
    title: `${is_batch ? '批量' : ''}修改频率`,
    showConfirmButton: true,
    showCancelButton: true,
    confirmButtonText: '确认',
    cancelButtonText: '取消',
    message: () => h('div', [
      h('p', { style: { marginBottom: '16px' } }, `${is_batch ? '批量修改所有选中的模板' : '修改该模板下'}, 所有页面的更新频率`),
      h(ElSelect, {
        placeholder: '请选择',
        modelValue: cycle_time.value,
        onChange: (value) => cycle_time.value = value
      }, () => frequencyList.map(( frequency ) => h(ElOption, { label: frequency, value: frequency })))
    ]),
    callback: ( action: string ) => {
      if (action === 'confirm' && cycle_time.value) {
        useTryCatch( async () => {
          const params = {
            site_id:  site_id_all.value,
            channel_id: channel_item_all.value.channel_id as number,
            tpl_ids,
            cycle_time: cycle_time.value
          }

          const res = await editCycleTime(params)
          if (res.code === 200) {
            ElMessage.success(res.msg)
            handleSearch()
          } else {
            ElMessage.error(res.msg)
          }
        } )
      }
    }
  })
}
const handleEditWeightValue = (tpl_ids: string, is_batch = false, default_value = '') => {
  ElMessageBox.prompt(`${is_batch ? '批量修改所有选中的模板' : '修改该模板下'}, 所有页面的优先权比值`, `${is_batch ? '批量' : ''}修改权重`, {
    confirmButtonText: '确认',
    cancelButtonText: '取消',
    inputValue: default_value,
    inputPattern: /^(0\.[1-9]|1(\.0)?)$/,
    inputPlaceholder: '0.1~1.0',
    inputErrorMessage: '允许填入数值为0.1~1.0, 只能填小数点后一位',
    callback: ( action: any ) => {
      if (action.action === 'confirm') {
        useTryCatch( async () => {
          const params = {
            site_id:  site_id_all.value,
            channel_id: channel_item_all.value.channel_id as number,
            tpl_ids,
            weight_value: action.value
          }

          const res = await editWeightValue(params)
          if (res.code === 200) {
            ElMessage.success(res.msg)
            handleSearch()
          } else {
            ElMessage.error(res.msg)
          }
        })
      }
    }
  })
}

Listener( route, () => {
  if (channel_item_all.value.channel_id) {
    handleSearch()
  }
} )
</script>