<template>
  <div class="rowSC">
    <!--  切换sidebar按钮  -->
    <hamburger
      :is-active="sidebar.opened"
      class="hamburger-container"
      @toggle-click="toggleSideBar"
    />
      <!--  面包屑导航  -->
    <breadcrumb class="breadcrumb-container" />
  </div>
</template>

<script setup lang="ts">
import Breadcrumb from './Breadcrumb.vue'
import Hamburger from './Hamburger.vue'
import { useBasicStore } from '@/store/basic'

const basicStore = useBasicStore()
const { sidebar, setToggleSideBar } = basicStore
const toggleSideBar = () => {
  setToggleSideBar()
}
</script>