<template>
  <el-dialog
    v-model="show"
    title="选择供应商"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    append-to-body
    align-center
    width="1200"
    @closed="emit('close')"
  >
    <template #default>
      <el-table
        ref="tableRef"
        :data="tableData"
        row-key="c_id"
        @row-click="handleRowClick"
      > 
        <el-table-column label="任务ID" width="100">
          <template #default="scope">
            {{ scope.row.isChildren ? '' : scope.row.id }}
          </template>
        </el-table-column>
        <el-table-column label="任务名称" minWidth="200" show-overflow-tooltip>
          <template #default="scope">
            {{ scope.row.isChildren ? '' : scope.row.name }}
          </template>
        </el-table-column>
        <el-table-column label="翻译语言" minWidth="100">
          <template #default="scope">
            <strong v-if="scope.row.isChildren" style='padding-left: 38px'>
              {{ scope.row.target_language.toUpperCase() }}
            </strong>
            <template v-else>
              <strong style='width: 20px; display: inline-block'>{{ scope.row.source_language.toUpperCase() }}</strong> 
              &gt; 
              <strong>{{ scope.row.target_language.toUpperCase() }}</strong>
            </template>
          </template>
        </el-table-column>
        <el-table-column label="期望完成时间" width="180">
          <template #default="scope">
            {{ scope.row.isChildren ? '' : scope.row.dead_line }}
          </template>
        </el-table-column>
        <el-table-column label="供应商" minWidth="300" show-overflow-tooltips>
          <template #default="scope">
            <template v-if="scope.row.isChildren && scope.row.type === 1">
              <el-select v-model="scope.row.serviceObj" value-key="id" placeholder="请选择供应商" @change="() => handleServiceChange(scope.row)">
                <el-option 
                  v-for="d in (serviceListMap[`${scope.row.source_language}_${scope.row.target_language}`] || [])"
                  :key="d.id"
                  :label="d.full_name"
                  :value="d"
                />
              </el-select>
            </template>
            <div v-else-if="scope.row.type === 1">
              <span style="cursor: pointer;">{{ serviceMap.get(scope.row.id)?.names || '分配供应商' }}</span>
              <template v-if="!serviceMap.get(scope.row.id)?.all_selected">
                <br><span style="font-size: 12px; color: var(--el-color-error);"> 存在还未选择供应商, 请确认所有供应商已选择完毕 </span>
              </template>
            </div>
            <template v-else> {{ scope.row.type_name }} </template>
          </template>
        </el-table-column>
      </el-table>
    </template>
    <template #footer>
      <div class="text-center">
        <el-button @click="show = false">取消</el-button>
        <el-button type="primary" :loading="loading" @click="handleSubmit">提交任务</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="tsx">
import { ref } from 'vue'
import { ElMessage } from 'element-plus'
import useTryCatch from '@/hooks/use-try-catch'
import useLoading from '@/hooks/use-loading'
import { submitTask, servicesList } from '@/api/translate-management'
import type { listItem } from '@/api/translate-management'

interface extendListItem extends listItem {
  expanded: boolean;
  services: { full_name: string; id: number; }[];
}

type serviceData = {
  [propName: string]: {
    id: number;
    full_name: string;
  }[]
}

const props = defineProps<{ data: listItem[] }>()
const emit = defineEmits(['close', 'success'])

const { loading, setLoading } = useLoading()
const show = ref(true)
const serviceMap = ref(new Map<number, { 
  ids: number[];
  names: string;
  children: any[];
  all_selected: boolean;
  type: number;
}>())

const tableRef = ref()
const tableData = ref<extendListItem[]>([])
const serviceListMap = ref<serviceData>({})

const handleSubmit = () => {
  useTryCatch( async () => {
    setLoading(true)

    const data: { job_id: number; serviceIds: string; }[] = []

    for ( const [ key, { ids, all_selected, type } ] of serviceMap.value.entries() ) {
      if (!all_selected && type === 1) {
        return ElMessage.warning('供应商信息不全, 请确认所有服务对应供应商已选择完毕后进行提交')
      }
      data.push({
        job_id: key,
        serviceIds: ids.join(',')
      })
    }

    const res = await submitTask(data)
    if (res.code === 200) {
      ElMessage.success(res.msg)
      show.value = false
      emit('success')
    } else {
      ElMessage.error(res.msg)
    }

    setLoading(false)
  }, () => setLoading(false) )
}

const getServiceList = () => {
  useTryCatch( async () => {
    const res = await servicesList()
    if (res.code === 200) {
      const data = res.data as serviceData
      Object.entries(data).forEach(( [ key, service ] ) => {
        data[key] = service.map( ( { id, full_name } ) => {
          return { id, full_name }
        } )
      })
      serviceListMap.value = data
      sessionStorage.setItem('serviceData', JSON.stringify(data))
    } else {
      ElMessage.error(res.msg)
    }
  } )
}

const handleServiceChange = (row: extendListItem) => {
  const children = serviceMap.value.get(row.id)?.children
  if (children) {
    const ids = [] as number[]
    const names = new Set<string>()
    children.forEach( ( child ) => {
      if (child.serviceObj.id) {
        ids.push(child.serviceObj.id)
        names.add(child.serviceObj.full_name)
      }
    } )
    console.log(ids)
    const all_selected = ids.length === children.length
    serviceMap.value.set(row.id, { ids, names: [...names].join(','), children, all_selected, type: row.type })
  }
}

tableData.value = props.data.map((item) => {
  if (item.children) {
    item.children.forEach((child: any) => {
      child.serviceObj = {}
    })
  }
  serviceMap.value.set(item.id, { ids: [], names: '', children: item.children || [], all_selected: false, type: item.type })
  return {
    ...item,
    expanded: false,
    services: [],
  }
})

const handleRowClick = ( row: extendListItem ) => {
  tableRef.value?.toggleRowExpansion(row)
}

if (sessionStorage.getItem('serviceData')) {
  serviceListMap.value = JSON.parse(sessionStorage.getItem('serviceData')!)
} else {
  getServiceList()
}
</script>