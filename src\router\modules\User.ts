import type { moduleMap } from '../aysncRoutes'

export default {
  'MyAudit': {
    component: () => import('@/views/user-center/audit/index.vue'),
    path: 'audit',
    title: '我的流程',
    cachePage: true,
  },
  'MyPublish': {
    component: () => import('@/views/user-center/publish/index.vue'),
    path: 'publish',
    title: '我的发布',
    cachePage: true,
  },
  'Setting': {
    component: () => import('@/views/user-center/setting/index.vue'),
    path: 'setting',
    title: '个性化配置',
    cachePage: true,
  }
} as {
  [propName: string]: moduleMap
}