<template>
  <div class="scroll-y with-flex">
    <QueryForm 
      :form-list="formList"
      :loading="loading"
      @search="handleSearch"
      hide-reset
    />
    <div v-loading="loading" class="sticky-table">
      <CustomTable
        ref="tableRef"
        :columns="columns"
        :data="tableData"
        :table-method="getList"
        :operate-list="operateList"
      />
    </div>

    <OperateModal
      v-if="loadModal"
      :row="currentRow"
      @close="loadModal = false"
      @success="handleSearch"
    />
  </div>
</template>

<script setup lang="tsx">
import { ref, reactive, toRef, defineAsyncComponent } from 'vue'
import { ElMessage, ElButton, ElMessageBox } from 'element-plus'
import useTryCatch from '@/hooks/use-try-catch'
import useLoading from '@/hooks/use-loading'
import { siteAnnounceList, publishAnnounce, deleteAnnounce } from '@/api/site-config'
import type { announceItem } from '@/api/site-config'

import QueryForm from '@/components/QueryForm/index.vue'
import CustomTable from '@/components/CustomTable/index.vue'
const OperateModal = defineAsyncComponent( () => import( './components/operateModal.vue' ) )

defineOptions( { name: 'AnnounceConfig' } )

const { loading, setLoading } = useLoading()

const loadModal = ref(false)
const currentRow = ref<announceItem|null>(null)
const queryParams = reactive({
  state: '',
  is_top: '',
  type: ''
})

const formList = ref([
  {
    label: '公告类型',
    placeholder: '公告类型',
    value: toRef(queryParams, 'type'),
    component: 'select',
    selections: [
      {
        value: 'A',
        label: 'A类型'
      },
      {
        value: 'B',
        label: 'B类型'
      }
    ]
  },
  {
    label: '发布状态',
    placeholder: '发布状态',
    value: toRef(queryParams, 'state'),
    component: 'select',
    selections: [
      {
        value: 'able',
        label: '已发布'
      },
      {
        value: 'disable',
        label: '待发布'
      }
    ]
  },
  {
    label: '列表展示状态',
    placeholder: '列表展示状态',
    value: toRef(queryParams, 'is_top'),
    component: 'select',
    selections: [
      {
        value: '1',
        label: '置顶'
      },
      {
        value: '2',
        label: '不置顶'
      }
    ]
  },
])

const tableRef = ref()
const tableData = ref<announceItem[]>([])
const columns = ref([
  {
    title: '公告ID',
    dataKey: 'announcement_id',
    width: 120
  },
  {
    title: '公告标题',
    dataKey: 'title',
    minWidth: 200
  },
  {
    title: '公告类型',
    dataKey: 'type',
    width: 120,
    cellRenderer: ( scope: { row: announceItem } ) => (
      <>
        <span style={ { color: scope.row.type === 'A' ? 'var(--el-color-success)' : '' } }>{scope.row.type}类型</span>
      </>
    )
  },
  {
    title: '展示状态',
    dataKey: 'is_top_name',
    width: 120,
    cellRenderer: ( scope: { row: announceItem } ) => (
      <>
        <span style={ { color: scope.row.is_top === 1 ? 'var(--el-color-success)' : '' } }>{scope.row.is_top_name}</span>
      </>
    )
  },
  {
    title: '发布状态',
    dataKey: 'state',
    width: 120,
    cellRenderer: ( scope: { row: announceItem } ) => (
      <>
        <span style={ { color: scope.row.state === 'able' ? 'var(--el-color-success)' : '' } }>{scope.row.state_name}</span>
      </>
    )
  },
  {
    title: '更新时间',
    dataKey: 'edit_time',
    width: 180
  },
  {
    title: '更新人',
    dataKey: 'user_name',
    width: 120
  },
  {
    title: '发布时间',
    dataKey: 'publish_time',
    width: 180
  },
  {
    title: '发布人',
    dataKey: 'user_name_p',
    width: 120
  },
  {
    title: '操作',
    dataKey: '',
    fixed: 'right',
    width: 240,
    cellRenderer: ( scope: { row: announceItem } ) => (
      <>
        <ElButton type="primary" plain onClick={ () => handleEdit(scope.row) }>编辑</ElButton>
        <ElButton type="success" plain disabled={ scope.row.state === 'able' } onClick={ () => handlePublish(scope.row) }>发布</ElButton>
        <ElButton type="danger" plain onClick={ () => handleDelete(scope.row) }>删除</ElButton>
      </>
    )
  }
])
const operateList = ref([
  {
    title: '创建新公告',
    method: () => {
      currentRow.value = null
      loadModal.value = true
    }
  }
])

const getList = ( { page, pageSize } ) => {
  useTryCatch( async () => {
    setLoading(true)

    const params = {
      ...queryParams,
      page,
      page_size: pageSize
    }
    const res = await siteAnnounceList(params)
    if (res.code === 200) {
      tableData.value = res.data.items
      tableRef.value?.setTotal(res.data.total)
    } else {
      ElMessage.error(res.msg)
    }
    
    setLoading(false)
  }, () => setLoading(false) )
}

const refresh = () => {
  const { page, pageSize } = tableRef?.value?.getPagination()
  getList( { page, pageSize } )
}

const handleSearch = () => {
  tableRef.value.setPage(1)
  refresh()
}

const handleEdit = ( row: announceItem ) => {
  currentRow.value = row
  loadModal.value = true
}
const handlePublish = ( row: announceItem ) => {
  ElMessageBox.confirm('请确认是否发布?', '发布公告', {
    dangerouslyUseHTMLString: true,
    confirmButtonText: '确认',
    cancelButtonText: '取消',
    callback: (action: string) => {
      if (action === 'confirm') {
        useTryCatch( async () => {
          setLoading(true)
          
          const res = await publishAnnounce(`${row.announcement_id}`)
          if (res.code === 200) {
            ElMessage.success('发布成功')
            refresh()
          }else {
            ElMessage.error(res.msg)
          }

          setLoading(false)
        }, () => setLoading(false) )
      }
    }
  })
}
const handleDelete = ( row: announceItem ) => {
  ElMessageBox.confirm('请确认是否删除?', '删除公告', {
    dangerouslyUseHTMLString: true,
    confirmButtonText: '确认',
    cancelButtonText: '取消',
    callback: (action: string) => {
      if (action === 'confirm') {
        useTryCatch( async () => {
          setLoading(true)
          
          const res = await deleteAnnounce(`${row.announcement_id}`)
          if (res.code === 200) {
            ElMessage.success('删除成功')
            refresh()
          }else {
            ElMessage.error(res.msg)
          }

          setLoading(false)
        }, () => setLoading(false) )
      }
    }
  })
}
</script>