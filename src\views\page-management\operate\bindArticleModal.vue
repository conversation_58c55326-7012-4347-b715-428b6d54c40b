<template>
  <el-dialog
    v-model="show"
    :title="`${title}, 请绑定文章`"
    append-to-body
    width="80%"
    @close="() => emit('close', addArticle, backToList)"
  >
    <template #default>
      <QueryForm
        :form-list="formList"
        :loading="loading"
        @search="handleSearch" 
        hide-reset
      />
      <div v-loading="loading" style="min-height: 100px;">
        <CustomTable 
          ref="tableRef"
          :columns="columns"
          :data="tableData"
          :table-method="getList"
          :operate-list="operateList"
          single-selectable
          @current-change="handleSingleSelect"
          height="auto"
          max-height="600px"
        />
      </div>
    </template>
    <template #footer>
      <div class="text-center">
        <el-button plian @click="handleBackToList"> 返回列表 </el-button>
        <el-button type="primary" :loading="loading" :disabled="!artId" @click="handleConfirm"> 关联此文章 </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, reactive, toRef } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import { storeToRefs } from 'pinia'
import { useParamsStore } from '@/store'
import { useBasicStore } from '@/store/basic'
import useTryCatch from '@/hooks/use-try-catch'
import useLoading from '@/hooks/use-loading'
import { getArtTempPage, pageBindArt } from '@/api/page-management'
import { getClassifyData } from '@/api/article-management'
import type { artTempPageItem, artTempPageQuery } from '@/api/page-management'
import type { classifyTreeItem } from '@/api/article-management'

import QueryForm from '@/components/QueryForm/index.vue'
import CustomTable from '@/components/CustomTable/index.vue'

const props = defineProps<{
  isAdd: boolean;
  page_id: number;
  channel_id?: number;
}>()

const emit = defineEmits(['confirm', 'close'])

const router = useRouter()
const { site_id_all, channel_item_all, userList } = storeToRefs(useParamsStore())
const basicStore = useBasicStore()
const { loading, setLoading } = useLoading()
const show = ref(true)
const title = ref(props.isAdd ? '页面创建成功' : '页面还未绑定文章')

const artId = ref(0)
const addArticle = ref(false)
const backToList = ref(false)

const queryParams = reactive<artTempPageQuery>({
  site_id: site_id_all.value,
  channel_id: props.channel_id || channel_item_all.value.channel_id as number,
  cat_id: '',
  user_id: '',
  title: '',
  art_id: '',
})

const classifyData = ref<classifyTreeItem[]>([])

const formList = ref([
  {
    label: '文章ID',
    clearable: true,
    placeholder: '文章ID',
    value: toRef(queryParams, 'art_id'),
    component: 'input',
  },
  {
    label: '文章标题',
    clearable: true,
    placeholder: '文章标题',
    value: toRef(queryParams, 'title'),
    component: 'input',
  },
  {
    label: '文章分类',
    clearable: true,
    placeholder: '文章分类',
    value: toRef(queryParams, 'cat_id'),
    component: 'tree-select',
    valueKey: 'id',
    props: { label: 'label', children: 'children' },
    visibleChange: (val: boolean) => {
      val && initClassifyData()
    },
    data: classifyData
  },
  {
    label: '创建人',
    clearable: true,
    placeholder: '创建人',
    value: toRef(queryParams, 'user_id'),
    component: 'select',
    labelKey: 'real_name',
    valueKey: 'user_id',
    selections: userList
  },
])
const columns = ref([
  {
    title: '文章ID',
    dataKey: 'art_id',
    width: 100,
  },
  {
    title: '文章标题',
    dataKey: 'title',
    minWidth: 200,
  },
  {
    title: '文章分类',
    dataKey: 'cat_name',
    minWidth: 150,
  },
  {
    title: '文章创建人',
    dataKey: 'user_name',
    minWidth: 100,
  },
])
const tableRef = ref()
const tableData = ref<artTempPageItem[]>([])
const operateList = ref([
  {
    title: '创建文章并绑定页面',
    button: true,
    append: true,
    method: () => {
      addArticle.value = true
      handleClose()
      basicStore.delCachedView('ArticleManageOperate')
      router.push({
        name: 'ArticleManageOperate',
        query: {
          type: 'add',
          page_id: props.page_id,
          channel_id: queryParams.channel_id
        }
      })
    },
    disabled: computed<boolean>(() => queryParams.channel_id ? false : true)
  },
])

const initClassifyData = () => {
  if (classifyData.value.length === 0) {
    useTryCatch( async () => {
      const res = await getClassifyData(site_id_all.value, props.channel_id)
      if (res.code === 200) {
        classifyData.value = res.data
      }
    } )
  }
}

const getList = ( { page, pageSize } ) => {
  useTryCatch( async () => {
    setLoading(true)
    const params = {
      ...queryParams,
      site_id: site_id_all.value,
      page,
      page_size: pageSize,
      cat_id: String(queryParams.cat_id),
    }

    const res = await getArtTempPage(params)
    if (res.code === 200) {
      tableData.value = res.data.list
      tableRef.value?.setTotal(res.data.total)
    } else {
      ElMessage.error(res.msg)
    }
    setLoading(false)
  }, () => setLoading(false) )
}

const handleSearch = () => {
  artId.value = 0
  tableRef.value.setPage(1)
  const { page, pageSize } = tableRef?.value?.getPagination()
  getList( { page, pageSize } )
}

const handleClose = () => {
  show.value = false
}
const handleBackToList = () => {
  backToList.value = true
  handleClose()
}

const handleConfirm = () => {
  useTryCatch( async () => {
    setLoading(true)
    const params = {
      tpl_page_id: props.page_id,
      art_id: artId.value
    }
    const res = await pageBindArt(params)
    if (res.code === 200) {
      ElMessage.success('页面绑定文章成功')
      handleClose()
      emit('confirm')
    } else {
      ElMessage.error(res.msg)
    }
    setLoading(false)
  }, () => setLoading(false) )
  
}

const handleSingleSelect = (row?: artTempPageItem) => {
  if (row) {
    artId.value = row.art_id
  }
}

defineExpose({
  show: () => show.value = true,
  hide: handleClose
})
</script>