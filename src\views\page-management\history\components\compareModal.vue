<template>
  <el-dialog
    title="页面历史对比"
    v-model="show"
    top="3vh"
    width="95%"
    append-to-body
    align-center
    @closed="emit('close')"
  >
    <template #default>
      <div style="overflow: auto; max-height: calc(100vh - 15vh);">
        <el-form label-suffix=":" label-width="120px" style="overflow: hidden; word-break: break-all;">
          <el-row :gutter="20">
            <el-col :span="12">
              <el-card shadow="never" style="height: 100%;">
                <template #header>
                  页面-{{ page_id }} V{{ prev?.version }} 基础信息
                </template>
                <template #default>
                  <div>
                    <el-form-item
                      v-for="d in commonFields"
                      :label="d.label"
                      :style="{ color: prev?.[d.key] === current?.[d.key] ? '' : 'var(--el-color-success)' }"
                    >
                      <el-tooltip v-if="d.key === 'page_extend'" content="点击查看扩展字段详情">
                        <span class="with-hand" @click="handleExtendView(prev?.[d.key])">{{ prev?.[d.key] }}</span>
                      </el-tooltip>
                      <el-tooltip v-else-if="d.key === 'buy_info'" content="点击查看购买页定制字段详情">
                        <span class="with-hand" @click="handleBuyInfoView(prev?.[d.key])">{{ prev?.[d.key] }}</span>
                      </el-tooltip>
                      <span v-else>{{ prev?.[d.key] }}</span>
                    </el-form-item>
                  </div>
                  <el-form-item label="自定义字段">
                    <el-table 
                      :data="prev?.field_value"
                    >
                      <el-table-column label="表字段名" prop="field_name" />
                      <el-table-column label="字段名" prop="input_label" show-overflow-tooltip />
                      <el-table-column label="是否存在差异" width="120">
                        <template #default="scope">
                          <span :style="{ color: prevFieldMap.get(`diff-${scope.row.field_name}`) === '1' ? 'var(--el-color-danger)' : '' }">
                            {{ prevFieldMap.get(`diff-${scope.row.field_name}`) === '1' ? '是' : '否' }}
                          </span>
                        </template>
                      </el-table-column>
                      <el-table-column label="操作" width="100">
                        <template #default="scope">
                          <el-button type="primary" plain @click="handleFiledCompare(scope.row.field_name)"> 对比 </el-button>
                        </template>
                      </el-table-column>
                    </el-table>
                  </el-form-item>
                </template>
              </el-card>
            </el-col>
            <el-col :span="12">
              <el-card shadow="never" style="height: 100%;">
                <template #header>
                  页面-{{ page_id }} V{{ current?.version }} 基础信息
                </template>
                <template #default>
                  <div>
                    <el-form-item
                      v-for="d in commonFields"
                      :label="d.label"
                      :style="{ color: prev?.[d.key] === current?.[d.key] ? '' : 'var(--el-color-danger)' }"
                    >
                      <el-tooltip v-if="d.key === 'page_extend'" content="点击查看扩展字段详情">
                        <span class="with-hand" @click="handleExtendView(current?.[d.key])">{{ current?.[d.key] }}</span>
                      </el-tooltip>
                      <el-tooltip v-else-if="d.key === 'buy_info'" content="点击查看购买页定制字段详情">
                        <span class="with-hand" @click="handleBuyInfoView(current?.[d.key])">{{ current?.[d.key] }}</span>
                      </el-tooltip>
                      <span v-else>{{ current?.[d.key] }}</span>
                    </el-form-item>
                  </div>
                  <el-form-item label="自定义字段">
                    <el-table 
                      :data="current?.field_value"
                    >
                      <el-table-column label="表字段名" prop="field_name" />
                      <el-table-column label="字段名" prop="input_label" show-overflow-tooltip />
                      <el-table-column label="是否存在差异" width="120">
                        <template #default="scope">
                          <span :style="{ color: currentFieldMap.get(`diff-${scope.row.field_name}`) === '1' ? 'var(--el-color-danger)' : '' }">
                            {{ currentFieldMap.get(`diff-${scope.row.field_name}`) === '1' ? '是' : '否' }}
                          </span>
                        </template>
                      </el-table-column>
                      <el-table-column label="操作" width="100">
                        <template #default="scope">
                          <el-button type="primary" plain @click="handleFiledCompare(scope.row.field_name)"> 对比 </el-button>
                        </template>
                      </el-table-column>
                    </el-table>
                  </el-form-item>
                </template>
              </el-card>
            </el-col>
          </el-row>
        </el-form>
      </div>
    </template>
  </el-dialog>
  <el-dialog 
    v-model="showDiff" 
    title="自定义字段对比"
    width="90%"
    append-to-body
    align-center
  >
    <template #default>
      <div v-if="showDiff" style="overflow: auto; max-height: calc(100vh - 15vh);">
        <Diff :prev="prevContent" :current="currentCotent" />
      </div>
    </template>
  </el-dialog>

  <el-dialog 
    title="扩展字段详情"
    v-model="showExtend"
    width="90%"
    append-to-body
    align-center
  >
    <template #default>
      <el-form label-suffix=":" label-width="120px">
        <div v-for="d in page_extend" class="extend-item">
          <el-form-item label="字段名称"> {{ d.key }} </el-form-item>
          <el-form-item label="字段值">
            <el-input type="textarea" v-model="d.val" :rows="3" readonly />
          </el-form-item>
        </div>
      </el-form>
    </template>
  </el-dialog>

  <buyInfoModal v-if="showBuyInfo" :buy-info="buyInfo" @close="showBuyInfo = false" />
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { ElMessage } from 'element-plus'
import useParamsStore from '@/store/modules/params'
import useTryCatch from '@/hooks/use-try-catch'
import { getComparisons } from '@/api/page-management'
import type { comparisonItem } from '@/api/page-management'

import Diff from '@/components/CodeDiff/index.vue'
import buyInfoModal from './buyInfoModal.vue'

const props = defineProps<{
  tpl_id: number|string;
  page_id: number|string;
  version_ids: string;
}>()
const emit = defineEmits(['close'])

const commonFields = [
  {
    label: '页面标题',
    key: 'title'
  },
  {
    label: '页面keyword',
    key: 'keyword'
  },
  {
    label: '页面Description',
    key: 'description'
  },
  {
    label: '页面URL',
    key: 'url'
  },
  {
    label: '备份说明',
    key: 'remark'
  },
  {
    label: '拓展字段',
    key: 'page_extend'
  },
  {
    label: '购买页定制',
    key: 'buy_info'
  }
]

const { site_id_all } = useParamsStore()
const show = ref(true)
const showDiff = ref(false)
const showExtend = ref(false)
const page_extend = ref<{key: string; val: string}[]>([])

const prev = ref<comparisonItem>()
const current = ref<comparisonItem>()
const prevContent = ref('')
const currentCotent = ref('')

const prevFieldMap = new Map<string,string>()
const currentFieldMap = new Map<string,string>()

const showBuyInfo = ref(false)
const buyInfo = ref<any>('')

const getDetail = () => {
  useTryCatch( async () => {
    const { tpl_id, page_id, version_ids } = props
    const res = await getComparisons(site_id_all, tpl_id, page_id, version_ids)
    if (res.code === 200) {
      prev.value = res.data[1]
      current.value = res.data[0]
      handleCompareFields()
    } else {
      ElMessage.error(res.msg)
    }
  } )
}

const handleCompareFields = () => {
  prev.value?.field_value.forEach(( field ) => {
    prevFieldMap.set(field.field_name, field.field_value)
  })
  current.value?.field_value.forEach(( field ) => {
    currentFieldMap.set(field.field_name, field.field_value)
  })

  const fieldMap = new Map(prevFieldMap)
  fieldMap.forEach(( value, key ) => {
    let isDiff = '0'
    if (value !== currentFieldMap.get(key)) {
      isDiff = '1'
    }
    prevFieldMap.set(`diff-${key}`, isDiff)
    currentFieldMap.set(`diff-${key}`, isDiff)
  })
}

const handleFiledCompare = (field_name: string) => {
  prevContent.value = prevFieldMap.get(field_name) || ''
  currentCotent.value = currentFieldMap.get(field_name) || ''
  showDiff.value = true
}

const handleExtendView = (pageExtend?: string) => {
  if (pageExtend) {
    const extend = JSON.parse(pageExtend)
    page_extend.value = extend
    showExtend.value = true
  }
}

const handleBuyInfoView = (_buyInfo?: string) => {
  if (_buyInfo) {
    buyInfo.value = JSON.parse(_buyInfo)
    showBuyInfo.value = true
  }
}

getDetail()
</script>

<style lang="scss" scoped>
:deep(.el-card) {
  display: flex;
  flex-direction: column;
  .el-card__body {
    flex: 1;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
  }
}
.extent-item {
  border: 1px dashed var(--el-border-color-light); 
  margin-bottom: 10px;
  padding: 10px; 
  border-radius: 6px;
}
</style>