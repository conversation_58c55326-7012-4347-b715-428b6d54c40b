import type { moduleMap } from '../aysncRoutes'
export default {
  'ArticleManageList': {
    component: () => import('@/views/article-management/index.vue'),
    path: 'article-manage',
    title: '文章列表',
    cachePage: true
  },
  'ArticleClassifyList': {
    component: () => import('@/views/article-management/classify/index.vue'),
    path: 'article-classify',
    title: '文章分类',
    cachePage: true
  },
  'ArticleManageOperate': {
    component: () => import('@/views/article-management/operate/index.vue'),
    path: 'article-manage-operate',
    title: '文章编辑',
    cachePage: true,
    hidden: true,
    hasDropdown: true,
    queryRequired: true
  },
  'ArticleClassifyOperate': {
    component: () => import('@/views/article-management/classify/operate.vue'),
    path: 'article-classify-operate',
    title: '文章分类编辑',
    hidden: true,
  },
  'KeywrodManagement': {
    component: () => import('@/views/article-management/keyword/index.vue'),
    path: 'keyword-manage',
    title: '关键词库',
    cachePage: true,
  },
  'KeywordDetail': {
    component: () => import('@/views/article-management/keyword/detail.vue'),
    path: 'keyword-detail',
    title: '关键词详情',
    cachePage: true,
    hidden: true,
    queryRequired: true
  },
  'ArticleAuthorList': {
    component: () => import('@/views/article-management/author/index.vue'),
    path: 'author-manage',
    title: '文章作者',
    cachePage: true,
  },
  'ArticleAuthorOperate': {
    component: () => import('@/views/article-management/author/operate.vue'),
    path: 'author-operate',
    title: '作者编辑',
    cachePage: false,
    hidden: true,
  },
  'OdArticleList': {
    component: () => import('@/views/article-management/od/index.vue'),
    path: 'od-article-list',
    title: '外包文章',
    cachePage: true,
    hidden: true
  },
  'ArticleHistory': {
    component: () => import('@/views/article-management/history/index.vue'),
    path: 'article-history',
    title: '文章历史记录',
    cachePage: true,
    hidden: true,
  },
  'ClassifyHistory': {
    component: () => import('@/views/article-management/classify/history.vue'),
    path: 'article-classify-history',
    title: '文章分类历史记录',
    cachePage: true,
    hidden: true,
  },
} as {
  [propName: string]: moduleMap
}