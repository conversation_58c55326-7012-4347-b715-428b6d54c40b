<template>
  <div class="resource-center-container">
    <template v-if="channel_item_all.channel_id">
      <div class="file-display-area" :style="{'--de-height': deHeight}">
        <div class="menu-tree">
          <div class="menu-bar">
            <el-tooltip content="创建子文件夹">
              <SvgIcon icon-class="folder-add" @click="handleAppend(null)" />
            </el-tooltip>
            <span v-show="!isRootNode">
              <el-tooltip content="重命名">
                <SvgIcon icon-class="folder-rename" @click="handleRename(null)" />
              </el-tooltip>
              <el-tooltip content="删除">
                <SvgIcon icon-class="delete" @click="handleRemove(null)" />
              </el-tooltip>
            </span>
          </div>
          <div style="padding: 0 8px 16px 0;">
            <el-input v-model="filterText" placeholder="输入目录名进行过滤"></el-input>
          </div>
          <el-tree
            ref="menuTreeRef"
            :data="menuData"
            :props="menuProps"
            node-key="id"
            :highlight-current="false"
            :render-after-expand="true"
            :expand-on-click-node="false"
            :default-expanded-keys="defaultExpand"
            :filter-node-method="filterNode"
            @node-click="handleNodeClick"
            @node-contextmenu="handleContextmenu"
          />
        </div>
        <div
          class="file-panel"
          :class="{isDraging: dragover}"
          @drop.prevent="onDrop"
          @dragover.prevent="onDragover"
          @dragleave.prevent="dragover = false"
        >
          <div class="top-func-area">
            <div class="filter-area">
              <el-space wrap :size="12">
                <el-input 
                  v-model="fileListParams.file_name" 
                  :placeholder="`${buttonTypeName}名称`" 
                  clearable
                  style="width: 200px;"
                />
                <el-select
                  v-model="fileListParams.publish"
                  :placeholder="`${buttonTypeName}状态`"
                  :teleported="false"
                  clearable
                >
                  <el-option v-for="[value, label] of statusMapFilter" :key="value" :label="label" :value="value" />
                </el-select>
                <el-select
                  v-model="fileListParams.user_id_e"
                  :placeholder="`操作人`"
                  :teleported="false"
                  filterable
                  clearable
                >
                  <el-option v-for="d in userList" :key="d.user_id" :label="d.real_name" :value="d.user_id" />
                </el-select>
                <el-cascader
                  v-model="cascaderValue"
                  :placeholder="`排序`"
                  :teleported="false"
                  filterable
                  clearable
                  :options="options"
                  :props="{expandTrigger: 'hover'}"
                  @change="handleSortChange"
                />
                <el-button type="primary" :icon="Search" @click="handleSearch"> 查询 </el-button>
              </el-space>
            </div>
            <div class="upload" style="display: none;">
              <el-space direction="vertical" :size="0">
                <template v-if="fileType === 1">
                  <el-tooltip :disabled="showBottom" :content="`后台${buttonTypeName}压缩有延迟，上传完成后可点击查询按钮刷新页面数据`">
                    <el-button type="primary" :icon="Upload" @click="handleImgUpload">上传{{ buttonTypeName }}</el-button>
                  </el-tooltip>
                </template>
                <template v-else>
                  <el-tooltip :disabled="fileType != 2 || showBottom" :content="`后台${buttonTypeName}压缩有延迟，请稍后刷新页面数据(仅支持MP4格式压缩)`">
                    <el-upload
                      ref="uploadRef"
                      action="string"
                      :multiple="true"
                      :auto-upload="false"
                      :show-file-list="false"
                      :on-success="handleUploadSuccess"
                      :on-change="handleFileChange">
                      <el-button ref="uploadButtonRef" type="primary" :icon="Upload" @click="handleBatch_noUpdate">上传{{ buttonTypeName }}</el-button>
                    </el-upload>
                  </el-tooltip>
                </template>
              </el-space>
            </div>
            <PublicOperate :operate-list="operateList" style="--el-font-weight-primary: 400" />
            <div v-show="selectedFiles.size" style="position: absolute; bottom: -20px; font-size: 14px; color: var(--el-text-color-regular)">
              已选中{{ `${selectedFiles.size}${fileType === 1 ? '张' : '个'}${buttonTypeName}` }}
            </div>
          </div>
            <div
              v-show="!loadList"
              ref="scrollLoadContainerRef"
              class="scroll-load-container"
              v-infinite-scroll="handleScrollLoad"
              :infinite-scroll-immediate="false" 
              :infinite-scroll-disabled="loading"
              :infinite-scroll-distance="15"
              :style="{ '--columns': columns }"
            >
              <template v-if="fileList.length > 0">
                <div class="scroll-container">
                  <div
                    v-for="d in fileList"
                    :key="d.id"
                    class="file-item-container"
                  >
                    <div 
                      class="item-grid" 
                      :class="{selected: d.selected}"
                      @click.stop="handleFileSelect(d)"
                      @contextmenu.prevent="handleFileContextmenu($event, d)"
                    >
                      <span v-if="d.audit === 1" class="tag" :style="{ '--bg-color': 'var(--el-color-danger)' }">
                        待审核
                      </span>
                      <span 
                        v-else-if="d.publish"
                        class="tag" 
                        :style="{'--bg-color': d.publish == 3 ? 'var(--el-color-success)' : d.publish == 4 ? 'var(--el-color-danger)' : 'var(--el-color-primary)'}"
                      >
                        {{ statusMap.get(d.publish) }}
                      </span>
                      <div class="preview">
                        <template v-if="imgReg.test(d.suffix) && d.publish == 3">
                          <img :src="`${previewUrl(d.file_url)}?t=${new Date().getTime()}`" />
                        </template>
                        <template v-else>
                          <SvgIcon :icon-class="fileSuffixReg.test(d.suffix) ? d.suffix : 'file'" class="preview-icon" />
                        </template>
                      </div>
                      <div class="content">
                        <div class="title" :title="d.file_name">
                          {{ d.file_name }}
                        </div>
                        <div class="info">
                          {{ d.compress == 1 ? '压缩中' : formatSize(d.file_size) }}
                          |
                          {{ d.edit_name }}
                          <div style="padding-top: 12px;"> {{ d.edit_time }} </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </template>
              <template v-else>
                <el-empty description="暂无数据" style="width: 100%" />
              </template>
            </div>
          <template v-if="loadList">
            <ListView
              ref="listviewRef"
              :file-list-params="fileListParams"
              :format-size="formatSize"
              @selection-change="handleSelectionChange"
              @preview="handleListPreview"
              @download="handleListDownload"
              @rename="handleListRename"
              @delete="handleListDelete"
              @copy="handleListCopy"
              @compress="handleListCompress"
              @filelog="handleListOpenFileLog"
            />
          </template>
        </div>
      </div>

      <template>
        <ImgUpload
          ref="imgUploadRef"
          :base-path="basePath"
          @cancel="handleCloseImgUpload"
          @qualityChange="handleQualityChange"
          @save="handleImgListUpload"
          @compress-change="handleCompressChange"
        />
      </template>
      <div style="text-align: center; padding-top: 20px" v-if="showBottom">
        <el-button v-if="!selectedMulti" type="primary" :disabled="selectedFiles.size === 0" @click="handleConfirm"> 确定 </el-button>
        <el-button  @click="handleCancel"> 取消 </el-button>
      </div>
    </template>
    <template v-else>
      <el-result
        icon="warning"
        title="请先选择具体渠道!" 
        sub-title="您还未选择渠道"
        class="custom-result custom-layout-box"
      />
    </template>

    <ul 
      ref="contextMenuRef" 
      v-show="contextMenuVisible"
      :style="{ left: menuLeft, top: menuTop }" 
      class="fixed-contextmenu"
    >
      <template v-if="menuType === 1">
        <li class="item" @click="handleAppend(menuNode)"> 创建子文件夹 </li>
        <template v-if="!isContextRootNode">
          <li class="item" @click="handleRename(menuNode)"> 重命名 </li>
          <li class="item" @click="handleRemove(menuNode)"> 删除 </li>
        </template>
      </template>
      <template v-else>
        <li class="item" @click="handleCopyLink(menuFile)"> 复制链接 </li>
        <li class="item" @click="handlePreview(menuFile)"> 查看 </li>
        <li class="item" @click="handleFileRename(menuFile)"> 重命名 </li>
        <li class="item" @click="handleFileDownload(menuFile)"> 下载 </li>
        <li class="item" @click="handleImgCompress(menuFile)" v-if="fileType == 1"> 压缩图片 </li>
        <li class="item" @click="handleOpenFileLog(menuFile as selectFileItem)"> 文件历史 </li>
        <li class="item" @click="handleRemoveFile(menuFile)"> 删除 </li>
      </template>
    </ul>

    <ReplaceFiles 
      v-if="sameFiles.length > 0 && uploadingFiles === 0" 
      :file-list="sameFiles"
      @replace="handleReplace"
      @save="handleSaveBoth"
      @replace-all="handleReplaceAll"
      @save-all="handleSaveBothAll"
      @close="handleReplaceClose"
    />

    <HistoryModal 
      v-if="loadHistoryModal"
      :log-id="logId"
      @close="() => loadHistoryModal = false"
    />
  </div>
</template>
<script setup lang="ts">
import { ref, reactive, computed, watch, nextTick, onMounted, defineAsyncComponent } from 'vue'
import { ElMessage } from 'element-plus'
import { Upload, Search} from '@element-plus/icons-vue'
import { storeToRefs } from 'pinia'
import { useParamsStore, useTaskRecordStore } from '@/store'
import useLoading from '@/hooks/use-loading'
import useTryCatch from '@/hooks/use-try-catch'
import useFile from './hooks/use-file'
import useFolder from './hooks/use-folder'
import useContextMenu from './hooks/use-contextmenu'
import useFilter from './hooks/use-filter'
import { getFolderList, fileUpload, getFileList, fileReplace } from '@/api/resource-center'
import type { Ref } from 'vue'
import type { folderItem, fileItem, fileQueryParams } from '@/api/resource-center'
import type Node from 'element-plus/es/components/tree/src/model/node'
import type { UploadFile, UploadRawFile } from 'element-plus'
import type { RouteLocationNormalizedLoaded } from 'vue-router'
import { imgReg, videoSupportType, fileSuffixReg, handleIsRootNode, statusMap, statusMapFilter } from './types'
import type { selectFiles, selectFileItem, ImgListItem } from './types'
import Listener from '@/utils/site-channel-listeners'
import emitter from '@/utils/bus'
import { handleConcurrecy, useThrottle } from '@/utils/common'

import SvgIcon from '@/icons/SvgIcon.vue'
import PublicOperate from '@/components/PublicOperate/index.vue'

// const ImgUpload = defineAsyncComponent(() => import('./components/img-upload.vue'))
import ImgUpload from './components/img-upload.vue'
import HistoryModal from './components/history-modal.vue'
const ReplaceFiles = defineAsyncComponent(() => import('./components/repeat-files.vue'))
const ListView = defineAsyncComponent(() => import('./list.vue'))

defineOptions( { name: 'ResourceCenter' } )

const props = withDefaults(
  defineProps<{
    fileType?: number;
    deHeight?: string;
    showBottom?: boolean;
    cacheRoute?: RouteLocationNormalizedLoaded;
  }>(), {
    fileType: () => 3,
    deHeight: () => '210px'
  }
)
const emit = defineEmits<
  {
    (e: 'confirm', arg_1: { url: string; name: string; }): void;
    (e: 'cancel'): void;
  }
>()

const { site_id_all, site_name_all, channel_item_all, userList } = storeToRefs(useParamsStore())
const { appendRecord, typeMap } = useTaskRecordStore()
const { loading, setLoading } = useLoading()
const throttle = useThrottle()
const throttle2 = useThrottle(300)

let batch_no = new Date().getTime()
// 响应式的修改分页数量: [ 屏幕宽度: ， 分页数目 ]
const columns = ref(4)

const menuProps = {
  label: 'name',
  children: 'children',
}

const buttonGroupType = ref<1|2>(1)
const menuData = ref<folderItem[]>([])
const menuTreeRef = ref()
const fileList = ref<fileItem[]>([])
const total = ref(0)
const currentNode = ref<Node>() // 用于记录当前选择目录节点
const defaultExpand = ref<number[]>([])

const loadImgUpload = ref(false)
const loadHistoryModal = ref(false)
const logId = ref(0)
const uploadRef = ref()
const imgUploadRef = ref()
const scrollLoadContainerRef = ref()
const quality = ref(props.fileType === 1 ? 60 : 100)
const compress = ref(props.fileType === 3 ? 0 : 1)
const dragover = ref(false)

const isRootNode = ref(true) // 当前选择目录是否为根目录
const selectedFiles = ref<selectFiles>(new Map())
const uploadingFiles = ref(0) // 正在上传的文件数
const totalFiles = ref(0) //单次批量上传的文件数
const batchFiles = ref<UploadRawFile[]>([]) // 批量上传文件数
const sameFiles = ref<{file: UploadRawFile; fileName: string}[]>([])

const filterText = ref('')
watch(filterText, (val) => {
  throttle2(() => menuTreeRef.value!.filter(val))
})
// 目录过滤
const filterNode = (value: string, data: folderItem, node: Node) => useFilter(value, data, node)

// 引用文件及目录处理相关方法
const {
  previewUrl,
  formatSize,
  handleCopyLink,
  handlePreview,
  handleFileRename,
  handleFileDownload,
  handleImgCompress,
  handleRemoveFile,
  getFileItems
} = useFile(selectedFiles, (file_num) => {
  if (currentNode.value) {
    const { data } = currentNode.value
    reset()
    handleGetFileList(data.id)
  }
  // appendRecord({
  //   title: typeMap['delete'],
  //   type: '3,4',
  //   task_id: '100',
  //   task_type: 'upload',
  //   file_num,
  //   site_id: site_id_all.value,
  //   site_name: site_name_all.value,
  //   channel_id: channel_item_all.value.channel_id as number,
  //   channel_name: channel_item_all.value.channel_code as string,
  // })
})
const { handleAppend, handleRemove, handleRename } = useFolder(
  props.fileType,
  site_id_all,
  channel_item_all,
  currentNode as Ref<Node>,
  defaultExpand,
  () => {
    reset()
    const default_id = menuData.value[0].id
    handleGetFileList(default_id)
    nextTick(() => {
      currentNode.value = menuTreeRef.value.getNode(default_id)
    })
  }
)
/* 右键快捷菜单 */
const contextMenuRef = ref<HTMLInputElement|null>(null)
const contextMenuVisible = ref(false)
const menuType = ref<1|2>(1)
const menuNode = ref<Node|null>(null)
const menuFile = ref<selectFileItem|null>(null)
const menuLeft = ref(0)
const menuTop = ref(0)
const isContextRootNode = ref(false) // 右键选中目录是否为根目录
/* 右键快捷菜单 */
const { handleContextmenu, handleFileContextmenu, handCloseContextMenu } = useContextMenu(
  contextMenuRef,
  contextMenuVisible,
  menuType,
  menuNode,
  menuFile,
  menuLeft,
  menuTop,
  isContextRootNode,
  props.showBottom
)
watch(contextMenuVisible, (value) => {
  if (value) {
    document.body.addEventListener('click', handCloseContextMenu)
  } else {
    document.body.removeEventListener('click', handCloseContextMenu)
  }
})

const selectedMulti = computed<boolean>(() => {
  const size = selectedFiles.value.size
  return size > 1 ? true : false
})
const basePath = computed<string>(() => {
  const { data } = currentNode.value ? currentNode.value : { data: null }
  const path = data ? data.file_url : menuData.value[0] ? menuData.value[0].file_url : ''
  return path || ''
})
const buttonTypeName = computed(() => {
  const name = props.fileType === 1 ? '图片' : props.fileType === 2 ? '视频' : '文件'
  return name
})
const loadFinished = computed(() => fileList.value.length >= total.value)

const orderList = [
  {
    value: 'add_time',
    label: '添加时间'
  },
  {
    value: 'edit_time',
    label: '编辑时间'
  },
  {
    value: 'file_size',
    label: '文件大小'
  }
]
const options = [
  {
    value: 'order_asc',
    label: '顺序排序',
    children: orderList
  },
  {
    value: 'order_desc',
    label: '逆序排序',
    children: orderList
  }
]
const cascaderValue = ref([])
const handleSortChange = (value: ['order_asc'|'order_desc', string]) => {
  fileListParams.order_asc = ''
  fileListParams.order_desc = ''

  if (value && value.length === 2) {
    const [ key, val ] = value
    fileListParams[key] = val
  }
}
const fileListParams = reactive<fileQueryParams>({
  file_name: '',
  publish:'',
  user_id_e: '',
  type: props.fileType,
  page: 1,
  page_size: 10,
  path_id: 0,
  order_asc: '',
  order_desc: '',
})

const uploadButtonRef = ref()
const handleUploadTrigger = () => {
  uploadRef.value?.clearFiles()
  uploadButtonRef.value?.$el?.click()
}

const disableMulti = computed(() => selectedFiles.value.size === 0 || selectedFiles.value.size > 1)
const disableSingle = computed(() => selectedFiles.value.size === 0)
const operateList = ref([
  {
    title: computed(() => `上传${buttonTypeName.value}`),
    button: true,
    append: true,
    icon: 'upload',
    method: () => {
      props.fileType === 1 
      ? handleImgUpload()
      : handleUploadTrigger()
    }
  },
  {
    title: ``,
    append: true,
    icon: computed(() => loadList.value ? 'view-grid' : 'view-list'),
    tooltip: computed(() => loadList.value ? '网格模式' : '列表模式'),
    method: () => {
      handleSwitch()
    }
  },
  {
    title: ``,
    append: true,
    hide: computed(() => loadList.value),
    icon: 'select-all',
    tooltip: '全选',
    method: () => {
      fileList.value.forEach((fileItem) => {
        fileItem.selected = true
        selectedFiles.value.set(fileItem.id, fileItem)
      })
    }
  },
  {
    title: ``,
    append: true,
    hide: computed(() => loadList.value),
    icon: 'clear',
    tooltip: '取消已选',
    method: () => {
      for (const fileItem of selectedFiles.value.values()) {
        fileItem.selected = false
      }
      selectedFiles.value.clear()
    }
  },
  {
    title: '复制链接',
    icon: 'copy',
    disabled: disableMulti,
    method: () => {
      handleCopyLink()
    }
  },
  {
    title: '查看',
    icon: 'preview',
    disabled: disableMulti,
    method: () => {
      handlePreview()
    }
  },
  {
    title: '重命名',
    icon: 'folder-rename',
    disabled: disableMulti,
    method: () => {
      handleFileRename()
    }
  },
  {
    title: '批量下载',
    icon: 'export',
    disabled: disableSingle,
    method: () => {
      handleFileDownload()
    }
  },
  {
    title: '压缩图片',
    icon: 'compress',
    hide: computed(() => props.fileType !== 1),
    disabled: disableSingle,
    method: () => {
      handleImgCompress()
    }
  },
  {
    title: '删除',
    icon: 'delete',
    disabled: disableSingle,
    method: () => {
      handleRemoveFile()
    }
  },
])

// 必须放入mounted中调用
const initView = () => {
  nextTick(() => {
    // 渲染完毕后动态计算需要获取的单页数，避免无法触发滚动
    const SLC_el = scrollLoadContainerRef.value as HTMLElement
    const { width, height } = SLC_el.getBoundingClientRect()
    columns.value = Math.floor( width / 300 ) || 1
    const itemHeight = 120
    const rows = Math.ceil( height / itemHeight )
    const page_size = rows * columns.value
    fileListParams.page_size = page_size
  })
}
// 初始化数据加载及视图渲染,默认展开节点数组需要在树形组件数据前完成赋值，否则无法展开节点
const init = async () => {
  const data = await handleGetFolderList()
  const default_id = data[0].id
  defaultExpand.value.push(default_id)
  menuData.value = data
  fileListParams.path_id = default_id
  handleGetFileList(default_id)

  nextTick(() => {
    currentNode.value = menuTreeRef.value.getNode(default_id)
    menuTreeRef.value.setCurrentKey(default_id)
  })
}
const reset = () => {
  fileListParams.page = 1
  fileList.value = []
  selectedFiles.value.clear()
}
const handleGetFolderList = async () => new Promise<folderItem[]>((resolve) => {
  const data: any[] = []
  useTryCatch( async () => {
    const params = {
      site_id: site_id_all.value,
      channel_id: channel_item_all.value.channel_id as number,
      type: props.fileType,
    }
    const res = await getFolderList(params)
    if (res.code === 200) {
      data.push(...res.data)
    } else {
      ElMessage.error(res.msg)
    }
    resolve(data)
  }, () => resolve(data))
}) 
const handleGetFileList = (id?: number) => {
  if (!id) {
    const { data } = currentNode.value as Node
    id = data.id
  }
  useTryCatch( async () => {
    const params = {
      ...fileListParams,
      path_id: id as number
    }
    fileListParams.page++
    setLoading(true)

    const res = await getFileList(params)
    if (res.code === 200) {
      fileList.value.push(...res.data.list)
      total.value = res.data.total
    }
    setLoading(false)
  }, () => setLoading(false))
}

const handleNodeClick = (data: folderItem, node: Node) => {
  buttonGroupType.value = 1
  if (currentNode.value && data.id === currentNode.value.data.id) return
  currentNode.value = node
  isRootNode.value = handleIsRootNode(node)
  reset()
  fileListParams.path_id = data.id
  handleGetFileList(data.id)
  if (loadList.value) {
    listviewRef.value?.refresh()
  }
}
const handleSearch = () => {
  if (loadList.value) {
    listviewRef.value?.refresh()
  } else {
    reset()
    handleGetFileList()
  }
  
}

const onDrop = (e: DragEvent) => {
  dragover.value = false
  if (props.fileType === 1) {
    handleImgUpload()
    nextTick(() => {
      imgUploadRef.value?.drop(e)
    })
    return
  }
  const files = e.dataTransfer?.files
  if (files) {
    handleBatchUpload([].slice.call(files).map( (file: UploadRawFile) => file))
  }
}
const onDragover = () => {
  dragover.value = true
}
const handleImgUpload = () => {
  handleBatch_noUpdate()
  imgUploadRef.value.show()
}
const handleFileUpload = (file: UploadRawFile, isFinished = true, name?: string) => new Promise( (resolve, reject) => {
  useTryCatch( async () => {
    const params = {
      file,
      type: props.fileType,
      site_id: site_id_all.value,
      channel_id: channel_item_all.value.channel_id as number,
      quality: quality.value,
      compress: compress.value,
      batch_no,
      path_id: currentNode.value?.data.id as number,
      is_end: isFinished ? 1 : 0,
      is_batch: totalFiles.value === 1 ? 0 : 1
    }
    uploadingFiles.value++
    const res = await fileUpload(params, name)
    if (res.code === 200) {
      // 将最新上传的数据添加到数组头
      fileList.value.unshift(res.data)
      if (isFinished) {
        batchFiles.value = []
        ElMessage.success(res.msg)
        emitter.emit('noticeUpdate')
        // appendRecord({
        //   title: typeMap['upload'],
        //   type: '3,4',
        //   task_id: res.data.id,
        //   task_type: 'upload',
        //   file_num: totalFiles.value,
        //   site_id: site_id_all.value,
        //   site_name: site_name_all.value,
        //   channel_id: channel_item_all.value.channel_id as number,
        //   channel_name: channel_item_all.value.channel_code as string,
        // })
      }
      resolve(res.data)
    } else if(res.code === 282805) {
      sameFiles.value.push({
        file,
        fileName: file.name
      })
      reject(null)
    } else {
      ElMessage.error(res.msg)
      reject(null)
    }
    uploadingFiles.value--
  }, () => (uploadingFiles.value--))
}).catch(() => {})
// 上传前进行并发控制
const handleBatchUpload = (fileList: UploadRawFile[]) => {
  totalFiles.value = fileList.length
  const promises: (()=>Promise<any>)[] = []
    fileList.forEach((raw, index) => {
    promises.push(() => new Promise( async (res, rej) => {
      const data = await handleFileUpload(raw, (index === fileList.length - 1) ? true : false)
      data ? res(data) : rej(data)
    }))
  })

  handleConcurrecy(promises, fileList.length - 1)
}
const handleUploadSuccess = () => ElMessage.success('上传成功')
const handleFileChange = (file: UploadFile) => {
  uploadRef.value?.clearFiles()
  batchFiles.value.push( file.raw as UploadRawFile )
  throttle(() => {
    handleBatchUpload(batchFiles.value)
  })
}
const handleBatch_noUpdate = () => {
  batchFiles.value = []
  totalFiles.value = 0
  batch_no = new Date().getTime()
}
const handleCloseImgUpload = () => {
  imgUploadRef.value.close()
}
const handleQualityChange = (value: number) => {
  quality.value = value
}
// 处理图片列表上传组件上传事件
const handleImgListUpload = (fileList: ImgListItem[]) => {
  handleCloseImgUpload()
  totalFiles.value = fileList.length
  const promises: (()=>Promise<any>)[] = []
  fileList.forEach((file, index) => {
    const { raw } = file
    promises.push(() => new Promise( async (res, rej) => {
      const data = await handleFileUpload(raw, (index === fileList.length - 1) ? true : false)
      data ? res(data) : rej(data)
    }))
  })
  handleConcurrecy(promises, fileList.length -1)
}
const handleCompressChange = (val: number) => {
  compress.value = val
}

const handleScrollLoad = () => {
  if (!loadFinished.value) {
    handleGetFileList()
  }
}
const handleFileSelect = (fileItem: fileItem) => {
  buttonGroupType.value = 2
  fileItem.selected = !fileItem.selected
  if (fileItem.selected) {
    selectedFiles.value.set(fileItem.id, fileItem)
  } else {
    selectedFiles.value.delete(fileItem.id)
  }

  const size = selectedFiles.value.size
  if (size === 0) {
    buttonGroupType.value = 1
  }
}
/* 处理文件替换逻辑 */
const handleFileReplace = (file: UploadRawFile, isFinished = true, cb?: () => any) => new Promise((resolve, reject) => {
  useTryCatch( async () => {
    const params = {
      file,
      type: props.fileType,
      site_id: site_id_all.value,
      channel_id: channel_item_all.value.channel_id as number,
      quality: quality.value,
      compress: compress.value,
      batch_no,
      path_id: currentNode.value?.data.id as number,
      is_end: isFinished ? 1 : 0,
      is_batch: totalFiles.value === 1 ? 0 : 1
    }
    uploadingFiles.value++
    const res = await fileReplace(params)
    if (res.code === 200) {
      ElMessage.success('替换成功')
      emitter.emit('noticeUpdate')
      // appendRecord({
      //   title: typeMap['replace'],
      //   type: '3,4',
      //   task_id: res.data.id,
      //   task_type: 'upload',
      //   file_num: totalFiles.value,
      //   site_id: site_id_all.value,
      //   site_name: site_name_all.value,
      //   channel_id: channel_item_all.value.channel_id as number,
      //   channel_name: channel_item_all.value.channel_code as string,
      // })
      resolve(res.data)
    } else {
      ElMessage.error(res.msg)
      reject(res)
    }
    if (isFinished) {
      sameFiles.value = []
    }
    uploadingFiles.value--
    cb && cb()
  }, () => {
    cb && cb()
    uploadingFiles.value--
  } )
}).catch(() => {})
const handleReplace = (file: UploadRawFile, index: number) => {
  handleBatch_noUpdate()
  totalFiles.value = 1
  handleFileReplace(file, true, () => sameFiles.value.splice(index, 1))
}
const handleSaveBoth = async (file: UploadRawFile, index: number) => {
  handleBatch_noUpdate()
  totalFiles.value = 1
  const lastIndex = file.name.lastIndexOf('.')
  await handleFileUpload(file, true, `${file.name.slice(0, lastIndex)}-${new Date().getTime()}${file.name.slice(lastIndex)}`)
  sameFiles.value.splice(index, 1)
}
const handleReplaceAll =() => {
  handleBatch_noUpdate()
  const fileList = [...sameFiles.value]
  totalFiles.value = fileList.length
  const promises: (()=>Promise<any>)[] = []
  fileList.forEach(({ file }, index) => {
    promises.push(() => new Promise( async (res, rej) => {
      const data = await handleFileReplace(file, (index === fileList.length - 1) ? true : false)
      sameFiles.value.splice(index, 1)
      data ? res(data) : rej(data)
    }))
  })

  handleConcurrecy(promises, fileList.length - 1)
}
const handleSaveBothAll = () => {
  handleBatch_noUpdate()
  const fileList = [...sameFiles.value]
  totalFiles.value = fileList.length
  const promises: (()=>Promise<any>)[] = []
  fileList.forEach(({ file }, index) => {
    promises.push(() => new Promise( async (res, rej) => {
      const lastIndex = file.name.lastIndexOf('.')
      const data = await handleFileUpload(
        file, 
        (index === fileList.length - 1) ? true : false, 
        `${file.name.slice(0, lastIndex)}-${new Date().getTime()}${file.name.slice(lastIndex)}`
      )
      sameFiles.value.splice(index, 1)
      data ? res(data) : rej(data)
    }))
  })

  handleConcurrecy(promises, fileList.length - 1)
}
const handleReplaceClose = () => {
  sameFiles.value = []
}
/* 处理文件替换逻辑 */

const handleOpenFileLog = (menuFile: selectFileItem) => {
  logId.value = menuFile.id 
  nextTick( () => {
    loadHistoryModal.value = true
  } )
}

/* 列表模式 */
const loadList = ref(false)
const listviewRef= ref()

const handleSwitch = () => {
  buttonGroupType.value = 1
  selectedFiles.value.clear()
  loadList.value = !loadList.value
  if (!loadList.value) {
    handleSearch()
  }
}
const handleSelectionChange = (list: fileItem[]) => {
  const { length } = list
  buttonGroupType.value = length > 0 ? 2 : 1
  selectedFiles.value.clear()
  list.forEach((item) => {
    selectedFiles.value.set(item.id, item)
  })
}
const handleListPreview = (item: selectFileItem) => {
  handlePreview(item)
}
const handleListRename = (item: selectFileItem) => {
  handleFileRename(item)
}
const handleListDownload = (item: selectFileItem) => {
  handleFileDownload(item)
}
const handleListDelete = (item: selectFileItem) => {
  handleRemoveFile(item)
}
const handleListCopy = (item: selectFileItem) => {
  handleCopyLink(item)
}
const handleListCompress = (item: selectFileItem) => {
  handleImgCompress(item)
}
const handleListOpenFileLog = (item: selectFileItem) => {
  handleOpenFileLog(item)
}
/* 列表模式 */

const handleConfirm = () => {
  const files = getFileItems()
  const { url, name } = files[0]
  emit('confirm', { url, name })
}
const handleCancel = () => {
  emit('cancel')
}
// 初始化
onMounted(() => {
  if (channel_item_all.value.channel_id) {
    initView()
    init()
  }
})
if (props.cacheRoute) {
  Listener(props.cacheRoute, () => {
    if (channel_item_all.value.channel_id) {
      reset()
      initView()
      init()
    }
  })
}
</script>