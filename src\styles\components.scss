.scroll-y {
  &.with-flex {
    display: flex;
    flex-direction: column;
  }
  .sticky-table {
    min-height: 500px;
    flex: 1 1 auto;
    overflow: auto;
  }
}
.query-form {
  margin-bottom: 10px;
  padding-bottom: 10px;
  border-bottom: 1px solid var(--el-border-color);
  position: relative;
  .tail-icon {
    transition: opacity .3s;
    opacity: 0;
  }
  &:hover .tail-icon{
    opacity: 1;
  }
  .form-collapse-arrow {
    width: 16px;
    cursor: pointer;
    transition: transform .3s;
    &:hover {
      color: var(--el-color-primary);
    }
  }
  .toggle-form {
    position: absolute;
    width: 100%;
    height: 52px;
    background-color: var(--el-bg-color);
    z-index: 10;
    white-space: nowrap;
    overflow: scroll;
  }
}
.query-form-item {
  width: 220px;
  display: inline-block;
  margin: 0 10px 10px 0;
  transition: width .3s;
  overflow: hidden;
  &.range {
    width: 400px !important;
    .hide {
      width: 0 !important;
    }
  }
  &.hide {
    width: 0 !important;
    margin-right: 0;
  }
}
.custom-table-container {
  position: relative;
  .fixed-tool {
    cursor: pointer;
    position: absolute;
    right: 0;
    top: 0;
    opacity: 0;
    transition: opacity .3s;
    z-index: 19;
  }
  &:hover {
    .fixed-tool {
      opacity: 1;
    }
  }
  .bg-gray {
    background-color: var(--el-color-info-light-9);
  }
  .current-row {
    .el-radio__inner {
      border-color: var(--el-color-primary);
      background: var(--el-color-primary);
      &::after {
        transform: translate(-50%, -50%) scale(1);
      }
    }
  }
}
.operate-bar {
  border-radius: 4px;
  line-height: 1;
  --el-font-weight-primary: 700;
  color: var(--el-text-color-regular);
  margin-bottom: 12px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  height: 32px;
  overflow: hidden;
  .icon {
    width: 1rem;
    height: 1rem;
    &+.title {
      padding-left: 8px;
    }
  }
  .append-item .icon {
    color: var(--el-text-color-primary);
    &.button-icon {
      color: inherit;
    }
  }
}

.content-table table{
  width: 100%;
  border-top: var(--el-table-border);
  border-left: var(--el-table-border);
  border-collapse: collapse;
  text-align: center;
}
.content-table a:hover{
  text-decoration: underline;
  color: var(--el-primary-color);
}
.content-table table tr:first-child {
  font-weight: 700;
  background-color: var(--el-color-info-light-9);
}
.content-table table tr td { width: 50%; }
.content-table table td {
  padding: 12px 10px;
  border-bottom: var(--el-table-border);
  border-right: var(--el-table-border);
  word-break: break-word;
}
.el-step__icon.is-icon {
  border: 2px solid transparent;
  border-radius: 50%;
  padding: 2px;
}
.step-primary .el-step__icon.is-icon{ border-color: var(--el-color-primary); color: var(--el-color-primary); }
.step-primary.step-warning .el-step__icon.is-icon { border-color: var(--el-color-warning); color: var(--el-color-warning); }
.step-primary.step-danger .el-step__icon.is-icon { border-color: var(--el-color-danger); color: var(--el-color-danger); }

/* tinymce */
.tox-collection__group {
  [title='Heading 1'], [title='一级标题'] {
    display: none !important;
  }
}
.tox div.tox-swatch:not(.tox-swatch--remove) svg {
  display: none;
  fill: #222f3e;
  height: 24px;
  margin: calc((30px - 24px)/ 2) calc((30px - 24px)/ 2);
  width: 24px;
}
.tinymce-editor-container {
  .el-dialog__wrapper {
    z-index: 99999 !important;
  }
}
.resource-dialog .el-dialog__body {
  padding-top: 0px;
  padding-bottom: 16px;
}
/* tinymce */
.display-upload-img {
  list-style: none;
  margin: 15px 0;
  padding: 15px;
  border: 1px dashed var(--el-border-color);
}
.display-upload-img.isDraging {
  border-color: var(--el-color-primary);
  border-style: dashed;
  position: relative;
}
.display-upload-img.isDraging::after {
  content: '';
  position: absolute;
  display: flex;
  align-items: center;
  justify-content: center;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  background-color: var(--el-fill-color-lighter);
}
.display-upload-img .list-card {
  position: relative;
  overflow: hidden;
  background-color: (--el-bg-color);
  border: 1px dashed var(--el-border-color);
  border-radius: 6px;
  box-sizing: border-box;
  width: 100px;
  height: 100px;
  margin: 0 8px 8px 0;
  display: inline-block;
  background-size: 100% auto;
  background-position: center;
  background-repeat: no-repeat;
}
.display-upload-img .img-card {
  overflow: visible;
}
.display-upload-img .upload-card,
.img-card .el-icon-close:hover {
  &:hover {
    border-color: var(--el-color-primary);
    color: var(--el-color-primary);
  }
}
.display-upload-img .list-card .el-icon-plus {
  cursor: pointer;
  width: 100px;
  height: 100px;
  line-height: 100px;
  transform: scale(.5);
}
.img-card .el-icon-close {
  position: absolute;
  right: 0;
  top: 0;
  width: 16px;
  height: 16px;
  cursor: pointer;
  transform: translate(50%, -50%);
  border-radius: 50%;
  background-color: var(--el-border-color);
}
.custom-result {
  height: 100%;
}
.resource-center-container {
  width: 100%;
  height: 100%;
  --scroll-height: calc(100% - 110px - 20px);
  .tag {
    display: inline-block;
    position: relative;
    padding: 3px 9px;
    font-size: 12px;
    color: var(--bg-color, inherit);

    &::after {
      content: '';
      position: absolute;
      left: 0;
      top: 0;
      width: 100%;
      height: 100%;
      border-radius: var(--radius, 0px 0px 8px 0px);
      background-color: var(--bg-color, transparent);
      opacity: .12;
    }
  }
  .file-display-area {
    display: flex;
    flex-wrap: nowrap;
    align-items: stretch;
    height: calc(100vh - var(--de-height));
    min-height: 500px;
    .menu-tree {
      --el-color-primary-light-9: transparent;
      --el-fill-color-light: transparent;
      width: 25%;
      min-width: 250px;
      max-width: 288px;
      border-right: 1px solid var(--el-border-color);
      max-height: 100%;
      overflow: auto;
      flex-shrink: 0;
      .menu-bar {
        padding: 16px;
        border-bottom: 1px solid var(--el-border-color);
        margin-bottom: 16px;
        .svg-icon {
          width: 18px;
          height: 18px;
          margin-right: 16px;
          cursor: pointer;
          &:hover {
            color: var(--el-color-primary);
          }
        }
      }
    }
    .file-panel {
      flex: 1 1 auto;
      margin-left: 16px;
      position: relative;
      overflow: hidden;
      border-width: 2px;
      border-style: dashed;
      border-color: transparent;
      &.isDraging {
        background-color: var(--el-fill-color-lighter);
        border-color: var(--el-color-primary);
      }
      .loading-files {
        position: absolute;
        right: 0;
        bottom: 0;
        font-size: 14px;
        text-align: center;
        color: var(--el-color-white);
        background-color: var(--el-color-white);
        padding: 10px;
        box-shadow: 0 2px 12px 0 var(--el-overlay-color-lighter);
        .progressbar {
          --size: 40px;
          --duration: 1s;
          padding: 5px 10px;
          width: 300px;
        }
      }
    }
  }
  .el-tree-node__content {
    height: auto;
    padding-top: 4px;
    padding-bottom: 4px;
    .el-tree-node__label {
      width: calc(100% - 16px * 2);
      border-radius: 3px;
      padding: 4px 4px;
      transition-property: background-color, color;
      transition-duration: .3s;
    }
    &:hover {
      .el-tree-node__label {
        background-color: rgba(0, 123, 255, 0.08);
        color: var(--el-color-primary);
      }
    }
  }
  .el-tree-node.is-current>.el-tree-node__content {
    .el-tree-node__label {
      background-color: rgba(0, 123, 255, 0.08);
      color: var(--el-color-primary);
    }
  }

  .tip-info {
    height: calc(100vh - 320px);
    text-align: center;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 36px;
    font-weight: 700;
    color: var(--el-color-danger);
    background-color: var(--el-overlay-color-lighter);
  }

  .top-func-area {
    margin-bottom: 20px;
    position: relative;
    .filter-area {
      flex-grow: 1;
      padding-top: 10px;
      padding-bottom: 24px;
      border-bottom: 1px solid var(--el-border-color);
      margin-bottom: 16px;
    }
    .upload {
      flex-shrink: 0;
    }
  }
  .scroll-load-container {
    height: var(--scroll-height, 100%);
    overflow: auto;
    padding: 10px 0;
    .scroll-container {
      display: flex;
      flex-wrap: wrap;
      align-content: flex-start;
      margin: 0 -12px;
    }
    .file-item-container {
      padding: 12px;
      width: calc(100% / var(--columns, 4));
      flex: 0 0 auto;
    }
    .item-grid {
      display: flex;
      flex-wrap: nowrap;
      align-items: center;
      background-color: var(--el-bg-color-overlay);
      height: 120px;
      width: 100%;
      font-size: 14px;
      position: relative;
      border: 1px solid transparent;
      border-radius: 4px;
      overflow: hidden;
      cursor: pointer;
      position: relative;
      transition: all .3s;
      .tag {
        position: absolute;
        z-index: 1;
        left: 0;
        top: 0;
        padding: 3px 9px;
      }
      .preview {
        width: 120px;
        height: 100%;
        background-color: var(--bg-file-item-color);
        flex-shrink: 0;
        position: relative;
        overflow: hidden;
        .preview-icon {
          width: 48px;
          height: 48px;
          position: absolute;
          left: 50%;
          top: 50%;
          transform: translate(-50%, -50%);
        }
        img {
          max-height: 100%;
          position: absolute;
          left: 50%;
          top: 50%;
          transform: translate(-50%, -50%);
        }
      }
      .content {
        padding: 0 12px;
        flex-grow: 0;
        width: calc(100% - 120px);
        .title {
          margin-bottom: 4px;
          overflow: hidden;
          text-overflow: ellipsis;
          display: -webkit-box;
          -webkit-line-clamp: 2;
          -webkit-box-orient: vertical;
          max-width: 100%;
          word-break: break-all;
        }
        .info {
          font-size: 12px;
          color: var(--el-text-color-disabled);
        }
      }
      &::before {
        content: '';
        position: absolute;
        right: 3px;
        bottom: 3px;
        width: 20px;
        height: 20px;
        background-image: url('data:image/svg+xml;charset=UTF-8,%3Csvg%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%20xmlns%3Axlink%3D%22http%3A%2F%2Fwww.w3.org%2F1999%2Fxlink%22%20width%3D%2214%22%20height%3D%2211%22%20viewBox%3D%220%200%2014%2011%22%3E%0A%20%20%3Cdefs%3E%0A%20%20%20%20%3Cpolygon%20id%3D%22ico-check-gray-a%22%20points%3D%224.75%208.127%201.623%205%20.557%206.058%204.75%2010.25%2013.75%201.25%2012.693%20.193%22%2F%3E%0A%20%20%3C%2Fdefs%3E%0A%20%20%3Cg%20fill%3D%22none%22%20fill-rule%3D%22evenodd%22%20transform%3D%22translate(-2%20-4)%22%3E%0A%20%20%20%20%3Cpolygon%20points%3D%220%200%2018%200%2018%2018%200%2018%22%2F%3E%0A%20%20%20%20%3Cg%20transform%3D%22translate(2%204)%22%3E%0A%20%20%20%20%20%20%3Cmask%20id%3D%22ico-check-gray-b%22%20fill%3D%22%23fff%22%3E%0A%20%20%20%20%20%20%20%20%3Cuse%20xlink%3Ahref%3D%22%23ico-check-gray-a%22%2F%3E%0A%20%20%20%20%20%20%3C%2Fmask%3E%0A%20%20%20%20%20%20%3Cuse%20fill%3D%22%23000%22%20fill-rule%3D%22nonzero%22%20xlink%3Ahref%3D%22%23ico-check-gray-a%22%2F%3E%0A%20%20%20%20%20%20%3Cg%20fill%3D%22%23ffffff%22%20mask%3D%22url(%23ico-check-gray-b)%22%3E%0A%20%20%20%20%20%20%20%20%3Cpolygon%20points%3D%220%200%2018%200%2018%2018%200%2018%22%20transform%3D%22translate(-1%20-1)%22%2F%3E%0A%20%20%20%20%20%20%3C%2Fg%3E%0A%20%20%20%20%3C%2Fg%3E%0A%20%20%3C%2Fg%3E%0A%3C%2Fsvg%3E%0A');
        background-position: center;
        background-size: contain;
        background-repeat: no-repeat;
        z-index: 9;
        opacity: var(--opacity, 0);
        transition: opacity .3s;
      }
      &::after {
        content: '';
        position: absolute;
        right: 0;
        bottom: 0;
        width: 50px;
        height: 60px;
        background-color: var(--el-color-primary);
        transform: rotate(45deg) translate(35px, 5px);
        opacity: var(--opacity, 0);
        transition: opacity .3s;
      }
      &.selected {
        --opacity: 1;
        border-color: var(--el-color-primary);
      }
      &:hover {
        border-color: var(--el-color-primary);
      }
    }
  }
}

.el-message-box.img-preview-dialog {
  max-width: 90%;
  text-align: center;
}
.dialog-button-center .el-message-box__btns {
  text-align: center;
  justify-content: center;
}

.fixed-contextmenu {
  position: fixed;
  z-index: 9999;
  padding: 10px 0;
  margin: 5px 0;
  background-color: var(--el-bg-color);
  border: 1px solid var(--el-border-color);
  border-radius: 4px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, .01);
  .item {
    list-style: none;
    line-height: 36px;
    padding: 0 20px;
    margin: 0;
    font-size: 14px;
    cursor: pointer;
    outline: none;
    white-space: nowrap;
    &:hover {
      background-color: var(--el-color-info-light-9);
      color: var(--el-color-primary);
    }
  }
}
.progressbar {
  --size: 20px;
  display: inline-block;
  border-radius: 50px;
  min-width: 150px;
  min-height: 12px;
  animation: progress-bar-stripes var(--duration, 2s) linear infinite;
  background-color: var(--el-color-primary);
  background-image: linear-gradient(45deg, rgba(255, 255, 255, 0.15) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, 0.15) 50%, rgba(255, 255, 255, 0.15) 75%, transparent 75%, transparent);
  background-size: var(--size) var(--size);
}

.ace-trigger-tip {
  position: absolute;
  left: 0;
  bottom: 0;
  transform: translateY(100%);
  font-size: 12px;
  line-height: 1;
}

@keyframes progress-bar-stripes {
  0% {
    background-position: var(--size) 0;
  }
  100% {
    background-position: 0 0;
  }
}

@keyframes jump-animaiton {
  0% {
    transform: translateX(0px);
  }
  60% {
    transform: translateX(10px);
  }
  100% {
    transform: translateX(0px);
  }
}