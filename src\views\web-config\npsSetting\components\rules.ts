import { reactive } from 'vue'
import type { FormRules } from 'element-plus'

export const formRules = reactive<FormRules>({
  name: [
    { required: true, message: '策略不能为空', trigger: 'blur' },
    { max: 30, message: '最多30个字符', trigger: 'blur'  }
  ],
  version: [
    { required: true, message: '弹出样式版本不能为空', trigger: 'change' },
  ],
  pop_percent: [
    { required: true, message: '弹出概率不能为空', trigger: 'change' },
  ],
  site_ids: [
    { required: true, message: '对应站点不能为空', trigger: 'change' },
  ],
  pop_time_login: [
    { required: true, message: '弹出时间不能为空', trigger: 'change' },
  ],
  pop_time_logout: [
    { required: true, message: '弹出时间不能为空', trigger: 'change' },
  ],
})

export const proList = [
  {
    label: '10%',
    value: 10
  },
  {
    label: '20%',
    value: 20
  },
  {
    label: '30%',
    value: 30
  },
  {
    label: '40%',
    value: 40
  },
  {
    label: '50%',
    value: 50
  },
  {
    label: '60%',
    value: 60
  },
  {
    label: '70%',
    value: 70
  },
  {
    label: '80%',
    value: 80
  },
  {
    label: '90%',
    value: 90
  },
  {
    label: '100%',
    value: 100
  },
]

export const timeList = [
  {
    label: '0s',
    value: 0
  },
  {
    label: '30s',
    value: 30
  },
  {
    label: '60s',
    value: 60
  },
  {
    label: '90s',
    value: 90
  },
  {
    label: '120s',
    value: 120
  },
]