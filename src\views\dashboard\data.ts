import Module1 from '@/assets/dashboard_images/module-1.png'
import Module2 from '@/assets/dashboard_images/module-2.png'
import Module3 from '@/assets/dashboard_images/module-3.png'
import Module4 from '@/assets/dashboard_images/module-4.png'
import Module5 from '@/assets/dashboard_images/module-5.png'
import Module6 from '@/assets/dashboard_images/module-6.png'

import cmsPush from '@/assets/dashboard_images/cms-push.svg'
import batchReplace from '@/assets/dashboard_images/batch-replace.svg'
import straightSearch from '@/assets/dashboard_images/straight-search.svg'
import translte from '@/assets/dashboard_images/translate.svg'
import marketTool from '@/assets/dashboard_images/market-tool.svg'
import designComponent from '@/assets/dashboard_images/design-component.svg'
import frontEndExample from '@/assets/dashboard_images/frontend-example.svg'
import odPlatform from '@/assets/dashboard_images/od-platform.svg'
import designGuide from '@/assets/dashboard_images/design-guide.svg'
import sysMigrateGuide from '@/assets/dashboard_images/sys-migrate-guide.svg'
import sysAuthority from '@/assets/dashboard_images/sys-authority.svg'
import sysRookieGuide from '@/assets/dashboard_images/sys-rookie-guide.svg'
import sysFaq from '@/assets/dashboard_images/sys-faq.svg'
import importArticle from '@/assets/dashboard_images/import-article.svg'

export const constantModule = [
  {
    title: '模板配置',
    name: 'TemplateManage',
    image: Module1,
    des: '模板内容管理的操作列表',
    icon: 'index-module'
  },
  {
    title: '块配置',
    name: 'BlockManagement',
    image: Module2,
    des: '块内容管理的操作列表',
    icon: 'index-block'
  },
  {
    title: '文章管理',
    name: 'ArticleManageList',
    image: Module3,
    des: '文章内容管理的操作列表',
    icon: 'index-article'
  },
  {
    title: '审核中心',
    name: 'ReviewCenter',
    image: Module4,
    des: '审核重点文件的发布流程',
    icon: 'index-audit'
  },
  {
    title: '页面配置',
    name: 'PageManagement',
    image: Module5,
    des: '页面内容管理的操作列表',
    icon: 'index-page'
  },
  {
    title: '发布记录',
    name: 'GeneratePublishRecord',
    image: Module6,
    des: '页面/文件发布记录及状态反馈',
    icon: 'index-publish'
  },
]

export const tools = [
  {
    title: 'CDN推送',
    inner: true,
    name: 'CdnManagement',
    image: cmsPush
  },
  {
    title: '批量替换',
    inner: true,
    name: 'BatchReplacement',
    image: batchReplace
  },
  {
    title: '定向搜索',
    inner: true,
    name: 'DetectedSearch',
    image: straightSearch
  },
  {
    title: '翻译管理',
    inner: true,
    name: 'TranslateManagement',
    image: translte
  },
  {
    title: '文章批量导入',
    inner: true,
    name: 'ArticleImport',
    image: importArticle
  }
]

export const externals = [
  {
    title: '页面运营工具',
    url: 'http://wdesign.pageswx.cn/WSCodex/#/commontools/logoquery',
    image: marketTool
  },
  {
    title: '设计组件',
    url: 'http://wdesign.pageswx.cn/WSCodex/#/components/button',
    image: designComponent
  },
  {
    title: '页面前端示例库',
    url: 'http://wdesign.pageswx.cn/WSCodex/#/animationlibrary/animationdemo',
    image: frontEndExample
  },
  {
    title: '外包手写平台',
    url: 'https://writer.wondershare.com/',
    image: odPlatform
  },
  {
    title: '网页设计指引',
    url: 'http://wdesign.pageswx.cn/WSCodex/#/tokens/colors',
    image: designGuide
  },
]

export const guides = [
  {
    title: 'CMS40数据迁移指引',
    url: 'https://confluence.300624.cn/pages/viewpage.action?pageId=742654322',
    date: '2023-03-18',
    image: sysMigrateGuide
  },
  {
    title: '系统账号权限开通',
    url: 'https://confluence.300624.cn/pages/viewpage.action?pageId=724603061',
    date: '2023-03-18',
    image: sysAuthority
  },
  {
    title: 'CMS新手指南',
    url: 'https://confluence.300624.cn/pages/viewpage.action?pageId=844628096',
    date: '2023-03-18',
    image: sysRookieGuide
  },{
    title: 'CMS FAQ常见问题答疑',
    url: 'https://confluence.300624.cn/pages/viewpage.action?pageId=742654360',
    date: '2023-03-18',
    image: sysFaq
  }
]