import type { moduleMap } from '../aysncRoutes'
export default {
  'CdnManagement': {
    component: () => import('@/views/tools-management/cdnPush/index.vue'),
    path: 'cdn-management',
    title: 'CDN刷新记录',
    cachePage: true,
    icon: '',
  },
  'CdnDetail': {
    component: () => import('@/views/tools-management/cdnPush/detail.vue'),
    path: 'cdn-detail',
    title: 'CDN刷新详情',
    cachePage: false,
    hidden: true,
  },
  'AiTranslate': {
    component: () => import('@/views/tools-management/translate(test)/index.vue'),
    path: 'ai-translate',
    title: '机翻工具(测试版)',
    cachePage: true,
  },
  'BatchReplacement': {
    component: () => import('@/views/tools-management/batchReplace/index.vue'),
    path: 'batch-replacement',
    title: '批量替换',
    cachePage: true,
    icon: '',
  },
  'ReplaceDetail': {
    component: () => import('@/views/tools-management/batchReplace/detail.vue'),
    path: 'replace-detail',
    title: '批量替换详情',
    hidden: true,
    queryRequired: true
  },
  'ShortLinks': {
    component: () => import('@/views/tools-management/shortLinks/index.vue'),
    path: 'short-links',
    title: '短链生成',
    cachePage: true,
  },
  'DetectedSearch': {
    component: () => import('@/views/tools-management/detectedSearch/index.vue'),
    path: 'detected-search',
    title: '定向搜索',
    cachePage: true,
    icon: '',
  },
  'ArticleImport': {
    component: () => import('@/views/tools-management/articleImport/index.vue'),
    path: 'article-import',
    title: '文章批量导入',
    cachePage: true,
    icon: '',
  },
  'ArticleImportDetail': {
    component: () => import('@/views/tools-management/articleImport/detail.vue'),
    path: 'article-import-detail',
    title: '文章批量导入详情',
    cachePage: true,
    hidden: true,
    queryRequired: true
  },
  'EdmTempResolve': {
    component: () => import('@/views/tools-management/edmTempResolve/index.vue'),
    path: 'edm-temp-resolve',
    title: 'EDM模板解析',
    cachePage: true,
  },
  'SiteFileManagement': {
    component: () => import('@/views/tools-management/siteFiles/index.vue'),
    path: 'site-file-management',
    title: '站点文件管理',
    cachePage: true,
  },
  'NotFindManagement': {
    component: () => import('@/views/tools-management/notFind/index.vue'),
    path: '404-management',
    title: '404页面',
    cachePage: true,
  },
  'SitemapManagement': {
    component: () => import('@/views/tools-management/siteMap/index.vue'),
    path: 'sitemap',
    title: 'sitemap',
    cachePage: true,
  },
  'VideoStatic': {
    component: () => import('@/views/tools-management/videoStatic/index.vue'),
    path: 'video-static',
    title: '视频覆盖率统计',
    cachePage: true,
  }
} as {
  [propName: string]: moduleMap
}