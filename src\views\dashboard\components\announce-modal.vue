<template>
  <el-dialog
    v-model="show"
    title="公告"
    center
    append-to-body
    :destroy-on-close="true">
    <el-form
      label-position="right"
      label-width="120px"
      style="max-width: 1000px;"
    >
      <el-form-item label="公告标题：">
        <strong>{{ detailInfo?.title }}</strong>
      </el-form-item>
      <el-form-item label="发布时间：">
        <strong>{{ detailInfo?.publish_time }}</strong>
      </el-form-item>
      <el-form-item label="内容：">
        <el-input
          type="textarea"
          :value="detailInfo?.content"
          readonly
          rows="20"
        />
      </el-form-item>
    </el-form>

    <template #footer>
      <el-button type="primary" @click="() => show = false">已阅</el-button>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, nextTick } from 'vue'
import useTryCatch from '@/hooks/use-try-catch'
import { getAnnounceDetail } from '@/api/announcement'
import type { announceDetail } from '@/api/announcement'

const props = defineProps<{
  detailInfo?: announceDetail
}>()

const emit = defineEmits(['update:detailInfo'])

const show = ref(true)
const announcementId = ref(0)

const getDetail = () => {
  useTryCatch( async () => {
    const res = await getAnnounceDetail(announcementId.value)

    if (res.code === 200) {
      emit('update:detailInfo', res.data)
    }
  } )
}
const handleShow = (id?: number) => {
  show.value = true
  nextTick( () => {
    if ( id && announcementId.value !== id) {
      announcementId.value = id
      getDetail()
    }
  } )
}

if (props.detailInfo) {
  announcementId.value = props.detailInfo.announcement_id
}
getDetail()
defineExpose({
  handleShow
})
</script>