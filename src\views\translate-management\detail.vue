<template>
  <div class="scroll-y">
    <div v-loading="loading">
      <CustomTable
        :data="tableData"
        :columns="columns"
        :operate-list="operateList"
        row-key="id"
        hide-pagination
        selectable
        default-expand-all
        :select-method="(row) => row.status !== 1 && row.status !== 2 && row.status !== 3"
        @selection-change="handleSelectionChange"
      />
    </div>

    <el-dialog
     v-model="showPreview"
     title="翻译预览"
     width="90%"
     append-to-body
    >
      <template #default>
        <el-row :gutter="20">
          <el-col :span="12">
            <h2 class="mb-50px"><strong>原文</strong></h2>
            <div v-html="source_html"></div>
          </el-col>
          <el-col :span="12">
            <h2 class="mb-50px"><strong>译文</strong></h2>
            <div v-html="target_html"></div>
          </el-col>
        </el-row>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="tsx">
import { ref, computed, h } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { 
  ElMessage, ElButton, ElTooltip, ElMessageBox, 
  ElSpace, ElUpload,
  ElSelect, ElOption,
  ElRadioGroup, ElRadio
} from 'element-plus'
import useTryCatch from '@/hooks/use-try-catch'
import useLoading from '@/hooks/use-loading'
import { getHost } from '@/hooks/use-env'
import { getTaskDetailList, createArticleCross, targetChannelList, targetTemplateList, transPreview } from '@/api/translate-management'
import type { detailListItem, channelItem, templateItem, crossData } from '@/api/translate-management'

import CustomTable from '@/components/CustomTable/index.vue'

defineOptions( { name: 'TranslateDetail' } )

const route = useRoute()
const router = useRouter()
const { loading, setLoading } = useLoading()
const is_cross = 1  // 原详情已废弃，直接写死此参数，减小改动暂不删代码
const can_create_status = new Set([4,5])  // 可创建状态码

const { job_id, target_language, channel_code, channel_name } = route.query as unknown as { 
  job_id: string; 
  target_language: string;
  channel_code: string;
  channel_name: string;
  is_cross?: string; 
}

const tableData = ref<detailListItem[]>([])
const channelList = ref<channelItem[]>([])
const templateList = ref<templateItem[]>([])
const selectIds = ref('')
const downloading = ref(false)
const showPreview = ref(false)
const source_html = ref('')
const target_html = ref('')
const columns = ref([
  {
    title: '所属模板',
    dataKey: 'tpl_id',
    width: 150,
    cellRenderer: (scope: { row: detailListItem }) => (
      <>
        { is_cross && scope.row.isChildren ? '' : scope.row.tpl_id }
      </>
    )
  },
  {
    title: '模板类型',
    dataKey: 'tpl_type_name',
    width: 150,
    cellRenderer: (scope: { row: detailListItem }) => (
      <>
        { is_cross && scope.row.isChildren ? '' : scope.row.tpl_type_name }
      </>
    )
  },
  {
    title: '页面id',
    dataKey: 'entity_id',
    width: 100,
  },
  {
    title: '页面url',
    dataKey: 'url',
    minWidth: 180,
  },
  {
    title: '翻译内容',
    dataKey: 'translation_field_name',
    minWidth: 180,
  },
  {
    title: '翻译语言',
    dataKey: 'source_language',
    minWidth: 120,
    cellRenderer: (scope: { row: detailListItem }) => (
      <>
        {
          scope.row.isChildren
          ? <>
            <strong>{ scope.row.source_language.toUpperCase() }</strong> &gt; <strong>{ scope.row.target_language.toUpperCase() }</strong>
          </>
          : <strong>{ scope.row.target_language.toUpperCase() }</strong>
        }
      </>
    )
  },
  {
    title: '任务状态',
    dataKey: 'status_name',
    width: 100,
    cellRenderer: (scope: { row: detailListItem }) => (
      <span style={ { color: scope.row.status === 5 ? 'var(--el-color-success)' : scope.row.status === 6 ? 'var(--el-color-error)' : '' } }>
        { scope.row.isChildren ? scope.row.status_name : scope.row.status === 5 ? '可创建' : '不可创建' }
      </span>
    )
  },
  {
    title: '译文创建状态',
    dataKey: 'is_create_name',
    width: 120,
    cellRenderer: (scope: { row: detailListItem }) => (
      <span
        style={{
          color: scope.row.is_create === 1 ? 'var(--el-color-primary)' 
          : scope.row.is_create === 2 ? 'var(--el-color-success)' 
          : scope.row.is_create === 3 ? 'var(--el-color-error)' : ''
        }}
      >
        { scope.row.is_create_name || '/' }
      </span>
    )
  },
  {
    title: '操作',
    dataKey: '',
    width: 240,
    cellRenderer: (scope: { row: detailListItem }) => (
      <>
        {
          is_cross && !scope.row.isChildren
          ? <ElTooltip content='当前仅文章内容页模板，支持跨站点创建' disabled={scope.row.tpl_type === 'content'}>
            <ElButton 
              type='primary' plain 
              disabled={scope.row.tpl_type !== 'content' || scope.row.status === 6} 
              onClick={() => handleCreateArtilce(scope.row)}> 跨站点创建 </ElButton>
          </ElTooltip>
          : <>
          <ElTooltip content='跳转译文页面'>
            <ElButton type='primary' plain disabled={scope.row.is_create !== 2} onClick={() => handleJump(scope.row)}> 跳转 </ElButton>
          </ElTooltip>
          <ElTooltip content='译文下载'>
            <ElButton type='primary' plain disabled={scope.row.status === 1 || scope.row.status === 2 || scope.row.status === 3} onClick={() => handleDownload(scope.row)}> 下载 </ElButton>
          </ElTooltip>
          <ElTooltip content='预览原文'>
            <ElButton type='primary' plain onClick={() => handlePreview(scope.row)}> 预览 </ElButton>
          </ElTooltip>
          </>
        }
      </>
    )
  }
])
const operateList = ref([
  {
    title: '批量下载',
    icon: 'export',
    method: () => {
      handleBatchDownload()
    },
    disabled: computed( () => !selectIds.value )
  }
])

const resolveList = (list: detailListItem[]) => {
  const tplMap = new Map<string, any[]>()
  const tplStatusMap = new Map<string, number>()
  const result: any[] = []
  list.forEach(( item ) => {
    if (!tplMap.get(`${item.tpl_id}-${item.target_language}`)) {
      tplMap.set(`${item.tpl_id}-${item.target_language}`, [])
    }
    tplMap.get(`${item.tpl_id}-${item.target_language}`)?.push({...item, isChildren: true, id: item.id})
    if ( tplStatusMap.get(`${item.tpl_id}-${item.target_language}`) !== 6 ) {
      tplStatusMap.set(`${item.tpl_id}-${item.target_language}`, can_create_status.has(item.status) ? item.status : 6)
    }
  })

  tplMap.forEach((value, key) => {
    result.push({
      id: key,
      tpl_id: key.replace(/-.*/, ''),
      status: tplStatusMap.get(key),
      tpl_type: value[0].tpl_type,
      tpl_type_name: value[0].tpl_type_name,
      source_language: value[0].source_language,
      target_language: value[0].target_language,
      site_id: value[0].site_id,
      channel_id: value[0].channel_id,
      children: value
    })
  })

  return result
}

const handleGetChannelList = (row: detailListItem) => {
  useTryCatch( async () => {
    const params = {
      source_site_id: row.site_id,
      source_channel_id: row.channel_id,
      target_language: row.target_language
    }

    const res = await targetChannelList(params)
    if (res.code === 200) {
      channelList.value = res.data
    }
  } )
}

const handleGetTmepList = (site_id: number,  channel_id: number) => {
  useTryCatch( async () => {
    const params = {
      target_site_id: site_id,
      target_channel_id: channel_id,
    }

    const res = await targetTemplateList(params)
    if (res.code === 200) {
      templateList.value = res.data
    }
  } )
}

const getList = () => {
  useTryCatch( async () => {
    setLoading(true)

    const res = await getTaskDetailList(job_id, target_language || '')
    if (res.code === 200) {
      tableData.value = resolveList(res.data.detailList)
    } else {
      ElMessage.error(res.msg)
    }

    setLoading(false)
  }, () => setLoading(false) )
}

const handleSelectionChange = (selections: detailListItem[]) => {
  const id_set = new Set<number>()
  selections.forEach( (item) => {
    if (!item.children) {
      id_set.add(item.id)
    } 
  })

  selectIds.value = [...id_set].join(',')
}

const handleCreateArtilce = (row: detailListItem) => {
  const type = ref(1)
  const target_channel = ref<number|string>()
  const target_multilingual = ref<any[]>([])
  const channelItem = ref<channelItem>()
  const target_tpl_id = ref<number|string>('')

  handleGetChannelList(row)

  const detail_ids = row.children?.map(( { id } ) => id ) as number[]

  ElMessageBox({
    title: '跨站点创建页面/文章',
    customClass: 'dialog-button-center',
    customStyle: {
      maxWidth: 'none',
      width: '420px',
    },
    showCancelButton: false,
    showConfirmButton: false,
    closeOnPressEscape: false,
    closeOnClickModal: false,
    callback: ( action: string ) => {
      
    },
    message: h('div', {}, [
      h(ElSpace, {
        alignment: 'start',
        size: 16,
        style: { marginBottom: '16px', paddingTop: '6px' },
      }, () => [
        h('div', '此任务的内容来源 : '),
        h('div', [ channel_name, ' ', channel_code ])
      ]),
      h(ElSpace, {
        alignment: 'start',
        size: 16,
        style: { marginBottom: '16px' },
      }, () => [
        h('div', { style: { paddingTop: '6px' } }, '选择目标站点 : '),
        h(ElSelect, {
          filterable: true,
          placeholder: '请选择',
          valueKey: 'channel_id',
          modelValue: channelItem.value,
          onChange: (val: channelItem) => {
            channelItem.value = val
            target_channel.value = ''
            target_multilingual.value = val.targetChannelList
          }
        }, () => channelList.value.map( (item) => h(ElOption, {
          label: item.name,
          value: item,
        }) )),
      ]),
      h(ElSpace, {
        alignment: 'start',
        size: 16,
        style: { marginBottom: '16px' },
      }, () => [
        h('div', { style: { paddingTop: '6px' } }, '自动创建目标渠道 : '),
        h(ElRadioGroup, {
          modelValue: target_channel.value,
          onChange: (value) => {
            target_tpl_id.value = ''
            target_channel.value = value as number
            handleGetTmepList(channelItem.value?.site_id as number, target_channel.value)
          }
        }, () => target_multilingual.value.map((item) => h(ElRadio, { label: item.channel_id }, () => item.channel_code))),
      ]),
      h(ElSpace, {
        alignment: 'start',
        size: 16,
        style: { marginBottom: '16px' },
      }, () => [
        h('div', { style: { paddingTop: '6px' } }, '选择目标模板 : '),
        h(ElSelect, {
          filterable: true,
          placeholder: '请选择',
          modelValue: target_tpl_id.value,
          onChange: (val: number) => target_tpl_id.value = val,
        }, () => templateList.value.map( ( item ) => h(ElOption, {
          label: item.tpl_id + '--' + item.name,
          value: item.tpl_id
        }) ))
      ]),
      h(ElSpace, {
        alignment: 'start',
        size: 16,
        style: { marginBottom: '16px' }
      }, () => [
        h('div', { style: { paddingTop: '6px' } }, [
          '是否将内链同时本地化 : ',
          h('div', { style: { color: 'var(--el-color-info)' } }, '(将内链进行批量替换)')
        ] ),
        h(ElRadioGroup, {
          style: { width: '200px'},
          modelValue: type.value,
          onChange: (value) => type.value = value as number
        }, () => [
          h(ElRadio, { label: 1 }, () => '批量替换同时创建文章'),
          h(ElRadio, { label: 2 }, () => '直接创建文章')
        ]),
      ]),
      h(ElSpace, {
        wrap: false,
        size: 16,
        style: { marginBottom: '16px' }
      }, () => type.value === 1 ? [
        h(ElButton, {
            type: 'primary',
            link: true,
            style: { marginLeft: '56px' },
            onClick: () => {
              location.href = `${getHost()}/api/v1/translate/download_replace_template?job_id=${job_id}&target_language=${row.target_language}`
            }
          }, () => '点击下载批量导入模板.xslx'
        ),
        h(ElUpload, {
          action: 'string',
          accept: 'xlsx',
          autoUpload: false,
          showFileList: false,
          limit: 1,
          beforeUpload: () => true,
          onChange: (file) => {
            const params = {
              job_id,
              target_language: row.target_language,
              type: 1,
              file: file.raw,
              content: detail_ids.map( ( detail_id ) => {
                return {
                  detail_id,
                  target_channel: String(target_channel.value),
                  target_template_id: target_tpl_id.value as number,
                  is_cross: 1
                }
              } )
            }
            createArticleMethod(params)
          }
        }, { default: () => h(ElButton, { type: 'primary', loading: loading.value, disabled: !target_channel.value || !target_tpl_id.value }, () => '上传') })
      ] : []),
      h(ElSpace, {
        wrap: false,
        alignment: 'center',
        style: { width: '100%', justifyContent: 'center'}
      }, () => [
        h(ElButton, { plain: true, onClick: () => ElMessageBox.close() }, () => '取消'),
        type.value === 2 ? h(ElButton, { 
          type: 'primary', 
          disabled: !target_channel.value || !target_tpl_id.value, 
          onClick: () => {
            const params = {
              job_id,
              target_language: row.target_language,
              type: 2,
              content: detail_ids.map( ( detail_id ) => {
                return {
                  detail_id,
                  target_channel: String(target_channel.value),
                  target_template_id: target_tpl_id.value as number,
                  is_cross: 1
                }
              } )
            }
            createArticleMethod(params)
          } 
        }, () => '确认') : ''
      ]),
    ])
  })
}

const createArticleMethod = (data: crossData) => {
  useTryCatch( async () => {
    setLoading(true)
    const res = await createArticleCross(data)
    if (res.code === 200) {
      ElMessage.success(res.msg)
    } else {
      ElMessage.error(res.msg)
    }
    ElMessageBox.close()
    setLoading(false)
  }, () => setLoading(false) )
}

const handleJump = (row: detailListItem) => {
  router.push(
    {
      name: 'PageManagementOperate',
      query: {
        type: 'edit',
        page_id: row.create_page_id,
        tpl_id: row.tpl_id,
        page_url: `/api/v1/templates/${row.tpl_id}/pages/${row.create_page_id}/preview`,
      }
    }
  )
}
const handlePreview = (row: detailListItem) => {
  // window.open(`/api/v1/articles/preview?tpl_page_id=${row.entity_id}&fields=${row.translation_field}&page_check=0`, '_blank')
  useTryCatch( async () => {
    setLoading(true)

    const res = await transPreview(row.id)
    if (res.code === 200) {
      showPreview.value = true
      source_html.value = res.data.source_html
      target_html.value = res.data.target_html
    } else {
      ElMessage.error(res.msg)
    }

    setLoading(false)
  }, () => setLoading(false) )
}
const handleDownload = (row: detailListItem) => {
  handleFrequency( () => {
    location.href = `${getHost()}/api/v1/translate/smartcat/document_export?job_id=${row.job_id}&target_language=${row.target_language}&detail_id=${row.id}`
  } )
}
const handleBatchDownload = () => {
  handleFrequency( () => {
    location.href = `${getHost()}/api/v1/translate/smartcat/document_batch_export?job_id=${job_id}&detail_id=${selectIds.value}`
  } )
}

const handleFrequency = (cb?: () => any) => {
  if (!downloading.value) {
    downloading.value = true
    cb && cb()
    setTimeout(() => {
      downloading.value = false
    }, 3500)
  } else {
    ElMessage.warning('操作频繁，稍后操作')
  }
}

getList()
</script>