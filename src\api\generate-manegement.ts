import axiosReq from './axios'
import qs from 'query-string'

export type recordQueryParams = {
  task_type: string;
  action_type: string;
  task_status: string|number;
  check_status: string|number;
  status: string|number;
  user_id_a: string|number;
  task_id: string;
  url?: string;
}

export type recordListQueryParams = {
  S: recordQueryParams;
  site_id: number;
  channel_id: string|number;
  page: number;
  page_size: number;
}

export type recordListItem = {
  task_id: number,
  site_id: number,
  task_srv_id: number,
  audit_task_id: string,
  channel_id: number,
  tpl_level: string,
  task_name: string,
  task_type: number,
  action_type: string,
  status: number,
  task_status: number,
  check_status: number,
  state: string,
  user_id_a: number,
  user_name_a: string,
  edit_time: string,
  add_time: string,
  total: number,
  remark: string,
  cost_time: string,
  channel_code: string;
}

export type detailQueryParams = {
  S: {
    status: string|number;
    tpl_page_ids: string;
    url: string;
  };
  task_id: number;
  site_id: number;
  page: number;
  page_size: number;
}

export type detailsListItem = {
  add_time: string;
  channel_code: string;
  channel_id: number;
  channel_name: string;
  color: string;
  edit_time: string;
  entity_id: string;
  file_name: string;
  id: number;
  line_name: string;
  log_remark: string;
  site_id: number;
  state: string;
  status: number;
  task_id: number;
  url: string;
  user_id_a: number;
  user_name_a: string;
  task_status: number;
}

export type pageGenerateData = {
  tpl_page_ids: string;
  site_id: number;
  remark?: string;
  store_buy_type?: number;
}

export type blockGenerateData = {
  blk_ids: string;
  site_id: number;
  remark?: string;
}

export type tempGenerateData = {
  tpl_ids: string;
  site_id: number;
  stage?: string;
  curr?: number;
  remark?: string;
  store_buy_type?: number;
}

export const getRecordList = (params: recordListQueryParams) => 
  axiosReq('get', '/api/v1/templates/pages/task', {
    params,
    paramsSerializer: {
      serialize: (obj: recordListQueryParams) => qs.stringify({
        S: JSON.stringify(obj.S),
        page: obj.page,
        channel_id: obj.channel_id,
        page_size: obj.page_size,
        site_id: obj.site_id
      })
    }
  })

export const getDetailList = (params: detailQueryParams) => 
  axiosReq('get', '/api/v1/templates/pages/task/detail', {
    params,
    paramsSerializer: {
      serialize: (obj: detailQueryParams) => qs.stringify({
        S: JSON.stringify(obj.S),
        page: obj.page,
        page_size: obj.page_size,
        site_id: obj.site_id,
        task_id: obj.task_id
      })
    }
  })

// 页面生成操作
export const pageGenerate = (data: pageGenerateData) => 
  axiosReq('post', '/api/v1/templates/pages/send', data)

// 模板生成操作
export const tempGenerate = (data: tempGenerateData) => 
  axiosReq('post', '/api/v1/templates/send', data)

// 块生成操作
export const blockGenerate = (data: { data: blockGenerateData }) => 
  axiosReq('post', '/api/v1/blocks/create', data)

// 任务重新发布
export const taskPush = (task_id: number) => 
  axiosReq('post', '/api/v1/templates/task/push', { task_id })