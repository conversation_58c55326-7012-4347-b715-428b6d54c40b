import type { moduleMap } from '../aysncRoutes'

export default {
  'SiteConfiguration': {
    component: () => import('@/views/site-config/site/index.vue'),
    path: 'site-configutation',
    title: '站点配置',
    cachePage: true,
  },
  'SiteOperate': {
    component: () => import('@/views/site-config/site/operate/index.vue'),
    path: 'site-operate',
    title: '站点操作',
    cachePage: true,
    hidden: true
  },
  'SiteConfigChannel': {
    component: () => import('@/views/site-config/site/channel/index.vue'),
    path: 'site-config-channel',
    title: '站点渠道配置',
    cachePage: true,
    hidden: true,
    queryRequired: true
  },
  'LangConfiguration': {
    component: () => import('@/views/site-config/language/index.vue'),
    path: 'lang-configutation',
    title: '语言配置',
    cachePage: true,
  },
  'ChannelConfiguration': {
    component: () => import('@/views/site-config/channel/index.vue'),
    path: 'channel-configutation',
    title: '渠道配置',
    cachePage: true,
  },
  'PublicBlockManagement': {
    component: () => import('@/views/site-config/publicBlock/index.vue'),
    path: 'publick-block-manage',
    title: '公共块管理',
    cachePage: true,
  },
  'AnnounceConfig': {
    component: () => import('@/views/site-config/announce/index.vue'),
    path: 'announce-config-manage',
    title: '公告配置',
    cachePage: true,
  },
  'SitePermission': {
    component: () => import('@/views/site-config/permission/index.vue'),
    path: 'site-permission',
    title: '站点权限',
    cachePage: true,
  }
} as {
  [propName: string]: moduleMap
}