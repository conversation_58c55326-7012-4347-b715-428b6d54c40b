<template>
  <div class="scroll-y with-flex">
    <div style="font-size: 24px; margin-bottom: 20px;">
      问题类型: {{ issue_title || '-'}} 
      <el-tooltip raw-content effect="light">
        <template #content>
          <div class="tooltip-box">
            <div class="flex-box" style="margin-bottom: 20px;">
              <h3 class="item">问题描述</h3>
              <h3 class="item">修复方法</h3>
            </div>
            <div class="flex-box divider-line">
              <div class="item" v-html="issue_des"></div>
              <div class="item" v-html="issue_solution"></div>
            </div>
          </div>
        </template>
        <SvgIcon icon-class="question" />
      </el-tooltip>
    </div>
    <div v-loading="loading" class="sticky-table">
      <CustomTable 
        ref="tableRef"
        :data="tableData"
        :columns="columns"
        :table-method="getList"
        :index-method="indexMethod"
        :operate-list="operateList"
      />
    </div>
  </div>
</template>

<script setup lang="tsx">
import { ref } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import { useBasicStore } from '@/store/basic'
import useTryCatch from '@/hooks/use-try-catch'
import { getHost } from '@/hooks/use-env'
import useLoading from '@/hooks/use-loading'
import { getSemrushIssueDetail } from '@/api/web-quality-management'
import type { issueDetailListItem } from '@/api/web-quality-management'
import issueList from './data/issue_list'

defineOptions({ name: 'EarlyWarningDetail' })

import CustomTable from '@/components/CustomTable/index.vue'

const route = useRoute()
const router = useRouter()
const basicStore = useBasicStore()
const { loading, setLoading } = useLoading()

const { channel_id, issue_id, snapshot_id, issue_type } = route.query as unknown as {
  channel_id: string;
  issue_id: string;
  snapshot_id: string;
  issue_type: string;
}

const issue_title = ref('')
const issue_des = ref('')
const issue_solution = ref('')
const tableRef = ref()
const tableData = ref<issueDetailListItem[]>([])
const operateList = ref([
  {
    title: '导出EXCEL',
    button: true,
    append: true,
    method: () => {
      location.href = `${getHost()}/api/v1/semrush/download?channel_id=${channel_id}&snapshot_id=${snapshot_id}&issue_id=${issue_id}`
    }
  }
])
const columns = ref([
{
    title: '页面ID',
    dataKey: 'page_id',
    width: 200,
    cellRenderer: (scope: { row: issueDetailListItem }) => <>
      <u class='with-hand' onClick={() => handleToPageList(scope.row)}>{ scope.row.page_id }</u>
    </>
  },
  {
    title: '页面url',
    dataKey: 'source_url',
    minWidth: 300,
    cellRenderer: (scope: { row: issueDetailListItem }) => <>
      <a href={scope.row.source_url} target='blank'>{ scope.row.source_url }</a>
    </>
  },
  {
    title: '错误url',
    dataKey: 'target_url',
    minWidth: 300
  }
])

const indexMethod = (index: number) => {
  const { page, pageSize } = tableRef.value?.getPagination()
  return (page - 1) * pageSize + index + 1
}

const getList = ( { page, pageSize } ) => {
  useTryCatch( async () => {
    setLoading(true)

    const params = {
      channel_id,
      issue_id,
      snapshot_id,
      page,
      page_size: pageSize
    }

    const res = await getSemrushIssueDetail(params)
    if (res.code === 200) {
      tableData.value = res.data.list
      tableRef.value?.setTotal(res.data.total)
    } else {
      ElMessage.error(res.msg)
    }

    setLoading(false)
  }, () => setLoading(false) )
}

const handleToPageList = (row: issueDetailListItem) => {
  basicStore.delCachedView('PageManagement')
  router.push({
    name: 'PageManagement',
    query: {
      entity_ids: row.page_id
    }
  })
}

if (issueList[issue_id]) {
  const issue = issueList[issue_id]
  issue_title.value = issue.title
  issue_des.value = issue.description
  issue_solution.value = issue.solution
}
</script>

<style lang="scss">
.tooltip-box {
  width: 520px; 
  padding: 10px;
  .flex-box {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin: 0 -20px;
    position: relative;
    &.divider-line::before {
      content: '';
      position: absolute;
      height: 100%;
      border-right: 1px solid var(--el-border-color-light);
      right: 50%;
      top: 0;
    }
    .item {
      width: 100%;
      padding: 0 20px;
    }
  }
}
</style>