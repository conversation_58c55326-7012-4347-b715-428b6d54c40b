<template>
  <div class="scroll-y" ref="scrollY">
    <div style="overflow: hidden; height: 100%">
      <el-row :gutter="20" style="height: 100%">
        <el-col :span="12">
          <el-input 
            ref="textareaRef"
            type="textarea" 
            v-model="rawHTML" 
            placeholder="edm代码片段"
            resize="none"
            v-ace="{ 
              getVal: () => rawHTML, 
              save: (val: string) => rawHTML = val, 
              showTips: true 
            }"
            style="height: calc(100% - 70px)"
            :class="{isDraging: dragover}"
            @drop.prevent="onDrop"
            @dragover.prevent="dragover = true"
            @dragleave.prevent="dragover = false"
          />
          <div class="text-center" style="padding-top: 30px;">
            <el-space :size="16">
              <el-upload
                action="string"
                accept=".html"
                :auto-upload="false"
                :show-file-list="false"
                :on-change="handleFileChange"
              >
                <el-button type="primary" :loading="loading"> 上传EDM </el-button>
              </el-upload>
              
              <template v-if="resolvedHTML">
                <el-button @click="handleExport(rawHTML)" type="primary" :loading="loading"> 导出原始EDM </el-button>
                <el-button @click="handleExport(resolvedHTML)" type="primary" :loading="loading"> 导出已编译EDM </el-button>
              </template>
            </el-space>
          </div>
        </el-col>
        <el-col :span="12">
          <div style="overflow: hidden;">
            <div 
              style="width: 200%; display: flex; flex-wrap: nowrap; transition: transform .3s;" 
              :style="{ transform: `translateX(${step === 2 ? '-50%' : 0})`, height }"
            >
              <div style="width: 50%; overflow: auto;" :style="{ height }">
                <template v-if="rawHTML">
                  <el-card shadow="never">
                    <template #header><strong> 模板中包含变量: </strong></template>
                    <el-form label-width="150"  label-suffix=":">
                      <template v-for="d in resolveFields">
                        <el-form-item :label="d.key">
                          <el-input v-model="d.value" />
                        </el-form-item>
                      </template>
                    </el-form>
                  </el-card>
                  <el-card shadow="never" v-if="resolveMap.size > 0">
                    <template #header><strong> 模板中包含循环变量: </strong></template>
                    <el-form label-width="150"  label-suffix=":">
                      <div v-for="( d, index ) in resolveMap" :style="{ marginBottom: index + 1 < resolveMap.size ? '15px' : '0' }">
                        <div style="margin-bottom: 10px;"><strong> 循环变量{{ index + 1 }}: {{ d[0] }} </strong></div>
                        <div v-for="(item, index) in d[1]" 
                          style="border-radius: var(--el-border-radius-base); border: 1px solid var(--el-border-color); padding: 10px; margin-bottom: 15px;">
                          <div class="rowBC" style="margin-bottom: 10px;">
                            <strong> 条目{{ index + 1 }}: </strong>
                            <Close v-if="d[1].length > 1" class="with-hand" style="height: 22px" @click="handleDelete(d[1], index)" />
                          </div>
                          <div v-for="_item in item">
                            <el-form-item :label="_item.key">
                            <el-input v-model="_item.value" />
                          </el-form-item>
                          </div>
                        </div>
                        <div class="text-center">
                          <el-button type="primary" @click="handleAppend(d[1])"> 增加条目 </el-button>
                        </div>
                      </div>
                    </el-form>
                  </el-card>
                </template>
                <div class="text-center" style="padding-top: 30px;">
                  <template v-if="rawHTML">
                    <el-button @click="handleResolve" type="primary" :loading="loading"> 开始编译 </el-button>
                  </template>
                </div>
              </div>
              <div style="width: 50%;" :style="{ height }">
                <el-input 
                  type="textarea" 
                  v-model="resolvedHTML" 
                  placeholder="edm代码片段"
                  resize="none"
                  v-ace="{ 
                    getVal: () => resolvedHTML, 
                    save: (val: string) => resolvedHTML = val, 
                    showTips: true 
                  }"
                  style="height: calc(100% - 70px)"
                />
                <div class="text-center" style="padding-top: 30px;">
                  <template v-if="rawHTML">
                    <el-button type="primary" @click="() => step = 1"> 返回字段编辑 </el-button>
                    <el-button type="primary" @click="handlePreview"> 预览 </el-button>
                  </template>
                </div>
              </div>
            </div>
          </div>
        </el-col>
      </el-row>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, watch, h, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Close } from '@element-plus/icons-vue'
import useTryCatch from '@/hooks/use-try-catch'
import useLoading from '@/hooks/use-loading'
import { resolveEDM } from '@/api/tools-management'
import type { UploadFile, UploadRawFile } from 'element-plus'

defineOptions( { name: 'EdmTempResolve' } )

const { loading, setLoading } = useLoading()
const textareaRef = ref()
const rawHTML = ref('')
const resolvedHTML = ref('')
const resolveFields = ref<{key: string; value: string}[]>([])
const resolveMap = ref(new Map<string, ({key: string; value: string}[])[]>())
const step = ref(1)
const dragover = ref(false)
const scrollY = ref<HTMLElement>()
const height = ref('auto')

const varReg = /(?<=\{=\s?)[a-z\d\_]+(?=\s?=\})/ig
const loopReg = /\{\%\s+for\s+[a-z\d\_]+\s+in\s+[a-z\d\_]+\s+\%\}.*\{\%\s+endfor\s+\%\}/ig
const loopVarReg = /(?<=\{\%\s+for\s+)[a-z\d\_]+\s+in\s+([a-z\d\_]+)(?=\s+\%\})/ig

const extractVars = (content: string) => new Set(content.match(varReg))
const extractLoop = (content: string) => {
  const plainContet = content.replace(/\n/g, '')
  const arr = plainContet.match(loopReg)
  const loopMap = new Map<string, ({key: string; value: string}[])[]>()
  if (arr) {
    arr.forEach((content) => {
      const vars = content.match(loopVarReg)?.[0].split(/\s+in\s+/) as [string, string]
      const innerVars = [...new Set(content.match(new RegExp(`(?<=${vars[0]}\\.)[a-z\d\_]+`, 'ig')))]
      loopMap.set(
        vars[1],
        [innerVars.map((val) => ({ key: val, value: '' }))]
      )
    })
  }

  return loopMap
}

watch(rawHTML, () => {
  const varsSet = extractVars(rawHTML.value)
  const fields: any[]  = []
  varsSet.forEach((key) => {
    fields.push({ key, value: '' })
  })
  resolveFields.value = fields
  if (loopVarReg.test(rawHTML.value)) {
    resolveMap.value = extractLoop(rawHTML.value)
  }
})

const handleResolve = () => {
  useTryCatch( async () => {
    setLoading(true)
    const param: { [propName: string]: any } = {}
    resolveFields.value.forEach(( { key, value } ) => {
      param[key] = value
    })
    resolveMap.value.forEach(( value, key ) => {
      const _arr: { [propName: string]: string }[] = []
      value.forEach(( arr ) => {
        const obj: { [propName: string]: string } = {}
        arr.forEach( ( { key, value } ) => {
          obj[key] = value
        } )
        _arr.push(obj)
      })
      param[key] = _arr
    })
    const res = await resolveEDM({ tpl: rawHTML.value, param })
    if (res.code === 200) {
      resolvedHTML.value = res.data.data
      step.value = 2
    } else {
      ElMessage.error(res.msg)
    }
    setLoading(false)
  }, () => {
    setLoading(false)
    ElMessage.error('解析失败')
  } )
}

const handleFileUpload = (file: UploadRawFile) => {
  const reader = new FileReader()
  reader.readAsText(file as Blob)
  reader.onloadend = function() {
    textareaRef.value.textarea.value = this.result as string
    rawHTML.value = textareaRef.value.textarea.value
  }
}

const handleFileChange = (e: UploadFile) => {
  handleFileUpload(e.raw as UploadRawFile)
}

const handleExport = (content: string) => {
  const a = document.createElement('a')

  a.setAttribute('download', '已解析EDM.html'),
  a.setAttribute('href', `data:text/plain;charset=utf-8,${encodeURIComponent(content)}`)
  document.body.appendChild(a)
  a.click()
  document.body.removeChild(a)
}

const handlePreview = () => {
  ElMessageBox({
    title: 'EDM效果预览',
    message: h('iframe', {
      style: {
        width: '618px',
        height: '1000px',
        border: '0'
      },
      src: `data:text/html;charset=utf-8,${encodeURIComponent(resolvedHTML.value)}`
    }),
    customClass: 'img-preview-dialog',
    callback: () => {}
  })
}

const onDrop = (e: DragEvent) => {
  dragover.value = false
  const files = e.dataTransfer?.files
  if (files) {
    if (files.length > 1) {
      return ElMessage.warning('请上传单个文件')
    }
    const file = files[0]
    if (file.type !== 'text/html') {
      return ElMessage.warning('请上传HTML文件')
    }
    handleFileUpload(file as UploadRawFile)
  }
}

const handleAppend = (arr: ({key: string; value: string}[])[]) => {
  const item = arr[0]
  arr.push(JSON.parse(JSON.stringify(item)))
}
const handleDelete = (arr: any[], index: number) => {
  arr.splice(index, 1)
}

onMounted(() => {
  if (scrollY.value) {
    height.value = scrollY.value.getBoundingClientRect().height + 'px'
  }
})
</script>

<style scoped>
:deep(.el-textarea__inner) {
  height: 100%;
}
:deep(.isDraging::after) {
  content: '';
  position: absolute;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  border: 1px dashed var(--el-color-primary);
  border-radius: 4px;
  background-color: var(--el-color-primary-light-9);
  pointer-events: none;
}
</style>