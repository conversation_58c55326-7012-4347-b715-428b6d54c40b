<template>
  <el-dialog
    v-model="show"
    :width="step === 1 ? '80%' : '30%'"
    :title="step === 1 ? '批量编辑字段内容--选择对应页面' : '批量编辑字段内容'"
    @closed="() => emit('closed')"
    append-to-body
  >
    <template #default>
      <template v-if="step === 1">
        <p style="color: var(--el-color-error); margin-bottom: 12px;">
          进行页面字段内容的批量编辑：会将此模板下被选中页面的该字段内容，批量替换为输入内容。
        </p>
        <QueryForm
          hide-reset
          :form-list="formList"
          :loading="loading"
          @search="handleSearch" 
        />
        <div v-loading="loading" style="min-height: 100px;">
          <CustomTable 
            ref="tableRef"
            :columns="columns"
            :data="tableData"
            :table-method="getList"
            :selectable="true"
            :operate-list="operateList"
            @selection-change="handleSelectionChange"
          />
        </div>
        <p style="padding-top: 20px;">选中页面数：<strong style="font-size: 1.5em;color: var(--el-color-primary)">{{ selectedPages }}</strong></p>
      </template>
      <template v-else>
        <p style="color: var(--el-color-error); margin-bottom: 12px;">
          <el-icon>
            <Warning />
          </el-icon>
          进行页面字段内容批量覆盖，会将此模板下所有页面的该字段内容替换为以下覆盖内容；如页面内字段已有内容，也会被更新覆盖。请谨慎操作！！！
        </p>
        <el-form label-width="120" label-suffix=":">
          <el-form-item label="选中页面数">
            {{ selectedPages }}
          </el-form-item>
          <el-form-item label="字段名称">
            <el-input :value="props.fieldLabel" readonly />
          </el-form-item>
          <el-form-item label="覆盖内容">
            <el-input 
              type="textarea" 
              v-model="editFieldData.field_value" 
              :rows="4" 
              v-ace="{ getVal: () => editFieldData.field_value, save: (val: string) => editFieldData.field_value = val, showTips: true }"
            />
          </el-form-item>
        </el-form>
      </template>
    </template>
    <template #footer>
      <div class="text-center">
        <template v-if="step === 1">
          <el-button type="primary" @click="handleNext" :disabled="disabledNext">下一步</el-button>
        </template>
        <template v-else>
          <el-button @click="handlePrev">上一步</el-button>
          <el-popconfirm
            confirm-button-text="确认"
            cancel-button-text="取消"
            title="请确认是否执行此操作"
            width="194"
            @confirm="handleConfirm"
          >
            <template #reference>
              <el-button type="primary" :loading="loading">确定</el-button>
            </template>
          </el-popconfirm>
        </template>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="tsx">
import { ref, reactive, toRef, computed } from 'vue'
import { ElMessage } from 'element-plus'
import { Warning } from '@element-plus/icons-vue'
import { storeToRefs } from 'pinia'
import { useParamsStore } from '@/store'
import useLoading from '@/hooks/use-loading'
import useTryCatch from '@/hooks/use-try-catch'
import { getPagesList } from '@/api/page-management'
import { changePageField } from '@/api/template-management'
import type { listItem } from '@/api/page-management'
import type { pageFieldData } from '@/api/template-management'

import QueryForm from '@/components/QueryForm/index.vue'
import CustomTable from '@/components/CustomTable/index.vue'

const props = withDefaults(defineProps<{
  tplId: string;
  step?: 1|2;
  fieldId: number;
  fieldLabel: string;
}>(), {
  step: () => 1
})
const emit = defineEmits(['confirm', 'closed'])

const page_state_map = {
  'new': {
    color: 'var(--el-color-primary)',
    text: '未创建'
  },
  'created': {
    color: 'var(--el-color-success)',
    text: '已生成'
  },
  'modified': {
    color: 'var(--el-color-primary)',
    text: '已修改'
  },
  'disable': {
    color: 'var(--el-color-error)',
    text: '已禁用'
  },
}

const { site_id_all, channel_item_all } = storeToRefs(useParamsStore())
const { pageStateList } = useParamsStore()
const { loading, setLoading } = useLoading()
const show = ref(true)
const step = ref(props.step)
const queryParams = reactive({
  tpl_id: props.tplId,
  page_state: '',
  url: '',
  page_group_id: '',
  tpl_page_ids: '',
  module: '',
  type: '',
  user_id_a: '',
  user_id_e: '',
  user_id_c: '',
  tpl_language: '',
  tpl_name: '',
  state: '',
})
const formList = ref([
  {
    label: '页面id',
    clearable: true,
    placeholder: '页面id',
    value: toRef(queryParams, 'tpl_page_ids'),
    component: 'input',
  },
  {
    label: '页面集id',
    clearable: true,
    placeholder: '页面集id',
    value: toRef(queryParams, 'page_group_id'),
    component: 'input',
  },
  {
    label: '页面状态',
    clearable: true,
    placeholder: '页面状态',
    value: toRef(queryParams, 'page_state'),
    component: 'select',
    selections: pageStateList
  },
  {
    label: '页面url',
    clearable: true,
    placeholder: '页面url',
    value: toRef(queryParams, 'url'),
    component: 'input',
  },
])
const tableRef = ref()
const columns = ref([
  {
    title: '页面id',
    dataKey: 'tpl_page_id',
    width: 100,
    showOverflowTooltip: true,
  },
  {
    title: '页面url',
    dataKey: 'url',
    width: 280,
    showOverflowTooltip: true,
    cellRenderer: (scope: { row: listItem }) => (
      <a href={scope.row.check_url} target='_blank'>
        { scope.row.url || '--' }
      </a>
    )
  },
  {
    title: '页面状态',
    dataKey: 'state',
    showOverflowTooltip: true,
    cellRenderer: (scope: { row: listItem }) => (
      <span style={ { 
        color: page_state_map[scope.row.page_state]?.color
      } }>
        { page_state_map[scope.row.page_state]?.text }
      </span>
    )
  },
  {
    title: '更新人',
    dataKey: 'user_name_e',
    showOverflowTooltip: true,
  },
  {
    title: '更新时间',
    dataKey: 'edit_time',
    showOverflowTooltip: true,
  },
])
const selectAll = ref(false)
const operateList = ref([
  {
    title: '全选所有页面',
    method: () => {
      const { customTableRef } = tableRef.value?.$refs
      if (customTableRef) {
        customTableRef.toggleAllSelection()
      }
      selectAll.value = true
    },
    disabled: computed(() => selectAll.value)
  },
  {
    title: '取消全选',
    method: () => {
      const { customTableRef } = tableRef.value?.$refs
      if (customTableRef) {
        customTableRef.toggleAllSelection()
      }
      selectAll.value = false
    },
    disabled: computed(() => !selectAll.value)
  },
])
const tableData = ref<listItem[]>([])
const tableSelections = ref<listItem[]>([])
const editFieldData = reactive<pageFieldData>({
  site_id: site_id_all.value,
  tpl_id: props.tplId,
  field_value: '',
  field_id: props.fieldId,
  tpl_page_id: '', //页面id，逗号隔开， 0表示所有页面
})

const disabledNext = computed(() => (tableSelections.value.length === 0 && !selectAll.value))
const selectedPages = computed(() => {
  const total = tableRef.value?.getTotal() as number
  return selectAll.value ? (total || 0) : tableSelections.value.length
})

const handleSearch = () => {
  tableRef.value?.setPage(1)
  const { page, pageSize } = tableRef.value?.getPagination()
  getList( { page, pageSize })
}
const getList = ( { page, pageSize } ) => {
  useTryCatch( async () => {
    setLoading(true)
    const params = {
      S: { 
        ...queryParams,
        channel_id: channel_item_all.value.channel_id as number
      },
      site_id: site_id_all.value,
      page,
      page_size: pageSize
    }
    const res = await getPagesList(params)
    if (res.code === 200) {
      tableData.value = res.data.items
      tableRef.value?.setTotal(res.data.total)
    } else {
      ElMessage.error(res.msg)
    }
    setLoading(false)
  }, () => setLoading(false) )
}
const handleSelectionChange = (selections: listItem[]) => {
  tableSelections.value = selections
  const total = tableRef.value?.getTotal() as number
  if (tableSelections.value.length < total) {
    selectAll.value = false
  }
}
const handleNext = () => {
  step.value = 2
}
const handlePrev = () => {
  step.value = 1
}
const handleConfirm = () => {
  useTryCatch( async () => {
    setLoading(true)
    const params = {
      ...editFieldData,
      tpl_page_id: selectAll.value ? '0' : tableSelections.value.map( ({ tpl_page_id }) => tpl_page_id ).join(',')
    }

    const res = await changePageField(params)
    if (res.code === 200) {
      show.value = false
      emit('confirm')
      ElMessage.success('操作成功')
    } else {
      ElMessage.error(res.msg)
    }
    setLoading(false)
  }, () => {
    setLoading(false)
  } )
}

defineExpose({
  show: () => show.value = true,
  setStep: ( n: 1|2 ) => step.value = n
})
</script>

<style>
.ace-editor-modal { z-index: 9999 !important; }
</style>