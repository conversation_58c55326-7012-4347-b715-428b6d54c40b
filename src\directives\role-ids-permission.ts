import { useUserStroe } from '@/store'
function checkPermission(el, { value }) {
  if (value && Array.isArray(value)) {
    if (value.length > 0) {
      const permissionRoleIds = value
      const { userInfo } = useUserStroe()
      const role_ids = userInfo.role_ids as string
      let hasPermission = false
      for ( const id of permissionRoleIds ) {
        if (role_ids?.indexOf(id) > -1) {
          hasPermission = true
          break
        }
      }
      if (!hasPermission) el.parentNode && el.parentNode.removeChild(el)
    }
  }
}
export default {
  mounted(el, binding) {
    checkPermission(el, binding)
  },
  componentUpdated(el, binding) {
    checkPermission(el, binding)
  }
}
