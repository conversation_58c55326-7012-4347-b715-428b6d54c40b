<template>
  <el-dialog
    title="渠道只读设置"
    v-model="show"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    width="1000"
    append-to-body
    align-center
    @closed="emit('close')"
  >
    <template #default>
      <el-form inline>
        <el-form-item label="添加站点:" >
          <el-space :size="16">
            <el-select 
              v-model="site"
              value-key="cms_site_id"
              placeholder="请选择"
              filterable
            >
              <el-option
                v-for="d in webList"
                :key="d.cms_site_id"
                :label="d.cms_site_name"
                :value="d"
              />
            </el-select>
            <el-button type="primary" @click="handleAdd"> 添加 </el-button>
          </el-space>
        </el-form-item>
      </el-form>
      <el-table :data="tableData" v-loading="loading">
        <el-table-column type="index" label="序号" width="80" />
        <el-table-column prop="cms_site_name" label="站点" />
        <el-table-column label="渠道">
          <template #default="{ row }">
            <el-select 
              v-model="row.channel_ids"
              multiple
              clearable
              collapse-tags
              collapse-tags-tooltip
              placeholder="请选择"
              @visible-change="handleLoadList(row)"
            >
              <el-option 
                v-for="d in row.channel_list"
                :key="d.channel_id"
                :label="d.channel_code"
                :value="d.channel_id"
              />
            </el-select>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="100">
          <template #default="{ row, $index }">
            <el-button type="danger" @click="handleDelete(row, $index)"> 删除 </el-button>
          </template>
        </el-table-column>
      </el-table>
      <!-- <p style="color: var(--el-color-danger); font-size: 12px;"> PS: 新增/修改渠道，需刷新页面后生效 </p> -->
    </template>
    <template #footer>
      <div class="text-center">
        <el-button @click="show = false"> 取消 </el-button>
        <el-button type="primary" :loading="loading" @click="handleSubmit"> 确认 </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { useUserStroe, useParamsStore } from '@/store'
import useTryCatch from '@/hooks/use-try-catch'
import useLoading from '@/hooks/use-loading'
import { channelReadUpdate } from '@/api/user'
import { getChannelList } from '@/api/params'

type siteItem = {
  cms_site_id: number; 
  cms_site_name: string;
}

type dataItem = {
  cms_site_id: number;
  cms_site_name: string;
  channel_ids: number[];
  channel_list: { channel_id: number; channel_code: string; }[];
}

const emit = defineEmits(['close'])

const { userInfo, updateChannelReadSession } = useUserStroe()
const { webList } = useParamsStore()
const { loading, setLoading } = useLoading()

const show = ref(true)
const site = ref<siteItem>()
const tableData = ref<dataItem[]>([])
const siteIdSet = new Set<number>()

const handleLoadList = (row: dataItem, cb?: () => any) => {
  return new Promise(async (resolve, reject) => {
    if (row.channel_list.length === 0) {
      const res = await getChannelList(row.cms_site_id)
      if (res.code === 200) {
        row.channel_list = res.data
        resolve(res.data)
      } else {
        ElMessage.error(res.msg)
        reject(res.msg)
      }
    }
  })
}

const handleAdd = () => {
  if (siteIdSet.has(site.value?.cms_site_id || 0)) {
    return ElMessage.warning('请勿重复添加')
  }
  if (site.value?.cms_site_id) {
    siteIdSet.add(site.value.cms_site_id)

    tableData.value.push({
      cms_site_id: site.value.cms_site_id,
      cms_site_name: site.value.cms_site_name,
      channel_ids: [],
      channel_list: []
    })
  }
}

const handleDelete = (row: dataItem,index: number) => {
  ElMessageBox.confirm('请确认是否删除', '提示', {
    type: 'warning',
    confirmButtonText: '确认',
    cancelButtonText: '取消',
    callback: (action: string) => {
      if (action === 'confirm') {
        tableData.value.splice(index, 1)
        siteIdSet.delete(row.cms_site_id)
      }
    }
  })
}

const handleSubmit = () => {
  let hasEmpty = false
  useTryCatch( async () => {
    setLoading(true)

    const params = JSON.stringify(tableData.value.map((item) => {
      const param = {
        cms_site_id: item.cms_site_id,
        cms_site_name: item.cms_site_name,
        channel_ids: item.channel_ids.toString(),
      }
      if (!param.channel_ids) {
        hasEmpty = true
      }
      return param
    }))

    if (hasEmpty) {
      setLoading(false)
      return ElMessage.warning('渠道不能为空, 请至少选择一个渠道')
    }

    const res = await channelReadUpdate(params)
    if (res.code === 200) {
      ElMessage.success(res.msg)
      show.value = false
      updateChannelReadSession(params)
      userInfo.channel_read = params
    } else {
      ElMessage.error(res.msg)
    }

    setLoading(false)
  }, () => setLoading(false) )
}

if (userInfo.channel_read) {
  setLoading(true)

  const channel_read = JSON.parse(userInfo.channel_read as string)
  const list: dataItem[] = []

  channel_read.forEach( ( item ) => {
    list.push( {
      cms_site_id: item.cms_site_id,
      cms_site_name: item.cms_site_name,
      channel_ids:  item.channel_ids ? item.channel_ids.split(',').map(( id: string ) => +id) : [],
      channel_list: [],
    } )

    siteIdSet.add(item.cms_site_id)
  } )

  const promises = list.map(( item ) => handleLoadList(item))

  Promise.allSettled(promises).then( () => {
    tableData.value = list
    setLoading(false)
  } ).catch ( () => {
    tableData.value = list
    setLoading(false)
  } )
}
</script>