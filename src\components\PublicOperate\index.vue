<template>
  <div class="operate-bar">
    <div class="prepend-item">
      <OperateItems :operate-list="prependList" :size="size" />
    </div>
    <div class="append-item" :style="{ marginRight: `-${16}px` }">
      <OperateItems :operate-list="appendList" :size="16" />
    </div>
  </div>
</template>

<script setup lang="ts">
import type { operateItem } from './type'
import OperateItems from './operateItems.vue'

defineOptions({name: 'PublicOperate'})

const props = withDefaults(defineProps<
  {
    operateList: operateItem[];
    size?: number;
  }
>(), {
  operateList: () => [],
  size: () => 16
})

const prependList = ref<operateItem[]>([])
const appendList = ref<operateItem[]>([])

for(let item of props.operateList) {
  if (item.append) {
    appendList.value.push(item)
  } else {
    prependList.value.push(item)
  }
}
</script>