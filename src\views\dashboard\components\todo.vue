<template>
  <div class="rowBC">
    <div 
      class="todo-item with-hand" 
      :style="{
        '--gradient': 'linear-gradient(180deg, #3172ff 0%, #719eff 100%)',
        '--image': `url(${Document})`
      }"
      @click="handleJump('/publish-center/audit-list', { typeName: 'pending' })"
    >
      <div class="content">
        <div>发布待审核</div>
        <div class="big">{{ auditNum }}</div>
      </div>
    </div>
    <div 
      class="todo-item with-hand" 
      :style="{
        '--gradient': 'linear-gradient(180deg, #796efa 0%, #9991ff 100%)',
        '--image': `url(${Plane})`
      }"
      @click="handleJump('/publish-center/generate-publish-record', { task_status: 4, user_id: userInfo.user_id })"
    >
      <div class="content">
        <div>生成待发布</div>
        <div class="big">{{ publishNum }}</div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onActivated } from 'vue'
import { useRouter } from 'vue-router'
import { storeToRefs } from 'pinia'
import { useParamsStore, useUserStroe } from '@/store'
import useTryCatch from '@/hooks/use-try-catch'
import { getAuditData } from '@/api/audit-management'

import Document from '@/assets/dashboard_images/image_generate.png'
import Plane from '@/assets/dashboard_images/image_release.png'

const router = useRouter()
const { site_id_all } = storeToRefs(useParamsStore())
const { userInfo } = storeToRefs(useUserStroe())
const auditNum = ref(0)
const publishNum = ref(0)

const getData = () => {
  useTryCatch( async () => {
    const res = await getAuditData(site_id_all.value)
    if (res.code === 200) {
      auditNum.value = res.data.audit_num
      publishNum.value = res.data.publish_num
    }
  } )
}
const handleJump = (path: string, query?: any) => {
  router.push({
    path,
    query
  })
}

onActivated( () => {
  getData()
} )
getData()
</script>