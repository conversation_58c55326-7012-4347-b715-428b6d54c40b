<template>
  <div class="banner-box">
    <el-carousel
      height="126px"
      :autoplay="list.length > 1"
      arrow="never"
      indicator-position="none"
    >
      <el-carousel-item
        v-for="(d, index) in list"
        :key="index"
      >
        <img :src="d.image" class="with-hand" style="width: 100%; height: 100%;" />
      </el-carousel-item>
    </el-carousel>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'

import Banner from '@/assets/dashboard_images/banner.png'

const list = ref([
  {
    image: Banner,
    method: () => {
      console.log(666)
    }
  }
])
</script>