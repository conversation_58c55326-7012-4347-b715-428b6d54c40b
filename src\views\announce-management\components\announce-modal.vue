<template>
  <el-dialog
    v-model="show"
    title="公告"
    center
    :destroy-on-close="true"
    append-to-body
  >
    <el-form
      label-position="right"
      label-width="120px"
      style="max-width: 1000px;"
    >
      <el-form-item label="公告标题：">
        <strong>{{ detailInfo?.title }}</strong>
      </el-form-item>
      <el-form-item label="发布时间：">
        <strong>{{ detailInfo?.publish_time }}</strong>
      </el-form-item>
      <el-form-item label="内容：">
        <el-input
          type="textarea"
          :value="detailInfo?.content"
          readonly
          rows="20"
        />
      </el-form-item>
    </el-form>

    <template #footer>
      <el-button type="primary" @click="() => show = false">已阅</el-button>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import type { announceListItem } from '@/api/announcement'

defineProps<{
  detailInfo?: announceListItem
}>()

const show = ref(true)
</script>