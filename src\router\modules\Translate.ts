import type { moduleMap } from '../aysncRoutes'
export default {
  'TranslateManagement': {
    component: () => import('@/views/translate-management/index.vue'),
    path: 'translate-management',
    title: '翻译列表',
    cachePage: true,
    icon: 'translate',
  },
  'TranslateDetail': {
    component: () => import('@/views/translate-management/detail.vue'),
    path: 'translate-detail',
    title: '翻译详情',
    hidden: true,
    cachePage: true,
    queryRequired: true,
  },
  'TranslateSupplier': {
    component: () => import('@/views/translate-management/supplier/index.vue'),
    path: 'translate-supplier',
    title: '供应商配置',
    cachePage: true,
  },
  'InternalLinkPool': {
    component: () => import('@/views/translate-management/InterLinkPool/index.vue'),
    path: 'internal-link-pool',
    title: '预设内链替换',
    cachePage: true,
  }
} as {
  [propName: string]: moduleMap
}