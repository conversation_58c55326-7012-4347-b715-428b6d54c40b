<template>
  <div class="scroll-y with-flex">
    <QueryForm
      :form-list="formList"
      :loading="loading"
      @search="handleSearch"
      hide-reset
    />
    <div v-loading="loading" class="sticky-table">
      <CustomTable 
        ref="tableRef"
        :data="tableData"
        :columns="columns"
        :table-method="getList"
      />
    </div>
  </div>
</template>

<script setup lang="tsx">
import { ref, reactive, toRef } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { ElMessage, ElButton } from 'element-plus'
import { useBasicStore } from '@/store/basic'
import { useParamsStore } from '@/store'
import { storeToRefs } from 'pinia'
import useTryCatch from '@/hooks/use-try-catch'
import useLoading from '@/hooks/use-loading'
import Listener from '@/utils/site-channel-listeners'
import { getArtImportTaskList } from '@/api/tools-management'
import type { artImportTaskItem, artImportQueryParams } from '@/api/tools-management'

import QueryForm from '@/components/QueryForm/index.vue'
import CustomTable from '@/components/CustomTable/index.vue'

defineOptions( { name: 'ArticleImportRecord' } )

const statusMap = {
  1: '待处理',
  2: '处理中',
  3: '已处理',
  4: '任务失败',
  5: '任务部分失败'
}
const statusList = Object.keys( statusMap ).map( ( key ) => ( { label: statusMap[ key ], value: key } ) )

const router = useRouter()
const route = useRoute()
const basicStore = useBasicStore()
const { channel_item_all, site_id_all, userList } = storeToRefs(useParamsStore())
const { loading, setLoading } = useLoading()

const queryParams = reactive<artImportQueryParams>( {
  site_id: site_id_all.value,
  id: '',
  status: '',
  user_id_e: '',
} )

const formList = ref([
  {
    placeholder: '任务ID',
    value: toRef(queryParams, 'id'),
    component: 'input',
  },
  {
    placeholder: '任务状态',
    value: toRef(queryParams, 'status'),
    component: 'select',
    selections: statusList
  },
  {
    placeholder: '编辑人',
    value: toRef(queryParams, 'user_id_e'),
    component: 'select',
    selections: userList,
    labelKey: 'real_name',
    valueKey: 'user_id',
  },
])

const tableRef = ref()
const tableData = ref<artImportTaskItem[]>([])
const columns = ref([
  {
    title: '任务ID',
    dataKey: 'id',
    width: 100,
  },
  {
    title: '任务名称',
    dataKey: 'name',
    minWidth: 200,
    cellRenderer: ( scope: { row: artImportTaskItem } ) => (
      <span>
        { scope.row.add_time + scope.row.add_user }
      </span>
    )
  },
  {
    title: '任务状态',
    dataKey: 'status',
    width: 150,
    cellRenderer: ( scope: { row: artImportTaskItem } ) => (
      <span style={ { 
        color: scope.row.status === 2 ? 'var(--el-coolor-primary)' 
        : scope.row.status === 3 ? 'var(--el-coolor-success)' 
        : scope.row.status === 4 || scope.row.status === 5 ? 'var(--el-color-danger)' : '' 
      } }>
        { statusMap[ scope.row.status ] }
      </span>
    )
  },
  {
    title: '任务创建人',
    dataKey: 'add_user',
    width: 150,
  },
  {
    title: '任务创建时间',
    dataKey: 'add_time',
    width: 180,
  },
  {
    title: '任务编辑人',
    dataKey: 'edit_user',
    width: 150,
  },
  {
    title: '任务编辑时间',
    dataKey: 'edit_time',
    width: 180,
  },
  {
    title: '任务完成时间',
    dataKey: 'finish_time',
    width: 180,
  },
  {
    title: '操作',
    dataKey: '',
    fixed: 'right',
    width: 100,
    cellRenderer: ( scope: { row: artImportTaskItem } ) => (
      <>
        <ElButton type='primary' plain onClick={() => handleToDetail(scope.row.id)}> 查看 </ElButton>
      </>
    )
  }
])

const handleSearch = () => {
  tableRef.value.setPage(1)
  const { page, pageSize } = tableRef?.value?.getPagination()
  getList( { page, pageSize } )
}

const getList = ( { page, pageSize } ) => {
  useTryCatch( async () => {
    setLoading(true)

    const params = {
      ...queryParams,
      site_id: site_id_all.value,
      channel_item_all: channel_item_all.value.channel_id,
      page,
      page_size: pageSize,
    }

    const res = await getArtImportTaskList( params )
    if (res.code === 200) {
      tableData.value = res.data.list
      tableRef.value?.setTotal(res.data.total)
    } else {
      ElMessage.error( res.msg )
    }

    setLoading(false)
  }, () => setLoading(false) )
}

const handleToDetail = ( id: number ) => {
  const name = 'ArticleImportDetail'
  basicStore.delCachedView(name)
  router.push( { name, query: { job_id: id } } )
}

Listener( route, () => {
  handleSearch()
} )
</script>