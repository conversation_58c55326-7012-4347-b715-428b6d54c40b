<template>
  <div class="scroll-y with-flex">
    <div v-loading="loading" class="sticky-table">
      <CustomTable
        ref="tableRef"
        :columns="columns"
        :data="tableData"
        :table-method="getList"
        :operate-list="operateList"
      />
    </div>

    <OperateModal 
      v-if="loadModal"
      ref="operateModalRef"
      :detail-data="detailData"
      :site_id="id"
      @save="handleRefresh"
    />
  </div>
</template>

<script setup lang="tsx">
import { ref, defineAsyncComponent } from 'vue'
import { useRoute } from 'vue-router'
import { ElButton, ElMessage, ElMessageBox } from 'element-plus'
import useTryCatch from '@/hooks/use-try-catch'
import useLoading from '@/hooks/use-loading'
import { siteChannelList, siteChannelStatus } from '@/api/site-config'
import type { siteChannelItem } from '@/api/site-config'

import CustomTable from '@/components/CustomTable/index.vue'
const OperateModal = defineAsyncComponent(() => import('./operateModal.vue'))

defineOptions( { name: 'SiteConfigChannel' } )

const route = useRoute()
const { loading, setLoading } = useLoading()
const { id } = route.query as unknown as { id: number }

const columns = ref([
  {
    title: '站点渠道ID',
    dataKey: 'channel_id',
    width: 100,
  },
  {
    title: '所属渠道',
    dataKey: 'channel_code',
    width: 100,
    cellRenderer: (scope: { row: siteChannelItem }) => (
      <span style={ { color: scope.row.color } }>
        { scope.row.channel_code }
      </span>
    )
  },
  {
    title: '渠道名称',
    dataKey: 'name',
    minWidth: 100,
  },
  {
    title: '渠道域名',
    dataKey: 'host',
    minWidth: 100,
  },
  {
    title: '渠道类型',
    dataKey: 'type_name',
    width: 100,
  },
  {
    title: '站点语言',
    dataKey: 'language_name',
    width: 180,
  },
  {
    title: '渠道状态',
    dataKey: 'state',
    width: 100,
    cellRenderer: (scope: { row: siteChannelItem }) => (
      <span style={ { color: scope.row.state === 'able' ? 'var(--el-color-success)' : 'var(--el-color-danger)' } }>
        { scope.row.state_name }
      </span>
    )
  },
  {
    title: '操作',
    dataKey: '',
    width: 180,
    cellRenderer: (scope: { row: siteChannelItem }) => (
      <>
        <ElButton type='primary' plain disabled={ scope.row.type === 'master' } onClick={() => handleEdit(scope.row)}>编辑</ElButton>
        <ElButton 
          type={scope.row.state === 'able' ? 'danger' : 'success'} 
          plain 
          disabled={ scope.row.type === 'master' } 
          onClick={() => handleStatus(scope.row)}
        >
          { scope.row.state === 'able' ? '禁用' : '启用' }
        </ElButton>
      </>
    )
  }
])
const operateList = ref([
  {
    title: '创建子渠道',
    button: true,
    append: true,
    method: () => {
      detailData.value = null
      openModal()
    }
  }
])
const tableRef = ref()
const tableData = ref<siteChannelItem[]>([])
const operateModalRef = ref()
const loadModal = ref(false)
const detailData = ref<siteChannelItem|null>(null)


const handleRefresh = () => {
  const { page, pageSize } = tableRef.value?.getPagination()
  getList({ page, pageSize })
}
const getList = ( { page, pageSize } ) => {
  useTryCatch( async () => {
    setLoading(true)
    const params = {
      site_id: id,
      page,
      page_size: pageSize
    }

    const res = await siteChannelList(params)
    if (res.code === 200) {
      tableData.value = res.data.items
      tableRef.value?.setTotal(res.data.total)
    } else {
      ElMessage.error(res.msg)
    }
    setLoading(false)
  }, () => setLoading(false) )
}

const handleEdit = (row: siteChannelItem) => {
  detailData.value = { ...row }
  openModal()
}
const openModal = () => {
  nextTick(() => {
    loadModal.value = true
    operateModalRef.value?.show()
    operateModalRef.value?.setData()
  })
  
}

const handleStatus = (row: siteChannelItem) => {
  const isDisable = row.state === 'able' ? false : true
  ElMessageBox.confirm(
    `请确认是否${isDisable ? '启用' : '禁用'}子渠道`,
    '提示',
    {
      dangerouslyUseHTMLString: true,
      confirmButtonText: '确认',
      cancelButtonText: '取消',
      callback: (action: string) => {
        if (action === 'confirm') {
          useTryCatch( async () => {
            setLoading(true)

            const res = await siteChannelStatus(row.channel_id, { state: isDisable ? 'able' : 'disable'})
            if (res.code === 200) {
              handleRefresh()
              ElMessage.success(res.msg)
            } else {
              ElMessage.error(res.msg)
            }
            setLoading(false)
          }, () => setLoading(false) )
        }
      }
    }
  )
}
</script>