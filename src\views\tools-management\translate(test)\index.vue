<template>
  <div class="scroll-y">
    <el-card shadow="never" style="max-width: 500px;">
      <template #header>
        <strong style="font-size: 1.25rem;">创建机翻任务</strong>
      </template>
      <template #default>
        <el-form ref="formRef" :model="submitData" label-suffix=":" :rules="rules" label-width="120">
          <el-form-item label="文章id" prop="art_id">
            <el-input v-model="submitData.art_id" placeholder="请输入文章ID, 暂不支持输入多个" style="width: 240px;" />
          </el-form-item>
          <el-form-item label="翻译语言" prop="to">
            <el-select v-model="submitData.to" filterable placehlder="请选择翻译语言" style="width: 240px;">
              <el-option 
                v-for="d in langs"
                :label="d.label"
                :value="d.value"
              />
            </el-select>
          </el-form-item>
          <el-form-item label="翻译工具" prop="type">
            <el-select v-model="submitData.type" placehlder="请选择翻译工具" style="width: 240px;">
              <el-option 
                v-for="d in tools"
                :label="d.label"
                :value="d.value"
              />
            </el-select>
          </el-form-item>
        </el-form>
        <div class="text-center" style="margin-top: 50px;">
          <el-button @click="handleReset"> 重置 </el-button>
          <el-button type="primary" :loading="loading" @click="handleSubmit">提交任务</el-button>
          <div style="font-size: 12px; padding-top: 12px; color: var(--el-color-error)"> 
            调试版本,  部分翻译任务时间较长, 请耐心等待, 部分翻译解析异常500, 请另外尝试。
          </div>
        </div>
      </template>
    </el-card>

    <ResultModal
      v-if="loadModal"
      :raw="rawText"
      :result="resultText"
      @close="loadModal = false"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, defineAsyncComponent } from 'vue'
import { ElMessage } from 'element-plus'
import useLoading from '@/hooks/use-loading'
import useTryCatch from '@/hooks/use-try-catch'
import { createTranslateTask } from '@/api/tools-management'

const ResultModal = defineAsyncComponent( () => import( './resultModal.vue' ) )

defineOptions( { name: 'AiTranslate' } )

const { loading, setLoading } = useLoading()
const formRef = ref()
const submitData = reactive({
  art_id: '',
  to: '',
  type: ''
})

const loadModal = ref(false)
const rawText = ref('')
const resultText = ref('')

const rules = {
  art_id: [
    { 
      required: true, 
      validator: (rule, value, callback) => {
        if (!value) {
          return callback(new Error('文章id不能为空'))
        }
        if (!/\d+$/.test(value)) {
          return callback(new Error('只能输入数字'))
        }
        callback()
      }, 
      trigger: 'blur' 
    },
  ],
  to: { required: true, message: '请选择翻译语言', trigger: 'change' },
  type: { required: true, message: '请选择翻译工具', trigger: 'change' }
}


const langs = ref([
  {
    label: '英语',
    value: 'en'
  },
  {
    label: '阿拉伯语',
    value: 'ar'
  },
  {
    label: '葡萄牙语',
    value: 'pt'
  },
  {
    label: '西班牙语',
    value: 'es'
  },
  {
    label: '德语',
    value: 'de'
  },
  {
    label: '法语',
    value: 'fr'
  },
  {
    label: '意大利语',
    value: 'it'
  },
  {
    label: '俄语',
    value: 'ru'
  },
  {
    label: '日语',
    value: 'ja'
  },
  {
    label: '韩语',
    value: 'ko'
  },
  {
    label: '印尼语',
    value: 'id'
  },
  {
    label: '中文(简体)',
    value: 'cn'
  },
  {
    label: '中文(繁体)',
    value: 'cht'
  },
])
const tools = ref([
  {
    label: '讯飞',
    value: 1
  },
  {
    label: '微软OpenAI',
    value: 2
  }
])

const handleReset = () => {
  formRef.value?.resetFields()
}

const handleSubmit = async () => {
  if (!formRef.value) {
    return 
  }
  await formRef.value?.validate( ( valid ) => {
    if (valid) {
      useTryCatch( async () => {
        setLoading(true)
        ElMessage.success('正在翻译中...')

        const res = await createTranslateTask(submitData)
        if (res.code === 200) {
          rawText.value = res.data.old_data
          resultText.value = res.data.data
          loadModal.value = true
          ElMessage.success('翻译完成')
        } else {
          ElMessage.error(res.msg)
        }

        setLoading(false)
      }, () => setLoading(false) )
    }
  } )
}
</script>