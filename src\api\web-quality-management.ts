import axiosReq from './axios'
import qs from 'query-string'

// http://yapi.wondershare.cn/project/32/interface/api/cat_9441

export type detailQueryParams = {
  channel_id: string|number;
  page: number;
  page_size: number;
  issue_id: string|number;
  snapshot_id: string|number;
}

export type snapshotListItem = {
  issue_id: number;
  issue_desc: string;
  source_page_num: number;
  target_page_num: number;
}

export type issueDetailListItem = {
  source_url: string;
  target_url: string;
  channel_id: number;
  page_id: number;
}

// semrush概况
export const getSemrushOverview = (channel_id: number) => 
  axiosReq('get', `/api/v1/semrush/overview?channel_id=${channel_id}`)

// semrush问题列表
export const getSemrushIssueList = () => 
  axiosReq('get', '/api/v1/semrush/issue_conf')

// semrush问题详情
export const getSemrushIssueDetail = (params: detailQueryParams) =>
   axiosReq('get', `/api/v1/semrush/detail`, {
    params,
    paramsSerializer: {
      serialize: (obj: detailQueryParams) => qs.stringify(obj)
    }
   })

// semrush快照概况列表
export const getSemrushSnapshotList = (channel_id: number, issue_id?: string|number) => 
  axiosReq('get', `/api/v1/semrush/list?channel_id=${channel_id}&issue_id=${issue_id||''}`)

// semrush详情导出
export const exportSemrushDetail = (params: { channel_id: number; snapshot_id: string|number; issue_id: string|number;}) =>
   axiosReq('get', `/api/v1/semrush/export`, {
    params,
    paramsSerializer: {
      serialize: (obj: any) => qs.stringify(obj)
    },
  })

// lighthouse概况
export const getLighthouseOverview = (channel_id: number) => 
  axiosReq('get', `/api/v1/lighthouse/overview?channel_id=${channel_id}`)