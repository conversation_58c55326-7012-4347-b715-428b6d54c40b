<template>
  <el-form :inline="true" label-width="140px" style="padding: 50px;">
    <template v-if="isAdd">
      <el-form-item 
        label="选择流程模板："
        style="margin-bottom: 30px;"
        :rules="{required: true}"
      >
        <el-select v-model="addData.template_id">
          <el-option 
            v-for="d in manage_templateList" 
            :key="d.template_id" 
            :value="d.template_id" 
            :label="d.name" 
          />
        </el-select>
      </el-form-item>
      <br />
      <el-form-item
        label="站点："
        style="margin-bottom: 30px;"
        :rules="{required: true}"
      >
        <el-input :value="addData.site_name" readonly />
      </el-form-item>
      <br />
      <el-form-item 
        label="选择渠道："
        style="margin-bottom: 30px;"
        :rules="{required: true}"
      >
        <el-select 
          v-model="channelItem" 
          value-key="channel_id"
          placeholder="所属渠道" 
          filterable
          @change="handleChannelChange"
        >
          <el-option 
            v-for="d in channelList" 
            :key="d.channel_id" 
            :value="d" 
            :label="d.channel_code" 
          />
        </el-select>
      </el-form-item>
      <br />
      <el-form-item label=" ">
        <el-button 
          type="primary" 
          :loading="loading" 
          :disabled="disabledNext" 
          @click="handleNext"
        >
          下一步
        </el-button>
      </el-form-item>
    </template>
    <template v-else>
      <h2 style="margin-bottom: 20px;">站点配置</h2>
      <el-form-item label="站点:">
        <el-input :value="addData.site_name" disabled />
      </el-form-item>
      <el-form-item label="渠道:">
        <el-input :value="addData.channel_name" disabled />
      </el-form-item>
      <h2 style="margin: 30px 0 20px;">环节配置</h2>
      <template v-if="detailData.template_id !== 3">
        <table class="el-table static-table" style="max-width: 800px;">
          <tbody>
            <tr>
              <td width="20%">人员/环节</td>
              <td width="80%"><strong>审核</strong></td>
            </tr>
            <tr>
              <td>处理人</td>
              <td>
                <el-select
                  v-model="audit_handlers"
                  value-key="wsId"
                  label="选择处理人员"
                  multiple
                  collapse-tags
                  collapse-tags-tooltip
                  filterable
                  @change="handleAuditHandlerChange"
                >
                  <el-option v-for="d in userList" :key="d.user_id" :label="d.real_name" :value="d"></el-option>
                </el-select>
              </td>
            </tr>
            <tr>
              <td>知会人</td>
              <td>
                <el-select
                  v-model="audit_ccs"
                  value-key="wsId"
                  label="选择处理人员"
                  multiple
                  collapse-tags
                  collapse-tags-tooltip
                  filterable
                  @change="handleAuditCcsChange"
                >
                  <el-option v-for="d in userList" :key="d.user_id" :label="d.real_name" :value="d"></el-option>
                </el-select>
              </td>
            </tr>
          </tbody>
        </table>
      </template>
      <table class="el-table static-table" style="max-width: 800px;">
        <tbody>
          <tr>
            <td width="20%">人员/环节</td>
            <td width="80%"><strong>结束</strong></td>
          </tr>
          <tr>
            <td>处理人</td>
            <td> / </td>
          </tr>
          <tr>
            <td>知会人</td>
            <td>
              <el-select
                v-model="notice_ccs"
                value-key="wsId"
                label="选择处理人员"
                multiple
                collapse-tags
                collapse-tags-tooltip
                filterable
                @change="handleNoticeCcsChange"
               >
                <el-option v-for="d in userList" :key="d.user_id" :label="d.real_name" :value="d"></el-option>
              </el-select>
            </td>
          </tr>
        </tbody>
      </table>
      <h2 style="margin: 30px 0 20px;">通知配置</h2>
      <el-form-item label="通知方式:">
        <el-checkbox-group v-model="checkList" @change="handleNoticeChange">
          <el-checkbox label="1">email</el-checkbox>
          <el-checkbox label="2">消息通知</el-checkbox>
        </el-checkbox-group>
      </el-form-item>

      <div style="margin: 20px 0;">
        <el-button type="primary" @click="handleSave"> 保存 </el-button>
        <el-button 
          plain 
          :loading="loading" 
          @click="handleCancle"
        >
          取消
        </el-button>
      </div>
    </template>
  </el-form>
</template>

<script setup lang="ts">
import { ref, reactive, computed } from 'vue'
import { ElMessage } from 'element-plus'
import { useRoute, useRouter } from 'vue-router'
import { storeToRefs } from 'pinia'
import { useParamsStore, useTagsViewStore } from '@/store'
import useLoading from '@/hooks/use-loading'
import useTryCatch from '@/hooks/use-try-catch'
import { getConfigDetail, addConfig, editConfig } from '@/api/audit-management'
import type { addData, configData, configDetail, nodeUser } from '@/api/audit-management'
import type { user } from '@/store/modules/params/types'
import Listener from '@/utils/site-channel-listeners'

// 不缓存此路由， 否则需要处理兼容逻辑
defineOptions( { name: 'ReviewConfigDetail' } )

const route = useRoute()
const router = useRouter()
const { manage_templateList, userList, channelList, site_id_all, site_name_all } = storeToRefs(useParamsStore())
const { delVisitedView } = useTagsViewStore()
const { loading, setLoading } = useLoading()

const isAdd = ref(route.query.entity_id ? false : true)

const channelItem = ref<{
  channel_id: string|number;
  channel_code: string;
}>({
  channel_id: '',
  channel_code: '',
})
const checkList = ref<string[]>([])
const audit_handlers = ref<user[]>([])  //审核处理人
const audit_ccs = ref<user[]>([])       //审核知会人
const notice_ccs = ref<user[]>([])      //结束知会人
const addData = reactive<addData>({
  template_id: '',
  site_id: site_id_all.value,
  site_name: site_name_all.value,
  channel_id: '',
  channel_name: '',
})
const detailData = reactive<configData>({
  template_id: '',
  entity_id: '',
  notice_type: '',
  nodes: [
    {
      node_id: '',
      ccs: [],
      handlers: [],
    },
    {
      node_id: '',
      ccs: [],
    }
  ]
})

const disabledNext = computed(() => {
  if (!addData.template_id || !addData.channel_id) {
    return true
  }
  return false
})

const formatNodeUsers = (userList: user[]) =>  {
  const value: nodeUser[] = []
  userList.forEach(( { real_name, wsId } ) => {
    value.push({
      user_id: wsId,
      name: real_name,
    })
  })
  return value
}
const handleAuditHandlerChange = () => {
  detailData.nodes[0].handlers = formatNodeUsers(audit_handlers.value)
}
const handleAuditCcsChange = () => {
  detailData.nodes[0].ccs = formatNodeUsers(audit_ccs.value)
}
const handleNoticeCcsChange = () => {
  detailData.nodes[1].ccs = formatNodeUsers(notice_ccs.value)
}
const handleNoticeChange = () => {
  detailData.notice_type = checkList.value.join(',')
}
const handleChannelChange = () => {
  const { channel_id, channel_code } = channelItem.value
  addData.channel_id = channel_id
  addData.channel_name = channel_code
}
const handleNext = () => {
  useTryCatch( async () => {
    setLoading(true)
    const params = { ...addData }
    const res = await addConfig(params)
    if (res.code === 200) {
      isAdd.value = false
      getDetail(res.data.entity_id)
    } else {
      ElMessage.error(res.msg)
    }
    setLoading(false)
  }, () => setLoading(false))
}
const deformatNodeUser = (nodeUsers: nodeUser[]): user[] => {
  const value: any[] = []
  for ( let { user_id, name } of nodeUsers) {
    value.push({wsId: user_id, real_name: name})
  }
  return value
}
const getDetail = (entity_id: string) => {
  useTryCatch( async () => {    
    const res = await getConfigDetail(entity_id)
    if (res.code === 200) {
      const { nodes, notice_type, entity_id, template_id, channel_name, site_name } = res.data as configDetail
      detailData.template_id = template_id
      detailData.entity_id = entity_id,
      detailData.notice_type = notice_type
      checkList.value = notice_type ? notice_type.split(',') : []
      addData.channel_name = channel_name
      addData.site_name = site_name

      if (nodes) {
        for ( const node of nodes ) {
          const { type, node_id, ccs, handlers  } = node

          if (type === 2) {
            if (handlers && handlers.length > 0) {
              audit_handlers.value = deformatNodeUser(handlers)
              handleAuditHandlerChange()
            }
            if (ccs && ccs.length > 0) {
              audit_ccs.value = deformatNodeUser(ccs)
              handleAuditCcsChange()
            }
            detailData.nodes[0].node_id = node_id
          }

          if (type === 4) {
            if (ccs && ccs.length > 0) {
              notice_ccs.value = deformatNodeUser(ccs)
              handleNoticeCcsChange()
            }
            detailData.nodes[1].node_id = node_id
          }
        }
      }
    } else {
      ElMessage.error(res.msg)
    }
  })
}
const handleSave = async () => {
  if (detailData.template_id !== 3 && audit_handlers.value.length === 0) {
    return ElMessage.warning('处理人不能为空!')
  }
  useTryCatch( async () => {
    setLoading(true)
    const params = {
      ...detailData
    }
    if (detailData.template_id === 3) {
      params.nodes.length === 2 && params.nodes.shift()
    }
    const res = await editConfig(params)
    if (res.code === 200) {
      router.push({ name: 'ReviewConfig' })
      delVisitedView(route)
      ElMessage.success(res.msg || '保存成功')
    } else {
      ElMessage.error(res.msg)
    }
    setLoading(false)
  }, () => setLoading(false))
}
const handleCancle = () => {
  router.push({ name: 'ReviewConfig' })
}
const reset = () => {
  if (isAdd.value) {
    channelItem.value = {
      channel_id: '',
      channel_code: ''
    }
    addData.site_id = site_id_all.value
    addData.site_name = site_name_all.value
  }
}

if (isAdd.value) {
  Listener(route, () => {
    reset()
  })
} else {
  const entity_id = route.query.entity_id as string
  if (entity_id) {
    getDetail(entity_id)
  }
}


</script>