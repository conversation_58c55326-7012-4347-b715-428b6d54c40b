<template>
  <el-dialog
    v-model="show"
    title="存储图片"
    append-to-body
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    @close="handleCancel"
  >
    <div style="font-size: 16px;">
      <div>
        存储路径：{{basePath}}
      </div>
      <div style="padding-top: 8px;">
        已选图片: {{ imgList.length }}张
      </div>
      <ul
        class="display-upload-img"
        :class="{isDraging: dragover}"
        @drop.prevent="onDrop"
        @dragover.prevent="onDragover"
        @dragleave.prevent="dragover = false"
      >
        <li class="list-card upload-card">
          <el-upload
            action="string"
            :multiple="true"
            :auto-upload="false"
            :show-file-list="false"
            :on-change="handleFileChange">
            <Plus class="el-icon-plus" />
          </el-upload>
        </li>
        <li
          class="list-card img-card"
          v-for="(d, index) in imgList"
          :style="{'background-image': `url(${d.dataUrl})`}"
        >
          <Close class="el-icon-close" @click="handDeleteList(index)"/>
        </li>
      </ul>
      <div>
        <strong>是否压缩图片：</strong>
        <el-radio-group v-model="compress" @change="handleCompressChange">
          <el-radio :label="0">否</el-radio>
          <el-radio :label="1">是</el-radio>
        </el-radio-group>
        <div style="display: flex; align-items: center; margin-top: 15px;" v-show="compress === 1">
          压缩品质:{{quality}}% (为保证图片质量, 压缩品质不得少于30%)
          <div style="flex: 1 1 auto; margin-left: 20px">
            <el-slider v-model="quality" @change="handleQulityChange" :min="30" :max="90" :step="1"></el-slider>
          </div>
        </div>
      </div>
    </div>
    <template #footer>
      <div class="text-center">
        <el-button type="primary" @click="handleSave"> 保存 </el-button>
        <el-button plain @click="handleCancel"> 取消 </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { ElMessage } from 'element-plus'
import { Plus, Close } from '@element-plus/icons-vue'
import { imgTypeSupport } from '../types'
import type { UploadFile, UploadRawFile } from 'element-plus'
import type { ImgListItem } from '../types'

defineOptions( { name: 'ImgUpload' } )

const props = defineProps<{
  basePath: string;
}>()
const emit = defineEmits<{
  (e: 'save', arg_1: ImgListItem[]): void;
  (e: 'cancel'): void;
  (e: 'qualityChange',  arg_1: number): void;
  (e: 'compressChange', arg_1: number): void;
}>()

const quality = ref(60)
const imgList = ref<ImgListItem[]>([])
const show = ref(false)
const compress = ref(1)
const dragover = ref(false)

const handleSave = () => {
  emit('save', imgList.value)
}
const handleCancel = () => {
  imgList.value = []
  emit('cancel')
}
const handleCompressChange = () => {
  quality.value = compress.value === 1 ? 100 : 60
  handleQulityChange()
  emit('compressChange', compress.value)
}
const handleQulityChange = () => {
  emit('qualityChange', quality.value)
}
const prependList = (item: ImgListItem) => {
  imgList.value.unshift(item)
}
const handDeleteList = (index: number) => {
  imgList.value.splice(index, 1)
}
const handleFileChange = (file: UploadFile) => {
  const { raw } = file
  if (raw) {
    const { type } = raw
    if (!imgTypeSupport.has(type)) {
      return ElMessage.warning('只能上传图片类型文件')
    }

    const reader = new FileReader()
    reader.readAsDataURL(raw)
    reader.onloadend = function() {
      const item = {
        raw,
        dataUrl: this.result
      }

      prependList(item as ImgListItem)
    }
  }
}

// 拖拽上传处理
const onDragover = () => {
  dragover.value = true
}
const onDrop = (e: DragEvent) => {
  dragover.value = false
  const files = e?.dataTransfer?.files
  if (files) {
    [].slice.call(files).forEach((file: UploadRawFile) => {
      const { type } = file
      if (!imgTypeSupport.has(type)) {
        return ElMessage.warning('只能上传图片类型文件')
      } else {
        handleFileChange( { raw: file } as UploadFile )
      }
    })
  }
}

const handleShow = () => show.value = true
const handleClose = () => show.value = false

defineExpose<{
  show: () => void
  close: () => void
  drop: (e: DragEvent) => void
}>({
  show: handleShow,
  close: handleClose,
  drop: onDrop
})
</script>