import type { moduleMap } from '../aysncRoutes'

export default {
  'WebRegisterGuide': {
    component: () => import('@/views/web-config/registerGuide/index.vue'),
    path: 'web-register-guide',
    title: '网站注册引导',
    cachePage: true,
    icon: 'web-setting',
  },
  'NpsSetting': {
    component: () => import('@/views/web-config/npsSetting/index.vue'),
    path: 'nps-setting',
    title: 'NPS配置',
    cachePage: true,
  },
  'CommentManagement': {
    component: () => import('@/views/web-config/commentManagement/index.vue'),
    path: 'comment-management',
    title: '评论管理',
    cachePage: true,
  },
  'CommentDetail': {
    component: () => import('@/views/web-config/commentManagement/detail.vue'),
    path: 'comment-detail',
    title: '评论详情',
    hidden: true,
  },
  'SocialMedia': {
    component: () => import('@/views/web-config/socialMedia/index.vue'),
    path: 'social-media',
    title: '推广位定制',
    cachePage: true,
  }
} as {
  [propName: string]: moduleMap
}