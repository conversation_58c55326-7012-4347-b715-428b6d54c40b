// 资源中心模块接口
import axiosReq from './axios'
import qs from 'query-string'
import type { HttpResponse } from './axios/types'

export type folderQueryParams = {
  site_id: number;
  channel_id: number;
  type: number; // 1 图片管理； 2 视频管理； 3 其他
}

export type folderData = {
  site_id: number;
  channel_id: number;
  name: string;
  type: number;
  pid: number;
}

export type fileData = {
  file: Blob;
  type: number; // 1 图片管理； 2 视频管理； 3 其他
  site_id: number;
  channel_id: number;
  quality: number;  // 图片压缩比， filyType为1时生效, 100表示不压缩，最低值30
  compress: number; // 1 代表启用压缩，0 则不启用
  batch_no: number; // 上传文件的时间戳，用于后端进行任务分类，点击上传按钮时更新
  path_id: number;  // 目录id
  is_end: number;
  is_batch: number;
}

export type fileQueryParams = {
  file_name: string;
  publish: ''|number;
  user_id_e: ''|number;
  type: number;
  page: number;
  page_size: number;
  path_id: number;
  order_asc?: string; // 顺序排序字段：edit_time ，add_name ，file_size
  order_desc?: string; // 倒叙排序字段：edit_time ，add_name ，file_size
}

export type folderItem = {
  id: number;
  site_id: number;
  channel_id: number;
  type: number;
  pid: number;
  path: string;
  add_time: string;
  edit_time: string;
  file_url: string;
  name: string;
  children: folderItem[];
}

export type fileItem = {
  id: number;
  site_id: number;
  channel_id: number;
  type: number;
  path_id: number;
  path_md5: string;
  file_url: string;
  file_name: string;
  file_size: number;
  suffix: string;
  file_path: string;
  file_host: string;
  state: number; // 状态: 1 正常 2 禁用 3 删除
  compress: number; // 是否压缩: 0 否 1 是
  quality: number;
  publish: number; // 状态: 1 待发布 2 发布中 3 已发布 4 发布失败
  add_time: string;
  edit_time: string;
  edit_name: string;
  path: string; // 目录路径
  selected?: boolean;
  audit?: number; // 是否审核：0 无审核 1 审核中 2 审核完成
}

/* 目录相关接口 */
// 资源目录列表接口
export const getFolderList = (params: folderQueryParams) => 
  axiosReq('get', '/api/v1/resource/get_path_list', {
    params,
    paramsSerializer: {
      serialize: (obj: folderQueryParams) => qs.stringify(obj)
    }
  })

// 资源目录添加接口
export const addFolder = (data: folderData) => 
  axiosReq('post', '/api/v1/resource/add_path', data)

// 资源目录删除接口
export const deleteFolder = (data: { id: number; }) => 
  axiosReq('post', '/api/v1/resource/delete_path', data)

// 资源目录重命名接口
export const renameFolder = (data: { id: number; name: string; }) => 
  axiosReq('post', '/api/v1/resource/rename_path', data)
/* 目录相关接口 */

/* 文件相关接口 */
// 文件上传接口
export const fileUpload = (data: fileData, name?: string) => {
  const formData = new FormData()
  Object.entries(data).forEach( ( [key, value] ) => {
    if (name && key === 'file') {
      formData.append('file', value as Blob, name)
    } else {
      formData.append(key, typeof value === 'number' ? `${value}` : value)
    }
  } )
  return axiosReq('post', '/api/v1/resource/upload', formData)
}
// 文件替换
export const fileReplace = (data: fileData) => {
  const formData = new FormData()
  Object.entries(data).forEach( ( [key, value] ) => {
    formData.append(key, typeof value === 'number' ? `${value}` : value)
  } )
  return axiosReq('post', '/api/v1/resource/cover', formData)
}
  

// 文件列表接口
export const getFileList = (params: fileQueryParams) => 
  axiosReq('get', '/api/v1/resource/get_file_list', {
    params,
    paramsSerializer: {
      serialize: (obj: fileQueryParams) => qs.stringify(obj)
    }
  })

// 文件重命名接口
export const renameFile = (data: { id: number; name: string; }) => 
  axiosReq('post', '/api/v1/resource/rename_file', data)

// 文件批量删除接口
export const deleteFile = (data: { ids: string; }) => 
  axiosReq('post', '/api/v1/resource/delete_file', data)

// 文件批量更改目录
export const moveFile = (data: any) => 
  axiosReq('post', '/api/v1/resource/change_file_path', data)

// 下载文件
export const downloadFile = (params: { url: string; }) => 
  axiosReq<any, Blob>('get', '/api/v1/resource/download', {
    responseType: 'blob',
    params,
    paramsSerializer: {
      serialize: (obj: { url: string; }) => qs.stringify(obj)
    }
  })

// 已上传文件后台压缩处理
export const compressFiles = (data: { ids: string; quality: number; }) => 
  axiosReq('post', '/api/v1/resource/compress_file', data)

// 获取文件操作历史
export const getFileLog = ( params: { resource_id: number; action_type: number|string; page_size: number; page: number } ) => 
  axiosReq('get', '/api/v1/resource/get_log_list', {
    params,
    paramsSerializer: {
      serialize: (obj: any) => qs.stringify(obj)
    }
  })
/* 文件相关接口 */