import { reactive } from 'vue'
import type { FormRules } from 'element-plus'

const patt = /^\/[a-z\d\/_-]+?\.html$/
const errorText = '以/开头、.html结尾,其它部分只允许包含小写英文、数字、/、_和-'

export const maxMap = {
  title: 200,
  keyword: 1000,
  description: 1000,
}

export const formRules = reactive<FormRules>({
  tpl_id: [
    { required: true, message: `模板不能为空`, trigger: 'change' },
  ],
  title: [
    { required: true, message: `页面标题不能为空`, trigger: 'blur' },
    { max: maxMap.title, message: `长度不能超过200字符`, trigger: 'blur' },
  ],
  keyword: [
    { required: false, message: `关键词不能为空`, trigger: 'blur' },
    { max: maxMap.keyword, message: `长度不能超过1000字符`, trigger: 'blur' }
  ],
  description: [
      { required: true, message: `页面描述不能为空`, trigger: 'blur' },
      { max: maxMap.description, message: `长度不能超过1000字符`, trigger: 'blur' }
  ],
  remark: [
    { required: true, message: `备份说明不能为空`, trigger: 'blur' }
  ],
  url: [
    { required: true, validator: (rule, value, callback) => {
      let count = 0
      for (var i = 0; i < value.length; i++) {
          if (value.charAt(i) >= 'A' && value.charAt(i) <= 'Z') {
              count++
          }
      }
      if (count) {
          callback(new Error(errorText))
      }
      if (value === '') {
          callback(new Error('url不能为空'))
      } else if (value.match(patt) === null) {
          callback(new Error(errorText))
      } else if ( value.indexOf('\'') > -1 ) {
          callback(new Error('不允许输入单引号字符'))
      } else {
          callback()
      }
    }, trigger: 'blur' }
  ]
})