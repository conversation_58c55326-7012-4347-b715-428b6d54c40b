<template>
  <div class="sidebar-logo-container" :class="{ collapse: collapse }">
    <transition name="sidebar-logo-fade">
      <!--  折叠显示   -->
      <router-link v-if="collapse" class="sidebar-logo-link" to="/">
        <!-- <el-avatar :icon="UserFilled" :size="32" /> -->
        <img src="@/assets/images/wondershare-square.svg" class="logo-img" style="width: 32px;">
      </router-link>
      <!--  正常显示   -->
      <router-link v-else class="sidebar-logo-link" to="/">
        <!-- <el-space :size="8">
          <el-avatar :icon="UserFilled" :size="40" />
          <div class="sidebar-title">
            {{ userInfo.name }}
            <div class="sub-info" :title="userInfo.email">
              {{ userInfo.email }}
            </div>
          </div>
        </el-space> -->
        <img src="@/assets/images/CompanyLogo.svg" class="logo-img" style="width: 100%;" />
      </router-link>
    </transition>
  </div>
</template>

<script setup lang="ts">
import { UserFilled } from '@element-plus/icons-vue'
import { useUserStroe } from '@/store'
const { userInfo } = useUserStroe()
defineProps({
  //是否折叠
  collapse: {
    type: Boolean,
    required: true
  }
})
</script>

<style lang="scss">
//vue3.0 过度效果更改  enter-> enter-from   leave-> leave-from
.sidebar-logo-container {
  position: relative;
  width: 100%;
  // background: var(--sidebar-logo-background);
  padding-left: 14px;
  padding-right: 14px;
  padding-top: 14px;
  padding-bottom: 10px;
  text-align: left;
  overflow: hidden;
  .sidebar-logo-link {
    height: 100%;
    width: 100%;
    .avatar {
      width: var(--sidebar-logo-width);
      height: var(--sidebar-logo-height);
      color: var(--sidebar-logo-title-color);
    }
    .sidebar-title {
      display: inline-block;
      margin: 0;
      color: var(--sidebar-logo-title-color);
      font-weight: 600;
      font-size: 16px;
      .sub-info {
        font-size: 12px;
        font-weight: 400;
        width: 100%;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
      }
    }
  }
  &.collapse {
    padding-left: 8px;
    .sidebar-logo {
      margin-right: 0;
    }
    .logo-img {
      height: 50px;
    }
  }
  .logo-img {
    height: 70px;
    transition: height .3s;
  }
}
.home {
  .sidebar-logo-container {
    .logo-img {
      height: 0;
    }
  }
}
.dark .logo-img {
  filter: invert(1);
}
</style>
