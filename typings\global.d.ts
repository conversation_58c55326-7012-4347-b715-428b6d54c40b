import type { defineOptions as _defineOptions } from 'unplugin-vue-define-options/macros.d.ts'
import type { RouteLocationRaw } from 'vue-router'
import type { loadAce } from '@/components/AceEditor/index'
declare global {
  interface ObjKeys {
    [propName: string]: any
  }
  const GLOBAL_VAR: String
  const defineOptions: typeof _defineOptions
  const $ref: any
  const docx: any
}
declare interface RouterLinkOptions {
  to: {
    fullPath?: string
  }
}

declare module 'vue' {
  interface ComponentCustomProperties {
    $Ace: loadAce
  }
}
export {}
