<template>
  <div class="scroll-y">
    <QueryForm 
      :loading="loading"
      :form-list="formList"
      hide-reset
      @search="reset"
    />
    <PublicOperate :operate-list="operateList" />

    <div v-loading="loading" style="min-height: 500px;">
      <el-table
        ref="treeTableRef"
        border
        highlight-current-row
        lazy
        row-key="cat_id"
        :data="tableData"
        :row-class-name="tableRowClassName"
        :tree-props="{ children: 'children', hasChildren: 'hasChildren' }"
        :load="loadData"
        @selectionChange="handleSelectionChange"
      >
        <el-table-column type="selection" />
        <el-table-column prop="cat_id" label="分类ID" show-overflow-tooltip />
        <el-table-column prop="name" label="分类名称" show-overflow-tooltip />
        <el-table-column prop="channel_code" label="渠道" width="100" show-overflow-tooltip />
        <el-table-column prop="layer" label="分类层级" width="100" show-overflow-tooltip />
        <el-table-column label="排序" width="80">
          <template #default="scope: { row: categoryListItem }">
            <el-link type="primary" title="点击修改排序"> {{ scope.row.sort }} </el-link>
          </template>
        </el-table-column>
        <el-table-column label="含子类文章总数/该类文章数">
          <template #default="scope: { row: categoryListItem }">
            <el-link type="primary" @click="handleToList(scope.row, 1)"> {{ scope.row.allchild_art_num }} </el-link>
            |
            <el-link type="primary" @click="handleToList(scope.row, 0)"> {{ scope.row.article_num }} </el-link>
          </template>
        </el-table-column>
        <el-table-column label="推荐" width="80">
          <template #default="scope: { row: categoryListItem }">
            <Check width="16" v-if="scope.row.is_recom === '1'" />
            <Close width="16" v-else />
          </template>
        </el-table-column>
        <el-table-column label="热门" width="80">
          <template #default="scope: { row: categoryListItem }">
            <Check width="16" v-if="scope.row.is_hot === '1'" />
            <Close width="16" v-else />
          </template>
        </el-table-column>
        <el-table-column prop="user_e" label="更新人" show-overflow-tooltip />
        <el-table-column prop="edit_time" label="更新时间" show-overflow-tooltip />
        <el-table-column label="操作" width="360">
          <template #default="scope: { row: categoryListItem }">
            <el-button type="primary" plain @click="handleEdit(scope.row)"> 编辑 </el-button>
            <el-button type="primary" plain @click="handleAdd(scope.row)"> 添加子类 </el-button>
            <el-button type="danger" plain @click="handleDelete(scope.row)"> 删除 </el-button>
            <el-button type="primary" plain @click="handleHistory(scope.row)"> 历史版本 </el-button>
          </template>
        </el-table-column>
      </el-table>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Check, Close } from '@element-plus/icons-vue'
import { storeToRefs } from 'pinia'
import { useParamsStore } from '@/store'
import { useBasicStore } from '@/store/basic'
import useTryCatch from '@/hooks/use-try-catch'
import useLoading from '@/hooks/use-loading'
import { getHost } from '@/hooks/use-env'
import { getCategoryList, changeClassifyState } from '@/api/article-management'
import type { categoryListItem } from '@/api/article-management'
import Listener from '@/utils/site-channel-listeners'

import PublicOperate from '@/components/PublicOperate/index.vue'
import QueryForm from '@/components/QueryForm/index.vue'

defineOptions( { name: 'ArticleClassifyList' } )

const route = useRoute()
const router = useRouter()
const { site_id_all, channel_item_all } = storeToRefs(useParamsStore())
const basicStore = useBasicStore()
const { loading, setLoading } = useLoading()
const expandedAll = ref(false)
const cat_id = ref('')
const name = ref('')

const formList = ref([
  {
    label: '文章分类ID',
    placeholder: '文章分类ID',
    value: cat_id,
    component: 'input',
  },
  {
    label: '分类名称',
    placeholder: '分类名称',
    value: name,
    component: 'input',
  },
])
const operateList = ref([
  {
    title: '添加分类',
    button: true,
    append: true,
    method: () => {
      router.push({
        name: 'ArticleClassifyOperate',
        query: {
          type: 'add'
        }
      })
    },
    disabled: computed( () => !channel_item_all.value )
  },
  {
    title: computed( () => expandedAll.value ? '折叠分类' : '展开分类' ),
    method: () => {
      expandedAll.value = !expandedAll.value
      tableData.value.forEach(( row ) => {
        if (row.hasChildren && !row.children) {
          row.children = lazyChildrenMap.get(row.cat_id)
        }
        treeTableRef.value?.toggleRowExpansion(row)
      })
    },
    disabled: computed( () => !channel_item_all.value || loading.value )
  },
  {
    title: '导出EXCEL',
    icon: 'export',
    disabled: computed(() => tableSelections.value.length === 0),
    method: () => {
      const cat_ids = tableSelections.value.map(( { cat_id } ) => cat_id).join(',')
      const channel_id = channel_item_all.value.channel_id
      location.href = `${getHost()}/api/v1/export/articles/categorys?site_id=${site_id_all.value}&cat_id=${cat_ids}${channel_id ? '&channel_id=' + channel_id : ''}`
    }
  },
  {
    title: '',
    icon: 'refresh',
    append: true,
    tooltip: '刷新列表',
    method: () => {
      reset()
    }
  }
])

const treeTableRef = ref()
const tableData = ref<categoryListItem[]>([])
const tableSelections = ref<categoryListItem[]>([])
const lazyChildrenMap = new Map<number, categoryListItem[]>()

const tableRowClassName = (scope: { row: categoryListItem }) => `row-${scope.row.layer}`
const getList = () => {
  useTryCatch( async () => {
    setLoading(true)
    const params = {
      site_id: site_id_all.value,
      channel_id: channel_item_all.value?.channel_id || '',
      cat_id: cat_id.value,
      name: name.value
    }

    const res = await getCategoryList(params)
    if (res.code === 200) {
      const data = res.data
      tableData.value = handleLazyTableData(data)
    } else {
      ElMessage.error(res.msg)
    }
    setLoading(false)
  }, () => setLoading(false) )
}
//懒加载table数据处理，父级的cat_id 与子列表parent_id 对应， 以此作为懒加载依据
const handleLazyTableData = (data: categoryListItem[]) => {
  const lazyData: categoryListItem[] = []

  data.forEach(( row ) => {
    const lazyRow = {} as categoryListItem
    if (row.children && row.children.length > 0) {
      row.hasChildren = true
      factorialData(row.children)
    }
    Object.entries(row).forEach(( [key, value] ) => {
      if (key !== 'children') {
        lazyRow[key] = value
      }
    })

    lazyData.push(lazyRow)
  })

  return lazyData
}
// 递归处理数据
const factorialData = (children: categoryListItem[]) => {
  children.forEach(( child ) => {
    const lazyRow = {} as categoryListItem
    if (!lazyChildrenMap.get(child.parent_id)) {
      lazyChildrenMap.set(child.parent_id, [])
    }
    if (child.children && child.children.length > 0) {
      child.hasChildren = true
      factorialData(child.children)
      Object.entries(child).forEach(( [key, value] ) => {
        if (key !== 'children') {
          lazyRow[key] = value
        }
      })
    }
    lazyChildrenMap.get(child.parent_id)?.push(child)
  })
}
//懒加载table数据处理，父级的cat_id 与子列表parent_id 对应， 以此作为懒加载依据
const loadData = (tree: categoryListItem, treeNode: unknown, resolve: (list: categoryListItem[]) => void) => {
  tree.hasChildren && resolve(lazyChildrenMap.get(tree.cat_id) || [])
}
/* table methods */
const handleToList = (row: categoryListItem, all_child) => {
  basicStore.delCachedView('ArticleManageList')
  router.push({
    name: 'ArticleManageList',
    query: {
      cat_id: row.cat_id,
      all_child
    }
  })
}
const handleEdit = (row: categoryListItem) => {
  basicStore.delCachedView('ArticleClassifyOperate')
  router.push({
    name: 'ArticleClassifyOperate',
    query: {
      type: 'edit',
      cat_id: row.cat_id
    }
  })
}
const handleAdd = (row: categoryListItem) => {
  router.push({
    name: 'ArticleClassifyOperate',
    query: {
      type: 'add',
      parent_id: row.cat_id
    }
  })
}
const handleDelete = (row: categoryListItem) => {
  ElMessageBox.confirm(
    '请确认是否删除该文章分类',
    '提示',
    {
      type: 'warning',
      callback: (action: string) => {
        if (action === 'confirm') {
          useTryCatch( async () => {
            const params = {
              cat_id: `${row.cat_id}`,
              state: 'deleted'
            }
            const res = await changeClassifyState(params)
            if (res.code === 200) {
              ElMessage.success('删除成功')
              reset()
            } else {
              ElMessage.error(res.msg)
            }
          } )
        }
      }
    }
  )
}
const handleHistory = (row: categoryListItem) => {
  router.push({
    name: 'ClassifyHistory',
    query: {
      cat_id: row.cat_id
    }
  })
}
const handleSelectionChange = (val: categoryListItem[]) => {
  tableSelections.value = val
}
/* table methods */
const reset = () => {
  tableData.value = []
  lazyChildrenMap.clear()
  getList()
}


onActivated(() => {
  if (basicStore.refresh) {
    basicStore.setRefresh(false)
    reset()
  }
})
Listener(route, () => {
  reset()
})

getList()
</script>

<style lang="scss">
.el-table {
  .row-1 {
    background-color: var(--el-color-primary-light-5);
  }
  .row-2 {
    background-color: var(--el-color-primary-light-7);
  }
  .row-3 {
    background-color: var(--el-color-primary-light-8);
  }
  .row-4 {
    background-color: var(--el-color-primary-light-9);
  }
}
</style>