<template>
  <el-dialog
    v-model="show"
    :title="title"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    append-to-body
    align-center
    width="1000"
    @closed="emit('close')"
  >
    <template #default>
      <el-form :model="formData" label-width="120" label-suffix=":" inline>
        <el-form-item label="供应商姓名" class="is-required">
          <el-select
            v-if="type === 'add'"
            v-model="supplier" 
            placeholder="请输入供应商名称搜索"
            filterable
            remote
            reserve-keyword
            remote-show-suffix
            value-key="id"
            :suffix-icon="Search"
            :loading="loading"
            :remote-method="getSupplierList"
            @change="handleChange"
          >
            <el-option 
              v-for="d in supplierList" 
              :key="d.id" 
              :value="d"
              :label="`${d.first_name} ${d.last_name}`"
            />
          </el-select>
          <template v-else>
            {{ formData.first_name }} {{ formData.last_name }}
          </template>
        </el-form-item>
        <el-form-item label="备注" style="width: 592px; margin-right: 0;">
          <el-input v-model="formData.remark" placeholder="备注信息" />
        </el-form-item>
      </el-form>
      <PublicOperate v-if="type === 'edit'" :operate-list="operateList" />
      <el-table :data="formData.services" border>
        <el-table-column label="序号" type="index" width="60" />
        <el-table-column label="源语言" prop="source_language" width="150" />
        <el-table-column label="目标语言" prop="target_language" width="150" />
        <el-table-column label="单价">
          <template #default="scope">
            <el-input v-model="scope.row.price_per_unit" :readonly="type === 'view'" placeholder="翻译单字所需单价" style="width: 100%;" />
          </template>
        </el-table-column>
        <el-table-column label="服务类型" prop="service_type_name" width="150" />
        <el-table-column label="货币单位" >
          <template #default="scope">
            <el-select v-model="scope.row.currency" :disabled="type === 'view'" placeholder="货币" style="width: 100%;">
              <el-option label="USD(美元)" value="USD" />
              <el-option label="CNY(人民币)" value="CNY" />
            </el-select>
          </template>
        </el-table-column>
      </el-table>
    </template>
    <template #footer>
      <div class="text-center">
        <el-button @click="show = false"> 取消 </el-button>
        <el-button 
          type="primary" 
          :loading="loading" 
          :disabled="!formData.vendor_account_id || type === 'view'"
          @click="handleSave"
        > 保存 </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, reactive, computed } from 'vue'
import { ElMessage } from 'element-plus'
import { Search } from '@element-plus/icons-vue'
import useTryCatch from '@/hooks/use-try-catch'
import useLoading from '@/hooks/use-loading'
import { addSupplier, editSupplier, getSmartcatSupplier, getSmartcatSupplierDetail } from '@/api/translate-management'
import type { supplierFormData, supplierItem, serviceItem, supplierListItem } from '@/api/translate-management'

import PublicOperate from '@/components/PublicOperate/index.vue'

const props = defineProps<{
  type: 'add'|'edit'|'view';
  formData?: supplierListItem;
}>()
const emit = defineEmits(['close', 'saved'])

const { loading, setLoading } = useLoading()

const show = ref(true)
const title = ref(`${props.type === 'add' ? '添加' : props.type === 'edit' ? '编辑' : '查看'}供应商`)
const supplier = ref<supplierItem>()
const supplierList = ref<supplierItem[]>([])
const formData_raw = {
  vendor_account_id: '',
  email: '',
  first_name: '',
  last_name: '',
  remark: '',
  services: [],
}

const formData = reactive<supplierFormData>({
  ...formData_raw
})

const operateList = ref([
  {
    title: '同步更新SC数据',
    disabled: computed( () => loading.value ),
    append: true,
    method: () => {
      useTryCatch( async () => {
        setLoading(true)

        const res = await getSmartcatSupplierDetail(props.formData?.id as number)
        if (res.code === 200) {

          const { vendor_account_id, email, first_name, last_name, services } = res.data
          formData.vendor_account_id = vendor_account_id
          formData.email = email
          formData.first_name = first_name
          formData.last_name = last_name
          formData.services = services

          ElMessage.success('同步成功')
        } else {
          ElMessage.error(res.msg)
        }

        setLoading(false)
      }, () => setLoading(false) )
    }
  }
])

const getSupplierList = (search_string: string) => {
  if (!search_string) {
    return
  }
  useTryCatch( async () => {
    setLoading(true)
    const res = await getSmartcatSupplier(search_string)
    if (res.code === 200) {
      supplierList.value = res.data
    } else {
      ElMessage.error(res.msg)
    }
    setLoading(false)
  }, () => setLoading(false) )
}

const handleChange = (item: supplierItem) => {
  formData.vendor_account_id = item.id
  formData.email = item.email
  formData.first_name = item.first_name
  formData.last_name = item.last_name
  const list: serviceItem[] = []
  item.services.forEach( ( service ) => {
    const appenditem = {
      ...service,
      price_per_unit: '',
      currency: '',
    }

    list.push(appenditem)
  } )

  formData.services = list
}

const handleSave = () => {
  const services = formData.services
  for ( const item of services ) {
    if ( !item.price_per_unit || !item.currency ) {
      ElMessage.warning('所有单价及货币单位均不能为空, 请确认无误后进行保存')
      return
    }
  }

  useTryCatch( async () => {
    setLoading(true)

    const api = props.type === 'add' ? addSupplier : editSupplier
    const res = await api(formData)
    if (res.code === 200) {
      ElMessage.success('保存成功')
      emit('close')
      emit('saved')
    } else {
      ElMessage.error(res.msg)
    }

    setLoading(false)
  }, () => setLoading(false) )
}

if (props.type !== 'add' && props.formData) {
  const data = props.formData
  formData.vendor_account_id = data.vendor_account_id
  formData.email = data.email
  formData.first_name = data.first_name
  formData.last_name = data.last_name
  formData.remark = data.remark
  const services = [...data.services]
  formData.services = services.map( ( item ) => {
    return {
      service_type: item.service_type,
      service_type_name: item.service_type_name,
      target_language: item.target_language,
      source_language: item.source_language,
      price_per_unit: item.price_per_unit,
      currency: item.currency
    }
  } )
}
</script>