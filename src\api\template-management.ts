
import axiosReq from './axios'
import qs from 'query-string'

export type queryParams = {
  module: string;
  type: string|number;
  user_id_a: string;
  user_id_e: string;
  block_id: string|number;
  state: string;
  tpl_ids: string;
  name: string;
  channel_id: string|number;
  is_mainmodule?: string;
  parent_id?: string|number;
  time_field: string;
  start_time: string;
  end_time: string;
}

export type listQueryParams = {
  S: queryParams;
  page: number;
  page_size: number;
  site_id: number;
  sort_key?: string;
  sort_type?: string;
}

export type listItem = {
  tpl_id: number;
  name: string;
  mainChannelTelId: number;
  channel_name: string;
  channel_code: string;
  pub_switch: string;
  level: string;
  user_name_a: string;
  user_name_e: string;
  edit_tiem: string;
  state: string;
  use_remark: string;
  color: string;
  parent_id: number;
  channel_id: number;
  module: string;
  type: string;
}

export type fileTemplateData = {
  import_file: Blob;
  site_id: number;
  entity_ids: string;
}

export type copyTemplateData = {
  site_id: number;
  entity_ids: string;
  channel_id: number;
  target_channel_id: number|'';
  transfer_type: string;
  task_version: string;
}

export type templateDetail = {
  site_id: number;
  channel_id: number|string;
  tpl_name: string;
  tpl_content: string;
  tpl_category: string;
  tpl_language?: string;
  tpl_type: string;
  tpl_level: string;
  tpl_is_check?: string;
  tpl_html_path?: string;
  tpl_host?: string;
  remark: string;
  use_remark: string;
  tpl_state?: string;
  tpl_pub_switch: string;
  tpl_is_list?: string;
  draft_version_id?: number;
  parent_id?: number|string;
  tpl_add_time?: string;
  tpl_edit_time?: string;
  tpl_fields?: tplField[];
  tpl_id?: number;
  tpl_is_default?: string;
  tpl_src_id?: number;
  tpl_user_id_a?: number;
  tpl_user_id_e?: number;
  tpl_user_name?: string;
}

export type templateData = {
  site_id: number;
  channel_id: number|string;
  tpl_name: string;
  tpl_content: string;
  tpl_category: string;
  tpl_language?: string;
  tpl_type: string;
  tpl_level: string;
  tpl_is_check?: string;
  tpl_html_path?: string;
  tpl_host?: string;
  remark: string;
  use_remark: string;
  tpl_state?: string;
  tpl_pub_switch: string;
  tpl_is_list?: string;
  custom_fields: tplField[];
  is_in_draft?: string;
  parent_id?: number;
}

export type tplField = {
  field_id?: number|string;
  field_name: string;
  field_type: string;
  field_len?: number;
  input_label: string;
  input_type: string;
  input_value: string;
  input_width?: number;
  input_height?: string|number;
  input_option: string;
  is_search?: string;
  is_list?: string;
  is_index?: string;
  is_unique?: string;
  is_null: string;
  order_edit?: number;
  order_list?: number;
  tpl_id?: number;
  user_id_a?: number;
  user_id_e?: number;
  add_time?: string;
  edit_time?: string;
  user_name?: string;
  state?: string;
  src_id?: string|number;
  is_quto?: number;
}

export type fieldListQuery = {
  S: {
    field_owner: string;
    field_name: string;
    field_type: string;
    input_type: string;
    input_label: string;
  
  };
  page: number;
  page_size: number;
  site_id: number;
}

export type fieldItem = {
  add_time?: string;
  edit_time?: string;
  field_id?: number|string;
  field_name: string;
  field_owner?: string;
  field_owner_name?: string;
  field_type: string;
  field_value?: string|null;
  input_label: string;
  input_value: string;
  input_option?: string|null;
  input_type: string;
  input_type_name?: string;
  is_required?: number;
  state?: string;
  state_name?: string;
  user_id_a?: number;
  user_id_e?: number;
  user_name?: string;
  is_null: string;
  is_index?: string;
  is_list?: string;
  is_search?: string;
  is_quto?: number;
}

export type pageFieldData = {
  site_id: number;
  tpl_id: string|number;
  field_value: string;
  field_id: number;
  tpl_page_id: string;
}

export type historyListItem = {
  id: number;
  site_id: number;
  entity_id: number;
  type: number;
  version: number;
  es_id: string;
  add_time: string;
  edit_time: string;
  user_name: string;
  user_id_a: number;
  user_id_e: number;
  remark: string;
  state: string;
  name: string;
  is_draft: number;
  is_current_version?: boolean;
}

export type comparisonItem = {
  action_name: string;
  add_time: string;
  channel_id: number;
  content: string;
  custom_fields: fieldItem[];
  draft_version_id: number;
  edit_time: string;
  is_check: number;
  is_default: string;
  is_list: string;
  language: string;
  level: string;
  module: string;
  name: string;
  parent_id: number;
  pub_switch: string;
  remark: string;
  site_id: number;
  src_id: number;
  src_version: string;
  state: string;
  tpl_id: number;
  type: string;
  use_remark: string;
  user_name: string;
  version: number;
}

/* 新版模板管理 */
export type groupListItem = {
  tpl_id: number;
  name: string;
  channel_id: number;
  level: string;
  user_id_e: number;
  edit_time: string;
  user_name_e: string;
  channel_name: string;
}

export type copyPageItem = {
  id: number;
  task_id: number;
  orgin_page_id: number;
  to_page_id: number;
  to_page_url: string;
  state: number;
  remark: string;
}

export type tplInfo = {
  tpl_id: number;
  name: string;
  channel_id: number;
  channel_code: string;
}

export type copyTaskInfo = {
  add_time: string;
  edit_time: string;
  orgin_tpl_info: tplInfo;
  to_tpl_info: tplInfo;
}
/* 新版模板管理 */

// 获取模板噶管理列表
export const getTemplateList = (params: listQueryParams) => 
  axiosReq('get', '/api/v1/templates', {
    params,
    paramsSerializer: {
      serialize: (obj: listQueryParams) => qs.stringify({
        S: JSON.stringify(obj.S),
        page: obj.page,
        page_size: obj.page_size,
        site_id: obj.site_id,
        ...obj.sort_type ? {
          sort_key: obj.sort_key,
          sort_type: obj.sort_type
        } : {}
      })
    }
  })

// 修改模板状态
export const changeStatus = (id: number, data: { state: 'able'|'disable', site_id: number; }) => 
  axiosReq('put', `/api/v1/templates/${id}/state`, data)

// 上传文件模板
export const uploadFileTemplate = (data: fileTemplateData) => {
  const formData = new FormData()
  Object.entries(data).forEach(([ key, value ]) => {
    formData.append(key, typeof value === 'number' ? `${value}` : value)
  })
  return axiosReq('post', '/api/v1/tool/tpl_transfer/import_pages', formData)
}

// 复用模板
export const copyTemplate = (data: copyTemplateData) => 
  axiosReq('post', '/api/v1/tool/tpl_transfer', data)

// 获取字模板列表
export const getChildTplList = (tpl_id: string|number) => 
  axiosReq('get', `/api/v1/templates/${tpl_id}/channels`)

// 更新子模板
export const updateChildTemplate = (tpl_id: string|number, data: {
  site_id: number;
  channel_tpl_ids: string;
  is_sync_page: '1'|'2'
}) => 
  axiosReq('post', `/api/v1/tool/sync_tpl/${tpl_id}`, data)

// 获取模板详情
export const getTemplateDetail = (tpl_id: string|number, params: { site_id: number }) => 
  axiosReq('get', `/api/v1/templates/${tpl_id}`)

// 新增模板
export const addTemplate = (data: templateData) => 
  axiosReq('post', '/api/v1/templates/add', data)

// 编辑模板
export const editTemplate = (data: templateData, tpl_id: number) => 
  axiosReq('post', `/api/v1/templates/edit`, { ...data, tpl_id })

// 获取模板字段列表
export const getFieldsList = (params: fieldListQuery) => 
  axiosReq('get', '/api/v1/fields', {
    params,
    paramsSerializer: {
      serialize: (obj: fieldListQuery) => qs.stringify({
        S: JSON.stringify(obj.S),
        page: obj.page,
        page_size: obj.page_size,
        site_id: obj.site_id
      })
    }
  })

// 批量编辑模板字段
export const changePageField = (data: pageFieldData) => 
  axiosReq('put', '/api/v1/templates/multi_change/page_field', data)

// 删除字段
export const deleteField = (tpl_id: number, field_id: number) =>
  axiosReq('post', `/api/v1/templates/${tpl_id}/columns/${field_id}`)

// 模板历史记录列表
export const getHistoryList = (tpl_id: number, params: { site_id: number; page: number; page_size: number }) => 
  axiosReq('get', `/api/v1/templates/${tpl_id}/versions`, {
    params,
    paramsSerializer: {
      serialize: (obj) => qs.stringify(obj)
    }
  })

// 回退至指定版本 
export const restoreTemplateVersion = (tpl_id: number, data: { site_id: number; version_id: number; }) => 
  axiosReq('post', `/api/v1/templates/${tpl_id}/versions/restoration`, data)

// 查看历史版本详情
export const getHistoryDetail = (tpl_id: number|string, version_id: number, site_id: number) => 
  axiosReq('get', `/api/v1/templates/${tpl_id}/versions/${version_id}?site_id=${site_id}`)

// 版本对比
export const getComparisons = (site_id: number, tpl_id: string|number, version_ids: string) => 
  axiosReq('get', `/api/v1/templates/${tpl_id}/versions/comparisons?site_id=${site_id}&version_ids=${version_ids}`)


/* 新版模板管理 */
// 模版集列表
export const getTempGroupList = (tpl_id: string|number) => 
  axiosReq('get', `/api/v1/templates/group_list?tpl_id=${tpl_id}`)

// 模板复制到站点渠道
export const copyTempGroup = (data: { tpl_id: string|number; channel_ids: string }) => 
  axiosReq('post', '/api/v1/templates/copy_template', data)

// 模板页面复制到站点渠道
export const copyTempPage = (data: { tpl_id: string|number; to_tpl_id: string|number; page_ids?: string }) => 
  axiosReq('post', '/api/v1/templates/copy_template_page', data)

// 模板下可用页面
export const tempPageList = (tpl_id: string|number) => 
  axiosReq('get', `/api/v1/templates/pages/list?tpl_id=${tpl_id}`)

// 页面复制任务详情
export const copyPageDetail = (id: string|number) => 
  axiosReq('get', `/api/v1/templates/copy_page_detail?id=${id}`)
/* 新版模板管理 */