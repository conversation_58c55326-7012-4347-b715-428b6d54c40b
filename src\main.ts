import { createApp } from 'vue'
import { createPinia } from 'pinia'
import piniaPluginPersistedstate from 'pinia-plugin-persistedstate'
import ElementPlus from 'element-plus'
import App from './App.vue'
import router from './router'
import Ace from './components/AceEditor'
import VueDiff from 'vue-diff'
import 'vue-diff/dist/index.css'

//import theme
import './theme/index.scss'

//import unocss
import 'uno.css'

//i18n
import { setupI18n } from '@/lang'

import '@/styles/index.scss' // global css

//svg-icon
import 'virtual:svg-icons-register'
import svgIcon from '@/icons/SvgIcon.vue'
import directive from '@/directives'
import aceDirective from './components/AceEditor/directive'

import '@/api/axios/interceptor'
//import router intercept
import './permission'

//import element-plus
import 'element-plus/dist/index.css'
const app = createApp(App)

//router
app.use(router)

//pinia
const pinia = createPinia()
pinia.use(piniaPluginPersistedstate)
app.use(pinia)

//i18n
app.use(setupI18n)
app.component('SvgIcon', svgIcon)
directive(app)
aceDirective(app)

//element-plus
app.use(ElementPlus)

//ace-editor
app.use(Ace)
//vue-diff
app.use(VueDiff, {
  componentName: 'Diff'
})

app.mount('#app')
