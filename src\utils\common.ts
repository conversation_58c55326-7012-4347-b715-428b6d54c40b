// 过滤对象中的空字符
export const filterEmptyKey = (obj: object, emptyRule = '') => {
  const filterObj = {}
  Object.entries(obj).forEach(( [ key,value ] ) => {
    if (value !== emptyRule && value !== undefined) {
      filterObj[key] = value
    }
  })
  return filterObj
}

// 大写首字母
export const capitalFirstLetter = (letters = '') => {
  return letters.replace(/^\S/, (s: string) => s.toUpperCase())
}

// 根据url下载文件
export const downloadUrl = (url: string, name: string) => {
  return new Promise((resolve, reject) => {
    const xhr = new XMLHttpRequest()
    xhr.open('get', url, true)
    xhr.responseType = 'blob'
    xhr.send()
    xhr.onload = function() {
      if (this.status === 200 || this.status === 304) {
        const fileReader = new FileReader()
        fileReader.readAsDataURL(this.response)   //readAsDataURL  获取base64编码
        fileReader.onloadend = function() {
          const a = document.createElement('a')
          a.style.display = 'none'
          a.href = this.result as string
          a.download = name
          document.body.appendChild(a)
          a.click()
          document.body.removeChild(a)
          resolve(null)
        }
      }else {
        reject(this.response)
      }
    }
    xhr.onerror = function() {
      reject(this)
    }
  })
}

// 已Blob形式下载文件
export const downloadStream = (dataStream: Blob, name: string) => {
  const fileReader = new FileReader()
  fileReader.readAsDataURL(dataStream)
  fileReader.onloadend = function() {
    const a = document.createElement('a')
    a.style.display = 'none'
    a.href = this.result as string
    a.download = name
    document.body.appendChild(a)
    a.click()
    document.body.removeChild(a)
  }
}

// 格式化日期时间
export const formatDatetime = (time: Date|string|number, cFormat = '') => {
  const format = cFormat || '{y}-{m}-{d} {h}:{i}:{s}'
  let date
  if (typeof time === 'object') {
      date = time
  } else {
      if ((typeof time === 'string') && (/^[0-9]+$/.test(time))) {
          time = parseInt(time)
      }
      if ((typeof time === 'number') && (time.toString().length === 10)) {
          time = time * 1000
      }
      date = new Date(time)
  }
  const formatObj = {
      y: date.getFullYear(),
      m: date.getMonth() + 1,
      d: date.getDate(),
      h: date.getHours(),
      i: date.getMinutes(),
      s: date.getSeconds(),
      a: date.getDay()
  }
  const time_str = format.replace(/{(y|m|d|h|i|s|a)+}/g, (result, key) => {
      let value = formatObj[key]
      // Note: getDay() returns 0 on Sunday
      if (key === 'a') { return ['日', '一', '二', '三', '四', '五', '六'][value] }
      if (result.length > 0 && value < 10) {
          value = '0' + value
      }
      return value || 0
  })
  return time_str
}

// 处理下载拼接参数
export const stringify = (obj: {[propName: string]: { [propName: string]: string|number } | string | number}) => {
  return Object.entries(obj).filter(( [ key, value ] ) => value !== '').map( ( [ key, value ] ) => {
    if (typeof value === 'object') {
      return Object.entries(value).map(( [ key2, value2 ] ) => {
        return `${key}[${key2}]=${encodeURIComponent(String(value2).replace(/\#[\d|\w]*(?=,)?/ig, ''))}`
      } )
    }
    return value ? `${key}=${encodeURIComponent(String(value).replace(/\#[\d|\w]*(?=,)?/ig, ''))}` : ''
  } ).flat().join('&')
}

// 并发控制
export const handleConcurrecy = (cbs: (()=>Promise<any>)[], limit: number) => {
  const waitList = cbs.slice(limit)
  const excutors = cbs.slice(0, limit)

  const excutor = () => {
    const cb = waitList.shift()
    if (cb) {
      cb().then(excutor).catch(excutor)
    }
  }

  Promise.allSettled([...excutors.map((cb) => cb())]).then(excutor).catch(excutor)
}

// 节流
export const useThrottle = (delay = 100) => {
  let timer: any = null
  const throttle = (cb: () => any) => {
    clearTimeout(timer)
    timer = setTimeout(cb, delay)
  }
  
  return throttle
}

// 去抖
export const useDebounce = (delay = 100) => {
  let canRun = true
  const debounce = (cb: () => any) => {
    if (canRun) {
      canRun = false
      setTimeout(() => {
        cb()
        canRun = true
      }, delay)
    }
  }
  
  return debounce
}

export const toggleBodyClass = (className: string) => {
  document.querySelectorAll('body')[0].className = className
}

// 获取元素距离指定容器的offsetTop
export const getOffsetTop = (element: HTMLElement, container: HTMLElement|HTMLBodyElement|null = document.querySelector('body')) => {
  let offsetTop = 0
  while (element && element !== container) {
    offsetTop += element.offsetTop
    element = element.offsetParent as HTMLElement
  }
  return offsetTop
}