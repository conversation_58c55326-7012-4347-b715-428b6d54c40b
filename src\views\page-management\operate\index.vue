<template>
  <div class="scroll-y" ref="scrollEl">
    <div 
      style="overflow: hidden;" 
      :style="{ minHeight: `calc(100% - ${fixBottomHeight}px)` }"
    >
      <el-form
        ref="formRef"
        label-suffix=":"
        label-width="140px"
        :model="pageData"
        :rules="formRules"
      >
        <el-row :gutter="20">
          <el-col :span="8">
            <el-card shadow="never">
              <template #header>
                <strong style="font-size: 1.25rem;">基础信息</strong>
              </template>
              <template #default>
                <el-form-item label="页面ID" v-if="type === 'edit'">
                  {{ tplPageId }}
                </el-form-item>
                <el-form-item label="模板所属模块" v-if="type === 'add'">
                  <template v-if="type === 'add'">
                    <el-select v-model="moduleData.module" :disabled="!!art_id" @change="handleModuleChange">
                      <el-option 
                        v-for="(d, index) in moduleList"
                        :key="index"
                        :label="d.label"
                        :value="d.value"
                      />
                    </el-select>
                  </template>
                  <template v-else> {{ pageDetailInfo_new?.tpl_module_name }} </template>
                </el-form-item>
                <el-form-item label="模板类型">
                  <template v-if="type === 'add'">
                    <el-select v-model="moduleData.tpl_type" :disabled="!!art_id" @change="handleModuleTypeChange">
                      <el-option 
                        v-for="(d, index) in moduleTypeMap[moduleData.module]"
                        :key="index"
                        :label="d.label"
                        :value="d.value"
                      />
                    </el-select>
                  </template>
                  <strong v-else> {{ pageDetailInfo_new?.tpl_module_name }}-{{ pageDetailInfo_new?.tpl_type_name }} </strong>
                </el-form-item>
                <el-form-item label="页面渠道">
                  <template v-if="pageDetailInfo_new?.channel_code || channel_item_all.channel_code">
                    {{ pageDetailInfo_new?.channel_code || channel_item_all.channel_code }}
                  </template>
                  <template v-else>
                    <el-select v-model="channelId" disabled>
                      <el-option
                        v-for="(d, index) in channelList"
                        :key="index"
                        :label="d.channel_code"
                        :value="d.channel_id"
                      />
                    </el-select>
                  </template>
                </el-form-item>
                <el-form-item label="所选模板" prop="tpl_id">
                  <template v-if="type === 'add'">
                    <el-select v-model="pageData.tpl_id" filterable @change="handleTempChange">
                      <el-option 
                        v-for="(d ,index) in tempList"
                        :key="d.tpl_id"
                        :label="`${d.tpl_id}-${d.name}`"
                        :value="d.tpl_id"
                      />
                    </el-select>
                  </template>
                  <template v-else>
                    {{ pageDetailInfo_new?.tpl_name }}
                  </template>
                </el-form-item>
                <el-form-item label="页面标题" prop="title">
                  <el-input v-model="pageData.title" type="textarea" :rows="4" :maxlength="maxMap.title" show-word-limit />
                </el-form-item>
                <el-form-item label="关键词" prop="keyword">
                  <el-input v-model="pageData.keyword" type="textarea" :rows="4" :maxlength="maxMap.keyword" show-word-limit />
                </el-form-item>
                <!-- <el-form-item label="产品CBSID" prop="cbs_ids">
                  <el-input v-model="pageData.cbs_ids" />
                </el-form-item> -->
                <el-form-item label="页面description" prop="description">
                  <el-input v-model="pageData.description" type="textarea" :rows="4" :maxlength="maxMap.description" show-word-limit />
                </el-form-item>
                <el-form-item label="页面URL" prop="url">
                  {{ pageDetailInfo_new?.domain_host || channel_item_all.host }}
                  <el-input v-model="pageData.url" />
                </el-form-item>
                <!-- <el-form-item label="页面状态" prop="state">
                  <span :stlye="{ color: pageData.state === 'able' ? 'var(--el-color-success)' : 'var(--el-color-error)'}">
                    {{ pageData.state === 'able' ? '启用' : '禁用' }}
                  </span>
                </el-form-item> -->
                <el-form-item label="备份说明" prop="remark">
                  <el-input v-model="pageData.remark" type="textarea" :rows="4" />
                </el-form-item>
                <PageExtend :extend="pageData.page_extend" :disabled="disabledBuy" />
                <el-form-item v-if="pageDetailInfo_new?.tpl_type === 'buy' || moduleData.tpl_type === 'buy'" label="购买页定制">
                  <el-button type="primary" @click="loadBuyPageModal = true">购买页信息配置</el-button>
                </el-form-item>

                <template v-if="pageData.buy_info">
                  <div v-for="(d, index) in pageData.buy_info.card_detail" :key="index" style="margin-bottom: 10px;">
                    <el-table :data="d.default">
                      <el-table-column label="Sku id" prop="sku_id" width="100" />
                      <!-- <el-table-column label="购买链接" prop="url" show-overflow-tooltip /> -->
                      <el-table-column label="购买链接" prop="url">
                        <template #default="scope">
                          <el-popover :content="scope.row.url" placement="top-start" width="650" trigger="hover">
                            <template #reference>
                              <div style="width: 100%; white-space: nowrap;overflow: hidden;text-overflow: ellipsis;">
                                {{ scope.row.url }}
                              </div>
                            </template>
                          </el-popover>
                        </template>
                      </el-table-column>
                    </el-table>
                  </div>
                </template>
              </template>
            </el-card>
          </el-col>
          <el-col :span="16">
            <el-card shadow="never">
              <template #header>
                <div class="rowBC">
                  <strong style="font-size: 1.25rem;">页面自定义字段</strong>
                  <div>
                    <el-button type="primary" plain @click="showFieldsModal = true">查看字段</el-button>
                    <el-button type="primary" plain @click="loadFieldResolveModal = true">html填充模式</el-button>
                  </div>
                </div>
              </template>
              <template v-for="(d, index) in pageData.tpl_fields" :key="index">
                <el-form-item 
                  :label="d.input_label" 
                  :prop="`tpl_fields.${index}.field_value`" 
                  :rules="{ required: d.is_null === 'N', message: `${d.input_label}不能为空`, trigger: 'blur' }"
                >
                  <template v-if="d.input_type === 'input'">
                    <el-input v-model="d.field_value" :placeholder="d.input_label" :disabled="!fieldReg.test(d.field_name) && disabledBuy" />
                  </template>
                  <template v-else-if="d.input_type === 'textarea'">
                    <el-input 
                      v-if="!fieldReg.test(d.field_name) && disabledBuy"
                      v-model="d.field_value" 
                      type="textarea" 
                      :placeholder="d.input_label" 
                      :rows="8" 
                      :disabled="true" 
                    />
                    <el-input 
                      v-else
                      v-model="d.field_value" 
                      type="textarea" 
                      :placeholder="d.input_label" 
                      :rows="8" 
                      v-ace="{ getVal: () => d.field_value, save: (val: string) => d.field_value = val, showTips: true }"
                    />
                  </template>
                  <template v-else-if="d.input_type === 'select' && d.input_option">
                    <el-select v-model="d.field_value" placeholder="请选择" clearable :disabled="!fieldReg.test(d.field_name) && disabledBuy" >
                      <el-option 
                        v-for="item in JSON.parse(d.input_option)"
                        :key="item.v"
                        :label="item.v"
                        :value="item.v"
                      />
                    </el-select>
                  </template>
                  <template v-else>
                    <el-input v-model="d.field_value" :placeholder="d.input_label" :disabled="!fieldReg.test(d.field_name) && disabledBuy"  />
                  </template>
                </el-form-item>
              </template>
            </el-card>
          </el-col>
        </el-row>
      </el-form>
    </div>

    <div class="fixed-bottom text-center" v-if="type !== 'view'">
      <el-button plain :icon="Back" @click="handleBack">返回列表</el-button>
      <el-button 
        v-if="type !== 'add'"
        type="primary" 
        :icon="View" 
        :loading="loading" 
        @click="handlePreview"
      >
        预览
      </el-button>
      <el-button
        type="primary"
        :icon="Finished"
        :loading="loading"
        @click="handleSave(formRef)"
      >
        保存
      </el-button>
      <el-button
        v-if="artBinded"
        type="success"
        :loading="loading"
        @click="handleEditArt"
      >
        编辑已绑定文章
      </el-button>
      <span 
        v-if="(!artBinded && isArticlePage && type !== 'add') || (type === 'add' && moduleData.tpl_type === 'content')" 
        style="color: var(--el-color-error); font-size: 12px;"
      >
        提示: 当前页面尚未绑定文章, 如需绑定文章, 点击保存按钮唤醒弹窗
      </span>
    </div>

    <BindArticle
      v-if="loadBindArticleModal"
      ref="bindArtRef"
      :is-add="isAdd"
      :page_id="tplPageId"
      :channel_id="pageData.channel_id || pageDetailInfo_new?.channel_id"
      @close="handleClose"
    />

    <FieldResolve 
      v-if="loadFieldResolveModal"
      :tpl_fields="pageData.tpl_fields"
      @close="loadFieldResolveModal = false"
    />

    <BuyPageConfig 
      v-if="loadBuyPageModal"
      :buy-info="pageData?.buy_info"
      @closed="loadBuyPageModal = false"
      @save="handleBuyInfoSave"
    />

    <el-dialog 
      v-model="showFieldsModal"
      title="页面自定义字段一览"
      width="900"
      append-to-body
      align-center
    >
      <el-table :data="pageData.tpl_fields">
        <el-table-column label="字段名称" prop="input_label" />
        <el-table-column label="字段key名称" prop="field_name" />
        <el-table-column label="字段插入格式">
          <template #default="{ row }">
            {-{{row.field_name}}-}
          </template>
        </el-table-column>
        <el-table-column label="操作" width="280">
          <template #default="{ row }">
            <el-button 
              type="primary" 
              size="default" 
              link
              @click="() => copyValueToClipboard(row.field_name)"
            >  
              复制字段key
            </el-button>
            <el-button 
              type="primary" 
              size="default" 
              link
              @click="() => copyValueToClipboard(`{-${row.field_name}-}`)"
            >  
              复制字段插入格式
            </el-button>
          </template>
        </el-table-column>
      </el-table>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, defineAsyncComponent } from 'vue'
import { ElMessage } from 'element-plus'
import { Back, View, Finished } from '@element-plus/icons-vue'
import { useRoute, useRouter } from 'vue-router'
import { useParamsStore, useTagsViewStore, useAsyncMethodStroe, useUserStroe } from '@/store'
import { useConfigStore } from '@/store/config'
import { useBasicStore } from '@/store/basic'
import useTryCatch from '@/hooks/use-try-catch'
import useLoading from '@/hooks/use-loading'
import { formatDatetime, getOffsetTop } from '@/utils/common'
import { 
  addPage_new, 
  editPage_new, 
  getListByType, 
  getFieldInfo, 
  getPageDetail_new,
  pageBindArt,
  getHistoryDetail
} from '@/api/page-management'
import type { listByType, pageInfoData, addPageData_new, pageDetailCommon } from '@/api/page-management'
import type { buyInfo } from '@/api/buy-management'
import type { FormInstance } from 'element-plus'
import { formRules, maxMap } from './rules'
import { copyValueToClipboard } from '@/hooks/use-common'

const BindArticle = defineAsyncComponent( () => import('./bindArticleModal.vue') ) 
const FieldResolve = defineAsyncComponent( () => import('./fieldResolve.vue') )
const BuyPageConfig = defineAsyncComponent( () => import('./buyPageConfig.vue') )
import PageExtend from './customField.vue'

defineOptions( { name: 'PageManagementOperate' } )

// 购买页放开编辑的字段key
const fieldReg = /content|_content/

const route = useRoute()
const router = useRouter()
const { site_id_all, channel_item_all, moduleList, moduleTypeMap, channelList } = useParamsStore()
const { delVisitedView, changeViewTitle, updateHistoryTitle } = useTagsViewStore()
const { handleGlobalPageCheck } = useAsyncMethodStroe()
const { userInfo } = useUserStroe()
const { fixBottomHeight } = useConfigStore()
const basicStore = useBasicStore()
const { loading, setLoading } = useLoading()
const { page_id, tpl_id, type, page_url, art_id, tpl_type, module: module_type, channel_id, version_id } = route.query as unknown as {
  page_id: number;
  tpl_id: number;
  type: string;
  page_url: string;
  art_id?: number;
  tpl_type?: string;
  module?: string;
  channel_id: number;
  version_id: number;
}

const addPageData = {
  channel_id: +channel_id || channel_item_all.channel_id as number,
  site_id: site_id_all,
  tpl_id: '',
  title: '',
  keyword: '',
  description: '',
  url: '',
  remark: formatDatetime(new Date()),
  tpl_fields: [],
  page_extend: [],
  buy_info: null
}

const editPageData = {
  tpl_page_id: page_id,
  title: '',
  keyword: '',
  description: '',
  url: '',
  remark: formatDatetime(new Date()),
  tpl_fields: [],
  tpl_id,
  page_extend: [],
  buy_info: null
}

const scrollEl = ref<HTMLElement>()
const formRef = ref<FormInstance>()
const pageData = reactive<addPageData_new>({
  ...addPageData,
})
const moduleData = reactive({
  module: '',
  tpl_type: '',
})

const pageDetailInfo_new = ref<pageDetailCommon|null>(null)
const channelId = ref(+channel_id || 0)
const isArticlePage = ref(false)

const tempList = ref<listByType>([])
const bindArtRef = ref()
const isAdd = ref(true)
const tplPageId = ref(0)
const artBinded = ref(false)
const artQuery = reactive({
  type: 'edit',
  art_id: 0,
  channel_id: 0,
  page_preview_url: page_url,
})
const loadBindArticleModal = ref(false)
const loadFieldResolveModal = ref(false)
const showFieldsModal = ref(false)
const loadBuyPageModal = ref(false)

const disabledBuy = computed( () => pageDetailInfo_new.value?.tpl_type === 'buy' && !new Set(userInfo.user_parent_id?.split(',')).has('2') )

const getDetail = () => {
  useTryCatch( async () => {
    setLoading(true)
    // const res = await getPageDetail(page_id, site_id_all)
    const res = await getPageDetail_new(page_id)
    if (res.code === 200) {
      const { is_article_page, page_info, page_field } = res.data as pageInfoData
      isArticlePage.value = is_article_page
      pageDetailInfo_new.value = page_info
      tplPageId.value = page_info.tpl_page_id
      Object.keys(editPageData).forEach(( key ) => {
        if (page_info[key]) {
          pageData[key] = page_info[key]
        }
      })
      pageData.remark = formatDatetime(new Date())
      if (type === 'copy') {
        pageData.channel_id = page_info.channel_id
        pageData.title += '-copy'
        pageData.url = pageData.url.replace('.html', '-copy.html')
      } 
      if (type === 'edit' && page_info.art_id) {
        artBinded.value = true
        artQuery.art_id = page_info.art_id
        artQuery.channel_id = page_info.channel_id
      }
      pageData.tpl_fields = page_field
      updateHistoryTitle(route, pageData.title)
    }

    setLoading(false)
  }, () => setLoading(false) )
}
const handleBack = () => {
  delVisitedView(route)
  const name = route.query.from === 'tempList' ? 'TemplatPageManagement' : 'PageManagement'
  router.push({ name })
}
const handleBackToList = () => {
  basicStore.setRefresh(true)
  delVisitedView(route)
  router.push({ name: 'PageManagement' })
}
const handlePreview = () => {
  const url = `${page_url}?site_id=${site_id_all}`
  window.open(url, '_blank')

  if (pageDetailInfo_new.value) {
    const { tpl_id, tpl_page_id } = pageDetailInfo_new.value
    handleGlobalPageCheck(tpl_id, tpl_page_id, site_id_all)
  }
}
const handleSave = async ( formEl: FormInstance | undefined ) => {
  if (!formEl) {
    return
  }
  await formEl.validate(( valid ) => {
    if (valid) {
      (type === 'add' || type === 'copy' )? handleAdd() : handleEdit()
    } else {
      const el = document.querySelector('.el-form-item.is-error') as HTMLElement
      if (el && scrollEl.value) {
        const offsetTop = getOffsetTop(el, scrollEl.value)
        const elRect = el.getBoundingClientRect()
        const scrollElRect = scrollEl.value.getBoundingClientRect()
        scrollEl.value.scrollTop = offsetTop - scrollElRect.height + elRect.height
      }
    }
  })
}

const handleAdd = () => {
  useTryCatch( async () => {
    setLoading(true)
    Object.keys(addPageData).forEach((key) => {
      if (pageData[key]) {
        addPageData[key] = pageData[key]
      }
    })
    const params = {
      ...addPageData,
      keyword: addPageData.keyword.replace(/\n/g, ''),
      description: addPageData.description.replace(/\n/g, '')
    }
    const res = await addPage_new(params)
    if (res.code === 200) {
      ElMessage.success(res.msg)
      if ( res.data.is_article_page ) {
        if (art_id) {
          const _res = await pageBindArt( { tpl_page_id: res.data.tpl_page_id, art_id: +art_id } )
          if (_res.code === 200) {
            ElMessage.success('绑定成功')
          } else {
            ElMessage.success('绑定失败,请进入编辑后保存再次绑定')
          }
          handleBackToList()
          return
        } 
        loadBindArticleModal.value = true
        bindArtRef.value?.show()
        tplPageId.value = res.data.tpl_page_id
      } else {
        handleBackToList()
      }
    } else {
      ElMessage.error(res.msg)
    }
    setLoading(false)
  }, () => setLoading(false) )
}
const handleEdit = () => {
  useTryCatch( async () => {
    setLoading(true)
    Object.keys(editPageData).forEach((key) => {
      if (pageData[key]) {
        editPageData[key] = pageData[key]
      }
    })
    const params = {
      ...editPageData,
      keyword: editPageData.keyword.replace(/\n/g, ''),
      description: editPageData.description.replace(/\n/g, '')
    }
    const res = await editPage_new(params)
    if (res.code === 200) {
      if (isArticlePage.value && !pageDetailInfo_new.value?.art_id) {
        loadBindArticleModal.value = true
        bindArtRef.value?.show()
      }
      ElMessage.success(res.msg)
      basicStore.setRefresh(true)
    } else {
      ElMessage.error(res.msg)
    }
    setLoading(false)
  }, () => setLoading(false) )
}

const handleModuleChange = () => {
  moduleData.tpl_type = ''
  pageData.tpl_id = ''
  tempList.value = []
}
const handleModuleTypeChange = () => {
  pageData.tpl_id = ''
  tempList.value = []
  useTryCatch( async () => {
    const params = {
      channel_id: channelId.value || channel_item_all.channel_id as number,
      type: moduleData.tpl_type,
      module: moduleData.module
    }
    const res = await getListByType(params)
    if (res.code === 200) {
      tempList.value = res.data
    } else {
      ElMessage.error(res.msg)
    }
  } )
}
const handleTempChange = () => {
  useTryCatch( async () => {
    const res = await getFieldInfo(pageData.tpl_id as number)
    if (res.code === 200) {
      pageData.tpl_fields = res.data
      pageData.tpl_fields.forEach((field) => {
        if (field.input_value && !field.field_value) {
          field.field_value = field.input_value
        }
      })
    } else {
      ElMessage.error(res.msg)
    }
  } )
}
const handleClose = (addArticle: boolean, backToList: boolean) => {
  if (isAdd.value && !addArticle) {
    handleBackToList()
    return
  }
  if (addArticle) {
    delVisitedView(route)
  }
  if (backToList) {
    handleBackToList()
  }
}
const handleEditArt = () => {
  basicStore.delCachedView('ArticleManageOperate')
  router.push({
    name: 'ArticleManageOperate', query: { ...artQuery }
  })
}

const handleBuyInfoSave = (buyInfo: buyInfo) => {
  pageData.buy_info = buyInfo
}

if (page_id) {
  isAdd.value = false
  type !=='view' &&  getDetail()
}
if (type === 'add') {
  updateHistoryTitle(route, '添加页面')
  changeViewTitle(route,  art_id ? '创建页面并绑定文章' : '创建页面')
  if (tpl_type && module_type && tpl_id) {
    moduleData.module = module_type
    moduleData.tpl_type = tpl_type
    handleModuleTypeChange()
    pageData.tpl_id = +tpl_id
    handleTempChange()
  }
}
if ( type === 'copy' ) {
  changeViewTitle(route, '复用页面')
}
if ( type === 'edit' ) {
  changeViewTitle(route, '页面编辑')
}
if ( type === 'view') {
  changeViewTitle(route, '页面历史详情')
  useTryCatch( async () => {
    const res = await getHistoryDetail(page_id, version_id, site_id_all)
    if (res.code === 200) {
      const data = res.data
      Object.keys(data).forEach((key) => {
        if (pageData[key] !== undefined) {
          pageData[key] = key === 'page_extend' ? JSON.parse(data[key]) : data[key]
        }
      })
      pageData.tpl_fields = data.field_value
      updateHistoryTitle(route, pageData.title)
    }
  } )
}
if (art_id) {
  moduleData.module = 'article'
  moduleData.tpl_type = 'content'
  handleModuleTypeChange()
}
</script>