import { reactive } from 'vue'
import type { FormRules } from 'element-plus'
export const formRules = reactive<FormRules>({
  tpl_name: [
    {
      required: true,
      message: '模板名称不能为空',
      trigger: 'blur'
    }
  ],
  tpl_category: [
    {
      required: true,
      message: '模板所属模块不能为空',
      trigger: 'change'
    }
  ],
  tpl_type: [
    {
      required: true,
      message: '模板类型不能为空',
      trigger: 'change'
    }
  ],
  tpl_level: [
    {
      required: true,
      message: '模板等级不能为空',
      trigger: 'change'
    }
  ],
  site_id: [
    {
      required: true,
      message: '所属站点不能为空',
      trigger: 'change'
    }
  ],
  tpl_host: [
    {
      required: true,
      message: '对应域名不能为空',
      trigger: 'change'
    }
  ],
  tpl_state: [
    {
      required: true,
      message: '静态文件路径状态不能为空',
      trigger: 'change'
    }
  ],
  tpl_pub_switch: [
    {
      required: true,
      message: '模板发布开关不能为空',
      trigger: 'change'
    }
  ],
  tpl_content: [
    {
      required: true,
      message: '模板内容不能为空',
      trigger: 'blur'
    }
  ],
  remark: [
    {
      required: true,
      message: '版本备份不能为空',
      trigger: 'change'
    }
  ],
  channel_id: [
    {
      required: true,
      message: '所属渠道不能为空',
      trigger: 'change'
    }
  ],
}
)
export const fieldRules = reactive<FormRules>({
  field_name: [
    {
      required: true,
      validator: (rule, value, callback) => {
        if (!value) {
          return callback(new Error('表字段名称不能为空'))
        }
        if (!/^[a-z\_\d]+$/.test(value)) {
          return callback(new Error('只能输入小写字母、数字及下划线'))
        }
        callback()
      },
      trigger: 'blur'
    }
  ],
  field_type: [
    {
      required: true,
      message: '字段类型不能为空',
      trigger: 'change'
    }
  ],
  input_label: [
    {
      required: true,
      message: '字段名称不能为空',
      trigger: 'blur'
    }
  ],
  input_type: [
    {
      required: true,
      message: '输入框类型不能为空',
      trigger: 'change'
    }
  ],
  input_option: [
    {
      required: true,
      message: '选项不能为空',
      trigger: 'blur'
    }
  ],
})