//获取用户信息
import axiosReq from './axios'

// 根据code获取token
export const getSysTokenByCode = (code: string|number) => 
  axiosReq('post', '/api/oauth/token', { code })

// 获取用户信息
export const getWcwUserInfo = () =>
  axiosReq('get', '/api/oauth/user')

// 获取用户常用站点
export const getMarkSiteList = (siteId) =>
  axiosReq('get', `/api/v1/users/user_sites?wsId=${siteId}`)

// 获取子系统菜单数据
export const getSysOauthMenu = () => 
  axiosReq('get', `/api/oauth/menu`)

// 退出登录
export const logOut = () => 
  axiosReq('get', '/api/oauth/user/logout')

// 用户常用站点配置
export const setUserSites = (data: { site_ids: string; wsId: string; }) => 
  axiosReq('post', '/api/v1/users/user_sites', data)

// cms获取用户信息
export const getUserInfo = () => 
  axiosReq('get', '/api/v1/public/user_info')

// 用户渠道操作限制
export const channelReadUpdate = (channel_ids: string) => 
  axiosReq('post', '/api/v1/users/channel_read_update', { channel_ids })
