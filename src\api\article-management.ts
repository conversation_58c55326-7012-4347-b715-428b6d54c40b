import axiosReq from './axios'
import qs from 'query-string'

export type queryParams = {
  site_id: number;
  channel_id: number|string;
  user_id_a: number|string;
  user_id_e: number|string;
  author_id: number|string;
  cat_id: string|number;
  page_state: string;
  art_ids: string;
  title: string;
  page_url: string;
  art_group_id: string;
  content: string;
  is_recom: number|string;
  is_hot: number|string;
  page_id: string;
  tpl_id: string;
  page: number;
  page_size: number;
  all_child?: string;
  sort_key?: string;
  sort_type?: string;
}

export type listItem = {
  art_id: number;
  title: string;
  subtitle: string;
  summary: string;
  content_word_num: number;
  image_url: string;
  sort: number;
  author_id: number;
  site_id: number;
  cat_id: number;
  cat_id_v: string;
  tag_ids: string;
  rel_pro_ids: string;
  rel_article_ids: string;
  is_recom: string;
  is_hot: string;
  source_proportion: number;
  tpl_id: number;
  page_id: number;
  page_keyword: string;
  page_desc: string;
  page_state: string;
  page_url: string;
  page_title: string;
  user_id_a: number;
  user_id_e: number;
  add_time: string;
  edit_time: string;
  user_name: string;
  state: string;
  side_block: string|number;
  language: string;
  remark: string;
  art_group_id: number;
  channel_id: number;
  src_version: string;
  state_name: string;
  page_state_name: string;
  site_name: string;
  language_name: string;
  online_url: string;
  check_url: string;
  channel_code: string;
  page_preview_url: string;
  user_name_a: string;
  user_name_e: string;
  page_group_id: number;
  channel_name: string;
  color: string;
  cat_id_name: string;
  cat_id_v_name: string;
}

export type classListItem = {
  add_time: string;
  allchild_art_num: number;
  article_num: number;
  article_num_hot: number;
  article_num_rec: number;
  cat_id: number;
  channel_code: string;
  channel_id: number;
  children: classListItem[];
  description: string;
  edit_time: string;
  icon_url: string;
  image_url: string;
  is_display: string;
  is_hot: string;
  is_recom: string;
  layer: number;
  name: string;
  online_url: string;
  page_id: number;
  page_keyword: string;
  page_preview_url: string;
  page_state: string;
  page_state_name: string;
  page_url: string;
  parent_id: number;
  productOS: string;
  rel_cat_ids: string;
  rel_pro_ids: string;
  remark: string;
  site_id: number;
  sort: number;
  src_id: number;
  src_version: string;
  state: string;
  summary: string;
  thumb_url: string;
  tpl_id: number;
  user_e: string;
  user_name: string;
}

export type classifyTreeItem = {
  id: number;
  label: string;
  parent_id: number;
  children?: classifyTreeItem[]
}

export interface articleData {
  art_id?: number;
  art_group_id?: string;
  site_id?: number;
  title: string;
  subtitle: string;
  summary: string;
  cat_id: string;
  channel_id: number;
  author_id: string;
  side_block: string|number;
  image_url: string;
  is_hot: string;
  is_recom: string;
  remark: string;
  cat_id_v: string;
  tag_ids: string;
  rel_article_ids: string;
  rel_pro_ids: string;
  content: string;
  page_group_id?: number
  video_url: string;
  icon_url: string;
  sort: number;
  content_word_num?: number;
  extend: { value: string; desc: string; }[];
  page_id?: number;
}

export type articleDetail = {
  action_user: string;
  add_time: string;
  art_group_id: number;
  art_id: number;
  author_id: number;
  bottom_pro_id: number;
  cat_id: number;
  cat_id_v: string;
  channel_id: number;
  click_count: number;
  content: string;
  content_word_num: number;
  edit_time: string;
  head_block: string;
  icon_url: string;
  image_url: string;
  is_hot: string;
  is_recom: string;
  language: string;
  page_desc: string;
  page_group_id: number;
  page_id: number;
  page_keyword: string;
  page_state: string;
  page_title: string;
  page_url: string;
  rel_article_ids: string;
  rel_pro_ids: string;
  remark: string;
  side_block: string;
  site_id: number;
  sort: number;
  source_proportion: number;
  src_id: number;
  src_version: string;
  state: string;
  subtitle: string;
  summary: string;
  tag_ids: string;
  title: string;
  topios_pro_id: number;
  topmac_pro_id: number;
  topwin_pro_id: number;
  tpl_id: number;
  user_name: string;
  video_url: string;
}

export type authorQuery = {
  S: {
    language: string;
    author_id: string;
    site_id: number;
  },
  page: number;
  page_size: number;
}

export type authorListItem = {
  add_time: string;
  article_count: number;
  author_id: number;
  avatar: string;
  channel_urls_json: string;
  edit_time: string;
  email: string;
  facebook_account: string;
  google_plus: string;
  language: string;
  language_name: string;
  level: string;
  linkedin_account: string;
  name: string;
  password: string;
  see_user_ids: string;
  site_id: string;
  site_name: string;
  sort: number;
  src_version: string;
  state: string;
  state_name: string;
  summary: string;
  twitter_account: string;
  user_id_a: number;
  user_id_e: number;
  user_name: string;
}

export type authorData = {
  site_id: number;
  name: string;
  level: string;
  language: string;
  email: string;
  password: string;
  avatar: string;
  facebook_account: string;
  twitter_account: string;
  google_plus: string;
  linkedin_account: string;
  sort: number;
  summary: string;
  channel_urls_json?: string;
}

export type categoryListItem = {
  add_tiem: string;
  allchild_art_num: number;
  article_num: number;
  article_num_hot: number;
  article_num_rec: number;
  cat_id: number;
  channel_code: string;
  channel_id: number;
  description: string;
  edit_time: string;
  icon_url: string;
  image_url: string;
  is_display: string;
  is_hot: string;
  is_recom: string;
  layer: number;
  name: string;
  online_url: string;
  page_id: number;
  page_keyword: string;
  page_preview_url: string;
  page_state: string;
  page_state_name: string;
  page_url: string;
  parent_id: number;
  productOS: string;
  rel_cat_ids: string;
  rel_pro_ids: string;
  remark: string;
  site_id: number;
  sort: number;
  src_id: number;
  src_version: string;
  state: string;
  summary: string;
  thumb_url: string;
  tpl_id: number;
  user_a: string;
  user_e: string;
  user_id_a: number;
  user_id_e: number;
  user_name: string;
  children?: categoryListItem[];
  hasChildren?: boolean;
}

export type classifyData = {
  site_id: number;
  channel_id: number;
  name: string;
  parent_id: number|null;
  sort: string|number;
  image_url: string;
  thumb_url: string;
  summary: string;
  remark: string;
  productOS: string;
  page_url: string;
  is_display: string;
  is_hot: string;
  is_recom: string;
}

export type odArticleQuery = {
  site_id: number;
  channel_id: number|string;
  page: number;
  page_size: number;
}

export type odArticleItem = {
  id: number;
  channel_code: string;
  title: string;
  user_name_a: string;
  user_name_c: string;
  status: number;
  content: string;
  status_desc: string;
}

/* 新版文章管理 */
export type artTempPageQuery = {
  channel_id: number;
  page?: number;
  page_size?: number;
  tpl_page_id?: number|string;
  tpl_id?: number|string;
  user_id?: number|string;
  title?: string;
  url?: string;
}

export type artTempPageItem = {
  tpl_page_id: number;
  title: string;
  url: string;
  tpl_id: number;
  user_id_a: number;
}

/* 新版文章管理 */

export type historyListItem = {
  id: number;
  site_id: number;
  entity_id: number;
  type: number;
  version: number;
  es_id: string;
  add_time: string;
  edit_time: string;
  user_name: string;
  user_id_a: number;
  user_id_e: number;
  remark: string;
  state: string;
  name: string;
  is_current_version?: boolean;
}

export type comparisonItem = {
  action_user: string;
  author_id: number;
  bottom_pro_id: number;
  cat_id: number;
  content: string;
  is_hot: string;
  is_recom: string;
  language: string;
  remark: string;
  src_id: number;
  src_version: string;
  subtitle: string;
  summary: string;
  title: string;
  version: number;
}

export type keywordArrItem = {
  id: number;
  name: string;
  topic_id: number;
  channel_id: number;
  remark: string;
  add_time: string;
  edit_time: string;
  user_name_a: string;
  user_name_e: string;
}

export type queryParams_keyword = {
  name: string;
  user_id_a: number|string;
  user_id_e: number|string;
  start_time: string;
  end_time: string;
  time_field: string;
  channel_id: number|string;
  page?: number;
  page_size?: number;
  has_art?: number|string; // 空所有  0 没有  1有
  star_art?: string; // 打标文章ID
  parent_id?: string|number;
}

export type keywordItem = {
  id: number;
  name: string;
  channel_id: number;
  art_num: number;
  top_relevance: number;
  state: number;
  last_update: string;
  remark: string;
  add_time: string;
  edit_time: string;
  user_name_a: string;
  user_name_e: string;
  keyword_arr: keywordArrItem[];
  isChildren?: boolean;
  children?: keywordArrItem[];
  star_art: number;
  parent_id: number;
  parent_name: string;
  topic_count: number;
}

export type keywordDetailItem = {
  art_id: number;
  art_title: string;
  page_url: string;
  relevance: number;
  keyword_list: { [propName: string]: string[] };
  star_flag: boolean;
  sort_num: number;
}

// 获取文章数据列表
export const getArticleList = (params: queryParams) => 
  axiosReq('get', '/api/v1/articles', {
    params,
    paramsSerializer: {
      serialize: (obj: queryParams) => qs.stringify(obj)
    }
  })

// 获取文章分类树形数据（仅于筛选）
export const getClassifyData = (site_id: number, channel_id?: number|string) => 
  axiosReq('get', `/api/v1/articles/categorys/tree?site_id=${site_id}&channel_id=${channel_id || ''}`)

// 获取作者列表
export const getAuthorList = (params: authorQuery) => 
  axiosReq('get', '/api/v1/articles/authors', {
    params,
    paramsSerializer: {
      serialize: (obj: authorQuery) => qs.stringify({
        S: JSON.stringify(obj.S),
        page: obj.page,
        page_size: obj.page_size
      })
    }
  })

// 获取作者详情
export const getAuthorDetail = (author_id: string|number) => axiosReq('get', `/api/v1/public/entities/article_author/${author_id}`)
// 添加作者
export const addAuthor = (data: authorData) => axiosReq('post', '/api/v1/articles/authors', {data})
// 编辑作者
export const editAuthor = (author_id: string|number, data: authorData) => axiosReq('put', `/api/v1/articles/authors/${author_id}`, {data})
// 删除作者
export const deleteAuthor = (author_id: string|number, data = { state: 'deleted' }) => axiosReq('put', `/api/v1/articles/authors/${author_id}/state`, {data})

// 修改文章排序
export const changeSort = (data: { art_id: number; sort: number }) => 
  axiosReq('post', '/api/v1/articles/sort', data)

// 修改推荐/热门
export const handleRecomHot = (data: { art_id: string; type: 'is_recom'|'is_hot'; status: string; }) => 
  axiosReq('post', '/api/v1/articles/batch_update_status', data)

// 批量修改相关文章
export const changeRelArt = (data: { art_ids: string; rel_article_ids: string; }) => 
  axiosReq('post', '/api/v1/articles/batch_update_rel_art', data)

// 获取文章详情
export const getAticleDetail = (art_id: string|number) => 
  axiosReq('get', `/api/v1/articles/${art_id}`)

// 添加文章
export const addArticle = (data: articleData) => 
  axiosReq('post', `/api/v1/articles`, data)

// 编辑文章
export const editArticle = (data: articleData, art_id: string|number) => 
  axiosReq('post', `/api/v1/articles/${art_id}`, data)

// 获取文章分类列表
export const getCategoryList = (params: { site_id: number; channel_id: number|string; cat_id: string; name?: string; }) => 
  axiosReq('get', `/api/v1/articles/categorys?site_id=${params.site_id}&channel_id=${params.channel_id}&cat_id=${params.cat_id}&name=${params.name || ''}`)

// 删除文章
export const changeClassifyState = (data: { cat_id: string; state: string }) => 
  axiosReq('post', '/api/v1/articles/categorys/state', data)

// 添加分类
export const addClassify = (data: classifyData) => 
  axiosReq('post', '/api/v1/articles/categorys', data)

// 编辑分类
export const editClassify = (data: classifyData, cat_id: number) => 
  axiosReq('post', `/api/v1/articles/categorys/${cat_id}`, data)

// 获取分类详情
export const getClassifyDetail = (cat_id: number) => 
  axiosReq('get', `/api/v1/articles/categorys/${cat_id}`)

// 获取分类历史
export const getClassifyHistory = (cat_id: number, params: { site_id: number; page: number; page_size: number; }) => 
  axiosReq('get', `/api/v1/articles/categorys/${cat_id}/versions`, {
    params,
    paramsSerializer: {
      serialize: (obj: any) => qs.stringify(obj)
    }
  })

// 分类历史详情
export const getClassifyHistoryDetail = ( cat_id: number, version_id: number, site_id: number ) => 
  axiosReq('get', `/api/v1/articles/categorys/${cat_id}/versions/${version_id}?site_id=${site_id}`)

// 回退分类版本
export const restoreClassifyVersion = (cat_id: number, version_id: number, data: any) => 
  axiosReq('post', `/api/v1/articles/categorys/${cat_id}/versions/${version_id}/restoration`, data)

// 历史版本对比
export const historyClassifyCompare = (site_id: number, cat_id: number, version_ids: string) => 
  axiosReq('get', `/api/v1/articles/categorys/${cat_id}/versions/comparisons?version_ids=${version_ids}&site_id=${site_id}`)

// 获取外包文章列表
export const getOdArticleList = (params: odArticleQuery) => 
  axiosReq('get', `/api/v1/writer/article/check/list`, {
    params,
    paramsSerializer: {
      serialize: (obj: queryParams) => qs.stringify(obj)
    }
  })

// 审核外包文章
export const auditOdArticle = (data: { art_id: number; remark: string; status: number }) => 
  axiosReq('put', `/api/v1/writer/article/check`, data)

// 修改文章状态
export const changeState = (data: { art_id: number|string; state: 'able'|'disable' }) => 
  axiosReq('post', '/api/v1/articles/state', data)


/* 新版文章管理 */
// 渠道下未绑定文章的页面列表
export const getArtTempPage = (params: artTempPageQuery) => 
  axiosReq('get', '/api/v1/page/art_template_page', {
    params,
    paramsSerializer: {
      serialize: (obj: queryParams) => qs.stringify(obj)
    }
  })

// 模板下可用页面简化数据
export const getTempPageList = (tpl_id: number) => 
  axiosReq('get', `/api/v1/templates/pages/list?tpl_id=${tpl_id}`)

// 添加文章
export const addArticle_new = (data: articleData) => 
  axiosReq('post', '/api/v1/page/add_art', data)

// 编辑文章
export const editArticle_new = (data: articleData) => 
  axiosReq('post', '/api/v1/page/edit_art', data)

// 文章侧边栏列表
export const getSideBlockList = (channel_id?: string|number) => 
  axiosReq('get', `/api/v1/articles/side_blk/list?channel_id=${channel_id}`)
/* 新版文章管理 */


// 模板历史记录列表
export const getHistoryList = (art_id: number, params: { site_id: number; page: number; page_size: number }) => 
  axiosReq('get', `/api/v1/articles/${art_id}/versions`, {
    params,
    paramsSerializer: {
      serialize: (obj) => qs.stringify(obj)
    }
  })

// 回退至指定版本 
export const restoreVersion = (art_id: number, version_id: number, data: { site_id: number; }) => 
  axiosReq('post', `/api/v1/articles/${art_id}/versions/${version_id}/restoration`, data)

// 查看历史版本详情
export const getHistoryDetail = (art_id: number|string, version_id: number, site_id: number ) => 
  axiosReq('get', `/api/v1/articles/${art_id}/versions/${version_id}?site_id=${site_id}`)

// 批量修改分类
export const BatchEditClassify = (data: { art_ids: string[]; cat_id: number|string }) => 
  axiosReq('post', `/api/v1/articles/batch-update`, data)

// 批量修改侧边栏
export const BatchEditSideBlock = (data: { art_id: string; side_block: number|string }) => 
  axiosReq('post', `/api/v1/articles/batch_update_side_block`, data)

// 批量修改作者
export const BatchEditAuthor = (data: { art_id: string; author_id: number|string }) => 
  axiosReq('post', `/api/v1/articles/batch_update_author`, data)

// 版本对比
export const getComparisons = (site_id: number, art_id: string|number, version_ids: string) => 
  axiosReq('get', `/api/v1/articles/${art_id}/versions/comparisons?site_id=${site_id}&version_ids=${version_ids}`)

/* 关键词库管理 */
// 词组列表
export const getTopicList = (params) => 
  axiosReq('get', '/api/v1/keyword/topic_list', {
    params,
    paramsSerializer: {
      serialize: (obj) => qs.stringify(obj)
    }
  })

// 词组添加
export const addTopic = (data: { channel_id: number; name: string; keyword_list?: string[]; parent_id: string|number }) => 
  axiosReq('post', '/api/v1/keyword/topic_add', data)

// 词组删除
export const deleteTopic = (data: { id: string }) =>  
  axiosReq('post', '/api/v1/keyword/topic_del', data)

// 关键词添加
export const addKeyword = (data: { id: string|number; keyword_list: string[]; channel_id: number }) =>  
  axiosReq('post', '/api/v1/keyword/add', data)

// 关键词删除
export const deleteKeyword = (data: { keyword_id: string|number }) =>  
  axiosReq('post', '/api/v1/keyword/del', data)

// 词组详情列表
export const getTopicDetailList = ( { id, page, page_size }: {id: string|number; page: number; page_size: number}) =>  
  axiosReq('get', `/api/v1/keyword/topic_info?id=${id}&page=${page}&page_size=${page_size}`)

// 获取导入execl模板
export const getImportExcel = () => axiosReq('post', '/api/v1/keyword/import_tpl')

// 关键词批量导入
export const importKeyword = (data: { import_keyword_file: Blob; channel_id: number, parent_id: string|number }) => {
  const formData = new FormData()
  Object.entries(data).forEach( ( [key, value] ) => {
    formData.append(key, typeof value === 'number' ? `${value}` : value)
  } )
  return axiosReq('post', '/api/v1/keyword/import', formData)
}

// 词组数据更新
export const updateKeyword = (topic_ids: string) =>
  axiosReq('post', '/api/v1/keyword/topic_update', { topic_ids })

// 关键词组文章打标
export const markStar = (data: { topic_id: string; art_id: string }) =>
  axiosReq('post', '/api/v1/keyword/star_art', data)

// 词组分类列表（带分页）
export const topicCateList = (params) => 
  axiosReq('get', `/api/v1/keyword/topic_cate_list`, {
    params,
    paramsSerializer: {
      serialize: (obj) => qs.stringify(obj)
    }
  })

// topic分类列表
export const _topicList = (channel_id: number) =>
  axiosReq('get', `/api/v1/keyword/topic_cate?channel_id=${channel_id}`)

// 词组/分类修改名称
export const editTopic = (data: { id: number; name: string; parent_id: number; }) => 
  axiosReq('post', '/api/v1/keyword/topic_edit', data)
/* 关键词库管理 */
