<template>
  <el-form-item label="拓展字段">
    <div v-for="(d, index) in extend" class="extend-item" style="width: 100%">
      <strong>拓展字段{{ index + 1 }}</strong> <br>
      <Close class="close" @click="handleDelete(index)" v-if="!disabled" />
      <div style="padding-bottom: 12px; display: flex; flex-wrap: nowrap;">
        <span class="label">字段名称：</span>
        <div class="form" :class="{ 'is-error': isError(d.key) }" style="flex: 1 0 auto;">
          <el-input v-model="d.key" placeholder="字段名称" :disabled="disabled" />
          <span class="error-info"> 只能输入字母、数字以及下划线 </span>
        </div>
      </div>
      <div style="padding-bottom: 8px; display: flex; flex-wrap: nowrap;">
        <span class="label">字段值：</span>
        <div style="flex: 1 0 auto;">
          <el-input 
            v-if="!disabled"
            type="textarea"
            v-model="d.val"
            :rows="2"
            placeholder="字段值"
            v-ace="{ getVal: () => d.val, save: (val: string) => d.val = val, showTips: true }"
          />
          <el-input 
            v-else
            type="textarea"
            v-model="d.val"
            :rows="2"
            placeholder="字段值"
            :disabled="true"
          />
        </div>
      </div>
    </div>
    <template v-if="!disabled">
      <el-button v-if="extend.length < limit" type="primary" :icon="Plus" @click="handleAdd"> 添加字段 </el-button>
    </template>
  </el-form-item>
</template>

<script setup lang="ts">
import { ElMessage } from 'element-plus'
import { Plus, Close } from '@element-plus/icons-vue'

const keyReg = /^[a-zA-Z\d\_]{0,}$/
const isError = (key: string) => !keyReg.test(key)

const props = withDefaults(defineProps<{
  limit?: number;
  extend: { val: string; key: string; }[];
  disabled?: boolean;
}>(), {
  limit: 5,
  disabled: false
})

const handleAdd = () => {
  const { extend, limit } = props
  if ( extend.length === limit ) {
    return ElMessage.warning(`最多添加${limit}个`)
  }
  const item = {
    val: '',
    key: ''
  }
  extend.push(item)
}
const handleDelete = (index: number) => {
  const { extend } = props
  extend.splice(index, 1)
}
</script>

<style lang="scss">
.extend-item {
  border: 1px dashed var(--el-border-color-light); 
  margin-bottom: 10px;
  padding: 10px; 
  border-radius: 6px;
  position: relative;
  .label {
    display: inline-block;
    width: 70px;
    text-align: right;
  }
  .close {
    width: 16px;
    height: 16px;
    position: absolute;
    right: 0px;
    top: 5px;
    cursor: pointer;
  }
  .form {
    position: relative;
    .error-info {
      color: var(--el-color-danger);
      font-size: 12px;
      line-height: 1;
      position: absolute;
      width: 100%;
      left: 0;
      bottom: 0;
      transform: translateY(100%);
      opacity: 0;
    }
    &.is-error {
      .el-input__wrapper {
        box-shadow: 0 0 0 1px var(--el-color-danger) inset;
      }
      .error-info {
        opacity: 1;
      }
    }
  }
}
.hide .el-form-item__label {
    opacity: 0;
  }
</style>