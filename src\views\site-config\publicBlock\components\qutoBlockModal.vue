<template>
  <el-dialog
    v-model="show"
    title="公共块嵌入的块"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    append-to-body
    align-center
    width="1080"
    @closed="emit('close')"
  >
    <template #default>
      <p>
        公共块是被引用到各个块中, 当公共块更新后, 仅需重<strong style="color: var(--el-color-primary)">新生成对应引用块并发布</strong>, 即可实现线上的更新 <br>
        PS: <strong style="color: var(--el-color-primary)">公共块本身无需生成</strong>, 因为是以代码方式嵌入到各个块中,在块生成时进行代码的嵌入替换
      </p>
      <QueryForm
        :form-list="formList"
        :loading="loading"
        @search="handleSearch" 
        hide-reset
      />
      <div v-loading="loading">
        <CustomTable 
          ref="tableRef"
          :data="tableData"
          :columns="columns"
          :table-method="getList"
          :operate-list="operateList"
          selectable
          height="600"
          @selection-change="handleSelectionChange"
        />
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="tsx">
import { ref, computed } from 'vue'
import { ElMessage, ElButton } from 'element-plus'
import { useParamsStore } from '@/store'
import useTryCatch from '@/hooks/use-try-catch'
import useLoading from '@/hooks/use-loading'
import { useBlockGenerate } from '@/hooks/use-generate'
import { getEmbededList } from '@/api/block-management'
import type { listItem } from '@/api/block-management'

import CustomTable from '@/components/CustomTable/index.vue'
import QueryForm from '@/components/QueryForm/index.vue'

const props = defineProps<{
  blk_id: number;
}>()
const emit = defineEmits(['close'])

const { site_id_all } = useParamsStore()
const { loading, setLoading } = useLoading()
const show = ref(true)
const tableRef = ref()
const tableData = ref<listItem[]>([])
const tableSelections = ref<listItem[]>([])
const state = ref('')
const name = ref('')
const formList = ref([
  {
    label: '块状态',
    placeholder: '块状态',
    value: state,
    component: 'select',
    selections: [
      {
        label: '未生成',
        value: 'uncreated',
      },
      {
        label: '已生成',
        value: 'created',
      },
      {
        label: '已修改',
        value: 'modified',
      }
    ],
    multiple: true
  },
  {
    placeholder: '块名称',
    value: name,
    component: 'input'
  }
])
const columns = ref([
  {
    title: '站点名称',
    dataKey: 'site_name',
    minWidth: 150,
  },
  {
    title: '模板渠道',
    dataKey: 'channel_name',
    width: 150,
    cellRenderer: (scope: { row: listItem }) => (
      <span style={ { color: scope.row.color } }> { scope.row.channel_name } </span>
    )
  },
  {
    title: '块ID',
    dataKey: 'blk_id',
    width: 100,
  },
  {
    title: '块名称',
    dataKey: 'blk_name',
    minWidth: 180,
  },
  {
    title: '块类型',
    dataKey: 'blk_type_name',
    width: 150,
  },
  {
    title: '状态',
    dataKey: 'created_state',
    width: 100,
    cellRenderer: (scope: { row: listItem }) => (
      <span style={ { color: scope.row.created_state === 'created' ? 'var(--el-color-success)' : '' } } >
        { scope.row.created_state_name }
      </span>
    )
  },
  {
    title: '操作',
    dataKey: '',
    width: 120,
    cellRenderer: (scope: { row: listItem }) => (
      <>
        <ElButton type='success' plain onClick={() => handleGenerate(scope.row.blk_id)}> 生成块 </ElButton>
      </>
    )
  }
])
const operateList = ref([
  {
    title: '批量生成选中块',
    method: () => {
      const blk_ids = tableSelections.value.map( ( { blk_id } ) => blk_id ).join(',')
      useBlockGenerate({
        blk_ids,
        site_id: site_id_all
      }, () => {
        show.value = false
      })
    },
    disabled: computed(() => tableSelections.value.length === 0)
  }
])

const handleSearch = () => {
  tableRef.value.setPage(1)
  const { page, pageSize } = tableRef?.value?.getPagination()
  getList( { page, pageSize } )
}

const getList = ( { page, pageSize } ) => {
  useTryCatch( async () => {
    setLoading(true)

    const params = {
      blk_id: props.blk_id,
      created_state: String(state.value) || '',
      blk_name: name.value,
      page,
      page_size: pageSize
    }
    const res = await getEmbededList(params)
    if (res.code === 200) {
      tableData.value = res.data.items
      tableRef.value?.setTotal(res.data.total)
    } else {
      ElMessage.error(res.msg)
    }
    setLoading(false)
  }, () => setLoading(false) )
}

const handleSelectionChange = (selections: listItem[]) => {
  tableSelections.value = selections
}
const handleGenerate = (blk_id: number) => {
  useBlockGenerate({
    blk_ids: `${blk_id}`,
    site_id: site_id_all
  })
}
</script>