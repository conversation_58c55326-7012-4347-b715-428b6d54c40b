<template>
  <el-dialog
    v-model="show"
    title="对账确认单记录"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    append-to-body
    align-center
    width="1000"
    @closed="emit('close')"
  >
    <template #default>
      <div v-loading="loading" style="min-height: 300px;" class="table_bill">
        <CustomTable 
          ref="tableRef"
          :data="tableData"
          :columns="columns"
          :table-method="getList"
          height="800px"
        />
      </div>
    </template>
  </el-dialog>
</template>


<script setup lang="tsx">
import { ref } from 'vue'
import { ElMessage, ElButton, ElMessageBox } from 'element-plus'
import useTryCatch from '@/hooks/use-try-catch'
import useLoading from '@/hooks/use-loading'
import { getHost } from '@/hooks/use-env'
import { getBillList, retryBill } from '@/api/translate-management'
import type { billItem } from '@/api/translate-management'

import CustomTable from '@/components/CustomTable/index.vue'

const emit = defineEmits(['close'])

const { loading, setLoading } = useLoading()
const show = ref(true)

const tableRef = ref()
const tableData = ref<billItem[]>([])
const columns = ref([
  {
    title: '对账单ID',
    dataKey: 'id',
    width: 90
  },
  {
    title: '生成日期',
    dataKey: 'add_time',
    width: 180
  },
  {
    title: '需对账人员',
    dataKey: 'job_users_name',
    minWidth: 180
  },
  {
    title: '对账日期',
    dataKey: 'edit_time',
    width: 180
  },
  {
    title: '状态',
    dataKey: 'status',
    width: 100,
    cellRenderer: ( scope: { row: billItem } ) => (
      <>
        <span style={ { 
          color: scope.row.status === 2 
          ? 'var(--el-color-success)' : scope.row.status === 3 
          ? 'var(--el-color-error)' : 'var(--el-color-primary)' 
        } }>
          { scope.row.status_name }
        </span>
      </>
    )
  },
  {
    title: '操作',
    dataKey: '',
    fixed: 'right',
    width: 150,
    cellRenderer: ( scope: { row: billItem } ) => (
      <>
        {
          scope.row.status === 2 
          ? <>
            <ElButton type='primary' link onClick={() => handlePreview(scope.row)}> 预览 </ElButton>
            <ElButton type='primary' link onClick={() => handleDownload(scope.row)}> 下载 </ElButton>
          </>
          : scope.row.status === 3
          ? <ElButton type='primary' link onClick={() => handleRetry(scope.row)}> 重新生成 </ElButton>
          : null
        }
      </>
    )
  }
])

const refresh = () => {
  tableRef.value.setPage(1)
  const { page, pageSize } = tableRef?.value?.getPagination()
  getList( { page, pageSize } )
}

const getList = ( { page, pageSize } ) => {
  useTryCatch( async () => {
    setLoading(true)

    const res = await getBillList( { page, pageSize} )
    if (res.code === 200) {
      tableData.value = res.data.list
      tableRef.value?.setTotal(res.data.total)
    } else {
      ElMessage.error(res.msg)
    }

    setLoading(false)
  }, () => setLoading(false) )
}

const handlePreview = ( row: billItem ) => {
  window.open(`${getHost()}/api/v1/translate/bill_preview?id=${row.id}`)
}

const handleDownload = ( row: billItem ) => {
  ElMessageBox.confirm('请确认是否下载', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    callback: ( action: string ) => {
      if (action === 'confirm') {
        location.href = `${getHost()}/api/v1/translate/bill_export?id=${row.id}`
      }
    }
  })
}

const handleRetry = ( row: billItem ) => {
  ElMessageBox.confirm('请确认是否重新生成', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    callback: ( action: string ) => {
      if (action === 'confirm') {
        useTryCatch( async () => {
          const res = await retryBill(`${row.id}`)
          if (res.code === 200) {
            refresh()
            ElMessage.success('操作成功')
          } else {
            ElMessage.error(res.msg)
          }
        } )
      }
    }
  })
}
</script>