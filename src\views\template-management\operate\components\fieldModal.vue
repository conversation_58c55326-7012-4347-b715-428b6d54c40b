<template>
  <el-dialog
    v-model="show"
    title="新增自定义字段"
    width="500px"
    @closed="() => emit('closed')"
    append-to-body
    align-center
  >
    <template #default>
      <el-form
        ref="formRef"
        :model="fieldItem"
        :rules="fieldRules"
        label-width="130"
        label-suffix=":"
      >
        <el-form-item label="表字段名称" prop="field_name">
          <el-input v-model="fieldItem.field_name" placeholder="小写字母、数字及下划线" >
            <template #prepend>_</template>
          </el-input>
        </el-form-item>
        <el-form-item label="字段名称" prop="input_label">
          <el-input v-model="fieldItem.input_label" />
        </el-form-item>
        <el-form-item label="输入框类型" prop="input_type">
          <el-select v-model="fieldItem.input_type">
            <el-option 
              v-for="(d, index) in inputTypes"
              :key="index"
              :label="d.label"
              :value="d.value"
            />
          </el-select>
        </el-form-item>
        <template v-if="fieldItem.input_type === 'select'">
          <el-form-item label="下拉选项">
            <template v-for="(d, index) in inputOptions" :key="index">
              <el-input v-model="d.v" label="请输入选项值" style="width: calc(100% - 20px); margin-bottom: 4px;" />
              <Delete 
                class="ml-4px with-hand"
                width="16px"
                @click="handleDeleteOption(index)"
              />
            </template>
          </el-form-item>
          <el-form-item label="">
            <el-button type="primary" @click="handleAddOption"> 添加选项 </el-button>
          </el-form-item>
        </template>
        <el-form-item label="字段类型" prop="field_type">
          <el-select v-model="fieldItem.field_type">
            <el-option 
              v-for="(d, index) in fieldTypes"
              :key="index"
              :label="d.label"
              :value="d.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="默认值" prop="input_value">
          <el-input v-model="fieldItem.input_value" palceholder="请输入默认值" />
        </el-form-item>
        <el-form-item label="是否必填" prop="is_null">
          <el-switch 
            v-model="fieldItem.is_null"
            active-value="N"
            inactive-value="Y"
            active-text="是"
            inactive-text="否"
          />
        </el-form-item>
      </el-form>
    </template>
    <template #footer>
      <div class="text-center">
        <el-button type="primary" @click="handleConfirm">确定</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, reactive } from 'vue'
import { Delete } from '@element-plus/icons-vue'
import { useParamsStore } from '@/store'
import type { fieldItem } from '@/api/template-management'
import { fieldRules } from '../rules'
import type { FormInstance } from 'element-plus'

const emit = defineEmits(['confirm', 'closed'])

const { fieldTypes, inputTypes } = useParamsStore()

const show = ref(true)
const formRef = ref<FormInstance>()
const fieldItem = reactive<fieldItem>({
  input_label: '',
  input_value: '',
  input_type: '',
  field_name: '',
  field_type: '',
  is_null: 'Y',
})
const inputOptions = ref<{k:string;v:string}[]>([ { k: '', v: '' } ])

const handleAddOption = () => {
  inputOptions.value.push({ k: '', v: '' })
}
const handleDeleteOption = (index: number) => {
  inputOptions.value.splice(index, 1)
}

const reset = () => {
  inputOptions.value = [ { k: '', v: '' } ]
  formRef.value?.resetFields()
}

const handleConfirm = async () => {
  if (formRef.value) {
    await formRef.value.validate(( valid ) => {
      if (valid) {
        show.value = false
        if (fieldItem.input_type === 'select') {
          fieldItem.input_option = JSON.stringify(inputOptions.value)
        }
        fieldItem.field_name = `_${fieldItem.field_name}`
        emit('confirm', {...fieldItem})
        reset()
      }
    })
  }
}

defineExpose({
  show: () => show.value = true
})
</script>