import { reactive } from 'vue'
import type { FormRules } from 'element-plus'

export const formRules = reactive<FormRules>({
  name: [
    { required: true, message: '订阅名称不能为空', trigger: 'blur' },
  ],
  channel_id: [
    { required: true, message: '站点渠道不能为空', trigger: 'change' },
  ],
  notice_type: [
    { required: true, message: '通知方式不能为空', trigger: 'change' },
  ],
  cycle: [
    { required: true, message: '通知频率不能为空', trigger: 'change' },
  ],
  notice_content: [
    { required: true, message: '通知内容不能为空', trigger: 'blur' },
  ],
  notice_user_id: [
    { required: true, message: '通知用户不能为空', trigger: 'change' },
  ],
  rule: [
    { trigger: ['change', 'blur'], validator: (rule, value, callback) => {
      for ( const item of value ) {
        for ( const val of Object.values(item) ) {
          if (!val) {
            return callback(new Error('所有规则项均不能为空'))
          }
        }
      }
      callback()
    } },
  ]
})