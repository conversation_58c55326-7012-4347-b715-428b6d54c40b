<template>
  <div class="scroll-y">
    <div style="overflow: hidden;">
      <el-row :gutter="20">
        <el-col :span="10">
          <el-card shadow="never" style="margin-bottom: 20px;">
            <template #header>
              <strong style="font-size: 1.25rem;">评论信息</strong>
            </template>
            <template #default>
              <p> 页面ID: {{ page_id }} </p>
              <p> 页面地址: {{ url }} </p>
              <p> 评论ID: {{ commentId }} </p>
            </template>
          </el-card>
          <el-card class="box-card" shadow="never" style="border-color: var(--el-border-color-light)">
            <template #header class="clearfix">
              <span style="font-weight:600">展示效果</span>
            </template>
            <div style="border:1px solid var(--el-border-color-light);padding: 10px">
              用户{{ uid }}信息：
              <p style="padding-left: 20px"> {{ message }} </p>
            </div>
            <div style="padding-left: 30px">
              <div v-for="(d, index) in reply_list" :key="d.uid + index" style="border:1px solid var(--el-border-color-light);padding: 10px">
                {{ d.type === 2 ? '运营' : '用户' }}{{ d.uid }}信息：
                <p style="padding-left: 20px"> 回复 用户{{ d.reply_to_uid }}: {{ d.message }} </p>
              </div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="14">
          <el-card shadow="never" style="min-height: 100%;">
            <template #header>
              <strong style="font-size: 1.25rem;">操作</strong>
            </template>
            <template #default>
              <el-table v-loading="loading" :data="tableData" :row-class-name="( { row } )  => row.type === 2 ? 'bg-lighter' : ''">
                <el-table-column prop="type" label="类型">
                  <template #default="scope">
                    {{ scope.row.type === 1 ? '用户回复' : scope.row.type === 2 ? '运营回复' : '评论' }}
                  </template>
                </el-table-column>
                <el-table-column prop="check_state" label="审核状态">
                  <template #default="scope">
                    <span :style="{ color: scope.row.check_state === 'able' ? 'var(--el-color-success)' : scope.row.check_state === 'disable' ? 'var(--el-color-error)' : 'inherit' }">
                      {{ scope.row.check_state === 'pending' ? '待审核' : scope.row.check_state === 'able' ? '审核通过' : '审核不通过' }}
                    </span>
                  </template>
                </el-table-column>
                <el-table-column prop="uid" label="账号信息" />
                <el-table-column prop="time" label="回复时间" show-overflow-tooltip />
                <el-table-column prop="message" label="回复内容" show-overflow-tooltip />
                <el-table-column label="操作" width="260">
                  <template #default="scope">
                    <template v-if="scope.row.type !== 2">
                      <el-button type="success" plain @click="handleCheck(scope.row.id, 1)"> 通过 </el-button>
                      <el-button type="danger" plain @click="handleCheck(scope.row.id, 2)"> 不通过 </el-button>
                      <el-button type="primary" plain :disabled="scope.row.check_state !== 'able'" @click="handleReply(scope.row)"> 回复 </el-button>
                    </template>
                  </template>
                </el-table-column>
              </el-table>
            </template>
          </el-card>
        </el-col>
      </el-row>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { useRoute } from 'vue-router'
import { ElMessage, ElMessageBox, ElInput, ElForm, ElFormItem } from 'element-plus'
import useTryCatch from '@/hooks/use-try-catch'
import useLoading from '@/hooks/use-loading'
import { commentReplyDetail, commentCheck, commetReply } from '@/api/web-config'

defineOptions( { name: 'CommentDetail' } )

const route = useRoute()
const { loading, setLoading } = useLoading()
const { comment_id, channel_id } = route.query as unknown as { comment_id: number; channel_id: number; }

const page_id = ref('')
const url = ref('')
const commentId = ref(0)
const uid = ref('')
const message = ref('')
const reply_list = ref<any[]>([])
const tableData = ref<any[]>([])

const getDetail = () => {
  useTryCatch( async () => {
    setLoading( true )

    const params = {
      id: comment_id,
      channel_id,
    }
    const res = await commentReplyDetail(params)
    if (res.code === 200) {
      reply_list.value = res.data.show_info.reply_list
      page_id.value = res.data.page_id
      url.value = res.data.url
      commentId.value = res.data.id
      uid.value = res.data.uid
      message.value = res.data.message

      const official_reply = res.data.operate_info.official_reply
      const replyList =  res.data.operate_info.reply_list

      const list: any[] = []
      list.push({
        id: res.data.id,
        message: res.data.message,
        time: res.data.time,
        check_state: res.data.check_state,
        uid: res.data.uid,
        type: 0,
        official_reply_msg: official_reply ? official_reply.message : ''
      })

      official_reply && list.push({
        id: null,
        message: official_reply.message,
        time: official_reply.time,
        check_state: official_reply.check_state,
        uid: official_reply.user_id,
        type: 2,
        official_reply_msg: ''
      })

      replyList && replyList.forEach(item => {
        list.push({
          id: item.id,
          message: item.message,
          time: item.time,
          check_state: item.check_state,
          uid: item.uid,
          type: 1,
          official_reply_msg: item.official_reply ? item.official_reply.message : ''
        })
        if (item.official_reply) {
          list.push({
            id: null,
            message: item.official_reply.message,
            time: item.official_reply.time,
            check_state: item.official_reply.check_state,
            uid: item.official_reply.user_id,
            type: 2,
            official_reply_msg: ''
          })
        }
      })

      tableData.value = list
    } else {
      ElMessage.error(res.msg)
    }

    setLoading( false )
  }, () => setLoading( false ) )
}

const handleCheck = (id: number, type: number) => {
  const params = {
    ids: String(id),
    type: id === commentId.value ? 1 : 2,
    state: type === 1 ? 'able' : 'disable'
  }

  useTryCatch( async () => {
    setLoading(true)

    const res = await commentCheck(params)
    if (res.code === 200) {
      getDetail()
      ElMessage.success('操作成功')
    } else {
      ElMessage.error(res.msg)
    }

    setLoading(false)
  }, () => setLoading(false) )
}

const handleReply = (row: any) => {
  const replyMsg = ref(row.official_reply_msg || '')
  ElMessageBox({
    title: '回复内容',
    customClass: 'dialog-button-center',
    customStyle: {
      maxWidth: 'none',
      width: '500px',
    },
    showCancelButton: true,
    showConfirmButton: true,
    cancelButtonText: '取消',
    confirmButtonText: '确认',
    callback: (action: string) => {
      if (action === 'confirm') {
        useTryCatch( async () => {
          const params = {
            id: row.id,
            message: replyMsg.value,
            type: row.id === commentId.value  ? 1 : 2
          }
          const res = await commetReply(params)

          if (res.code === 200) {
            getDetail()
            ElMessage.success('回复成功')
          } else {
            ElMessage.error(res.msg)
          }
        } )
      }
    },
    message: () => h(ElForm, { labelWidth: '120px', labelSuffix: ':' }, () => [
      h(ElFormItem, { label: '用户评论/回复' }, () => row.message),
      h(ElFormItem, { label: '回复内容' }, () => h(ElInput, {
        type: 'textarea',
        modelValue: replyMsg.value,
        onInput: (value) => replyMsg.value = value
      }))
    ])
  })
}

getDetail()
</script>