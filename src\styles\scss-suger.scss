/*脱落文档流定位*/
.center-50 {
  //居中定位
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  z-index: 10;
}
.center-top60 {
  position: absolute;
  top: 60%;
  left: 50%;
  transform: translate(-50%, -60%);
  z-index: 10;
}
.center-top70 {
  position: absolute;
  top: 70%;
  left: 50%;
  transform: translate(-50%, -70%);
  z-index: 10;
}
.center-top80 {
  position: absolute;
  top: 80%;
  left: 50%;
  transform: translate(-50%, -80%);
  z-index: 10;
}
.center-top90 {
  position: absolute;
  top: 80%;
  left: 50%;
  transform: translate(-50%, -90%);
  z-index: 10;
}
/*fixed*/
.fixed-center-50 {
  //居中定位
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  z-index: 10;
}
.fixed-center-top60 {
  position: fixed;
  top: 60%;
  left: 50%;
  transform: translate(-50%, -60%);
  z-index: 10;
}
.fixed-center-top70 {
  position: fixed;
  top: 70%;
  left: 50%;
  transform: translate(-50%, -70%);
  z-index: 10;
}
.fixed-center-top80 {
  position: fixed;
  top: 80%;
  left: 50%;
  transform: translate(-50%, -80%);
  z-index: 10;
}
.fixed-center-top90 {
  position: fixed;
  top: 90%;
  left: 50%;
  transform: translate(-50%, -90%);
  z-index: 10;
}
.fixed-center-top95 {
  position: fixed;
  top: 95%;
  left: 50%;
  transform: translate(-50%, -95%);
  z-index: 10;
}

/*
flex布局 第一个字母为主轴
*/
//start
.rowSS {
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  align-items: flex-start;
}
.rowSC {
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  align-items: center;
}
.rowSE {
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  align-items: flex-end;
}
//space-between
.rowBS {
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  align-items: flex-start;
}
.rowBC {
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
}
.rowBE {
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  align-items: flex-end;
}
//space-around
.rowAS {
  display: flex;
  flex-direction: row;
  justify-content: space-around;
  align-items: flex-start;
}
.rowAC {
  display: flex;
  flex-direction: row;
  justify-content: space-around;
  align-items: center;
}
.rowAE {
  display: flex;
  flex-direction: row;
  justify-content: space-around;
  align-items: flex-end;
}
//center
.rowCS {
  display: flex;
  flex-direction: row;
  justify-content: center;
  align-items: flex-start;
}
.rowCC {
  display: flex;
  flex-direction: row;
  justify-content: center;
  align-items: center;
}

.rowCE {
  display: flex;
  flex-direction: row;
  justify-content: center;
  align-items: flex-end;
}

/*col*/
//start
.columnSS {
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  align-items: flex-start;
}
.columnSC {
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  align-items: center;
}
.columnSE {
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  align-items: flex-end;
}
//space-between
.columnBS {
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  align-items: flex-start;
}
.columnBC {
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  align-items: center;
}
.columnBE {
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  align-items: flex-end;
}
//space-around
.columnAS {
  display: flex;
  flex-direction: column;
  justify-content: space-around;
  align-items: flex-start;
}
.columnAC {
  display: flex;
  flex-direction: column;
  justify-content: space-around;
  align-items: center;
}
.columnAE {
  display: flex;
  flex-direction: column;
  justify-content: space-around;
  align-items: flex-end;
}
//center
.columnCS {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: flex-start;
}
.columnCC {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
}
.columnCE {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: flex-end;
}

//*图标
.star-icon {
  color: #f56c6c;
  font-size: 14px;
  margin-right: 4px;
}

.fix-btn-to-bottom {
  position: fixed;
  bottom: 0;
  left: 50%;
  transform: translate(-50%, 0);
  z-index: 10;
  height: 60px;
  background: #fff;
  width: 100vw;
}

//table操作栏
.table-operation-btn {
  span {
    //点击样式
    cursor: pointer;
    color: #477ef5;
  }
}

//table操作栏
.btn-click-style {
  //点击样式
  cursor: pointer;
  color: #477ef5;
}
