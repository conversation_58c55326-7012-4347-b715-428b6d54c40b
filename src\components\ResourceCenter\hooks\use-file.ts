import { h } from 'vue'
import { ElMessageBox, ElMessage } from 'element-plus'
import { deleteFile, renameFile, downloadFile, compressFiles } from '@/api/resource-center'
import { copyValueToClipboard } from '@/hooks/use-common'
import useTryCatch from '@/hooks/use-try-catch'
import { downloadStream } from '@/utils/common'
import { imgReg, videoReg, msgboxParams, getSelectFileItem } from '../types'
import type { Ref } from 'vue'
import type { selectFiles, selectFileItem } from '../types'

const VITE_APP_ENV = import.meta.env.VITE_APP_ENV

export default 
( selectedFiles: Ref<selectFiles>,
  deleteCallbak: (...args: any[]) => any
) => {
  // 获取已勾选的文件
  const getFileItems = () => {
    const files = [...selectedFiles.value.values()]
    const urls: selectFileItem[] = files.map((file) => {
      return getSelectFileItem(file)
    })
    return urls
  }
  return {
      previewUrl: (url: string) => VITE_APP_ENV === 'prod' ? url : url.replace('https', 'http'),
      // 格式化文件大小
      formatSize: (bytes: number) => {
        const exp = Math.log(bytes) / Math.log(1024) | 0
        const result = (bytes / Math.pow(1024, exp)).toFixed(2)

        return result + ' ' + (exp == 0 ? 'bytes' : 'KMGTPEZY' [exp - 1] + 'B')
      },
      getFileItems,
      // 复制文件链接
      handleCopyLink: (fileItem?: selectFileItem|null) => {
        const { url } = fileItem || getFileItems()[0]
        copyValueToClipboard(url)
        ElMessageBox.confirm(
          `您复制的链接为：${url}`,
          '复制成功',
          {
            ...msgboxParams,
            customStyle: {
              'word-break': 'break-all'
            }
          }
        )
      },
      // 在线查看文件
      handlePreview: (fileItem?: selectFileItem|null) => {
        const { url, suffix, name, file } = fileItem || getFileItems()[0]
        if (file.publish !== 3) {
          return ElMessage.warning('文件还没有发布，不支持查看')
        }

        if (imgReg.test(suffix) || videoReg.test(suffix)) {
          ElMessageBox({
            title: name,
            message: h('div', null, [
              h(videoReg.test(suffix) ? 'video' : 'img',
                {
                  style: {
                    'max-width': '100%',
                    'max-height': '85vh',
                  },
                  src: url,
                  muted: true,
                  autoplay: true,
                  controls: true,
                }
              )
            ]),
            customClass: 'img-preview-dialog',
            callback: () => {}
          })
        } else {
          window.open(url)
        }
      },
      // 下载文件
      handleFileDownload(fileItem?: selectFileItem|null) {
        const files: selectFileItem[] = []
        if (fileItem) {
          files.push(fileItem)
        } else {
          files.push(...getFileItems())
        }
        files.forEach(({ url, name }) => {
          useTryCatch(
            async () => {
              const data = await downloadFile({ url })
              downloadStream(data, name),
              () => {},
              () => ElMessage.error(`${name}下载出错，请重试`)
            }
          )
        })
      },
      // 重命名文件
      handleFileRename: (fileItem?: selectFileItem|null) => {
        const { name, id, suffix, file } = fileItem || getFileItems()[0]
        const real_name = name.replace(`.${suffix}`, '')

        ElMessageBox.prompt(
          `请输入修改的文件名称`,
          '重命名文件',
          {
            ...msgboxParams,
            inputPlaceholder: real_name,
            inputPattern: /.+/,
            inputErrorMessage: '请输入文件名称',
            callback: (action: any) => {
              if (action.action === 'confirm') {
                useTryCatch(
                  async () => {
                    const reName = `${action.value}.${suffix}`
                    const params = {
                      id,
                      name: reName
                    }
                    const res = await renameFile(params)
                    if (res.code === 200) {
                      file.file_name = reName
                      ElMessage.success('修改成功')
                    } else {
                      ElMessage.error(res.msg)
                    }
                  }
                )
              }
            }
          }
        )
      },
      // 压缩图片
      handleImgCompress: (fileItem?: selectFileItem|null) => {
        const files = getFileItems()
        const idArr = files.map(({ id }) => id)
        fileItem && idArr.push(fileItem.id)
        if (idArr.length === 0) {
          return ElMessage.error('请至少选择一张图片')
        }
        ElMessageBox.prompt(
          `请输入压缩质量(30%~90%)`,
          '压缩图片',
          {
            ...msgboxParams,
            inputPlaceholder: '请输入30至90间的正整数',
            inputPattern: /^([3-8][0-9]|90)$/,
            inputErrorMessage: '请输入30至90间的正整数',
            callback: (action: any) => {
              if (action.action === 'confirm') {
                useTryCatch(
                  async () => {
                    const params = {
                      ids: idArr.join(','),
                      quality: action.value
                    }
                    const res = await compressFiles(params)
                    if (res.code === 200) {
                      ElMessage.success(res.msg)
                    } else {
                      ElMessage.error(res.msg)
                    }
                  }
                )
              }
            }
          }
        )
      },
      // 删除文件
      handleRemoveFile: (fileItem?: selectFileItem|null) => {
        const files = getFileItems()
        const idArr = files.map(({ id }) => id)
        fileItem && idArr.push(fileItem.id)
        if (idArr.length === 0) {
          return ElMessage.error('请至少选择一张图片')
        }
        ElMessageBox.confirm(
          '请确认是否需要删除',
          '删除文件',
          {
            ...msgboxParams,
            callback: (action: string) => {
              if (action === 'confirm') {
                useTryCatch(
                  async () => {
                    const params = {
                      ids: idArr.join(',')
                    }
                    const res = await deleteFile(params)
                    if (res.code === 200) {
                      deleteCallbak(idArr.length)
                      ElMessage.success(res.msg)
                    } else {
                      ElMessage.error(res.msg)
                    }
                  }
                )
              }
            }
          }
        )
      },
    }
  }