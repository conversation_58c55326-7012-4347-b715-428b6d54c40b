<template>
  <div class="scroll-y with-flex">
    <QueryForm
      :form-list="formList"
      :loading="loading"
      @search="handleSearch" 
      @reset="reset"
    />
    <div v-loading="loading" class="sticky-table">
      <CustomTable
        ref="tableRef"
        :columns="columns"
        :data="tableData"
        :table-method="getList"
        customize-head
        selectable
        :operate-list="operateList"
        @selection-change="handleSelectionChange"
        @sort-change="handleSortChange"
      />
    </div>

    <QutoBlock 
      v-if="loadQutoBlock"
      :blk_id="blkId"
      @close="loadQutoBlock = false"
    />
  </div>
</template>

<script setup lang="tsx">
import { ref, reactive, toRef, computed, onActivated, defineAsyncComponent } from 'vue'
import { ElMessage, ElButton } from 'element-plus'
import { useRoute, useRouter } from 'vue-router'
import { storeToRefs } from 'pinia'
import { useParamsStore } from '@/store'
import { useBasicStore } from '@/store/basic'
import useTryCatch from '@/hooks/use-try-catch'
import { getHost } from '@/hooks/use-env'
import useLoading from '@/hooks/use-loading'
import { useBlockGenerate } from '@/hooks/use-generate'
import { getBlockList } from '@/api/block-management'
import type { queryParams, listItem } from '@/api/block-management'
import Listener from '@/utils/site-channel-listeners'
import { stringify } from '@/utils/common'

import QueryForm from '@/components/QueryForm/index.vue'
import CustomTable from '@/components/CustomTable/index.vue'
const QutoBlock = defineAsyncComponent(() => import('./components/qutoBlockModal.vue'))

defineOptions( { name: 'PublicBlockManagement' } )

const router = useRouter()
const route = useRoute()
const { site_id_all, channel_item_all, userList } = storeToRefs(useParamsStore())
const { blockTypeList } = useParamsStore()
const basicStore = useBasicStore()
const { loading, setLoading } = useLoading()
const tableRef = ref()
const tableData = ref<listItem[]>([])
const tableSelections = ref<listItem[]>([])
const disableOperate = computed(() => tableSelections.value.length > 0 ? false : true)
const selectedIds = computed(() => tableSelections.value.map(( { blk_id } ) => blk_id).join(','))
const loadQutoBlock = ref(false)
const blkId = ref(0)
const queryParams_raw = {
  site_id: site_id_all.value,
  blk_name: '',
  blk_type: '',
  language: '',
  user_id_a: '',
  user_id_e: '',
  user_id_c: '',
  created_state: '',
  tpl_ids: '',
  art_ids: '',
  blk_ids: '',
  file_path: '',
  is_main: ''
}
const block_type_map = {}
blockTypeList.forEach(({ label, value }) => {
  block_type_map[value] = label
})
const queryParams = reactive<queryParams>({
  ...queryParams_raw,
  blk_ids: route.query.entity_ids as string || ''
})
const formList = ref([
  {
    label: '块id',
    clearable: true,
    placeholder: '块id',
    value: toRef(queryParams, 'blk_ids'),
    component: 'input',
  },
  {
    label: '块名称',
    clearable: true,
    placeholder: '块名称',
    value: toRef(queryParams, 'blk_name'),
    component: 'input',
  },
  {
    label: '块类型',
    clearable: true,
    placeholder: '块类型',
    value: toRef(queryParams, 'blk_type'),
    component: 'select',
    selections: blockTypeList
  },
  {
    label: '块路径',
    clearable: true,
    placeholder: '块路径',
    value: toRef(queryParams, 'file_path'),
    component: 'input',
  },
  {
    label: '更新人',
    clearable: true,
    placeholder: '更新人',
    value: toRef(queryParams, 'user_id_e'),
    component: 'select',
    labelKey: 'real_name',
    valueKey: 'user_id',
    append: true,
    selections: userList
  },
  {
    label: '生成状态',
    clearable: true,
    placeholder: '生成状态',
    value: toRef(queryParams, 'created_state'),
    component: 'select',
    selections: [
      {
        label: '未生成',
        value: 'uncreated',
      },
      {
        label: '已生成',
        value: 'created',
      },
      {
        label: '已修改',
        value: 'modified',
      }
    ]
  },
])
const columns = ref([
  {
    title: '块id',
    dataKey: 'blk_id',
    width: 100,
  },
  {
    title: '块名称',
    dataKey: 'blk_name',
    minWidth: 200,
  },
  {
    title: '块类型',
    dataKey: 'blk_type',
    width: 150,
    cellRenderer: (scope: { row: listItem }) => (<>{ block_type_map[scope.row.blk_type] || '/' }</>)
  },
  {
    title: '块语言',
    dataKey: 'language_name',
    width: 150,
  },
  {
    title: '文件路径',
    dataKey: 'file_path',
    minWidth: 180,
  },
  {
    title: '生成状态',
    dataKey: 'created_state',
    cellRenderer: (scope: { row: listItem }) => (
      <span style={{ color: scope.row.created_state === 'uncreated' ? 'inherit' : scope.row.created_state === 'created' ? 'var(--el-color-success)' : 'var(--el-color-primary)' }}>
        { scope.row.created_state_name }
      </span>
    )
  },
  {
    title: '更新人',
    dataKey: 'user_name_e',
  },
  {
    title: '更新时间',
    dataKey: 'edit_time',
    width: 180,
  },
  {
    title: '操作',
    dataKey: '',
    fixed: 'right',
    width: 310,
    cellRenderer: (scope: { row: listItem }) => (
      <>
        <ElButton type='primary' plain onClick={() => handleEdit(scope.row)}>编辑</ElButton>
        <ElButton type='success' plain onClick={() => handleQutoBlcok(scope.row)}>生成引用块</ElButton>
        <ElButton type='primary' plain onClick={() => handleHistory(scope.row)}>历史版本</ElButton>
      </>
    )
  }
])
const operateList = ref([
  {
    title: '创建主块',
    button: true,
    append: true,
    method: () => {
      basicStore.delCachedView('BlockManagementOperate')
      router.push({
        name: 'BlockManagementOperate',
        query: {
          type: 'add',
          is_public: 1
        }
      })
    },
  },
  {
    title: '导出EXCEL',
    icon: 'export',
    method: () => {
      const { page, pageSize } = tableRef.value
      const params = {
        S: {
          ...queryParams,
          site_id: site_id_all.value,
          blk_ids: selectedIds.value
        },
        is_public: 1,
        page: page || 1,
        page_size: pageSize || 10,
        channel_id: channel_item_all.value.channel_id || ''
      }
      location.href = `${getHost()}/api/v1/blocks/export?${stringify(params)}`
    },
    disabled: disableOperate
  },
])

const reset = () => {
  Object.entries(queryParams_raw).forEach(([ key, value ]) => {
    queryParams[key] = value
  })
}
const refresh = () => {
  const { page, pageSize } = tableRef?.value?.getPagination()
  getList( { page, pageSize } )
}
const handleSearch = () => {
  tableRef.value.setPage(1)
  refresh()
}
const getList = ( { page, pageSize }, prop?: string, order?: 'asc'|'desc' ) => {
  useTryCatch( async () => {
    const params = {
      S: {
        ...queryParams,
        site_id: site_id_all.value
      },
      page,
      page_size: pageSize,
      ...order ? {
        sort_key: prop,
        sort_type: order
      } : {},
      is_public: 1
    }
    setLoading(true)
    const res = await getBlockList(params)
    if (res.code === 200) {
      tableRef.value?.setTotal(res.data.total)
      tableData.value = res.data.items
    } else {
      ElMessage(res.msg)
    }
    setLoading(false)
  }, () => setLoading(false))
}
const handleSelectionChange = (selections: listItem[]) => {
  tableSelections.value = selections
}
/* table methods */
const handleEdit = (row: listItem) => {
  basicStore.delCachedView('BlockManagementOperate')
  router.push({
    name: 'BlockManagementOperate',
    query: {
      type: 'edit',
      blk_id: row.blk_id,
      is_public: 1
    }
  })
}
const handleQutoBlcok = (row: listItem) => {
  loadQutoBlock.value = true
  blkId.value = row.blk_id
}
const handleGenerateBlock = (row: listItem) => {
  useBlockGenerate({
    blk_ids: `${row.blk_id}`,
    site_id: site_id_all.value
  }, () => {
    refresh()
  })
}
const handleHistory = (row: listItem) => {
  basicStore.delCachedView('BlockHistory')
  router.push({
    name: 'BlockHistory',
    query: {
      id: row.blk_id
    }
  })
}
const handleSortChange = ({ prop, order }: { prop: string; order: 'ascending'|'descending'|null }) => {
  const { page, pageSize } = tableRef?.value?.getPagination()
  getList( { page, pageSize }, prop, order ? order.replace('ending', '') as 'asc'|'desc' : undefined )
}
/* table methods */
onActivated(() => {
  if (basicStore.refresh) {
    basicStore.setRefresh(false)
    refresh()
  }
})
Listener(route, () => {
  reset()
  handleSearch()
})
</script>