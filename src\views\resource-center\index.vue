<template>
  <div class="scroll-y">
    <el-tabs v-model="activeType" @tab-change="handleTabChange">
      <el-tab-pane label="文件管理" :name="3" />
      <el-tab-pane label="图片管理" :name="1" />
      <el-tab-pane label="视频管理" :name="2" />
    </el-tabs>
    <div style="height: calc(100% - 55px);" v-loading="switching">
      <template v-if="!switching">
        <ResourceCenter 
          :file-type="activeType"
          :cache-route="route"
          de-height="265px"
        />
      </template>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, nextTick } from 'vue'
import { useRoute } from 'vue-router'
import ResourceCenter from '@/components/ResourceCenter/index.vue'

defineOptions({ name: 'ResourceManagemnt' })

const route = useRoute()

const activeType = ref(3)
const switching = ref(false)

const handleTabChange = () => {
  switching.value = true
  nextTick(() => {
    switching.value = false
  })
}
</script>