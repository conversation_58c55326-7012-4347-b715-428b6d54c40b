export type listItem = {
  value: any;
  label?: string;
  start_placeholder?: string;
  end_placeholder?: string;
  range_separator?: string;
  placeholder?: string;
  component: string;
  clearable?: boolean;
  hideClearable?: boolean;
  labelKey?: string;
  valueKey?: string;
  selections?: {label?: string; value?: string|number|boolean; [propName: string]: string|number|boolean|undefined|any[]}[];
  dateChange?: (value: any) => any;
  handleChange?: (value: any) => any;
  visibleChange?: (value: boolean) => any;
  append?: boolean;
  data?: any[];
  props?: {
    [propName: string]: any;
  };
  multiple?: boolean;
  disabled?: boolean;
  field_list?: {label: string; value: string|number}[];
  field_key?: string;
  start_time?: string;
  end_time?: string;
  parse?: boolean;
  parseSymbol?: string;
}