import { reactive } from 'vue'
import type { FormRules } from 'element-plus'

export const formRules = reactive<FormRules>({
  title: [
    { required: true, message: '弹窗标题不能为空', trigger: 'blur' },
    { max: 200, message: '最多200个字符', trigger: 'blur'  }
  ],
  content: [
    { required: true, message: '正文内容不能为空', trigger: 'blur' },
    { max: 300, message: '最多300个字符', trigger: 'blur'  }
  ],
  button_txt: [
    { required: true, message: '弹窗按钮文案不能为空', trigger: 'blur' },
    { max: 50, message: '最多50个字符', trigger: 'blur'  }
  ],
  channel_id: [
    { required: true, message: '弹窗应用位置不能为空', trigger: 'change' },
  ]
})