<template>
  <el-dialog
    v-model="show"
    title="AI翻译结果"
    width="90%"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    append-to-body
    align-center
    @closed="emit('close')"
  >
    <template #default>
      <el-row :gutter="30">
        <el-col :span="12">
          <el-card shadow="never">
            <template #header>
              <strong style="font-size: 1.25rem;"> 原内容 </strong>
            </template>
            <template #default>
              <el-input type="textarea" v-model="raw" :rows="30" />
              <div class="text-center" style="padding-top: 30px;">
                <el-button type="primary" @click="handleExport(raw, '原内容')"> 导出HTML </el-button>
              </div>
            </template>
          </el-card>
        </el-col>
        <el-col :span="12">
          <el-card shadow="never">
            <template #header>
              <strong style="font-size: 1.25rem;"> 翻译内容 </strong>
            </template>
            <template #default>
              <el-input type="textarea" v-model="result" :rows="30" />
              <div class="text-center" style="padding-top: 30px;">
                <el-button type="primary" @click="handleExport(result, '翻译内容')"> 导出HTML </el-button>
              </div>
            </template>
          </el-card>
        </el-col>
      </el-row>
    </template>
    <template #footer>
      <div class="text-center">
        <el-button @click="show = false"> 关闭 </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref } from 'vue'

const show = ref(true)

const props = defineProps<
{
  raw: string;
  result: string;
}>()

const emit = defineEmits(['close'])

const raw = ref(props.raw)
const result = ref(props.result)

const handleExport = (content: string, title?: string) => {
  const a = document.createElement('a')

  a.setAttribute('download', `${title}.html`),
  a.setAttribute('href', `data:text/plain;charset=utf-8,${encodeURIComponent(content)}`)
  document.body.appendChild(a)
  a.click()
  document.body.removeChild(a)
}
</script>