<template>
  <el-dialog
    v-model="show"
    title="基础页面集内容新增"
    width="700px"
    @closed="() => emit('closed')"
    append-to-body
  >
    <el-form 
      ref="formRef"
      label-width="120"
      label-suffix=":"
      :model="addData"
      :rules="rules"
    >
      <el-form-item label="模板" prop="module">
        <el-select
          v-model="addData.module"
          placeholder="请选择"
          @change="() => (addData.type = '')"
        >
          <el-option 
            v-for="(d, index) in moduleList"
            :key="index"
            :label="d.label"
            :value="d.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="模板类型" prop="type">
        <el-select
          v-model="addData.type"
          placeholder="请选择"
          @change="handleTypeChange"
        >
          <el-option 
            v-for="(d, index) in moduleTypeList"
            :key="index"
            :label="d.label"
            :value="d.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="所选模板" prop="tpl_id">
        <el-space :size="20">
          <el-select
            v-model="addData.tpl_id"
            placeholder="请选择"
            :disabled="childChannel"
          >
            <el-option 
              v-for="(d, index) in tplList"
              :key="index"
              :label="d.name"
              :value="d.tpl_id"
            />
          </el-select>
          <el-checkbox v-model="childChannel" @change="handleChildChange">子渠道配置模式</el-checkbox>
        </el-space>
        <div style="color: var(--el-color-primary); line-height: 1; width: 90%;">
          创建页面集时，选择的是主渠道的模板；系统会根据主模板下属的子模板渠道，允许同时在主渠道和子渠道配置页面。
        </div>
      </el-form-item>
      <template v-if="childChannel">
        <el-form-item label="子渠道">
          <el-select
            v-model="addData.child_channel"
            placeholder="请选择"
            @change="handleChildChannelChange"
          >
            <el-option
              v-for="(d, index) in channelList"
              :key="index"
              :label="d.channel_code"
              :value="d.channel_id"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="子模板">
          <el-select
            v-model="addData.child_module"
            placeholder="请选择"
            @change="handleTypeChange"
          >
            <el-option
              v-for="(d, index) in childModulelList"
              :key="d.tpl_id"
              :label="d.name"
              :value="d.tpl_id"
            />
          </el-select>
        </el-form-item>
      </template>
      <el-form-item label="新增渠道" prop="channel_ids">
        <el-checkbox-group v-model="channelIds">
          <el-checkbox
            v-for="(d, index) in channelList"
            :key="index"
            :label="d.channel_id"
          >
            {{ d.channel_code }}
          </el-checkbox>
        </el-checkbox-group>
      </el-form-item>
    </el-form>
    <template #footer>
      <div class="text-center">
        <el-button @click="handleClose">取消</el-button>
        <el-button
          type="primary"
          :loading="loading"
          @click="handleConfirm(formRef)"
        >
          确认
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, reactive, computed } from 'vue'
import { ElMessage } from 'element-plus'
import { useParamsStore } from '@/store'
import useTryCatch from '@/hooks/use-try-catch'
import useLoading from '@/hooks/use-loading'
import { addPage } from '@/api/page-management'
import { getTemplateList } from '@/api/template-management'
import { addPageData, getChildModuleList } from '@/api/page-management'
import type { listItem } from '@/api/template-management'
import type { FormInstance, FormRules } from 'element-plus'

const emit = defineEmits(['confirm', 'closed'])
const props = defineProps<{
  module?: string;
  type?: string;
  tpl_id?: number;
  channel_id?: string;
}>()

const { site_id_all, channel_item_all, channelList, moduleList, moduleTypeMap } = useParamsStore()
const { loading, setLoading } = useLoading()

const validate = (condition: boolean, value: string, msg: string, callback: Function) => {
  if (condition) {
      return callback()
    }
    if (!value) {
      return callback(new Error(msg))
    }
    return callback()
}

const formRef = ref<FormInstance>()
const show = ref(true)
const channelIds = ref([])
const childChannel = ref(false)
const rules = reactive<FormRules>({
  module: [ { required: true, message: '模块不能为空' } ],
  type: [ { required: true, message: '模板类型不能为空' } ],
  tpl_id: [ { required: true, validator: (rule: any, value: any, callback: any) => 
    validate(childChannel.value, value, '模板不能为空', callback) } 
  ],
  channel_ids: [ { required: true, validator: (rule: any, value: any, callback: any) => {
    if (channelIds.value.length === 0) {
      return callback(new Error('渠道不能为空'))
    }
    callback()
  }} ],
  child_channel: [{ required: true, validator: (rule: any, value: any, callback: any) => 
    validate(!childChannel.value, value, '子渠道不能为空', callback) }
  ],
  child_module: [{ required: true, validator: (rule: any, value: any, callback: any) => 
    validate(!childChannel.value, value, '子模板不能为空', callback) }
  ]
})
const addData = reactive<addPageData>({
  site_id: 0,
  module: props.module || '',
  type: props.type || '',
  tpl_id: props.tpl_id || '',
  entity_id: '',
  channel_ids: '',
  child_channel: '',
  child_module: '',
})
const tplList = ref<listItem[]>([])
const childModulelList = ref<any[]>([])
const moduleTypeList = computed<{ value: string; label: string; }[]>(() => moduleTypeMap[addData.module] || [])

const getTplList = () => {
  useTryCatch( async () => {
    const params = {
      S: {
        tpl_ids: '',
        name: '',
        module: addData.module,
        type: addData.type,
        user_id_e: '',
        user_id_a: '',
        state: '',
        block_id: '',
        is_mainmodule: '',
        time_field: '',
        start_time: '',
        end_time: '',
        channel_id: `${ props.channel_id || (channel_item_all.channel_id ?  channel_item_all.channel_id : '')}`
      },
      page: 1,
      page_size: 1000,
      site_id: site_id_all
    }

    const res = await getTemplateList(params)
    if (res.code === 200) {
      tplList.value = res.data.items
    }
  } )
}

const handleTypeChange = () => {
  getTplList()
}
const handleChildChange = () => {
  addData.tpl_id = ''
}
const handleChildChannelChange = () => {
  addData.child_module = ''
  useTryCatch( async () => {
    const { child_channel, module, type } = addData 
    const res = await getChildModuleList(child_channel as string, { module, type })
    if (res.code === 200) {
      childModulelList.value = res.data
    }
  } )
}
const handleConfirm = ( formEl: FormInstance | undefined ) => {
  if (!formEl) return

  formEl?.validate((valid) => {
    if (valid) {
      useTryCatch( async () => {
        setLoading(true)
        const params = {
          ...addData,
          channel_ids: channelIds.value.join(',')
        }

        const res = await addPage(params)
        if (res.code === 200) {
          emit('confirm')
          ElMessage.success('添加页面集成功')
        } else {
          ElMessage.error(res.msg)
        }
        setLoading(false)
      }, () => setLoading(false) )
    }
  })
}
const handleClose = () => {
  show.value = false
}

defineExpose({
  show: () => (show.value = true),
  close: handleClose
})

if (props.module && props.type) {
  getTplList()
}
</script>