<template>
  <div class="navbar rowBC reset-el-dropdown custom-layout-box" :class="{ hide: route.name === 'Dashboard' }">
    
    <div class="rowSC">
      <div class="logo-container">
        <router-link to="/"><img src="@/assets/images/CompanyLogo.svg" class="logo-img" style="width: 184px" /></router-link>
      </div>
      <div class="site-channel-container">
        <SiteChannel />
      </div>
    </div>
    <!-- 下拉操作菜单 -->
    <div class="right-menu rowSC">
      <div class="nav-menu-item"><NoticeCenter /></div>
      <div class="nav-menu-item"><ThemeSelect /></div>
      <!-- <div class="nav-menu-item"><SizeSelect /></div> -->
      <div class="nav-menu-item"><User /></div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { useRoute } from 'vue-router'
import SiteChannel from './component/SiteChannel.vue'
import ThemeSelect from './component/ThemeSelect.vue'
import NoticeCenter from './component/NoticeCenter.vue'
import User from './component/User.vue'

const route = useRoute()
</script>

<style lang="scss" scoped>
.navbar {
  --border-color: rgba(0, 0, 0, 0.08);
  height: var(--nav-bar-height);
  position: relative;
  background: var(--nav-bar-background);
  border-bottom: 1px solid var(--border-color);
  z-index: 1;
  padding-left: 12px;
  .logo-container {
    width: var(--side-bar-width);
  }
  &.hide {
    --border-color: transparent;
    background-color: transparent;
    .site-channel-container {
      opacity: 0;
      pointer-events: none;
    }
  }
}
.dark .logo-container {
  filter: invert(1);
}

//drop-down
.right-menu {
  cursor: pointer;
  margin-right: 16px;
}
.nav-menu-item {
  display: inline-block;
  margin-left: 16px;
}
</style>
