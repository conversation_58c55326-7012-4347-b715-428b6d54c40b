// 审核中心模块接口
import axiosReq from './axios'
import qs from 'query-string'
import { filterEmptyKey } from '@/utils/common'

export type searchParams = {
  site_id: string|number;
  channel_id?: string|number;
  type?: string;  //业务类型，目前支持：1发布系统，2写手平台，其他暂时没有
  name?: string;   //标题名称
  status?: string|number;    //流程状态：1草稿、2流程中、3已通过、4已拒绝
  user_id?: number;
  user_name?: string;  //申请人名称
  handle_user_name?: string;  //审核人名称
  start_time?: string;
  end_time?: string;
  task_id?: string;
  class?: number;   //1自动审核，2人工审核~~~~}
  group?: string;  //pending待处理，done已处理
  task_ids?: string;
  from?: string;
}

export type listQueryParams = {
  q: searchParams,
  page?: number;
  limit?: number;
}

export type statQueryParams = {
  q: searchParams
}

export type addData = {
  template_id: string | number,
  site_id: number,
  site_name: string,
  channel_id: number | string,
  channel_name: string,
}

export type configData = {
  template_id: number|string;
  entity_id: number|string;
  notice_type: string;
  nodes: {
    node_id?: string|number;
    ccs: {name: string; user_id: string|number}[];
    handlers?: {name: string; user_id: string|number}[];
  }[]
}

export type listItem = {
  id: number;
  task_id: string;
  type_name: string;
  entity_id: string;
  entity_name: string;
  name: string;
  channel_name: string;
  handle_name: string;
  status: number;
  user_name: string;
  user_id: string;
  add_time: string;
  status_name?: string;
}

export type nodeUser = {
  node_id?: number;
  user_id: number|string;
  name: string;
}

export type configNode = {
  node_id: number; // 节点id
  type: number;  // 节点类型：1申请人节点、2审批节点、3填写节点、4结束节点
  handlers?: nodeUser[];
  ccs?: nodeUser[];
}

export type configDetail = {
  entity_id: string;
  template_id: number;
  site_id: number;
  site_name: string;
  channel_id: number;
  channel_name: string;
  name: string;
  type: number; // 流程所属业务：1cms、2写手平台
  notice_type: string; // 流程节点通知方式，逗号分割，1邮件，2短信，3teams
  status: number;
  remark: string;
  is_super: number;
  nodes: configNode[];
}

export type logNode = {
  node_id?: number;
  id?: number;
  user_id: string;
  type: number;
  type_name: string;
  operate: string;
  content: string;
  add_time?: string;
  remark?: string;
  name?: string;
}

export type taskDetail = {
  task_id?: string;
  type?: number;         //1草稿、2流程中、3已通过、4已拒绝
  type_name?: string;
  entity_name?: string;
  name?: string;
  content?: string;
  status?: number|string;
  channel_name?: string;
  status_name?: string;
  user_id?: string;
  user_name?: string;
  add_time?: string;
  remark?: string;
  is_super?: number;
  logs?: logNode[];
  current_node?: logNode;
}

// 获取审核中心列表
export const getAuditList = (params: listQueryParams) => 
  axiosReq('get', '/api/v1/audit/task_list', {
    params,
    paramsSerializer: {
      serialize: (obj: listQueryParams) => qs.stringify({
        q: JSON.stringify(filterEmptyKey(obj.q)),
        page: obj.page,
        limit: obj.limit
      })
    }
  })

// 获取审核任务数
export const getStat = (params: statQueryParams) => 
  axiosReq('get', '/api/v1/audit/task_statistic', {
    params,
    paramsSerializer: {
      serialize: (obj: statQueryParams) => qs.stringify({
        q: JSON.stringify(obj.q),
      })
    }
  })

// 获取任务详情
export const getTaskDetail = (task_id: string) => 
  axiosReq('get', `/api/v1/audit/task_detail?task_id=${task_id}`)

// 获取业务配置列表
export const getConfigList = (params: listQueryParams) => 
  axiosReq('get', '/api/v1/audit/list', {
    params,
    paramsSerializer: {
      serialize: (obj: listQueryParams) => qs.stringify({
        q: JSON.stringify(filterEmptyKey(obj.q)),
        page: obj.page,
        limit: obj.limit
      })
    }
  })

// 获取任务配置详情
export const getConfigDetail = (entity_id: string) => 
  axiosReq('get', `/api/v1/audit/detail?entity_id=${entity_id}`)

// 业务配置启用
export const enableConfig = (data: { entity_id: string }) => 
  axiosReq('post', '/api/v1/audit/enable', data)

// 业务配置禁用
export const disableConfig = (data: { entity_id: string }) => 
  axiosReq('post', '/api/v1/audit/disable', data)

// 添加业务配置
export const addConfig = (data: addData) =>
  axiosReq('post', '/api/v1/audit/add', data)

// 编辑业务配置
export const editConfig = (data: configData) =>
  axiosReq('post', '/api/v1/audit/update', data)

// 获取申请列表
export const getApplyList = (params: listQueryParams) => 
  axiosReq('get', '/api/v1/audit/task_apply', {
    params,
    paramsSerializer: {
      serialize: (obj: listQueryParams) => qs.stringify({
        q: JSON.stringify(filterEmptyKey(obj.q)),
        page: obj.page,
        limit: obj.limit
      })
    }
  })


// 我的审核列表
export const getCheckList = (params: listQueryParams) => 
  axiosReq('get', '/api/v1/audit/task_handle', {
    params,
    paramsSerializer: {
      serialize: (obj: listQueryParams) => qs.stringify({
        q: JSON.stringify(filterEmptyKey(obj.q)),
        page: obj.page,
        limit: obj.limit
      })
    }
  })

// 审核任务
export const taskUpdate = (data: { task_ids: string; operate: string; content?: string; }) => 
  axiosReq('post', '/api/v1/audit/task_update', data)

// 撤销审核
export const taskRevoke = (data: { task_ids: string }) => 
  axiosReq('post', '/api/v1/audit/task_cancel', data)

// 首页审核数据
export const getAuditData = (site_id: number) => 
  axiosReq('get', `/api/v1/users/user_todo_num?site_id=${site_id}`)