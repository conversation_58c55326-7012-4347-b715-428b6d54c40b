<template>
  <div class="scroll-y with-flex">
    <QueryForm 
      :loading="loading"
      :form-list="formList"
      @search="handleSearch"
      @reset="reset"
    />
    <div v-loading="loading" class="sticky-table">
      <CustomTable
        ref="tableRef"
        :columns="columns"
        :data="tableData"
        :table-method="getList"
        :customize-head="true"
        :selectable="true"
        :operate-list="operateList"
        @selection-change="handleSelectionChange"
        @sort-change="handleSortChange"
      />
    </div>

    <StartTaskModal 
      v-if="loadStartTaskModal"
      :entity_ids="tpl_page_ids"
      @close="loadStartTaskModal = false"
    />

    <RelatedArticle 
      v-if="loadRelart"
      ref="relartRef"
      :channel-id="channel_item_all.channel_id"
      :classify-data="classifyData"
      :author-list="authorList"
      @confirm="handleRelartConfirm"
    />
  </div>
</template>

<script setup lang="tsx">
import { ref, reactive, toRef, computed, onActivated, h } from 'vue'
import { 
  ElMessage, 
  ElMessageBox, 
  ElLink, 
  ElSwitch, 
  ElButton, 
  ElDropdown, 
  ElDropdownMenu, 
  ElDropdownItem, 
  ElSelect, 
  ElOption, 
  ElTreeSelect,
  ElRadio
} from 'element-plus'
import { useRoute, useRouter } from 'vue-router'
import { storeToRefs } from 'pinia'
import { useParamsStore, useAsyncMethodStroe, useUserStroe } from '@/store'
import { useBasicStore } from '@/store/basic'
import useTryCatch from '@/hooks/use-try-catch'
import useLoading from '@/hooks/use-loading'
import { usePageGenerate } from '@/hooks/use-generate'
import { 
  getArticleList, 
  getClassifyData, 
  getAuthorList, 
  changeSort, 
  handleRecomHot, 
  getSideBlockList,
  BatchEditAuthor,
  BatchEditClassify,
  BatchEditSideBlock,
  changeRelArt
} from '@/api/article-management'
import { changeStatus } from '@/api/page-management'
import type { queryParams, listItem, classifyTreeItem, authorListItem } from '@/api/article-management'
import Listener from '@/utils/site-channel-listeners'
import { exportDownload } from './components/export'

import QueryForm from '@/components/QueryForm/index.vue'
import CustomTable from '@/components/CustomTable/index.vue'
const StartTaskModal = defineAsyncComponent( () => import('@/views/translate-management/components/startTaskModal.vue') )
const RelatedArticle = defineAsyncComponent( () => import('./operate/components/relatedArticle.vue') )

defineOptions( { name: 'ArticleManageList' } )

const route = useRoute()
const router = useRouter()
const { site_id_all, channel_item_all, userList } = storeToRefs(useParamsStore())
const { artPageStateList, page_state_map } = useParamsStore()
const { handleGlobalPageCheck } = useAsyncMethodStroe()
const { banIds, isBanned } = useUserStroe()
const basicStore = useBasicStore()
const { loading, setLoading } = useLoading()
const loadRelart = ref(false)
const relartRef = ref()

const queryParams_raw = {
  user_id_a: '',
  user_id_e: '',
  author_id: '',
  cat_id: '',
  page_state: '',
  art_ids: '',
  title: '',
  page_url: '',
  art_group_id: '',
  content: '',
  is_recom: '',
  is_hot: '',
  page_id: '',
  tpl_id: '',
}
const isList = [
  {
    label: '是',
    value: '1',
  },
  {
    label: '否',
    value: '0',
  }
]
const queryParams = reactive<queryParams>({
  ...queryParams_raw,
  art_ids: route.query.entity_ids as string || '',
  site_id: 0,
  channel_id: 0,
  page: 1,
  page_size: 10,
})
const classifyData = ref<classifyTreeItem[]>([])
const authorList = ref<authorListItem[]>([])
const blockSideList = ref<{ blk_id: number, blk_name: string }[]>([])
const initClassifyData = () => {
  if (classifyData.value.length === 0) {
    useTryCatch( async () => {
      const res = await getClassifyData(site_id_all.value, channel_item_all.value.channel_id)
      if (res.code === 200) {
        classifyData.value = res.data
      }
    } )
  }
}
if (route.query.cat_id) {
  queryParams.cat_id = [ +route.query.cat_id ] as any
  initClassifyData()
}
if (route.query.all_child) {
  queryParams.all_child = route.query.all_child as string
}
const formList = ref([
  {
    label: '文章ID',
    placeholder: '文章ID',
    value: toRef(queryParams, 'art_ids'),
    component: 'input',
    parse: true
  },
  {
    label: '文章标题',
    placeholder: '文章标题',
    value: toRef(queryParams, 'title'),
    component: 'input',
  },
  {
    label: '页面状态',
    placeholder: '页面状态',
    value: toRef(queryParams, 'page_state'),
    component: 'select',
    selections: artPageStateList,
    multiple: true
  },
  {
    label: '关联页面ID',
    placeholder: '关联页面ID',
    value: toRef(queryParams, 'page_id'),
    component: 'input',
    parse: true
  },
  {
    label: '模板ID',
    placeholder: '模板ID',
    value: toRef(queryParams, 'tpl_id'),
    component: 'input',
    append: false,
    parse: true
  },
  {
    label: '页面url',
    placeholder: '页面url,逗号分隔',
    value: toRef(queryParams, 'page_url'),
    component: 'textarea',
    append: false,
  },
  {
    label: '文章类别',
    placeholder: '文章类别',
    value: toRef(queryParams, 'cat_id'),
    component: 'tree-select',
    append: true,
    valueKey: 'id',
    props: { label: 'label', children: 'children' },
    visibleChange: (val: boolean) => {
      queryParams.all_child = '1'
      val && initClassifyData()
    },
    data: classifyData
  },
  {
    label: '更新人',
    placeholder: '更新人',
    value: toRef(queryParams, 'user_id_e'),
    component: 'select',
    labelKey: 'real_name',
    valueKey: 'user_id',
    append: true,
    selections: userList
  },
  {
    label: '创建人',
    placeholder: '创建人',
    value: toRef(queryParams, 'user_id_a'),
    component: 'select',
    labelKey: 'real_name',
    valueKey: 'user_id',
    append: true,
    selections: userList
  },
  {
    label: '文章作者',
    placeholder: '文章作者',
    value: toRef(queryParams, 'author_id'),
    component: 'select',
    append: true,
    labelKey: 'name',
    valueKey: 'author_id',
    visibleChange: (val: boolean) => {
      if (val && authorList.value.length === 0) {
        handleGetAuthorList()
      }
    },
    selections: authorList
  },
  {
    label: '推荐',
    placeholder: '是否推荐',
    value: toRef(queryParams, 'is_recom'),
    component: 'select',
    append: true,
    selections: isList
  },
  {
    label: '热门',
    placeholder: '是否热门',
    value: toRef(queryParams, 'is_hot'),
    component: 'select',
    append: true,
    selections: isList
  },
  {
    label: '文章内容',
    placeholder: '文章内容',
    value: toRef(queryParams, 'content'),
    component: 'input',
    append: true,
  },
])
const columns = ref([
  {
    title: '文章ID',
    dataKey: 'art_id',
    width: 100,
    sortable: true
  },
  {
    title: '文章标题',
    dataKey: 'title',
    minWidth: 400,
  },
  {
    title: '关联页面URL',
    dataKey: 'page_url',
    minWidth: 300,
    hideColumn: true,
  },
  {
    title: '关联页面ID',
    dataKey: 'page_id',
    width: 100,
    cellRenderer: (scope: { row: listItem }) => (
      <span class='with-hand' onClick={() => handleToPageList(scope.row.page_id)}>
        { scope.row.page_id }
      </span>
    )
  },
  {
    title: '关联所属模板ID',
    dataKey: 'tpl_id',
    width: 130,
    cellRenderer: (scope: { row: listItem }) => (
      <span class='with-hand' onClick={() => handleToTempList(scope.row.tpl_id)}>
        { scope.row.tpl_id }
      </span>
    )
  },
  {
    title: '所属渠道',
    dataKey: 'channel_name',
    width: 115,
    hideColumn: true,
    cellRenderer: ( scope: { row: listItem } ) => (<span class='dot' style={ { '--color': scope.row.color } }>{ scope.row.channel_name }</span>)
  },
  {
    title: '文章简介',
    dataKey: 'summary',
    minWidth: 100,
  },
  {
    title: '排序',
    dataKey: 'sort',
    width: 70,
    hideColumn: true,
    cellRenderer: ( scope: { row: listItem } ) => (
      <ElLink type='primary' onClick={() => handleSortUpdate(scope.row)}>
        { scope.row.sort }
      </ElLink>
    )
  },
  {
    title: '推荐',
    dataKey: 'is_recom',
    width: 70,
    hideColumn: true,
    cellRenderer: ( scope: { row: listItem } ) => (
      <ElSwitch 
        modelValue={scope.row.is_recom}
        size='small'
        activeValue='1'
        inactiveValue='0'
        onChange={(val) => {
          handleUpdateStatus(scope.row, 'is_recom', val as string)
        }}
      />
    )
  },
  {
    title: '热门',
    dataKey: 'is_hot',
    width: 70,
    hideColumn: true,
    cellRenderer: ( scope: { row: listItem } ) => (
      <ElSwitch 
        modelValue={scope.row.is_hot}
        size='small'
        activeValue='1'
        inactiveValue='0'
        onChange={(val) => {
          handleUpdateStatus(scope.row, 'is_hot', val as string)
        }}
      />
    )
  },
  {
    title: '页面状态',
    dataKey: 'page_state_name',
    width: 100,
    cellRenderer: (scope: { row: listItem }) => (
      <span style={ { 
        color: page_state_map[scope.row.page_state]?.color
      } }>
        { scope.row.page_state_name || page_state_map[scope.row.page_state]?.text }
      </span>
    )
  },
  {
    title: '文章创建人',
    dataKey: 'user_name_a',
    width: 100,
  },
  {
    title: '创建时间',
    dataKey: 'add_time',
    width: 180,
    sortable: true
  },
  {
    title: '文章更新人',
    dataKey: 'user_name_e',
    width: 100,
  },
  {
    title: '更新时间',
    dataKey: 'edit_time',
    width: 180,
    hideColumn: true,
    sortable: true
  },
  {
    title: '操作',
    dataKey: '',
    fixed: 'right',
    width: 300,
    cellRenderer: (scope: { row: listItem }) => (
      <>
        <ElButton type='primary' plain onClick={() => handleEdit(scope.row)}>编辑</ElButton>
        <ElButton type='primary' plain disabled={!scope.row.page_url} onClick={() => handlePreview(scope.row)}>预览</ElButton>
        <ElButton type='success' 
          plain 
          disabled={!scope.row.page_id || scope.row.page_state === 'disable'} 
          onClick={() => handleGeneratePage(scope.row)}
          v-RoleIdsBanPermission={banIds}
        >生成页面</ElButton>
        <ElDropdown trigger='click' style={{ marginLeft: '10px' }}>
          {
            {
              default: () => <ElButton type='primary' link> <strong>...</strong> </ElButton>,
              dropdown: () => 
              <ElDropdownMenu>
                <ElDropdownItem>
                  <ElButton type='primary' plain onClick={() => handleEdit(scope.row, 'copy')}>复制文章</ElButton>
                  <ElButton type='primary' plain onClick={() => handleHistory(scope.row)}>历史版本</ElButton>
                  {
                    scope.row.page_url && 
                    <>
                      <ElButton type='primary' plain>
                        <a target='_blank' href={scope.row.online_url}>线上地址</a>  
                      </ElButton>
                      <ElButton type={scope.row.page_state === 'disable' ? 'success' : 'danger'} plain onClick={() => handleOperate({ row: scope.row })}>
                        { scope.row.page_state === 'disable' ? '启用' : '禁用' }  
                      </ElButton>
                    </>
                  }
                </ElDropdownItem>
              </ElDropdownMenu>
            }
          }
        </ElDropdown>
      </>
    )
  }
])
const tableRef = ref()
const tableData = ref<listItem[]>([])
const tableSelections = ref<listItem[]>([])
const disabled = computed(() => tableSelections.value.length === 0)
const disableTooltip = computed(() => channel_item_all.value.channel_id ? '' : '请先选择渠道')
const selectedIds = computed(() => tableSelections.value.map(( { art_id } ) => art_id).join(','))
const tpl_page_ids = ref('')
const operateList = ref([
  {
    title: '创建文章',
    button: true,
    append: true,
    tooltip: disableTooltip,
    method: () => {
      basicStore.delCachedView('ArticleManageOperate')
      router.push({
        name: 'ArticleManageOperate',
        query: {
          type: 'add',
        }
      })
    },
    disabled: computed<boolean>(() => channel_item_all.value.channel_id ? false : true)
  },
  {
    title: '导出EXCEL',
    icon: 'export',
    method: () => {
      const { page, pageSize, total } = tableRef?.value?.getPagination()
      if ( !selectedIds.value && total > 5000) {
        return ElMessageBox.confirm(
          '当前导出数据超过5000条,超出最大范围限制,请控制到5000条以内后再进行导出',
          '提示',
          {
            callback: () => {}
          }
        )
      }
      const exportCsv = () => exportDownload('csv', selectedIds.value || queryParams.art_ids, {
        ...queryParams,
        cat_id: String(queryParams.cat_id),
        page_url: queryParams.page_url.trim().replace(/\s+/g, ','),
        site_id: site_id_all.value,
        channel_id: channel_item_all.value.channel_id as number || '',
        page: page || 1,
        page_size: pageSize || 10,
        page_state: String(queryParams.page_state)
      })
      if (selectedIds.value) {
        exportCsv()
      } else {
        ElMessageBox.confirm(
          '导出当前查询条件下所有文章',
          '提示',
          {
            confirmButtonText: '确认',
            cancelButtonText: '取消',
            callback: (action: string) => {
              if (action === 'confirm') {
                exportCsv()
              }
            }
          }
        )
      }
    },
  },
  {
    title: '导出HTML',
    icon: 'export',
    method: () => {
      const { page, pageSize } = tableRef?.value?.getPagination()
      exportDownload('html', selectedIds.value, {
        ...queryParams,
        cat_id: String(queryParams.cat_id),
        page_url: queryParams.page_url.trim().replace(/\s+/g, ','),
        site_id: site_id_all.value,
        channel_id: channel_item_all.value.channel_id as number || '',
        page: page || 1,
        page_size: pageSize || 10,
        page_state: String(queryParams.page_state)
      })
    },
    disabled: disabled
  },
  ...isBanned() ? [] : [
    {
      title: '生成选中项',
      icon: 'generate',
      method: () => {
        const pageArr = tableSelections.value.filter(( { page_id } ) => !!page_id )
        if (pageArr.length === 0) {
          return ElMessage.warning('所选中文章均未绑定页面,请勾选已绑定页面文章进行生成')
        }
        usePageGenerate({
          tpl_page_ids: pageArr.map(( { page_id } ) => page_id ).join(','),
          site_id: site_id_all.value
        }, refresh)
      },
      disabled: disabled
    }
  ],
  {
    title: '禁用选中项',
    icon: '',
    method: () => {
      const pageArr = tableSelections.value.filter(( { page_id } ) => !!page_id )
      if (pageArr.length === 0) {
        return ElMessage.warning('所选中文章均未绑定页面,请勾选已绑定页面文章进行禁用')
      }
      handleOperate( { state: 'able', page_ids: pageArr.map(( { page_id } ) => page_id ).join(',') } )
    },
    disabled: disabled
  },
  {
    title: '批量修改分类',
    method: () => {
      if (classifyData.value.length === 0) {
        initClassifyData()
      }
      const cat_id = ref()
      ElMessageBox({
        title: '批量修改文章分类',
        showConfirmButton: true,
        showCancelButton: true,
        confirmButtonText: '确认',
        cancelButtonText: '取消',
        message: () => h('div', [
          h('p', {
            style: { marginBottom: '12px' },
            innerText: '将统一更新选中的文章的分类更新为：'
          }),
          h(ElTreeSelect, {
            placeholder: '文章分类',
            showCheckbox: true,
            checkOnClickNode: false,
            checkStrictly: true,
            valueKey: 'id',
            props: { label: 'label', children: 'children' },
            class: 'custom-tree',
            data: classifyData.value,
            modelValue: cat_id.value,
            filterable: true,
            onCheck: (value) => {
              cat_id.value = {...value}
              setTimeout(() => {
                const checkedEl = document.querySelector('.custom-tree .el-checkbox__input.is-checked')
                const el = document.querySelector('.custom-tree .el-checkbox__input.is-focus')

                if (el) {
                  checkedEl && checkedEl.classList.remove('is-checked')
                  el.classList.add('is-checked')
                }
                
              }, 0)
            }
          })
        ]),
        callback: (action: string) => {
          if (action === 'confirm') {
            if (!cat_id.value) {
              ElMessage.warning('请先选文章分类')
              return
            }
            useTryCatch( async () => {
              const res = await BatchEditClassify({ art_ids: selectedIds.value.split(','), cat_id: cat_id.value.id })
              if (res.code === 200) {
                ElMessage.success(res.msg)
                refresh()
              } else {
                ElMessage.error(res.msg)
              }
            } )
          }
        }
      })
    },
    disabled: computed( () => disabled.value || !channel_item_all.value.channel_id)
  },
  {
    title: '批量修改侧边栏',
    method: () => {
      if (blockSideList.value.length === 0) {
        getSideBlkList()
      }
      const blk_id = ref<string|number>('')
      ElMessageBox({
        title: '批量修改侧边栏',
        showConfirmButton: true,
        showCancelButton: true,
        confirmButtonText: '确认',
        cancelButtonText: '取消',
        message: () => h('div', [
          h('p', {
            style: { marginBottom: '12px' },
            innerText: '将统一更新选中的文章的侧边栏目块更新为：'
          }),
          h(ElSelect, {
            placeholder: '侧边栏目块',
            modelValue: blk_id.value,
            filterable: true,
            onChange: (value) => blk_id.value = value
          }, () => blockSideList.value.map( ( { blk_id, blk_name } ) => h(ElOption, {
            label: blk_name,
            value: blk_id
          }) ) ),
          h('p', { 
            style: { color: 'var(--el-color-primary)', marginTop: '12px' },
            innerText: '侧边栏展示为主渠道的侧边栏目块，子渠道文章只需选择主渠道块，生成页面时会自动调用对应的子渠道块'
          })
        ]),
        callback: (action: string) => {
          if (action === 'confirm') {
            if (blk_id.value === '') {
              ElMessage.warning('请先选择侧边栏目块')
              return
            }
            useTryCatch( async () => {
              const res = await BatchEditSideBlock({ art_id: selectedIds.value, side_block: blk_id.value })
              if (res.code === 200) {
                ElMessage.success(res.msg)
                refresh()
              } else {
                ElMessage.error(res.msg)
              }
            } )
          }
        }
      })
    },
    disabled: disabled
  },
  {
    title: '批量修改作者',
    method: () => {
      if (authorList.value.length === 0) {
        handleGetAuthorList()
      }
      const author_id = ref<string|number>('')
        ElMessageBox({
        title: '批量修改侧边栏',
        showConfirmButton: true,
        showCancelButton: true,
        confirmButtonText: '确认',
        cancelButtonText: '取消',
        message: () => h('div', [
          h('p', {
            style: { marginBottom: '12px' },
            innerText: '将统一更新选中的文章的作者更新为：'
          }),
          h(ElSelect, {
            placeholder: '请选择作者',
            modelValue: author_id.value,
            filterable: true,
            onChange: (value) => author_id.value = value
          }, () => authorList.value.map( ( { author_id, name } ) => h(ElOption, {
            label: name,
            value: author_id
          }) ) ),
        ]),
        callback: (action: string) => {
          if (action === 'confirm') {
            if (!author_id.value) {
              ElMessage.warning('请先选择作者')
              return
            }
            useTryCatch( async () => {
              const res = await BatchEditAuthor({ art_id: selectedIds.value, author_id: author_id.value })
              if (res.code === 200) {
                ElMessage.success(res.msg)
                refresh()
              } else {
                ElMessage.error(res.msg)
              }
            } )
          }
        }
      })
    },
    disabled: disabled
  },
  {
    title: '批量修改热门',
    method: () => {
      const is_hot = ref('0')
      ElMessageBox({
        title: '批量修改热门',
        showConfirmButton: true,
        showCancelButton: true,
        confirmButtonText: '确认',
        cancelButtonText: '取消',
        message: () => h('div', [
          h('p', {
            style: { marginBottom: '12px' },
            innerText: '将所选中的文章热门统一修改为：'
          }),
          h('div', [
            h('span', { style: { paddingRight: '24px' } }, '开启热门:'),
            h(ElRadio, {
              modelValue: is_hot.value,
              label: '0',
              onChange: () => is_hot.value = '0'
            }, () => '否'),
            h(ElRadio, {
              modelValue: is_hot.value,
              label: '1',
              onChange: () => is_hot.value = '1'
            }, () => '是')
          ])
        ]),
        callback: (action) => {
          if (action === 'confirm') {
            useTryCatch( async () => {
              const res = await handleRecomHot({ art_id: selectedIds.value, type: 'is_hot', status: is_hot.value })
              if (res.code === 200) {
                ElMessage.success(res.msg)
                refresh()
              } else {
                ElMessage.error(res.msg)
              }
            } )
          }
        }
      })
    },
    disabled: disabled
  },
  {
    title: '批量修改推荐',
    method: () => {
      const is_recom = ref('0')
      ElMessageBox({
        title: '批量修改推荐',
        showConfirmButton: true,
        showCancelButton: true,
        confirmButtonText: '确认',
        cancelButtonText: '取消',
        message: () => h('div', [
          h('p', {
            style: { marginBottom: '12px' },
            innerText: '将所选中的文章推荐统一修改为：'
          }),
          h('div', [
            h('span', { style: { paddingRight: '24px' } }, '开启推荐:'),
            h(ElRadio, {
              modelValue: is_recom.value,
              label: '0',
              onChange: () => is_recom.value = '0'
            }, () => '否'),
            h(ElRadio, {
              modelValue: is_recom.value,
              label: '1',
              onChange: () => is_recom.value = '1'
            }, () => '是')
          ])
        ]),
        callback: (action) => {
          if (action === 'confirm') {
            useTryCatch( async () => {
              const res = await handleRecomHot({ art_id: selectedIds.value, type: 'is_recom', status: is_recom.value })
              if (res.code === 200) {
                ElMessage.success(res.msg)
                refresh()
              } else {
                ElMessage.error(res.msg)
              }
            } )
          }
        }
      })
    },
    disabled: disabled
  },
  {
    title: '发起翻译',
    tooltip: disableTooltip,
    method: () => {
      if (tableSelections.value.length > 100) {
        return ElMessage.warning('单次发起翻译任务, 选择页面不超过100条, 请修改后重新发起')
      }
      const page_ids = new Set<number>()
      const art_ids = new Set<number>()
      tableSelections.value.forEach( function ( { art_id, page_id } ) {
        if (page_id) {
          page_ids.add(page_id)
        } else {
          art_ids.add(art_id)
        }
      } )
      tpl_page_ids.value = [...page_ids].join(',')
      if (tpl_page_ids.value === '') {
        return ElMessage.warning('所选中文章均未绑定页面, 请先绑定页面后再进行翻译操作')
      }
      loadStartTaskModal.value = true
      art_ids.size > 0 && ElMessage.warning(`文章id为: ${[...art_ids].join(',')}的文章未创建页面, 无法翻译`)
    },
    disabled: computed(() => !channel_item_all.value.channel_id || tableSelections.value.length === 0)
  },
  {
    title: '批量修改相关文章',
    method: () => {
      loadRelart.value = true
      relartRef.value?.show()
    },
    disabled: computed( () => disabled.value || !channel_item_all.value.channel_id)
  }
])
const loadStartTaskModal = ref(false)

const reset = () => {
  classifyData.value = []
  authorList.value = []
  Object.entries(queryParams_raw).forEach(([ key, value ]) => {
    queryParams[key] = value
  })
}
const refresh = () => {
  const { page, pageSize } = tableRef?.value?.getPagination()
  getList( { page, pageSize } )
}
const handleSearch = () => {
  tableRef.value?.setPage(1)
  refresh()
}
const getList = ( { page, pageSize }, prop?: string, order?: 'asc'|'desc' ) => {
  if (tableRef?.value) {
    const sortObj = tableRef?.value?.getSort()
    if (sortObj.order && !order) {
      prop = sortObj.prop
      order = sortObj.order
    }
  }
  
  useTryCatch( async () => {
    setLoading(true)

    const params = {
      ...queryParams,
      site_id: site_id_all.value,
      channel_id: channel_item_all.value.channel_id as number,
      page,
      page_size: pageSize,
      is_hot: queryParams.is_hot || '-1',
      is_recom: queryParams.is_recom || '-1',
      cat_id: String(queryParams.cat_id),
      ...order ? {
        sort_key: prop,
        sort_type: order
      } : {},
      page_state: String(queryParams.page_state)
    }

    const res = await getArticleList(params)
    if (res.code === 200) {
      tableData.value = res.data.items
      tableRef.value?.setTotal(res.data.total)
    } else {
      ElMessage.error(res.msg)
    }
    setLoading(false)
  }, () => setLoading(false) )
}
const getSideBlkList = () => {
  useTryCatch( async () => {
    const channel_id_real = channel_item_all.value?.channel_id || ''
    const res = await getSideBlockList(channel_id_real)
    if (res.code === 200) {
      blockSideList.value = [ { blk_name: '删除侧边栏', blk_id: 0 } ].concat(res.data.list)
    } 
  } )
}
const handleGetAuthorList = () => {
  useTryCatch( async () => {
    const params = {
      S: {
        language: '',
        author_id: '',
        site_id: site_id_all.value
      },
      page: 1,
      page_size: 1000
    }

    const res = await getAuthorList(params)
    if (res.code === 200) {
      authorList.value = res.data.items
    }
  } )
}
const handleSelectionChange = (selections: listItem[]) => {
  tableSelections.value = selections
}

// table-methods
const handleSortUpdate = (row: listItem) => {
  ElMessageBox.prompt(
    '请输入顺序值',
    '修改分类排序',
    {
      inputErrorMessage: '只能是0或者正整数',
      inputPattern: /^\d+$/,
      inputType: 'number',
      callback: (action: any) => {
        if (action.action === 'confirm') {
          useTryCatch( async () => {
            const params = {
              art_id: row.art_id,
              sort: +action.value
            }

            const res = await changeSort(params)
            if (res.code === 200) {
              row.sort = params.sort
              ElMessage.success('修改分类排序成功')
            } else {
              ElMessage.error(res.msg)
            }
          } )
        }
      }
    }
  )
}
const handleUpdateStatus = (row: listItem, type: 'is_hot'|'is_recom', val: string) => {
  useTryCatch( async () => {
    const params = {
      art_id: `${row.art_id}`,
      type,
      status: val
    }

    const res = await handleRecomHot(params)
    if (res.code === 200) {
      row[type] = val
      ElMessage.success('修改成功')
    } else {
      ElMessage.error(res.msg)
    }
  } )
}
const handleEdit = (row: listItem, type = 'edit') => {
  basicStore.delCachedView('ArticleManageOperate')
  router.push({
    name: 'ArticleManageOperate',
    query: {
      type,
      art_id: row.art_id,
      channel_id: row.channel_id,
      page_preview_url: row.page_preview_url
    }
  })
}
const handlePreview = (row: listItem) => {
  window.open(`${row.page_preview_url}?site_id=${site_id_all.value}`, '_blank')
  handleGlobalPageCheck(row.tpl_id, row.page_id, site_id_all.value)
}
const handleGeneratePage = (row: listItem) => {
  usePageGenerate({
    tpl_page_ids: `${row.page_id}`,
    site_id: site_id_all.value
  }, refresh)
}
const handleHistory = (row: listItem) => {
  basicStore.delCachedView('ArticleHistory')
  router.push({
    name: 'ArticleHistory',
    query: {
      art_id: row.art_id
    }
  })
}
const handleOperate = ({ row, state = '', page_ids = '' }: { row?: listItem; state?: string; page_ids?: string; }) => {
  state = row?.state || state
  page_ids = page_ids || `${row?.page_id}`
  const page_state = row?.page_state || state
  const isDisable = page_state === 'disable' ? true : false
  ElMessageBox.confirm(
    isDisable 
    ? `请确认是否启用${row ? '该页面' : '选中项'}` 
    : `<p>此操作会将${row ? '该' : '已选中'}页面文件, 实时从线上下架, 请确认清楚, 当前是否要下架线上页面</p>
      <div style="color: var(--el-color-danger)">Tips: 如页面等级为A级, 需要审核通过后, 才会自动发布（下架任务）</div>
    `,
    '提示',
    {
      dangerouslyUseHTMLString: true,
      confirmButtonText: isDisable ? '确认' : '下架页面',
      cancelButtonText: '取消',
      confirmButtonClass: isDisable ? '' : 'el-button--danger',
      callback: (action: string) => {
        if (action === 'confirm') {
          useTryCatch( async () => {
              const res = await changeStatus({ state: isDisable ? 'able' : 'disable', page_ids, site_id: site_id_all.value })
              if (res.code === 200) {
                refresh()
                ElMessage.success('操作成功')
              } else {
                ElMessage.error(res.msg)
              }
          } )
        }
      }
    }
  )
}
const handleToPageList = (page_id: number) => {
  basicStore.delCachedView('PageManagement')
  router.push(
    {
      name: 'PageManagement',
      query: {
        entity_ids: page_id
      }
    }
  )
}
const handleToTempList = (tpl_id: number) => {
  basicStore.delCachedView('TemplateManage')
  router.push(
    {
      name: 'TemplateManage',
      query: {
        tpl_id
      }
    }
  )
}
const handleSortChange = ({ prop, order }: { prop: string; order: 'ascending'|'descending'|null }) => {
  const { page, pageSize } = tableRef?.value?.getPagination()
  getList( { page, pageSize }, prop, order ? order.replace('ending', '') as 'asc'|'desc' : undefined )
}
// table-methods

const handleRelartConfirm = (selections: listItem[]) => {
  loadRelart.value = false
  const ids = tableSelections.value.map( (item: listItem) => item.art_id ).join(',')
  const rel_ids = selections.map( (item: listItem) => item.art_id ).join(',')
  useTryCatch( async () => {
    setLoading(true)

    const params = {
      art_ids: ids,
      rel_article_ids: rel_ids
    }
    const res = await changeRelArt(params)
    if (res.code === 200) {
      refresh()
      ElMessage.success(res.msg)
    } else {
      ElMessage.error(res.msg)
    }

    setLoading(false)
  }, () => setLoading(false) )
}

onActivated(() => {
  if (basicStore.refresh) {
    basicStore.setRefresh(false)
    if (route.query.cat_id) {
      queryParams.cat_id = route.query.cat_id as string
      initClassifyData()
    }
    refresh()
  }
})
Listener(route, () => {
  reset()
  handleSearch()
})
</script>