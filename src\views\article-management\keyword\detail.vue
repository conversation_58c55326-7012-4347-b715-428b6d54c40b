<template>
  <div class="scroll-y with-flex">
    <h2 style="padding: 15px 0">关键词文章相关度明细</h2>
    <div v-loading="loading" class="sticky-table">
      <CustomTable 
        ref="tableRef"
        :columns="columns"
        :data="tableData"
        :table-method="getList"
      />
    </div>
  </div>
</template>

<script setup lang="tsx">
import { ref } from 'vue'
import { ElMessage, ElTooltip } from 'element-plus' 
import { useRoute, useRouter } from 'vue-router'
import useTryCatch from '@/hooks/use-try-catch'
import useLoading from '@/hooks/use-loading'
import { getTopicDetailList, markStar } from '@/api/article-management'
import type { keywordDetailItem } from "@/api/article-management"

import CustomTable from '@/components/CustomTable/index.vue'
import SvgIcon from '@/icons/SvgIcon.vue'

defineOptions( { name: 'KeywordDetail' } )

const route = useRoute()
const router = useRouter()
const { id } = route.query as unknown as { id: number }
const { loading, setLoading } = useLoading()

const tableRef = ref()
const tableData = ref<keywordDetailItem[]>([])
const columns = ref([
  {
    title: '序号',
    dataKey: 'sort_num',
    width: 60
  },
  {
    title: '相关文章ID',
    dataKey: 'art_id',
    width: 100,
    cellRenderer: ( scope: { row: keywordDetailItem } ) => (
      <>
        <u onClick={ () => handleDetail(scope.row)} class='with-hand'> { scope.row.art_id } </u>
      </>
    )
  },
  {
    title: '相关文章URL',
    dataKey: 'page_url',
    minWidth: 300,
    cellRenderer: ( scope: { row: keywordDetailItem } ) => (
      <>
        <u onClick={ () => handleDetail(scope.row)} class='with-hand'> { scope.row.page_url } </u>
      </>
    )
  },
  {
    title: '',
    dataKey: 'star_flag',
    width: 100,
    cellRenderer: ( scope: { row: keywordDetailItem } ) => (
      <>
        <span class='with-hand' onClick={ () => handleMarkStar(scope.row) }>
          <ElTooltip content={ scope.row.star_flag ? '取消标记' : '标记为本组关键词相关度匹配值最高文章' }>
            <SvgIcon 
              style={ { color: 'var(--el-color-warning)' } }
              iconClass={ scope.row.star_flag ? 'star-fill' : 'star' }
            />
          </ElTooltip>
        </span>
      </>
    )
  },
  {
    title: '命中词汇分布',
    dataKey: 'art_title',
    minWidth: 300,
    cellRenderer: ( scope: { row: keywordDetailItem } ) => (
      <div style={ { marginBottom: '-10px' } }>
        { Object.entries(scope.row.keyword_list).map( ( [ key, value ] ) => 
          <div style={ { marginBottom: '10px' } }>
            { key }: &nbsp;&nbsp; { value.map((s) => s).join(',') }; ( { value.length }个 )
          </div> 
        ) }
      </div>
    )
  },
  {
    title: '相关度得分',
    dataKey: 'relevance',
    width: 120,
    headerRenderer: ( scope: { row: keywordDetailItem } ) => (
      <>
        相关度得分
        <ElTooltip content='相关度得分计算规则：【分数/命中1次】;包括文章标题(title: 5)、页面标题(page_title: 5分)、页面url(page_url: 5分)、关键词(page_keyword:10分)'>
          <SvgIcon iconClass='question' />
        </ElTooltip>
      </>
    )
  },
])

const getList = ( { page, pageSize } ) => {
  useTryCatch( async () => {
    setLoading(true)
    const res = await getTopicDetailList({ id, page, page_size: pageSize})
    if (res.code === 200) {
      tableData.value = res.data.list
      tableRef.value.setTotal(res.data.total)
    } else {
      ElMessage.error(res.msg)
    }
    setLoading(false)
  }, () => setLoading(false) )
}

const handleDetail = ( row: keywordDetailItem ) => {
  router.push({
    name: 'ArticleManageOperate',
    query: {
      type: 'edit',
      art_id: row.art_id
    }
  })
}

const handleMarkStar = ( row: keywordDetailItem ) => {
  if (loading.value) {
    return ElMessage.warning('请勿重复操作')
  }
  useTryCatch( async () => {
    setLoading(true)

    const res = await markStar({ topic_id: `${id}`, art_id: `${ row.star_flag ? 0 : row.art_id}` })
    if (res.code === 200) {
      ElMessage.success(res.msg)
      const { page, pageSize } = tableRef.value.getPagination()
      getList({ page, pageSize })
    } else {
      ElMessage.error(res.msg)
    }

    setLoading(false)
  }, () => setLoading(false))
}
</script>