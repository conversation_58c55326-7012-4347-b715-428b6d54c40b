import axiosReq from './axios'
import qs from 'query-string'

export type registerQueryParams = {
  id: string|number;
  title: string;
  channel_id: string|number;
  user_id_a: string|number;
  start_time: string;
  end_time: string;
  page?: number;
  pgae_size?: number;
}

export type registerListItem = {
  id: number;
  title: string;
  content: string;
  button_txt: string;
  button_url: string;
  channel_id: number;
  channel_host: string;
  channel_name: string;
  host_len: number;
  add_time: string;
  edit_time: string;
  user_id_a: string;
  user_id_e: string;
  user_id_a_name: string;
  user_id_e_name: string;
}

export type registerData = {
  id?: string|number;
  title: string;
  content: string;
  button_url: string;
  button_txt: string;
  channel_id: string|number;
}

export type npsData = {
  id?: number;
  name: string;
  version: string;
  pop_percent: number;
  pop_time_logout: number;
  pop_time_login: number;
  limit_template: string|string[];
  url_keyword: string;
  site_ids: string|number[];
}

export type npsListItem = {
  id: number;
  name: string;
  version: string;
  pop_percent: number;
  pop_time_logout: number;
  pop_time_login: number;
  limit_template: string;
  url_keyword: string;
  status: number;
  site: number[];
  user_id_a: number;
  user_id_e: number;
  add_user: string;
  edit_user: string;
  add_time: string;
  edit_time: string;
}

export type commentQueryParams = {
  page_id?: string|number;
  url: string;
  uid: string;
  country: string;
  check_state: string;
  begin_time: string;
  end_time: string;
  channel_id: string|number;
}

export type commentListItem = {
  id: number;
  page_id: number;
  url: string;
  message: string;
  name: string;
  uid: string;
  time: string;
  reply_total: number;
  likes: number;
  user_name: string;
  user_id: number;
  country: string;
  check_state: string;
  update_time: string;
}

export type replyListItem = {
  id: number;
  review_id: number;
  reply_id: number;
  message: string;
  reply_to_name: string;
  reply_to_uid: string;
  time: string;
  country: string;
  check_state: string;
  user_name: string;
  user_id: number;
  update_time: string;
  official_reply: {
    message: string;
    user_id: number;
    user_name: string;
  }[];
}

export type customQueryParams = {
  channel_id?: number;
  position_id: string|number;
  add_user: string;
  title: string;
  country: string;
  device: string;
  scope_type: string;
  add_time_start: string;
  add_time_end: string;
  page?: number;
  page_size?: number;
}

export type customListItem = {
  id: number;
  title: string;
  position_id: number;
  content: string;
  country: string;
  country_name: string;
  device: string;
  device_name: string;
  channel_id: number;
  scope_type: string;
  scope_type_name: string;
  scope_value: string;
  line_state: number;
  pop_tiem: string;
  pop_time_name: string;
  add_time: string;
  add_user: string;
  add_user_name: string;
  edit_time: string;
  edit_user: string;
  edit_user_name: string;
}

export type adItem = {
  id: number;
  name: string;
  child: {
    id: number;
    name: string;
  }[];
}

export type customData = {
  id?: number;
  position_id?: string|number;
  title: string;
  country: string|string[];
  device: string|string[];
  scope_type: string|number;
  scope_value: string|null;
  pop_time: string|number;
  channel_id: string|number;
  branch: number;
  content: {
    user_percent: number;
    image_info: {
      image_url: string;
      click_url: string;
      bg_color: string;
      text_content: string;
    }[];
  }[];
}

/* 弹窗管理 */
//  配置列表
export const getRegisterList = (params: registerQueryParams) => 
  axiosReq('get', '/api/v1/register/conf_list', {
    params,
    paramsSerializer: {
      serialize: (obj: registerQueryParams) => qs.stringify(obj)
    }
  })

// 配置详情
export const getRegisterDetail = (id: string|number) => 
  axiosReq('get', `/api/v1/register/conf_info?id=${id}`)

// 配置删除
export const deleteRegister = (id: string|number) =>
  axiosReq('post', `/api/v1/register/conf_del?id=${id}`)

// 配置添加
export const addRegister = (data: any) =>
  axiosReq('post', '/api/v1/register/conf_add', data)

// 配置修改
export const editRegister = (data: any) =>
  axiosReq('post', '/api/v1/register/conf_edit', data)
/* 弹窗管理 */

/* NPS配置 */
// nps列表
export const getNpsList = ( { page, pageSize }: { page: number; pageSize: number; }) => 
  axiosReq('get', `/api/v1/nps/template/list?page=${page}&page_size=${pageSize}`)

// nps添加
export const addNps = (data: npsData) =>
  axiosReq('post', '/api/v1/nps/template/add', data)

// nps编辑
export const editNps = (data: npsData) =>
  axiosReq('post', '/api/v1/nps/template/edit', data)

// nps详情
export const getNpsDetail = (id: number) =>
  axiosReq('get', `/api/v1/nps/template/detail?id=${id}`)

// nps启用
export const enableNps = (id: number) =>
  axiosReq('post', `/api/v1/nps/template/enable`, { id })

// nps禁用
export const disableNps = (id: number) =>
  axiosReq('post', `/api/v1/nps/template/disable`, { id })

// 获取版本
export const getVersion = () => 
  axiosReq('get', '/api/v1/nps/template/version')
/* NPS配置 */

/* 评论管理 */
// 页面评论列表
export const getCommentList = (params: { q: commentQueryParams; page: number; limit: number;}) => 
  axiosReq('get', '/api/v1/comment/page/list', {
    params,
    paramsSerializer: {
      serialize: (obj: any) => qs.stringify({
        q: JSON.stringify(obj.q),
        page: obj.page,
        limit: obj.limit,
      })
    }
  })

// 页面回复列表
export const getReplyList = (params: { q: commentQueryParams; page: number; limit: number;}) => 
  axiosReq('get', '/api/v1/comment/page/reply/list', {
    params,
    paramsSerializer: {
      serialize: (obj: any) => qs.stringify({
        q: JSON.stringify(obj.q),
        page: obj.page,
        limit: obj.limit,
      })
    }
  })

// 页面评论详情
export const commentReplyDetail = ( { id, channel_id }: { id: string|number; channel_id: string|number; } ) => 
  axiosReq('get', `/api/v1/comment/page/detail?id=${id}&channel_id=${channel_id}`)

// 回复
export const commetReply = ( data: { id: string|number; message: string; type: number; } ) => 
  axiosReq('post', '/api/v1/comment/page/official', data)

// 评论及回复审核
export const commentCheck = ( data: { ids: string; type: number; state: string; } ) => 
  axiosReq('post', '/api/v1/comment/page/check', data)

/* 评论管理 */

/* 推广位运营 http://yapi.wondershare.cn/project/32/interface/api/cat_10437 */

// 配置列表
export const getCustomList = ( params: customQueryParams ) => 
  axiosReq('get', '/api/v1/advertisement/conf_list', {
    params,
    paramsSerializer: {
      serialize: (obj: customQueryParams) => qs.stringify(obj)
    }
  })

// 添加推广位配置
export const addCustom = ( data: customData ) => 
  axiosReq('post', '/api/v1/advertisement/add_conf', data)

// 编辑推广位配置
export const editCustom = ( data: customData ) => 
  axiosReq('post', '/api/v1/advertisement/edit_conf', data)

// 获取配置参数
export const getCustomParams = () =>
  axiosReq('get', `/api/v1/advertisement/base_data`)

// 广告位列表
export const getAdList = () => 
  axiosReq('get', '/api/v1/advertisement/type_list')

// 配置详情
export const getCustomDetail = ( id: string|number ) => 
  axiosReq('get', `/api/v1/advertisement/conf_detail?id=${id}`)

// 推广位上线下线
export const customEnable = ( id: string|number, line_state: number ) =>
  axiosReq('post', '/api/v1/advertisement/change_line_state', { id, line_state })

// 推广位删除
export const customDelete = ( id: string|number ) =>
  axiosReq('post', '/api/v1/advertisement/del_conf', { id })

/* 推广位运营 */
