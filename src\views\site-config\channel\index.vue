<template>
  <div class="scroll-y with-flex">
    <div v-loading="loading" class="sticky-table">
      <CustomTable
        ref="tableRef"
        :columns="columns"
        :data="tableData"
        :table-method="getList"
        :operate-list="operateList"
      />
    </div>
  </div>
</template>

<script setup lang="tsx">
import { ref,  reactive, h } from 'vue'
import { ElMessage, ElButton, ElMessageBox, ElForm, ElFormItem, ElInput, ElColorPicker, ElSelect, ElOption } from 'element-plus'
import useTryCatch from '@/hooks/use-try-catch'
import useLoading from '@/hooks/use-loading'
import { getChannelList, getLangList, addChannel, editChannel } from '@/api/site-config'
import type { channelListItem, langListItem, channelData } from '@/api/site-config'

import CustomTable from '@/components/CustomTable/index.vue'

defineOptions( { name: 'LangConfiguration' } )

const { loading, setLoading } = useLoading()
const langList = ref<langListItem[]>([])
const submitData = reactive<channelData>({
  channel_code: '',
  channel_name: '',
  channel_language: '',
  color: '#000'
})
const formRules = {
  channel_code: [ { required: true, message: '渠道编码不能为空', trigger: 'blur' } ],
  channel_name: [ { required: true, message: '渠道名称不能为空', trigger: 'blur' } ],
  channel_language: [ { required: true, message: '渠道语言不能为空', trigger: 'blur' } ],
}

const tableRef = ref()
const columns = ref([
  {
    title: '渠道名称',
    dataKey: 'channel_code',
    width: 100,
  },
  {
    title: '渠道别名',
    dataKey: 'channel_name',
    minWidth: 180,
  },
  {
    title: '渠道语言',
    dataKey: 'channel_language_name',
    minWidth: 200,
  },
  {
    title: '颜色',
    dataKey: 'color',
    minWidth: 200,
    cellRenderer: (scope: { row: channelListItem }) => (
      <div style={ { backgroundColor: scope.row.color, height: '20px' } }></div>
    )
  },
  {
    title: '操作',
    dataKey: '',
    width: 120,
    cellRenderer: (scope: { row: channelListItem }) => (
      <>
        <ElButton type='primary' plain onClick={() => handleEdit(scope.row)}> 编辑 </ElButton>
      </>
    )
  },
])
const operateList = ref([
  {
    title: '创建新站点渠道',
    button: true,
    append: true,
    method: () => {
      handleGetLangList()
      handleModal()
    }
  }
])
const tableData = ref<channelListItem[]>([])

const getList = ( { page, pageSize } ) => {
  useTryCatch( async () => {
    setLoading(true)
    const res = await getChannelList({ page, page_size: pageSize })
    if (res.code === 200) {
      tableData.value = res.data.items
      tableRef.value?.setTotal(res.data.total)
    } else {
      ElMessage.error(res.msg)
    }

    setLoading(false)
  }, () => setLoading(false) )
}
const handleGetLangList = () => {
  if (langList.value.length === 0) {
    useTryCatch(async () => {
      const res = await getLangList()
      if (res.code === 200) {
        const list = res.data
        langList.value = list
      }
    })
  }
}

const handleEdit = (row: channelListItem) => {
  handleGetLangList()
  handleModal(row)
}

const handleModal = (row: channelListItem|null = null) => {
  submitData.channel_code = row?.channel_code || ''
  submitData.channel_name = row?.channel_code || ''
  submitData.channel_language = row?.channel_language || ''
  submitData.color = row?.color || '#000'
  ElMessageBox({
    title: `${row ? '编辑' : '添加'}站点渠道`,
    customStyle: {
      maxWidth: 'none',
      width: '500px',
    },
    showConfirmButton: true,
    showCancelButton: true,
    confirmButtonText: '确认',
    cancelButtonText: '取消',
    callback: (action: string) => {
      if (action === 'confirm') {
        const api = row?.id ? editChannel : addChannel
        useTryCatch( async () => {
          setLoading(true)
          const params = {
            ...submitData,
            ...row?.id ? { id: row.id } : {}
          }
          const res = await api(params)
          if (res.code === 200) {
            const { page, pageSize } = tableRef.value?.getPagination()
            getList( { page, pageSize } )
            ElMessage.success(res.msg)
          } else {
            ElMessage.error(res.msg)
          }
          setLoading(false)
        }, () => setLoading(false) )
      }
    },
    message: () => h(ElForm, {
      model: submitData,
      labelWidth: '110',
      labelSuffix: ':',
      rules: formRules
    }, {
      default: () => [
        h(ElFormItem, { label: '渠道名称', prop: 'channel_code' }, { default: () => h(ElInput, {
          placeholder: '请输入渠道名称',
          disabled: row ? true : false,
          modelValue: submitData.channel_code || '',
          onInput: (value) => (submitData.channel_code = value)
        })}),
        h(ElFormItem, { label: '渠道别名', prop: 'channel_name' }, { default: () => h(ElInput, {
          placeholder: '请输入渠道别名',
          modelValue: submitData.channel_name || '',
          onInput: (value) => (submitData.channel_name = value)
        })}),
        h(ElFormItem, { label: '渠道语言', prop: 'channel_language' }, { default: () => h(ElSelect, {
          placeholder: '请选择渠道语言',
          filterable: true,
          modelValue: submitData.channel_language,
          onChange: (value) => (submitData.channel_language = value)
        }, { default: () => langList.value.map(({ lang_code, name }) => h(ElOption, { value: lang_code, label: name })) })}),
        h(ElFormItem, { label: '颜色' }, { default: () => h(ElColorPicker, {
          modelValue: submitData.color || '',
          onChange: (value) => (submitData.color = value || '#000')
        })})
      ]
    })
  })
}
</script>