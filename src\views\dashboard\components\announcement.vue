<template>
  <div v-loading="loading" class="announce-box">
    <div 
      class="item"
      v-for="(d, index) in tableData"
      :key="index"
      @click="handleDetail(d)"
    >
      <div class="title" :class="{ red: d.is_read === 2 }" :title="d.title">
        {{ d.title }}
      </div>
      <div class="date">
        {{ d.publish_time }}
      </div>
    </div>
  </div>
  <AnnounceModal
    v-if="loadModal"
    ref="modalRef"
    v-model:detail-info="detailInfo"
  />
</template>

<script setup lang="ts">
import { ref, onActivated, nextTick } from 'vue'
import useLoading from '@/hooks/use-loading'
import useTryCatch from '@/hooks/use-try-catch'
import { getAnnounceList, setAnnounceReaded } from '@/api/announcement'
import type { announceListItem } from '@/api/announcement'
import emitter from '@/utils/bus'

import AnnounceModal from './announce-modal.vue'

const { loading, setLoading } = useLoading()

const tableData = ref<announceListItem[]>([])
const modalRef = ref()
const detailInfo = ref<announceListItem>()
const loadModal = ref(false)

const getList = () => {
  useTryCatch(async () => {
    setLoading(true)

    const params = {
      S: {
        state: '',
        is_read: '',
      },
      page: 1,
      page_size: 3
    }
    const res = await getAnnounceList(params)
    if (res.code === 200) {
      const items = res.data.items as announceListItem[]
      tableData.value = items
      for ( const item of items ) {
        if (item.is_read === 2 && item.type === 'A') {
          detailInfo.value = item
          loadModal.value = true
          setAnnounceReaded(item.announcement_id)
          break
        }
      }
    }

    setLoading(false)
  }, () => setLoading(false))
}

const handleDetail = (d: announceListItem) => {
  detailInfo.value = d
  loadModal.value = true
  modalRef.value?.handleShow?.()
  d.is_read === 2 && (
    setAnnounceReaded(d.announcement_id),
    d.is_read = 1
  )
}

onActivated(() => {
  getList()
})

getList()

emitter.on('announceModal', (id) => {
  loadModal.value = true
  nextTick ( () => {
    modalRef.value?.handleShow?.(id)
  } )
})
</script>

<style lang="scss">
.announce-box {
  .item {
    margin-bottom: 24px;
    cursor: pointer;
    .title {
      font-size: 16px;
      font-weight: 700;
      margin-bottom: 8px;
      overflow: hidden;
      white-space: nowrap;
      text-overflow: ellipsis;
      position: relative;
      color: var(--item-font-color);
      &.red {
        &::after {
          content: '';
          position: absolute;
          width: 12px;
          height: 12px;
          border-radius: 50%;
          background-color: var(--el-color-danger);
          right: 0;
          top: 0;
        }
      }
    }
    .date {
      color: var(--item-font-gray);
      font-size: 12px;
    }
  }
}
</style>