import axiosReq from './axios'
import qs from 'query-string'

export type taskListQuery = {
  site_id: number;
  channel_id: number;
  entity_type: string;
  entity_ids: string;
  type: string;
}

export type langItem = {
  channel_id: number;
  channel_code: string;
  site_id: number;
  language: string;
  smartcat_language: string;
  lang_name: string;
}

export type taskListItem = {
  tpl_page_id: number;
  tpl_id: number;
  title: string;
  url: string;
  state: string;
  channel_id: number;
  module: string;
  site_id: number;
  tpl_module_name: string;
  pageFieldList?: { field: string; name: string; }[];
  field?: string|string[];
  filed_list: { field: string; name: string }[];
  isChildren?: boolean;
  children?: taskListItem[];
  level?: string;
}

export type taskListData = {
  source_language: langItem;
  target_language: langItem[];
  list: taskListItem[];
}

export type submitTaskData = {
  site_id: number;
  channel_id: number;
  dead_line: string;
  source_language: string;
  target_language: string;
  entity_type: string;
  entitys: { entity_id: number; translation_field: string; }[];
  name: string;
  type: string;
}

export type queryParams = {
  site_id?: string|number;
  channel_id?: string|number;
  job_id?: string;
  status?: string;
  time_field?: string;
  start_time?: string;
  end_time?: string;
  add_user?: string;
  page?: number;
  page_size?: number;
  type?: string|number;
}

export type target_multilingualItem = {
  channel_code: string;
  channel_id: number;
  language: string;
  name: string;
  site_id: number;
}

export type documentListItem = {
  source_language: string; 
  status: number; 
  status_name: string; 
  target_language: string; 
  is_create: number; 
  is_create_name: string;
  target_multilingual: target_multilingualItem[];
  cross_or_not: number;
}

export interface listItem {
  id: number;
  name: string;
  c_id?: string;
  site_id: number;
  channel_id: number;
  status: number;  //1 待提交 2 提交中 3 翻译中 4 已完成 5 翻译失败
  source_language: string;
  target_language: string;
  dead_line: string;
  actual_finish: string;
  add_user: string;
  edit_user: string;
  add_time: string;
  edit_time: string;
  channel_code: string;
  channel_name: string;
  status_name: string;
  target_language_list?: string[];
  children?: listItem[];
  hasChildren?: boolean;
  isChildren?: boolean;
  reason: string;
  is_create?: number;
  target_multilingual: target_multilingualItem[];
  documentList: documentListItem[];
  smartcat_url: string;
  type: number;
  type_name: string;
  page_count: number;
  words_count: number;
  pre_price: string;
  my_team_name: string;
  cross_or_not?: number;
}

export type detailListItem = {
  id: number;
  job_id: number;
  site_id: number;
  channel_id: number;
  entity_id: number;
  entity_type: string;
  source_language: string;
  target_language: string;
  dead_line: string;
  actual_finish: string;
  translation_field: string;
  is_create: number; // 是否创建文章 0 未创建 1 已创建
  add_user: string;
  edit_user: string;
  add_time: string;
  edit_time: string;
  translation_field_name: string;
  url: string;
  tpl_id: number;
  module: string;
  tpl_module_name: string;
  tpl_type: string;
  tpl_type_name: string;
  is_create_name: string;
  create_page_id: number;
  status: number;
  status_name: string;
  is_cross: number;
  is_delete: number;
  translate_state: number;
  remark: string;
  my_team_info: string;
  isChildren?: boolean;
  children?: detailListItem[];
}

export type supplierListItem = {
  id: number;
  vendor_account_id: string;
  email: string;
  first_name: string;
  last_name: string;
  full_name: string;
  is_delete: number; //是否删除 0  否 1 是
  user_id_a: number;
  user_id_e: number;
  add_user: string;
  edit_user: string;
  add_time: string;
  edit_time: string;
  services: serviceItem[];
  remark: string;
  children?: supplierListItem[];
  isChildren?: boolean;
  service?: serviceItem;
  expanded?: boolean;
}

export type serviceItem = {
  id?: number;
  vendor_account_id?: string;
  source_language: string;
  source_language_name?: string;
  target_language: string;
  target_language_name?: string;
  price_per_unit: string;
  currency: string;
  service_type: string;
  service_type_name: string;
}

export type supplierFormData = {
  vendor_account_id?: string;
  email: string;
  first_name: string;
  last_name: string;
  remark: string;
  services: serviceItem[];
}

export type supplierItem = {
  id: string;
  email: string;
  first_name: string;
  last_name: string;
  services: { 
    service_type: string;
    service_type_name: string;
    source_language: string;
    target_language: string;
  }[]
}

export type billItem = {
  id: number;
  type: number;
  status: number; //状态：1 生成中 2 已完成 3 生成失败
  time_type: number;
  date: string;
  job_users: string;
  add_user: string;
  edit_user: string;
  add_time: string;
  edit_time: string;
  type_name: string;
  status_name: string;
  time_type_name: string;
  job_users_name: string;
}

// smartcat跨站点创建获取目标渠道列表
export type channelItem = {
  channel_id: number;
  channel_code: string;
  name: string;
  site_id: number;
  type: string;
  language: string;
  targetChannelList: {
    channel_id: number;
    channel_code: string;
    name: string;
    site_id: number;
    type: string;
    language: string;
  }[]
}

// smartcat跨站点创建获取模板内容页列表
export type templateItem = {
  tpl_id: number;
  name: string;
  module: string;
  type: string;
  level: string;
  site_id: number;
  state: string;
  language: string;
  channel_id: number;
  parent_id: number;
  tpl_module_name: string;
  tpl_type_name: string;
}

export type crossData = {
  job_id: string; 
  target_language: string; 
  type: number; 
  file?: Blob; 
  content: {
    detail_id: string|number;
    target_channel: string; 
    target_template_id: number;  
    is_cross: number;
  }[]
}

export type poolListItem = {
  id: number;
  source_language: string;
  target_language: string;
  source: string;
  target: string;
  is_delete: number;
  user_id_a: number;
  user_id_e: number;
  add_user: string;
  edit_user: string;
  add_time: string;
  edit_time: string;
}

export type poolQueryParams = {
  source: string;
  target: string;
  source_language: string;
  target_language: string;
  page?: number;
  page_size?: number;
}

// smartcat发起翻译列表接口
export const getStartList = (params: taskListQuery) => 
  axiosReq('get', '/api/v1/translate/get_plan_list', {
    params,
    paramsSerializer: {
      serialize: (obj: taskListQuery) => qs.stringify(obj)
    }
  })

// smartcat文章类型翻译字段列表
export const getPageField = (tpl_page_id: string|number) => 
  axiosReq('get', `/api/v1/templates/translate/page_field?tpl_page_id=${tpl_page_id}`)

// smartcat创建翻译任务接口
export const createJob = (data: submitTaskData) => 
  axiosReq('post', '/api/v1/translate/create_job', data)

// smartcat翻译任务驳回接口
export const rejectJob = (data: { id: string|number; reason: string; }) => 
  axiosReq('post', '/api/v1/translate/reject_job', data)

// 发起页面预览
export const getPreviewHtml = (tpl_page_id: string|number, fields: string) => 
  axiosReq('get', `/api/v1/articles/preview?tpl_page_id=${tpl_page_id}&fields=${fields}`)

// 翻译预览接口
export const transPreview = (detail_id: number) => 
  axiosReq('get', `/api/v1/translate/preview?detail_id=${detail_id}`)

// 翻译任务列表
export const getTaskList = (params: queryParams) => 
  axiosReq('get', '/api/v1/translate/get_job_list', {
    params,
    paramsSerializer: {
      serialize: (obj: queryParams) => qs.stringify(obj)
    }
  })

// 任务详情列表
export const getTaskDetailList = (job_id: string|number, target_language = '') => 
  axiosReq('get', `/api/v1/translate/get_job_detail_list?job_id=${job_id}&target_language=${target_language}`)

// 提交任务
export const submitTask = ( content: { job_id: number; serviceIds: string; }[] ) => axiosReq('post', '/api/v1/translate/submit_job', { content })

// 文档下载
export const exportDocument = (job_id: string|number, target_language : string) => 
  axiosReq('get', `/api/v1/translate/smartcat/document_export?job_id=${job_id}&target_language=${target_language}`)

// 创建文章
export const createArticle = (data: { job_id: string; target_language: string; type: number; target_channel: string; file?: Blob }) => {
  const formData = new FormData()
  Object.entries(data).forEach( ( [key, value] ) => {
    formData.append(key, typeof value === 'number' ? `${value}` : value)
  } )
  return axiosReq('post', '/api/v1/translate/smartcat/create_entity', formData)
}

// 跨站点创建文章
export const createArticleCross = (data: crossData) => {
  const formData = new FormData()
  Object.entries(data).forEach( ( [key, value] ) => {
    formData.append(key, typeof value === 'number' ? `${value}` : key === 'content' ? JSON.stringify(value) : value as string|Blob )
  } )
  return axiosReq('post', '/api/v1/translate/smartcat/create_entity_cross', formData)
}

// 跨站点获取目标渠道列表
export const targetChannelList = ( { source_site_id, source_channel_id, target_language }: { source_site_id: number; source_channel_id: number; target_language: string; } ) => 
  axiosReq('get', `/api/v1/translate/get_target_channel_list?source_site_id=${source_site_id}&source_channel_id=${source_channel_id}&target_language=${target_language}`)

// 跨站点获取模板内容列表
export const targetTemplateList = ( { target_site_id, target_channel_id }: { target_site_id: number ; target_channel_id: number; } ) => 
  axiosReq('get', `/api/v1/translate/get_target_template_list?target_site_id=${target_site_id}&target_channel_id=${target_channel_id}`)

// AI翻译重试
export const retryAITranslate = ( id: number ) => 
  axiosReq('post', `/api/v1/translate/ai/reset`, { id })


/* 翻译成本管理 */

// 供应商列表
export const getSupplierList = ( params: { id: number|string; name: string; page: number; pageSize: number } ) => 
  axiosReq('get', `/api/v1/translate/smartcat/my_team_list`, {
    params,
    paramsSerializer: {
      serialize: (obj: queryParams) => qs.stringify(obj)
    }
  })

// 供应商添加
export const addSupplier = ( data: supplierFormData ) => 
  axiosReq('post', '/api/v1/translate/smartcat/add_my_team', data)

// 供应商编辑
export const editSupplier = ( data: supplierFormData ) => 
  axiosReq('post', '/api/v1/translate/smartcat/edit_my_team', data)

// 供应商删除
export const deleteSupplier = ( id: number|string ) =>
  axiosReq('post', '/api/v1/translate/smartcat/delete_my_team', { id })

// 供应商拉取
export const getSmartcatSupplier = ( search_string = '' ) =>
  axiosReq('get', `/api/v1/translate/smartcat/search_my_team?search_string=${search_string}`)

// smartcat能提供该服务的所有供应商
export const servicesList = () => 
  axiosReq('get', '/api/v1/translate/smartcat/my_team_service')

// smartcat翻译对账单列表
export const getBillList = ( { page, pageSize } ) => 
  axiosReq('get', `/api/v1/translate/get_bill_list?page=${page}&page_size=${pageSize}`)

// smartcat翻译对账单创建
export const createBill = ( data: { time_type: string|number; date_time: string; start_time: string; end_time: string; job_users: string; } ) =>
  axiosReq('post', '/api/v1/translate/create_bill', data)

// smartcat翻译对账单预览
export const previewBill = (id: string|number) => 
  axiosReq('get', `/api/v1/translate/bill_preview?id=${id}`)

// smartcat翻译对账单导出
export const exportBill = (id: string|number) => 
  axiosReq('get', `/api/v1/translate/bill_export?id=${id}`)

// smartcat获取翻译任务添加人
export const addUserList = (params: { start_time: string; end_time: string; time_type: string|number; date_time: string; }) => 
  axiosReq('get', '/api/v1/translate/get_job_add_user', {
    params,
    paramsSerializer: {
      serialize: (obj: queryParams) => qs.stringify(obj)
    }
  })

// smartcat翻译对账单任务重试
export const retryBill = (ids: string) => 
  axiosReq('post', `/api/v1/translate/retry_bill`, { ids })

// smartcat拉取供应商详情
export const getSmartcatSupplierDetail = (id: string|number) => 
  axiosReq('get', `/api/v1/translate/smartcat/my_team?id=${id}`)

/* 翻译成本管理 */


/* 预设内链池 */

// 列表
export const poolList = ( params: poolQueryParams ) => 
  axiosReq('get', `/api/v1/translate/pool_list`, {
    params,
    paramsSerializer: {
      serialize: (obj: poolQueryParams) => qs.stringify(obj)
    }
  })

// 添加
export const addPool = ( data: { source_language: string; target_language: string; source: string; target: string; } ) =>  
  axiosReq('post', '/api/v1/translate/pool_add', data)

// 编辑
export const editPool = ( data: { id: string|number; source: string; target: string; } ) =>
  axiosReq('post', '/api/v1/translate/pool_edit', data)

// 导入数据
export const importPool = ( data: { file: Blob; source_language: string; target_language: string; } ) => {
  const formData = new FormData()
  Object.entries(data).forEach( ( [key, value] ) => {
    formData.append(key, typeof value === 'number' ? `${value}` : value)
  } )
  return axiosReq('post', '/api/v1/translate/pool_import', formData)
}

// 删除
export const deletePool = ( id: string|number ) =>
  axiosReq('post', '/api/v1/translate/pool_delete', { id })

// 下拉语言列表
export const poolLangList = () =>
  axiosReq('get', '/api/v1/translate/pool_language')

/* 预设内链池 */