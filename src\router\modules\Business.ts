import type { moduleMap } from '../aysncRoutes'
export default {
  'ReviewConfig': {
    component: () => import('@/views/audit-management/config/index.vue'),
    path: 'audit-config',
    title: '审核配置',
    cachePage: true,
    icon: 'audit',
  },
  'ReviewConfigDetail': {
    component: () => import('@/views/audit-management/config/operate.vue'),
    path: 'audit-config-detail',
    title: '审核流程配置',
    hidden: true,
  }
} as {
  [propName: string]: moduleMap
}