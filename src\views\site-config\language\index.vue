<template>
  <div class="scroll-y with-flex">
    <div v-loading="loading" class="sticky-table">
      <CustomTable
        ref="tableRef"
        hide-pagination
        :columns="columns"
        :data="tableData"
        :table-method="getList"
        :operate-list="operateList"
      />
    </div>
  </div>
</template>

<script setup lang="tsx">
import { ref, reactive, h } from 'vue'
import { ElMessage, ElButton, ElMessageBox, ElForm, ElFormItem, ElInput } from 'element-plus'
import useTryCatch from '@/hooks/use-try-catch'
import useLoading from '@/hooks/use-loading'
import { getLangList, addLang, editLang } from '@/api/site-config'
import type { langListItem, langData } from '@/api/site-config'

import CustomTable from '@/components/CustomTable/index.vue'

defineOptions( { name: 'LangConfiguration' } )

const { loading, setLoading } = useLoading()
const submitData = reactive<langData>({
  lang_code: '',
  smart_lang_code: '',
  name: ''
})
const formRules = {
  lang_code: [ { required: true, message: '语言编码不能为空', trigger: 'blur' } ],
  name: [ { required: true, message: '语言名称不能为空', trigger: 'blur' } ],
  smart_lang_code: [ { required: true, message: 'smart_cat语言编码不能为空', trigger: 'blur' } ],
}

const tableRef = ref()
const columns = ref([
  {
    title: '语言编码',
    dataKey: 'lang_code',
    width: 100,
  },
  {
    title: '语言名称',
    dataKey: 'name',
    minWidth: 180,
  },
  {
    title: 'smart_cat语言编码',
    dataKey: 'smart_lang_code',
    minWidth: 200,
  },
  {
    title: '操作',
    dataKey: '',
    width: 120,
    cellRenderer: (scope: { row: langListItem }) => (
      <>
        <ElButton type='primary' plain onClick={() => handleEdit(scope.row)}> 编辑 </ElButton>
      </>
    )
  },
])
const operateList = ref([
  {
    title: '创建新站点语言',
    button: true,
    append: true,
    method: () => {
      handleModal()
    }
  }
])
const tableData = ref<langListItem[]>([])

const getList = ( ) => {
  useTryCatch( async () => {
    setLoading(true)
    const res = await getLangList()
    if (res.code === 200) {
      tableData.value = res.data
    } else {
      ElMessage.error(res.msg)
    }

    setLoading(false)
  }, () => setLoading(false) )
}

const handleEdit = (row: langListItem) => handleModal(row)

const handleModal = (row: langListItem|null = null) => {
  submitData.lang_code = row?.lang_code || ''
  submitData.name = row?.name || ''
  submitData.smart_lang_code = row?.smart_lang_code || ''
  ElMessageBox({
    title: `${row ? '编辑' : '添加'}站点语言`,
    customStyle: {
      maxWidth: 'none',
      width: '500px',
    },
    showConfirmButton: true,
    showCancelButton: true,
    confirmButtonText: '确认',
    cancelButtonText: '取消',
    callback: (action: string) => {
      if (action === 'confirm') {
        const api = row?.lang_id ? editLang : addLang
        useTryCatch( async () => {
          setLoading(true)
          const params = {
            ...submitData
          }
          const res = await api(params, row?.lang_id || '')
          if (res.code === 200) {
            getList()
            ElMessage.success(res.msg)
          } else {
            ElMessage.error(res.msg)
          }
          setLoading(false)
        }, () => setLoading(false) )
      }
    },
    message: () => h(ElForm, {
      model: submitData,
      labelWidth: '150',
      labelSuffix: ':',
      rules: formRules
    }, {
      default: () => [
        h(ElFormItem, { label: '语言编码', prop: 'lang_code' }, { default: () => h(ElInput, {
          placeholder: '请输入语言编码',
          disabled: row ? true : false,
          modelValue: submitData.lang_code || '',
          onInput: (value) => (submitData.lang_code = value)
        })}),
        h(ElFormItem, { label: '语言名称', prop: 'name' }, { default: () => h(ElInput, {
          placeholder: '请输入语言名称',
          modelValue: submitData.name || '',
          onInput: (value) => (submitData.name = value)
        })}),
        h(ElFormItem, { label: 'smart cat语言编码', prop: 'smart_lang_code' }, { default: () => h(ElInput, {
          placeholder: '请输入smart cat语言编码',
          modelValue: submitData.smart_lang_code || '',
          onInput: (value) => (submitData.smart_lang_code = value)
        })})
      ]
    })
  })
}
</script>