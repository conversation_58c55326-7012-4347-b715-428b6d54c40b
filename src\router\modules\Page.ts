import type { moduleMap } from '../aysncRoutes'

export default {
  'TemplateManage': {
    component: () => import('@/views/template-management/index.vue'),
    path: 'template-manage',
    title: '模板配置',
    cachePage: true,
  },
  'CreateChildTemplate': {
    component: () => import('@/views/template-management/childTemplate/index.vue'),
    path: 'manage-child-template',
    title: '编辑模板集',
    cachePage: false,
    hidden: true,
  },
  'CopyPageDetail': {
    component: () => import('@/views/template-management/childTemplate/detail.vue'),
    path: 'copy-page-detail',
    title: '页面复用任务详情',
    cachePage: false,
    hidden: true,
    queryRequired: true
  },
  'TemplatPageManagement': {
    component: () => import('@/views/template-management/pageList/index.vue'),
    path: 'template-page-manage',
    title: '模板下页面管理',
    cachePage: true,
    hidden: true,
  },
  'TemplateManageOperate': {
    component: () => import('@/views/template-management/operate/index.vue'),
    path: 'template-manage-operate',
    title: '模板编辑',
    cachePage: true,
    hidden: true,
    hasDropdown: true,
  },
  'TemplateHistory': {
    component: () => import('@/views/template-management/history/index.vue'),
    path: 'template-history',
    title: '模板历史记录',
    cachePage: true,
    hidden: true,
  },
  'BlockManagement': {
    component: () => import('@/views/block-management/index.vue'),
    path: 'block-manage',
    title: '块配置',
    cachePage: true,
  },
  'BlockManagementOperate': {
    component: () => import('@/views/block-management/operate/index.vue'),
    path: 'block-manage-operate',
    title: '块编辑',
    cachePage: true,
    hidden: true,
    hasDropdown: true,
  },
  'CreateChildBlock': {
    component: () => import('@/views/block-management/childBlock/index.vue'),
    path: 'manage-chlid-block',
    title: '子块管理',
    cachePage: true,
    hidden: true,
  },
  'BlockHistory': {
    component: () => import('@/views/block-management/history/index.vue'),
    path: 'block-history',
    title: '块历史记录',
    cachePage: true,
    hidden: true,
  },
  'PageManagement': {
    component: () => import('@/views/page-management/index.vue'),
    path: 'page-management',
    title: '页面配置',
    cachePage: true,
  },
  'PageManagementOperate': {
    component: () => import('@/views/page-management/operate/index.vue'),
    path: 'page-manage-operate',
    title: '页面编辑',
    cachePage: true,
    hidden: true,
    hasDropdown: true,
    queryRequired: true
  },
  'PageHistory': {
    component: () => import('@/views/page-management/history/index.vue'),
    path: 'page-history',
    title: '页面历史记录',
    cachePage: true,
    hidden: true,
  },
  'PageWarning': {
    component: () => import('@/views/page-management/warning/index.vue'),
    path: 'page-warning',
    title: '页面预警详情',
    hidden: true,
  }
} as {
  [propName: string]: moduleMap
}