<template>
  <div class="scroll-y" style="position: relative;">
    <el-tabs v-model="activeName" @tab-change="switched = true">
      <el-tab-pane label="关键词库" name="list">
        <QueryForm 
          :loading="loading"
          :form-list="formList"
          @search="handleSearch"
          @reset="reset"
        />
        <div v-loading="loading" class="sticky-table">
          <CustomTable
            ref="tableRef"
            :columns="columns"
            :data="tableData"
            :table-method="getList"
            selectable
            customize-head
            :operate-list="operateList"
            :select-method="(row: keywordItem) => !row.isChildren"
            row-key="c_id"
            @selection-change="handleSelectionChange"
          />
        </div>
      </el-tab-pane>
      <el-tab-pane label="大类管理" name="class">
        <template v-if="switched">
          <QueryForm 
            :loading="loading"
            :form-list="formList2"
            @search="handleSearch2"
            hide-reset
          />
          <div v-loading="loading" class="sticky-table sticky-table2">
            <CustomTable
              ref="tableRef2"
              :columns="columns2"
              :data="tableData2"
              :table-method="getList2"
              selectable
              :operate-list="operateList2"
              container-class="sticky-table2"
              @selection-change="(selections: keywordItem[]) => selectIds2 = selections.map((item: keywordItem) => item.id).join(',')"
            />
          </div>
        </template>
      </el-tab-pane>
    </el-tabs>
    <el-result
      v-show="!channel_item_all.channel_id"
      icon="warning"
      title="请先选择具体渠道!" 
      sub-title="您还未选择渠道"
      style="position: absolute; bottom: 0; width: 100%; height: 100%; background-color: var(--bg-layout); z-index: 19"
    />
  </div>
</template>

<script setup lang="tsx">
import { ref, reactive, toRef, h } from 'vue'
import { ElButton, ElMessage, ElMessageBox, ElTooltip, ElInput, ElForm, ElFormItem, ElUpload, ElSpace, ElSelect, ElOption } from 'element-plus'
import { Delete, Plus } from '@element-plus/icons-vue'
import { useRoute, useRouter } from 'vue-router'
import { storeToRefs } from 'pinia'
import { useParamsStore } from '@/store'
import useTryCatch from '@/hooks/use-try-catch'
import useLoading from '@/hooks/use-loading'
import { getHost } from '@/hooks/use-env'
import { getTopicList, deleteKeyword, deleteTopic, addTopic, addKeyword, importKeyword, updateKeyword, editTopic, _topicList, topicCateList } from '@/api/article-management'
import type { queryParams_keyword, keywordItem } from '@/api/article-management'
import Listener from '@/utils/site-channel-listeners'
import type { UploadRawFile } from 'element-plus'

import QueryForm from '@/components/QueryForm/index.vue'
import CustomTable from '@/components/CustomTable/index.vue'
import SvgIcon from '@/icons/SvgIcon.vue'

defineOptions( { name: 'KeywrodManagement' } )

const route = useRoute()
const router = useRouter()
const { loading, setLoading } = useLoading()
const { channel_item_all, userList } = storeToRefs(useParamsStore())
const activeName = ref('list')
const switched = ref(false)
const topicList = ref<{id:number; name:string}[]>([])

const queryParams_raw = {
  name: '',
  user_id_a: '',
  user_id_e: '',
  start_time: '',
  end_time: '',
  time_field: '',
  channel_id: '',
  has_art: '',
  star_art: '',
  parent_id: '',
}
const timeRange = ref('')
const queryParams = reactive<queryParams_keyword>({
  ...queryParams_raw,
})
const queryParams2 = reactive({
  id: '',
  name: ''
})

const formList = ref([
  {
    label: '关键词主题',
    placeholder: '关键词主题',
    value: toRef(queryParams, 'name'),
    component: 'input',
  },
  {
    label: '是否关联文章',
    placeholder: '是否关联文章',
    value: toRef(queryParams, 'has_art'),
    component: 'select',
    selections: [
      {
        value: '1',
        label: '是'
      },
      {
        value: '0',
        label: '否'
      }
    ],
  },
  {
    label: '筛选大类',
    placeholder: '筛选大类',
    value: toRef(queryParams, 'parent_id'),
    component: 'select',
    labelKey: 'name',
    valueKey: 'id',
    selections: topicList,
  },
  {
    label: '创建人',
    placeholder: '创建人',
    value: toRef(queryParams, 'user_id_a'),
    component: 'select',
    labelKey: 'real_name',
    valueKey: 'user_id',
    selections: userList
  },
  {
    label: '更新人',
    placeholder: '更新人',
    value: toRef(queryParams, 'user_id_e'),
    component: 'select',
    labelKey: 'real_name',
    valueKey: 'user_id',
    selections: userList
  },
  
  {
    label: '时间筛选类型',
    placeholder: '时间筛选类型',
    value: toRef(queryParams, 'time_field'),
    component: 'select',
    selections: [
      {
        value: 'add_time',
        label: '创建时间'
      },
      {
        value: 'edit_time',
        label: '更新时间'
      }
    ],
    handleChange: () => {
      timeRange.value = ''
      queryParams.start_time = ''
      queryParams.end_time = ''
    },
    append: true
  },
  {
    label: '时间日期',
    clearable: true,
    start_placeholder: '开始日期',
    end_placeholder: '结束日期',
    value: timeRange,
    component: 'datetimerange',
    disabled: computed( () => queryParams.time_field ? false : true ),
    dateChange: (value: [string, string] | null) => {
      if (value) {
        queryParams.start_time = value[0]
        queryParams.end_time = value[1]
      } else {
        queryParams.start_time = ''
        queryParams.end_time = ''
      }
    },
    append: true
  },
])
const formList2 = ref([
  {
    label: '大类ID',
    placeholder: '大类ID',
    value: toRef(queryParams2, 'id'),
    component: 'input',
  },
  {
    label: '大类名称',
    placeholder: '大类名称',
    value: toRef(queryParams2, 'name'),
    component: 'input',
  },
])
const tableRef = ref()
const tableRef2 = ref()
const tableData = ref<keywordItem[]>([])
const tableData2 = ref<keywordItem[]>([])
const selectIds = ref('')
const selectIds2 = ref('')
const columns = ref([
  {
    title: '关键词主题',
    dataKey: 'name',
    minWidth: 200,
    cellRenderer: ( scope:  {row: keywordItem} ) => (
      <>
        { scope.row.isChildren ? '' : scope.row.name }
      </>
    )
  },
  {
    title: '关键词',
    dataKey: 'keyword_arr',
    minWidth: 200,
    cellRenderer: ( scope:  {row: keywordItem} ) => (
      <>
        {
          scope.row.isChildren
          ? scope.row.name
          : `${scope.row.children?.length}个`
        }
      </>
    )
  },
  {
    title: '所属大类',
    dataKey: 'parent_name',
    minWidth: 120,
  },
  {
    title: '星标文章',
    dataKey: 'star_art',
    width: 100,
    cellRenderer: ( scope:  {row: keywordItem} ) => (
      <>
        {
          scope.row.star_art
          ? <u onClick={ () => handleToArtDetail(scope.row)} class='with-hand'> { scope.row.star_art } </u>
          : '/'
        }
        
      </>
    )
  },
  {
    title: '相关文章',
    dataKey: 'art_num',
    width: 100,
    cellRenderer: ( scope:  {row: keywordItem} ) => (
      <>
        { scope.row.isChildren ? '/' : scope.row.art_num ? scope.row.art_num + '篇' : '0篇' }
      </>
    )
  },
  {
    title: '最高得分',
    dataKey: 'top_relevance',
    width: '120',
    headerRenderer: ( scope:  {row: keywordItem} ) => (
      <>
        最高得分
        <ElTooltip content='最高相关度得分计算默认每天00:00-09:00自动更新,如急需更新数据请手动刷新数据,手动刷新需要一定时间完成数据计算,请耐心等待,完成后会通过消息中心通知,请注意查收！'>
          <SvgIcon iconClass='question' />
        </ElTooltip>
      </>
    ),
    cellRenderer: ( scope:  {row: keywordItem} ) => (
      <>
        { scope.row.isChildren ? '' : scope.row.top_relevance ? scope.row.top_relevance + '' : '0' }
      </>
    )
  },
  {
    title: '创建人',
    dataKey: 'user_name_a',
    width: '100',
    hideColumn: true
  },
  {
    title: '创建时间',
    dataKey: 'add_time',
    width: '180',
    hideColumn: true
  },
  {
    title: '更新人',
    dataKey: 'user_name_e',
    width: '100'
  },
  {
    title: '更新时间',
    dataKey: 'edit_time',
    width: '180'
  },
  {
    title: '操作',
    dataKey: '',
    fix: 'right',
    width: 310,
    cellRenderer: ( scope:  {row: keywordItem} ) => (
      <>
        {
          scope.row.isChildren
          ? <>
            <ElTooltip content='删除关键词'>
              <ElButton type='danger' plain onClick={() => handleDeleteKeyword(scope.row)}> 删除 </ElButton>
            </ElTooltip>
          </>
          : <>
            <ElTooltip content='查看详情'>
              <ElButton type='primary' plain onClick={() => handleDetail(scope.row)}> 查看 </ElButton>
            </ElTooltip>
            <ElTooltip content='编辑主题'>
              <ElButton type='primary' plain onClick={() => handleEditTopic(scope.row)}> 编辑 </ElButton>
            </ElTooltip>
            <ElTooltip content='新增关键词'>
              <ElButton type='success' plain onClick={() => handleAddKeyword(scope.row)}> 新增 </ElButton>
            </ElTooltip>
            <ElTooltip content='删除主题'>
              <ElButton type='danger' plain onClick={() => handleDeleteTopic(scope.row)}> 删除 </ElButton>
            </ElTooltip>
          </>
        }
      </>
    )
  }
])
const columns2 = ref([
  {
    title: '大类ID',
    dataKey: 'id',
    width: 120
  },
  {
    title: '大类名称',
    dataKey: 'name',
    minWidth: 120
  },
  {
    title: '关键词组',
    dataKey: 'name',
    width: 120,
    cellRenderer: (scope: { row: keywordItem }) => (<> { scope.row.topic_count }组 </>)
  },
  {
    title: '操作',
    dataKey: '',
    fix: 'right',
    width: 240,
    cellRenderer: (scope: { row: keywordItem }) => (<>
      <ElButton type='primary' plain onClick={() => handleCheck(scope.row)}> 查看 </ElButton>
      <ElButton type='primary' plain onClick={() => handleCateModal(scope.row)}> 编辑 </ElButton>
      <ElButton type='danger' plain onClick={() => handleDeleteCateTopic(scope.row)}> 删除 </ElButton>
    </>)
  }
])
const operateList = ref([
  {
    title: '刷新主题数据',
    icon: 'refresh',
    method: () => {
      useTryCatch( async () => {
        const res = await updateKeyword(selectIds.value)
        if (res.code === 200) {
          handleSearch()
          ElMessage.success(res.msg)
        } else {
          ElMessage.error(res.msg)
        }
      } )
    },
    disabled: computed(() => !selectIds.value)
  },
  {
    title: '导入',
    method: () => {
      handleImport()
    }
  },
  {
    title: '新增主题',
    method: () => {
      handleAddTopic()
    }
  },
  {
    title: '批量删除',
    method: () => {
      handleDeleteTopic()
    },
    disabled: computed(() => !selectIds.value)
  }
])
const operateList2 = ref([
  {
    title: '新增大类',
    method: () => {
      handleCateModal()
    }
  },
  {
    title: '批量删除',
    method: () => {
      handleDeleteCateTopic()
    },
    disabled: computed(() => !selectIds2.value)
  }
])

const reset = () => {
  timeRange.value = ''
  Object.keys(queryParams_raw).forEach((key) => {
    queryParams[key] = queryParams_raw[key]
  })
}

const handleSearch = () => {
  tableRef.value?.setPage(1)
  const { page, pageSize } = tableRef?.value?.getPagination()
  getList( { page, pageSize } )
}
const handleSearch2 =() => {
  tableRef2.value?.setPage(1)
  const { page, pageSize } = tableRef2?.value?.getPagination()
  getList2( { page, pageSize } )
}

const getList = ( { page, pageSize } ) => {
  if (!channel_item_all.value.channel_id) {
    return
  }
  useTryCatch( async () => {
    setLoading(true)
    const params = {
      ...queryParams,
      channel_id: channel_item_all.value.channel_id as number,
      page,
      page_size: pageSize
    }

    const res = await getTopicList(params)
    if (res.code === 200) {
      const list = res.data.list.map((item: keywordItem) => {
        return {
          ...item,
          c_id: item.id,
          children: [...item.keyword_arr.map((keyword, index) => ({...keyword, isChildren: true, c_id: `${keyword.id}-${index}`}))]
        }
      })
      tableData.value = list
      tableRef.value?.setTotal(res.data.total)
    } else {
      ElMessage.error(res.msg)
    }
    setLoading(false)
  }, () => setLoading(false) )
}
const getList2 = ( { page, pageSize } ) => {
  if (!channel_item_all.value.channel_id) {
    return
  }
  useTryCatch( async () => {
    setLoading(true)
    const params = {
      ...queryParams2,
      channel_id: channel_item_all.value.channel_id as number,
      page,
      page_size: pageSize
    }

    const res = await topicCateList(params)
    if (res.code === 200) {
      const list = res.data.list
      tableData2.value = list
      tableRef2.value?.setTotal(res.data.total)
    } else {
      ElMessage.error(res.msg)
    }
    setLoading(false)
  }, () => setLoading(false) )
}

const _getTopicList = () => {
  if (!channel_item_all.value.channel_id) {
    return
  }
  useTryCatch( async () => {
    const res = await _topicList( channel_item_all.value.channel_id as number )
    if (res.code === 200) {
      topicList.value = res.data
    }
  } )
}

const handleSelectionChange = (selections: keywordItem[]) => {
  selectIds.value = selections.filter((item) => !item.isChildren).map((item) => item.id).join(',')
}

const handleImport = () => {
  const parent_id = ref('')
  ElMessageBox({
    title: '导入关键词',
    customClass: 'dialog-button-center',
    customStyle: {
      maxWidth: 'none',
      width: '500px',
    },
    showCancelButton: true,
    showConfirmButton: false,
    cancelButtonText: '取消',
    closeOnPressEscape: false,
    closeOnClickModal: false,
    callback: ( action: string ) => {

    },
    message: () => h('div', {class: 'text-center' }, [
      h(ElSpace, {
        align: 'center',
        wrap: false,
        size: 16,
        style: {
          marginBottom: '16px',
        },
      }, { 
        default: () => [
          '所属大类:',
          h(ElSelect, {
            placeholder: '请选择',
            modelValue: parent_id.value,
            onChange: (val) => parent_id.value = val,
          }, () => topicList.value.map(({ id, name }) => h(ElOption, {  label: name, value: id})))
        ]}
      ),
      h(ElSpace, {
        align: 'center',
        wrap: false,
        size: 16
      }, { default: () => [
        h(ElButton, {
          type: 'primary',
          link: true,
          onClick: () => {
            location.href = `${getHost()}/api/v1/keyword/import_tpl`
          }
        }, () => '点击下载批量导入模板.xslx'),
        h(ElUpload, {
          action: 'string',
          accept: 'xlsx',
          autoUpload: false,
          showFileList: false,
          limit: 1,
          beforeUpload: () => true,
          onChange: ( file ) => {
            const params = {
              channel_id: channel_item_all.value.channel_id as number,
              import_keyword_file: file.raw as UploadRawFile,
              parent_id: parent_id.value
            }

            useTryCatch( async () => {
              setLoading(true)
              const res = await importKeyword(params)
              if (res.code === 200) {
                handleSearch()
                ElMessage.success(res.msg)
              } else {
                ElMessage.error(res.msg)
              }
              ElMessageBox.close()
              setLoading(false)
            }, () => setLoading(false) )
          }
        }, {
          default: () => h(ElButton, { type: 'primary', loading: loading.value, disabled: !parent_id.value }, () => '上传')
        })
      ]})
    ])
  })
}

const handleAddTopic = () => {
  toppicModal()
}
const handleEditTopic = (row: keywordItem) => {
  toppicModal(row)
}

const toppicModal = (row?: keywordItem) => {
  const topic = ref( row ?  row.name : '')
  const parent_id = ref( row ? row.parent_id : '')
  ElMessageBox({
    title: `${row ? '编辑' : '新增'}主题`,
    customClass: 'dialog-button-center',
    customStyle: {
      maxWidth: 'none',
      width: '400px',
    },
    showCancelButton: false,
    showConfirmButton: false,
    callback: (action: string) => {
      if (action === 'confirm') {

      }
    },
    message: () => h(ElForm, {
      labelWidth: '120',
      labelSuffix: ':'
    }, {
      default: () => [
        h(ElFormItem, { label: '主题名称', rules: { required: true } }, {
          default: () => h(ElInput, {
            style: {
              width: '214px'
            },
            placeholder: '请输入主题',
            modelValue: topic.value,
            onInput: (value) => (topic.value = value)
          })
        }),
        h(ElFormItem, { label: '所属大类', rules: { required: true } }, {
          default: () => h(ElSelect, {
            modelValue: parent_id.value,
            onChange: (value) => (parent_id.value = value),
          }, {
            default: () => topicList.value.map(( { id, name } ) => h(ElOption, { label: name, value: id }) )
          })
        }),
        h('div', { class: 'text-center'}, h(ElButton, { 
          type: 'primary', onClick: () => {
            if (!topic.value || !parent_id.value) {
              return ElMessage.warning('请填写必填项')
            }
            useTryCatch( async () => {
              setLoading(true)
              const api = row ? editTopic : addTopic
              const params = {
                name: topic.value,
                parent_id: parent_id.value,
                ...row 
                ? { id: row.id } 
                : { channel_id: channel_item_all.value.channel_id as number }
              }
              const res = await api(params as any)
              if (res.code === 200) {
                handleSearch()
                ElMessage.success(res.msg)
              } else {
                ElMessage.error(res.msg)
              }
              ElMessageBox.close()
              setLoading(false)
            }, () => setLoading(false) )
          } }, 
          () => '确定')
        ),
      ]
    })
  })
}

const handleCheck = (row: keywordItem) => {
  activeName.value = 'list'
  queryParams.parent_id = row.id
  handleSearch()
}
const handleCateModal = (row?: keywordItem) => {
  const name = ref(row ? row.name : '')
  ElMessageBox({
    title: `${ row ? '编辑' : '新增' }大类`,
    customClass: 'dialog-button-center',
    customStyle: {
      maxWidth: 'none',
      width: '500px',
    },
    showCancelButton: false,
    showConfirmButton: false,
    callback: (action: string) => {
      if (action === 'confirm') {

      }
    },
    message: () => h(ElForm, {
      labelWidth: '120',
      labelSuffix: ':'
    }, {
      default: () => [
        h(ElFormItem, { label: '主题名称', rules: { required: true } }, {
          default: () => h(ElInput, {
            placeholder: '请输入大类名称',
            modelValue: name.value,
            onInput: (value) => (name.value = value)
          })
        }),
        h('div', { class: 'text-center'}, h(ElButton, { 
          type: 'primary', onClick: () => {
            if (!name.value) {
              return ElMessage.warning('大类名称不能为空')
            }
            useTryCatch( async () => {
              setLoading(true)
              const api = row ? editTopic : addTopic
              const params = {
                name: name.value,
                ...row 
                ? { id: row.id } 
                : {
                  channel_id: channel_item_all.value.channel_id as number,
                  parent_id: 0
                }
              }
              const res = await api(params as any)
              if (res.code === 200) {
                _getTopicList()
                handleSearch2()
                ElMessage.success(res.msg)
              } else {
                ElMessage.error(res.msg)
              }
              ElMessageBox.close()
              setLoading(false)
            }, () => setLoading(false) )
          } }, 
          () => '确定')
        ),
      ]
    })
  })
}

const handleAddKeyword = (row: keywordItem) => {
  handleAddModal(row)
}

const handleAddModal = (row?: keywordItem) => {
  const keyword_arr = ref([ { name: '' } ])
  const topic = ref(row ? row.name : '')
  ElMessageBox({
    title: `新增${row ? '关键词' : '主题'}`,
    customClass: 'dialog-button-center',
    customStyle: {
      maxWidth: 'none',
      width: '500px',
    },
    showCancelButton: true,
    showConfirmButton: true,
    cancelButtonText: '取消',
    confirmButtonText: '确认',
    callback: (action: string) => {
      if (action === 'confirm') {
        useTryCatch( async () => {
          setLoading(true)
          const api = row ? addKeyword : addTopic
          const params = {
            channel_id: channel_item_all.value.channel_id as number,
            keyword_list: keyword_arr.value.map((item) => item.name),
            ...row ? { id: row.id } : { name: topic.value }
          }
          const res = await api(params as any)
          if (res.code === 200) {
            handleSearch()
            ElMessage.success(res.msg)
          } else {
            ElMessage.error(res.msg)
          }
          setLoading(false)
        }, () => setLoading(false) )
      }
    },
    message: () => h(ElForm, {
      labelWidth: '120',
      labelSuffix: ':'
    }, {
      default: () => 
      [ 
        h(ElFormItem, { label: '所属大类' }, {
          default: () => h(ElInput, {
            style: {
              width: 'calc(100% - 20px)'
            },
            disabled: true,
            value: row?.parent_name
          })
        }),
        h(ElFormItem, { label: '关键词主题', rules: { required: true } }, {
          default: () => h(ElInput, { 
            style: {
              width: 'calc(100% - 20px)'
            },
            disabled: row ? true : false,
            placeholder: '请输入主题',
            modelValue: topic.value,
            onInput: (value) => (topic.value = value)
          }),
        }),
        ...keyword_arr.value.map((item, index) => h(ElFormItem, { label: `关键词${index + 1}`, rules: { required: true } }, 
        { 
          default: () => [
            h(ElInput, { 
              style: {
                width: 'calc(100% - 20px)'
              },
              placeholder: '请输入关键词',
              modelValue: item.name,
              onInput: (value) => (item.name = value)
            }),
            keyword_arr.value.length > 1 && h(Delete, {
              style: {
                marginLeft: '4px'
              },
              width: '16px',
              class: 'with-hand',
              onClick: () => keyword_arr.value.splice(index, 1)
            })
          ]
        })),
        h(ElFormItem, { label: '' }, { default: () => h(ElButton, {
          type: 'primary',
          onClick: () => {
            keyword_arr.value.push({ name: '' })
          },
          icon: Plus
        }, () => '添加关键词')})
      ]
    })
  })
}

const handleDeleteTopic = (row?: keywordItem) => {
  ElMessageBox.confirm(
    row 
    ? `删除当前关键词主题"<span style='color: var(--el-color-primary)'>${row.name}</span>", 
    将会把当前主题下的<span style='color: var(--el-color-primary)'>${row.keyword_arr.length}个关键词</span>一并删除,确定删除`
    : `删除所选关键词主题, 将会把<span style='color: var(--el-color-primary)'>所选主题下的所有关键词</span>一并删除，确定删除`,
    '删除主题', 
  {
    dangerouslyUseHTMLString: true,
    confirmButtonText: '确认',
    cancelButtonText: '取消',
    type: 'warning',
    callback: (action: string) => {
      if (action === 'confirm') {
        useTryCatch(async () => {
          setLoading(true)
          const res = await deleteTopic({ id: `${ row ? row.id : selectIds.value}` })
          if (res.code === 200) {
            handleSearch()
            ElMessage.success(res.msg)
          } else {
            ElMessage.error(res.msg)
          }
          setLoading(false)
        }, () => setLoading(false))
      }
    }
  })
}
const handleDeleteCateTopic = (row?: keywordItem) => {
  ElMessageBox.confirm(
    row
    ? `删除当前大类"<span style='color: var(--el-color-primary)'>${row.name}</span>",
    将会把当前大类下的<span style='color: var(--el-color-primary)'>${row.topic_count}个词组及关键词</span>一并删除,确定删除?` 
    : `删除所选大类, 将会把<span style='color: var(--el-color-primary)'>所选大类下的所有词组及关键词</span>一并删除，确定删除?`,
    '删除大类',
    {
      dangerouslyUseHTMLString: true,
      confirmButtonText: '确认',
      cancelButtonText: '取消',
      type: 'warning',
      callback: (action: string) => {
        if (action === 'confirm') {
          useTryCatch( async () => {
            setLoading(true)
            const res = await deleteTopic({ id: `${ row ? row.id : selectIds2.value}` })
            if (res.code === 200) {
              handleSearch()
              handleSearch2()
              ElMessage.success(res.msg)
            } else {
              ElMessage.error(res.msg)
            }
            setLoading(false)
          }, () => setLoading(false) )
        }
      }
    }
  )
}

const handleDeleteKeyword = (row: keywordItem) => {
  ElMessageBox.confirm(`删除当前关键词"<span style='color: var(--el-color-primary)'>${row.name}</span>"`, '删除关键词', 
  {
    dangerouslyUseHTMLString: true,
    confirmButtonText: '确认',
    cancelButtonText: '取消',
    type: 'warning',
    callback: (action: string) => {
      if (action === 'confirm') {
        useTryCatch(async () => {
          setLoading(true)
          const res = await deleteKeyword({ keyword_id: row.id })
          if (res.code === 200) {
            ElMessage.success(res.msg)
            handleSearch()
          } else {
            ElMessage.error(res.msg)
          }
          setLoading(false)
        }, () => setLoading(false))
      }
    }
  })
}

const handleDetail = (row: keywordItem) => {
  router.push({
    name: 'KeywordDetail',
    query: {
      id: row.id
    }
  })
}

const handleToArtDetail = (row: keywordItem) => {
  router.push({
    name: 'ArticleManageOperate',
    query: {
      type: 'edit',
      art_id: row.star_art
    }
  })
}

_getTopicList()
Listener(route, () => {
  if (channel_item_all.value.channel_id) {
    handleSearch()
    if (switched.value) {
      handleSearch2()
    }
    _getTopicList()
  } else {
    tableData.value = []
  }
})
</script>

<style scoped lang="scss">
:deep(.el-tabs) {
  display: flex;
  flex-direction: column;
  height: 100%;
  .el-tabs__content {
    flex-grow: 1;
    .el-tab-pane {
      display: flex;
      flex-direction: column;
      height: 100%;
    }
    
  }
}
</style>