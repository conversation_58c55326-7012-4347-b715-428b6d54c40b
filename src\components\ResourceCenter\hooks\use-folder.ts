import { ElMessageBox, ElMessage } from 'element-plus'
import useTryCatch from '@/hooks/use-try-catch'
import { addFolder, deleteFolder, renameFolder } from '@/api/resource-center'
import type { Ref } from 'vue'
import { msgboxParams } from '../types'
import type Node from 'element-plus/es/components/tree/src/model/node'
import type { channelItem } from '@/store/modules/params/types'
import type { folderItem } from '@/api/resource-center'

export default 
( fileType: number, 
  site_id_all: Ref<number>,
  channel_item_all: Ref<channelItem>,
  currentNode: Ref<Node>,
  defaultExpand: Ref<number[]>,
  handleEmptyFolder: () => any, // 处理当前目录删除后文件列表更新问题
) => ({
    // 添加子目录
    handleAppend: (node: Node|null) => {
      const { data } = node || currentNode.value
      ElMessageBox.prompt(
        `请输入文件夹名称, 上级目录：${data.name}`,
        '添加子目录',
        {
          ...msgboxParams,
          callback: (actions: any) => {
            if (actions.action === 'confirm') {
              useTryCatch(
                async () => {
                  const params = {
                    site_id: site_id_all.value,
                    channel_id: channel_item_all.value.channel_id as number,
                    name: actions.value as string,
                    type: fileType,
                    pid: data.id
                  }
                  const res = await addFolder(params)
                  if (res.code === 200) {
                    if (!data.children) {
                      data.children = []
                    }
                    data.children.push({
                      name: actions.value,
                      id: res.data.id,
                      children: [],
                      file_url: `${data.file_url}${actions.value}`
                    })
                    defaultExpand.value.push(data.id)
                    ElMessage.success('创建成功')
                  } else {
                    ElMessage.error(res.msg)
                  }
                }
              )
            }
          }
        }
      )
    },
    // 重命名目录
    handleRename: (node: Node|null) => {
      const { data } = node || currentNode.value
      ElMessageBox.prompt(
        '请输入修改的文件夹名称',
        '重命名文件夹',
        {
          ...msgboxParams,
          inputPlaceholder: data.name,
          callback: (actions: any) => {
            if (actions.action === 'confirm') {
              useTryCatch(
                async () => {
                  const params = {
                    id: data.id,
                    name: actions.value
                  }
                  const res = await renameFolder(params)
                  if (res.code === 200) {
                    data.name = actions.value
                    ElMessage.success('修改成功')
                  } else {
                    ElMessage.error(res.msg)
                  }
                }
              )
            }
          }
        }
      )
    },
    // 移除子目录
    handleRemove: (node: Node|null) => {
      const { data, parent } = node || currentNode.value
      const { children } = parent.data
      if (!children) {
        return ElMessage.warning('根目录不可删除!')
      }
      const index = children.findIndex((d: folderItem) => d.id === data.id)
      ElMessageBox.confirm(
        `请确认是否删除该文件夹：${data.name}`,
        '删除文件夹',
        {
          ...msgboxParams,
          callback: (action: string) => {
            if (action === 'confirm') {
              useTryCatch(
                async () => {
                  const params = {
                    id: data.id
                  }
                  const res = await deleteFolder(params)
                  if (res.code === 200) {
                    // 此处判断下当前节点是否为被删除节点 (兼容后续contextmenu事件)
                    if (currentNode.value && ( currentNode.value.data.id === data.id )) {
                      handleEmptyFolder()
                    }
                    children.splice(index, 1)
                    ElMessage.success('删除成功')
                  } else {
                    ElMessage.error(res.msg)
                  }
                }
              )
            }
          }
        }
      )
    }
  })