<template>
  <div class="scroll-y with-flex">
    <QueryForm 
      :form-list="formList"
      :loading="loading"
      @search="handleSearch"
      @reset="handleReset"
    />
    <div v-loading="loading" class="sticky-table">
      <CustomTable 
        ref="tableRef"
        :data="tableData"
        :columns="columns"
        :table-method="getList"
        :operate-list="operateList"
      />
    </div>

    <SubscribeModal 
     v-if="loadSubscribeModal"
     :id="subscribeId"
     :readonly="subscribeRead"
     @close="loadSubscribeModal = false"
     @save="handleSave"
    />
  </div>
</template>

<script setup lang="tsx">
import { ref, reactive, toRef, defineAsyncComponent } from 'vue'
import { ElMessage, ElButton, ElMessageBox } from 'element-plus'
import { storeToRefs } from 'pinia'
import { useParamsStore } from '@/store'
import useTryCatch from '@/hooks/use-try-catch'
import useLoading from '@/hooks/use-loading'
import { getSubscribeList, changeSubscribeState } from '@/api/subscribe-management'
import { getChannelList } from '@/api/params'
import type { queryParams, listItem } from '@/api/subscribe-management'
import type { channelItem } from '@/store/modules/params/types'

import QueryForm from '@/components/QueryForm/index.vue'
import CustomTable from '@/components/CustomTable/index.vue'

const SubscribeModal = defineAsyncComponent( () => import('./components/subscribeModal.vue') )

defineOptions( { name: 'WarningSubscribe' } )

const { webList, userList } = storeToRefs(useParamsStore())
const { loading, setLoading } = useLoading()
const loadSubscribeModal = ref(false)
const subscribeId = ref(0)
const subscribeRead = ref(false)

const queryParams_raw = {
  id: '',
  name: '',
  add_user: '',
  add_time_start: '',
  add_time_end: '',
  site_id: '',
  channel_id: '',
}
const queryParams = reactive<queryParams>( { ...queryParams_raw } )
const channelList = ref<channelItem[]>()
const timeRange = ref('')

const formList = ref([
  {
    placeholder: '预警ID',
    value: toRef(queryParams, 'id'),
    component: 'input',
  },
  {
    placeholder: '预警名称',
    value: toRef(queryParams, 'name'),
    component: 'input',
  },
  {
    placeholder: '站点名称',
    value: toRef(queryParams, 'site_id'),
    component: 'select',
    selections: webList,
    labelKey: 'cms_site_name',
    valueKey: 'cms_site_id',
    handleChange: () => {
      queryParams.channel_id = ''
      if (queryParams.site_id) {
        useTryCatch( async () => {
          const res = await getChannelList(queryParams.site_id as number)
          if (res.code === 200) {
            channelList.value = res.data
          }
        } )
      } else {
        channelList.value = []
      }
    }
  },
  {
    placeholder: '渠道名称',
    value: toRef(queryParams, 'channel_id'),
    component: 'select',
    selections: channelList,
    labelKey: 'channel_code',
    valueKey: 'channel_id',
  },
  {
    placeholder: '创建人',
    value: toRef(queryParams, 'add_user'),
    component: 'select',
    labelKey: 'real_name',
    valueKey: 'user_id',
    selections: userList,
  },
  {
    label: '时间日期',
    start_placeholder: '开始日期',
    end_placeholder: '结束日期',
    value: timeRange,
    component: 'datetimerange',
    append: true,
    dateChange: (value: [string, string] | null) => {
      if (value) {
        queryParams.add_time_start = value[0]
        queryParams.add_time_end = value[1]
      } else {
        queryParams.add_time_start = ''
        queryParams.add_time_end = ''
      }
    }
  }
])

const tableRef = ref()
const tableData = ref<listItem[]>([])
const operateList = ref([
  {
    title: '新增预警',
    button: true,
    append: true,
    method: () => {
      subscribeId.value = 0
      subscribeRead.value = false
      loadSubscribeModal.value = true
    }
  }
])
const columns = ref([
  {
    title: '预警ID',
    dataKey: 'id',
    width: 120
  },
  {
    title: '预警名称',
    dataKey: 'name',
    minWidth: 150
  },
  {
    title: '应用位置（站点渠道）',
    dataKey: 'channel_name',
    minWidth: 200
  },
  {
    title: '创建人',
    dataKey: 'add_user_name',
    width: 120
  },
  {
    title: '创建时间',
    dataKey: 'add_time',
    width: 180
  },
  {
    title: '操作',
    dataKey: '',
    width: 240,
    fixed: 'right',
    cellRenderer: (scope: { row: listItem }) => (
      <>
        <ElButton type="primary" plain onClick={() => { handleEdit(scope.row) }}>编辑</ElButton>
        <ElButton type="primary" plain onClick={() => { handleView(scope.row) }}>查看</ElButton>
        <ElButton type={scope.row.state === 'able' ? 'danger' : 'success'} plain onClick={() => { handleDelete(scope.row) }}>
          { scope.row.state === 'able' ? '禁用' : '启用' }
        </ElButton>
      </>
    )
  }
])

const getList = ( { page, pageSize } ) => {
  useTryCatch( async () => {
    setLoading(true)

    const params = {
      ...queryParams,
      page,
      page_size: pageSize,
    }
    const res = await getSubscribeList(params)
    if (res.code === 200) {
      tableData.value = res.data.list
      tableRef.value?.setTotal(res.data.total)
    } else {
      ElMessage.error(res.msg)
    }

    setLoading(false)
  }, () => setLoading(false) )
}

const handleReset = () => {
  timeRange.value = ''
  Object.entries(queryParams_raw).forEach(( [ key, value ] ) => {
    queryParams[key] = value
  })
}

const refresh = () => {
  const { page, pageSize } = tableRef.value?.getPagination()
  getList({ page, pageSize })
}

const handleSearch = () => {
  tableRef.value?.setPage(1)
  refresh()
}

const handleEdit = (row: listItem) => {
  subscribeId.value = row.id
  subscribeRead.value = false
  loadSubscribeModal.value = true
}
const handleView = (row: listItem) => {
  subscribeId.value = row.id
  subscribeRead.value = true
  loadSubscribeModal.value = true
}
const handleDelete = (row: listItem) => {
 const state = row.state
  ElMessageBox.confirm(
    `请确认是否${state === 'able' ? '禁用' : '启用'}?`,
    '提示',
    {
      confirmButtonText: '确认',
      cancelButtonText: '取消',
      callback: (action: string) => {
        if (action === 'confirm') {
          useTryCatch( async () => {
            const res = await changeSubscribeState(row.id, state === 'able' ? 'disable' : 'able')
            if (res.code === 200) {
              refresh()
              ElMessage.success(res.msg)
            } else {
              ElMessage.error(res.msg)
            }
          } )
        }
      }
    }
  )
}

const handleSave = () => {
  loadSubscribeModal.value = false
  handleSearch()
}
</script>