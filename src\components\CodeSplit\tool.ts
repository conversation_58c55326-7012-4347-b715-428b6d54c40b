import { html as beautify_html } from 'js-beautify'

type loc = {
  source: string;
  start: string;
  end: string;
}

export type props = {
  type: number;
  name: string;
  value: { content: string };
  loc: loc;
}

export type child = {
  type: number;
  content: string;
  tag: string;
  children: child[];
  loc: loc;
  props: props[];
}

const beautifyHtml = (content: string) => beautify_html(content, 
  {
    "indent_size": 2,
    "indent_char": " ",
    "max_preserve_newlines": "5",
    "preserve_newlines": true,
    "keep_array_indentation": false,
    "break_chained_methods": false,
    "indent_scripts": "normal",
    "brace_style": "collapse",
    "space_before_conditional": true,
    "unescape_strings": false,
    "end_with_newline": false,
    "wrap_line_length": "0",
    "indent_inner_html": false,
    "comma_first": false,
    "e4x": false,
    "indent_empty_lines": false
  }
)

const dfsText = (children: child[], result:string[] = []) => {
  children.forEach(child => {
    child.type === 1 && dfsText(child.children, result)
    child.type === 2 && child.content.trim() && result.push(child.content)
  })

  return result
}

const dfsHrefs = (children: child[], result:string[] = []) => {
  children.forEach(child => {
    if (child.type === 1) {
      if (child.tag === 'a' ) {
        for ( let prop of child.props ) {
          if (prop.name === 'href') {
            result.push(prop.value.content)
            break 
          }
        }
      }
      dfsText(child.children, result)
    }
  })

  return result
}

const dfsImgs = (children: child[], result: string[] = []) => {
  children.forEach(child => {
    if (child.type === 1) {
      if (child.tag === 'img' ) {
        for ( let prop of child.props ) {
          if (prop.name === 'src') {
            result.push(prop.value.content)
            break 
          }
        }
      }
      dfsText(child.children, result)
    }
  })

  return result
}

const dfsAll = (children: child[], result: { text: string[]; href: string[]; img: string[] } = { text: [], href: [], img: [] }) => {
  children.forEach(child => {
    if (child.type === 1) {
      if (child.tag === 'a' ) {
        for ( let prop of child.props ) {
          if (prop.name === 'href') {
            result.href.push(prop.value.content)
            break 
          }
        }
      }
      if (child.tag === 'img' ) {
        for ( let prop of child.props ) {
          if (prop.name === 'src') {
            result.img.push(prop.value.content)
            break 
          }
        }
      }
      dfsAll(child.children, result)
    }
    child.type === 2 && child.content.trim() && result.text.push(child.content)
  })
  return result
}

const isSelfCloseTag = (tagName: string) => {
  return /br|input|img|br|meta|link|source/.test(tagName)
}

// 提取出a标签和img标签的href以及src属性，其余保留返回
const resolveProps = (tag: string, props: props[]) => {
  const propKey = tag === 'a' ? 'href' : tag === 'img' ? 'src' : ''
  if (tag === 'a' || tag === 'img') {
    return props.filter(prop => prop.name !== propKey).map(prop => prop.loc.source).join(' ')
  }

  return props.map(prop => prop.loc.source).join(' ')
}

// 判断是否存在src或href属性
const hasProp = (props: props[]) => {
  for( let prop of props ) {
    if (prop.name === 'src' || prop.name === 'href') {
      return true
    }
  }
  return false
}

const setPropValue = (tag: string, props: props[], value: string) => {
  if (tag === 'a' || tag === 'img') {
    for( let prop of props ) {
      if (prop.name === 'src' || prop.name === 'href') {
        return `${prop.name}="${value}" `
      }
    }
  }
  return ''
}

// 是否为纯文本节点，若是则不添加收尾换行符号
const isTextNodeOnly = (child: child) => {
  if (child.children.length === 0) {
    return false
  }
  if (child.children.length > 1 ) {
    return true
  }

  if (child.children[0].type === 2) {
    return false
  }
  return true
}

export { beautifyHtml, dfsText, dfsHrefs, dfsImgs, dfsAll, isSelfCloseTag, resolveProps, hasProp, setPropValue, isTextNodeOnly }