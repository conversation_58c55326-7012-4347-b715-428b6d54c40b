<template>
  <div
    ref="aceEditorRef"
    class="ace-editor-container"
    :style="{
      width,
      height,
      borderRadius: isPopup ? '0' : '4px'
    }"
  >
    <template v-if="errorInfo.length > 0">
      <div class="ace-editor-info-box">
        <span v-show="showErrorInfo" style="background-color: var(--el-color-error); color: var(--el-color-white); padding: 0 5px;">
          此段代码在
          <strong>{{ errorInfo.toString() }}</strong>
          行存在语法报错，请检查该行及其所在上下行代码并修复错误(点击可依次跳转至错误行)
        </span>
        <Warning 
          style="width: 24px; height: 24px; cursor: pointer; color: var(--el-color-error);"
          @mouseover="() => showErrorInfo = true"
          @mouseleave="() => showErrorInfo = false"
          @click="handleErrorJump"
        />
      </div>
    </template>
    <div 
      class="ace-editor-tool-box-tigger"
      :style="{ 
        top: errorInfo.length > 0 ? '24px' : '0',
        color: theme === 'dark' ? '#fff' : 'inherit',
      }"
    >
      <svg-icon icon-class="tool" @click="toggleToolBox(true)" />
    </div>
    <div
      class="ace-editor-tool-box"
      :style="{ transform: `translateX(${ showToolBox ? '0' : '100%' })` }"
    >
      <div style="text-align: right;">
        <Close style="width: 24px; cursor: pointer;" @click="toggleToolBox(false)" />
      </div>
      <el-space direction="vertical" fill>
        <el-button type="primary" @click="handleWrap"> 切换换行模式 </el-button>
        <el-button type="primary" @click="handleRTL"> 切换左右阅读模式 </el-button>
        <el-button type="primary" @click="handleUndo"> 撤销更改 </el-button>
        <el-button type="primary" @click="handleRedo"> 恢复更改 </el-button>
        <el-button type="primary" @click="showFindWidget"> 查找/替换关键字 </el-button>
        <template v-if="!showJumpInput">
          <el-button type="primary" @click="() => showJumpInput = true"> 跳转指定行 </el-button>
        </template>
        <template v-else>
          <el-input v-model="jumpLine" type="number" placehoder="请输入行数">
            <template #append>
              <el-button type="primary" :icon="Right" @click="handleGotoLine(jumpLine)" />
            </template>
          </el-input>
        </template>
        <div class="text-right"> 总行数：{{ totalLineCount }} </div>
      </el-space>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted } from 'vue'
import { Warning, Close, Right } from '@element-plus/icons-vue'
import { useConfigStore } from '@/store/config'
// 主要面向运营编辑基础html代码，所以只引入html相关js，若有需要可去官网阅读文档按需进行拓展
// https://github.com/ajaxorg/ace
// https://github.com/ajaxorg/ace-builds/issues/129
// https://github.com/ajaxorg/ace/wiki/Configuring-Ace
import Ace from  'ace-builds/src-min-noconflict/ace'
import { Split } from "ace-builds/src-min-noconflict/ext-split"
import "ace-builds/src-min-noconflict/ext-language_tools"
import "ace-builds/src-min-noconflict/snippets/html"
import "ace-builds/src-min-noconflict/ext-prompt"
import "ace-builds/src-min-noconflict/ext-rtl"
import workerHtmlUrl from 'ace-builds/src-min-noconflict/worker-html?url'
import searchboxUrl from 'ace-builds/src-min-noconflict/ext-searchbox?url'
// 暂时提供两个主题，白色与黑色，后续再进行拓展
import Light from 'ace-builds/src-min-noconflict/theme-sqlserver'
import Dark from 'ace-builds/src-min-noconflict/theme-tomorrow_night'
import 'ace-builds/src-min-noconflict/mode-html'

import SvgIcon from '@/icons/SvgIcon.vue'

Ace.config.setModuleUrl('ace/mode/html_worker', workerHtmlUrl)
Ace.config.setModuleUrl('ace/ext/searchbox', searchboxUrl)

type splitOrientationEmnu = 'none'|'beside'|'below'
type annot = {
  row: number;
  type: string;
  text: string;
}
type aceTheme = {
  cssClass: string;
  cssText: string;
  isDark: boolean;
}

const wrap = localStorage.getItem('ace_wrap')
const { theme: configTheme } = useConfigStore()

const props = withDefaults(defineProps<
  {
    width?: string;
    height?: string;
    code: string;
    isPopup?: boolean;
  }
>(),{
  width: () => '100%',
  height: () => '100%'
})

const emit = defineEmits(['change'])

const options = {
  mode: 'ace/mode/html',
  theme: configTheme === 'dark' ? Dark : Light,
  selectionStyle: 'text',
  showPrintMargin: false,
  fontSize: 16,
  enableBasicAutocompletion: true,
  enableLiveAutocompletion: true,
  enableSnippets: true,
  tabSize: 2,
  wrap: wrap === '1' ? true : false
}

const aceEditorRef = ref()
const ace_editor = ref<HTMLElement|null>(null)
const theme = ref(configTheme)
const currentAceTheme = ref(options.theme)
const splitOrientation = ref<splitOrientationEmnu>('beside')
const errorInfo = ref<number[]>([])
const showErrorInfo = ref(false)
const totalLineCount = ref(0)
const annotChanged = ref(false) //是否主动修改了编辑器错误提示，触发clearAnnotations、 setAnnotations方法
const errorJumpIndex = ref(0)
const showJumpInput = ref(false)
const jumpLine = ref(1)
const showToolBox = ref(false)
const wrapMode = ref(wrap === '1' ? true : false)
const rtl = ref(false)
const editor = ref()
const split = ref()

const setValue = (val: string) => {
  editor.value?.setValue(val, -1)
}
// 拆分编辑器模式
const editorSplitLoad = () => {
  const SPLIT = split.value
  if (SPLIT) {
    SPLIT.setOrientation( splitOrientation.value === 'below' ? SPLIT.BELOW : SPLIT.BESIDE )

    if (SPLIT.getSplits() === 1) {
      SPLIT.setSplits(2)
      const session = SPLIT.getEditor(0).session
      const newSession = SPLIT.setSession(SPLIT.$cloneSession(session), 1)
      newSession.name = session.name
    }

    SPLIT.resize(true)
    SPLIT.getEditor(1).setOptions({ ...options, theme: editor.value?.getTheme() })
  }
}
// 单个编辑器模式
const handleSingle = () => split.value?.setSplits(1)
// 处理拆分操作
const handleSplit = (orientation: splitOrientationEmnu) => {
  splitOrientation.value = orientation
  if (orientation === 'none') {
    handleSingle()
    return
  }
  editorSplitLoad()
}
// 改变主题
const handleTheme = (mode: string) => {
  theme.value = mode
  const themeObj = mode === 'dark' ? Dark : Light
  for ( const editor of split.value?.$editors ) {
    editor.setTheme(themeObj)
    mockToggleAceClass(themeObj, currentAceTheme.value)
  }
}
// 修改字体大小
const handleFontSize = (size: number) => {
  for ( const editor of split.value?.$editors ) {
    editor.setFontSize(size)
  }
}
// resize
const handleResize = () => {
  split.value.resize(true)
}
// 打开查找widget
const showFindWidget = () => editor.value?.execCommand('find')
// 跳转指定行，命令式
const showGoToLine = () => editor.value?.execCommand('gotoline')
// 撤销
const handleUndo = () => editor.value?.execCommand('undo')
// 撤销反操作
const handleRedo = () => editor.value?.execCommand('redo')
// 跳转指定行
const handleGotoLine = (line: number) => {
  editor.value?.gotoLine(line, 0, true)
}
// 切换换行模式
const handleWrap = () => {
  wrapMode.value = !wrapMode.value
  editor.value?.session.setUseWrapMode(wrapMode.value)
  localStorage.setItem('ace_wrap', wrapMode.value ? '1' : '0')
}
// 获取总行数
const getTotalLine = (): number => editor.value?.session.getLength()
// 从右往左读模式，兼容阿语之类的阅读习惯
const handleRTL = () => {
  rtl.value = !rtl.value
  for ( const editor of split.value?.$editors ) {
    editor.setOption('rtl', rtl.value)
  }
}
// 跳转指定错误行
const handleErrorJump = () => {
  if (errorJumpIndex.value === errorInfo.value.length) {
    errorJumpIndex.value = 0
  }
  handleGotoLine(errorInfo.value[errorJumpIndex.value++])
}
// 切换工具栏
const toggleToolBox = (show: boolean) => showToolBox.value = show
const handleChange = () => {
  totalLineCount.value = getTotalLine()
  emit('change', editor.value.getValue())
}

// 模拟切换主题操作，修复此版本下无法自动切花主题类名问题
const mockToggleAceClass = (currentTheme: aceTheme, prevTheme: aceTheme) => {
  if (ace_editor.value) {
    ace_editor.value.classList.remove(prevTheme.cssClass)
    ace_editor.value.classList.add(currentTheme.cssClass)

    currentAceTheme.value = currentTheme
  }
}

// 插入文本
const handleInsert = (position: { row: number; column: number; }, text: string) => {
  editor.value?.session.insert(position, text)
}

onMounted(() => {
  const aceContainer = aceEditorRef.value as HTMLElement
  const splitInstance = new Split(aceContainer)
  ace_editor.value = aceContainer.querySelector('.ace_editor')
  ace_editor.value?.classList.remove('ace-tm')
  
  editor.value = splitInstance.getEditor(0)
  split.value = splitInstance

  editor.value.setOptions(options)
  handleTheme(configTheme)
  editor.value.setValue(props.code, -1)

  totalLineCount.value = getTotalLine()

  editor.value.on('change', handleChange)
  editor.value.session.setUndoManager(new Ace.UndoManager())

  // 监听提示，并提炼错误所在行
  editor.value.getSession().on('changeAnnotation', () => {
    if (annotChanged.value) {
      annotChanged.value = false
      return
    }

    const annots: annot[] = editor.value.getSession().getAnnotations()
    if (annots.length === 0) {
      errorInfo.value = []
      return
    }

    const rowSet = new Set<number>(), filterAnnots: annot[] = []
    let needFilter = false
    for ( const annot of annots ) {
      if (annot.text !== 'Named entity expected. Got none.') {
        filterAnnots.push(annot)
        annot.type === 'error' && rowSet.add(annot.row + 1)
        annotChanged.value = true
        needFilter = true
      }
    }

    if (filterAnnots.length === 0) {
      editor.value.session.clearAnnotations()
      return
    }

    needFilter && editor.value.session.setAnnotations(filterAnnots)
    errorInfo.value = [...rowSet]
    errorJumpIndex.value = 0
  })
})
onUnmounted(() => {
  editor.value && editor.value.destroy()
  split.value = null
})

defineExpose({
  setValue,
  getValue: (): string => editor.value.getValue(),
  handleFontSize,
  handleTheme,
  handleSplit,
  handleResize,
  showFindWidget,
  showGoToLine,
  handleRedo,
  handleUndo,
  handleRTL,
  handleWrap,
  handleInsert,
  hasError: () => errorInfo.value.length > 0
})
</script>

<style>
.ace-editor-container {
  position: relative;
  overflow: hidden;
  border: 1px solid var(--el-border-color-light);
}
.ace-editor-info-box {
  position: absolute;
  z-index: 5;
  right: 5px;
  top: 0;
  display: flex;
  align-items: center;
}
.ace-editor-tool-box {
    position: absolute;
    z-index: 6;
    right: 0;
    top: 0;
    height: 100%;
    width: 180px;
    padding: 5px 15px;
    box-shadow: var(--layout-box-shadow);
    background-color: var(--el-bg-color);
    transition: transform .5s;
    text-align: left;
}
.ace-editor-tool-box-tigger {
    position: absolute;
    right: 8px;
    top: 24px;
    z-index: 1;
    cursor: pointer;
}
.ace-tomorrow-night .ace_marker-layer .ace_selection {
  background: #4d6ba9 !important;
}
</style>