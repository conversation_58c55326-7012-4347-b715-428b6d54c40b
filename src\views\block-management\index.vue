<template>
  <div class="scroll-y with-flex">
    <QueryForm
      :form-list="formList"
      :loading="loading"
      @search="handleSearch" 
      @reset="reset"
    />
    <div v-loading="loading" class="sticky-table">
      <CustomTable
        ref="tableRef"
        :columns="columns"
        :data="tableData"
        :table-method="getList"
        :customize-head="true"
        :selectable="true"
        :operate-list="operateList"
        @selection-change="handleSelectionChange"
        @sort-change="handleSortChange"
      />
    </div>
  </div>
</template>

<script setup lang="tsx">
import { ref, reactive, toRef, computed, onActivated } from 'vue'
import { ElMessage, ElButton } from 'element-plus'
import { useRoute, useRouter } from 'vue-router'
import { storeToRefs } from 'pinia'
import { useParamsStore, useUserStroe } from '@/store'
import { useBasicStore } from '@/store/basic'
import useTryCatch from '@/hooks/use-try-catch'
import { getHost } from '@/hooks/use-env'
import useLoading from '@/hooks/use-loading'
import { useBlockGenerate } from '@/hooks/use-generate'
import { getBlockList } from '@/api/block-management'
import type { queryParams, listItem } from '@/api/block-management'
import Listener from '@/utils/site-channel-listeners'
import { stringify } from '@/utils/common'

import QueryForm from '@/components/QueryForm/index.vue'
import CustomTable from '@/components/CustomTable/index.vue'

defineOptions( { name: 'BlockManagement' } )

const router = useRouter()
const route = useRoute()
const { site_id_all, channel_item_all, userList } = storeToRefs(useParamsStore())
const { blockTypeList } = useParamsStore()
const { userInfo, banIds, isBanned } = useUserStroe()
const basicStore = useBasicStore()
const { loading, setLoading } = useLoading()
const tableRef = ref()
const tableData = ref<listItem[]>([])
const tableSelections = ref<listItem[]>([])
const disableOperate = computed(() => tableSelections.value.length > 0 ? false : true)
const selectedIds = computed(() => tableSelections.value.map(( { blk_id } ) => blk_id).join(','))
const queryParams_raw = {
  site_id: site_id_all.value,
  blk_name: '',
  blk_type: '',
  language: '',
  user_id_a: '',
  user_id_e: '',
  user_id_c: '',
  created_state: '',
  tpl_ids: '',
  art_ids: '',
  blk_ids: '',
  file_path: '',
  channel_id: '',
  is_main: '',
  time_field: '',
  start_time: '',
  end_time: ''
}
const block_type_map = {}
blockTypeList.forEach(({ label, value }) => {
  block_type_map[value] = label
})
const queryParams = reactive<queryParams>({
  ...queryParams_raw,
  blk_ids: route.query.entity_ids as string || ''
})
const timeRange = ref('')
const formList = ref([
  {
    label: '块id',
    clearable: true,
    placeholder: '块id',
    value: toRef(queryParams, 'blk_ids'),
    component: 'input',
    parse: true
  },
  {
    label: '块名称',
    clearable: true,
    placeholder: '块名称',
    value: toRef(queryParams, 'blk_name'),
    component: 'input',
  },
  {
    label: '块类型',
    clearable: true,
    placeholder: '块类型',
    value: toRef(queryParams, 'blk_type'),
    component: 'select',
    selections: blockTypeList
  },
  {
    label: '块路径',
    clearable: true,
    placeholder: '块路径',
    value: toRef(queryParams, 'file_path'),
    component: 'input',
  },
  {
    label: '创建人',
    clearable: true,
    placeholder: '创建人',
    value: toRef(queryParams, 'user_id_a'),
    component: 'select',
    labelKey: 'real_name',
    valueKey: 'user_id',
    append: true,
    selections: userList
  },
  {
    label: '更新人',
    clearable: true,
    placeholder: '更新人',
    value: toRef(queryParams, 'user_id_e'),
    component: 'select',
    labelKey: 'real_name',
    valueKey: 'user_id',
    append: true,
    selections: userList
  },
  {
    label: '生成人',
    clearable: true,
    placeholder: '生成人',
    value: toRef(queryParams, 'user_id_c'),
    component: 'select',
    labelKey: 'real_name',
    valueKey: 'user_id',
    append: true,
    selections: userList
  },
  {
    label: '生成状态',
    clearable: true,
    placeholder: '生成状态',
    value: toRef(queryParams, 'created_state'),
    component: 'select',
    selections: [
      {
        label: '未生成',
        value: 'uncreated',
      },
      {
        label: '已生成',
        value: 'created',
      },
      {
        label: '已修改',
        value: 'modified',
      }
    ],
    multiple: true
  },
  {
    label: '是否主块',
    clearable: true,
    placeholder: '是否主块',
    value: toRef(queryParams, 'is_main'),
    component: 'select',
    append: true,
    selections: [
      {
        label: '是',
        value: '1'
      }
    ]
  },
  {
    component: 'time-range-tab',
    field_key: toRef(queryParams, 'time_field'),
    value: timeRange,
    start_time: toRef(queryParams, 'start_time'),
    end_time: toRef(queryParams, 'end_time'),
    append: true,
    field_list: [
      {
        value: 'add_time',
        label: '创建时间'
      },
      {
        value: 'edit_time',
        label: '更新时间'
      },
      {
        value: 'create_time',
        label: '发布时间'
      },
    ],
  },
])
const columns = ref([
  {
    title: '块id',
    dataKey: 'blk_id',
    width: 100,
    sortable: true
  },
  {
    title: '块等级',
    dataKey: 'blk_level',
    width: 90,
    cellRenderer: (scope: { row: listItem }) => (
      <span style={ { 
        color: scope.row.blk_level === 'A' ? 'var(--el-color-error)' : 'inherit', 
        fontWeight: scope.row.blk_level === 'A' ? 700 : 400 
      } }>
        { scope.row.blk_level || '--' }
      </span>
    )
  },
  {
    title: '块名称',
    dataKey: 'blk_name',
    minWidth: 200,
  },
  {
    title: '块类型',
    dataKey: 'blk_type',
    width: 150,
    cellRenderer: (scope: { row: listItem }) => (<>{ block_type_map[scope.row.blk_type] || '/' }</>)
  },
  {
    title: '主渠道块ID',
    dataKey: 'parent_id',
    width: 100,
  },
  {
    title: '块渠道',
    dataKey: 'channel_name',
    width: 120,
    cellRenderer: (scope: { row: listItem }) => (
      <span class='dot' style={ { '--color': scope.row.color } }>
        { scope.row.channel_name }
      </span>
    )
  },
  {
    title: '块路径',
    dataKey: 'file_path',
    minWidth: 180,
  },
  {
    title: '生成状态',
    dataKey: 'created_state',
    cellRenderer: (scope: { row: listItem }) => (
      <span style={{ color: scope.row.created_state === 'uncreated' ? 'inherit' : scope.row.created_state === 'created' ? 'var(--el-color-success)' : 'var(--el-color-primary)' }}>
        { scope.row.created_state_name }
      </span>
    )
  },
  {
    title: '创建人',
    dataKey: 'user_name_a',
  },
  {
    title: '创建时间',
    dataKey: 'add_time',
    width: 180,
    sortable: true
  },
  {
    title: '更新人',
    dataKey: 'user_name_e',
  },
  {
    title: '更新时间',
    dataKey: 'edit_time',
    width: 180,
    sortable: true
  },
  {
    title: '生成人',
    dataKey: 'user_name_c',
  },
  {
    title: '生成时间',
    dataKey: 'create_time',
    width: 180,
  },
  {
    title: '操作',
    dataKey: '',
    fixed: 'right',
    width: 380,
    cellRenderer: (scope: { row: listItem }) => (
      <>
        <ElButton type='primary' plain onClick={() => handleEdit(scope.row)}>编辑</ElButton>
        <ElButton type='success' plain onClick={() => handleGenerateBlock(scope.row)} v-RoleIdsBanPermission={banIds}>生成块</ElButton>
        <ElButton type='primary' plain onClick={() => handleHistory(scope.row)}>历史版本</ElButton>
        <ElButton type='primary' plain disabled={scope.row.parent_id !== 0} onClick={() => handleCreateChildBlock(scope.row)}>子块管理</ElButton>
      </>
    )
  }
])
const operateList = ref([
  {
    title: '创建主块',
    button: true,
    append: true,
    method: () => {
      basicStore.delCachedView('BlockManagementOperate')
      router.push({
        name: 'BlockManagementOperate',
        query: {
          type: 'add'
        }
      })
    },
    disabled: computed<boolean>(() => channel_item_all.value.type === 'master' ? false : true)
  },
  ...isBanned() ? [] : [
    {
      title: '生成选中块',
      icon: 'generate',
      method: () => {
        const blk_ids = tableSelections.value.map( ( { blk_id } ) => blk_id ).join(',')
        useBlockGenerate({
          blk_ids,
          site_id: site_id_all.value
        }, () => {
          refresh()
        })
      },
      disabled: disableOperate
    }
  ],
  {
    title: '导出EXCEL',
    icon: 'export',
    method: () => {
      const { page, pageSize } = tableRef.value
      const params = {
        S: {
          ...queryParams,
          site_id: site_id_all.value,
          blk_ids: selectedIds.value
        },
        page: page || 1,
        page_size: pageSize || 10,
        channel_id: channel_item_all.value.channel_id || ''
      }
      location.href = `${getHost()}/api/v1/blocks/export?${stringify(params)}`
    },
    disabled: disableOperate
  },
])

const reset = () => {
  timeRange.value = ''
  Object.entries(queryParams_raw).forEach(([ key, value ]) => {
    queryParams[key] = value
  })
}
const refresh = () => {
  const { page, pageSize } = tableRef?.value?.getPagination()
  getList( { page, pageSize } )
}
const handleSearch = () => {
  tableRef.value.setPage(1)
  refresh()
}
const getList = ( { page, pageSize }, prop?: string, order?: 'asc'|'desc' ) => {
  if (tableRef?.value) {
    const sortObj = tableRef?.value?.getSort()
    if (sortObj.order && !order) {
      prop = sortObj.prop
      order = sortObj.order
    }
  }
  
  useTryCatch( async () => {
    const params = {
      S: {
        ...queryParams,
        channel_id: channel_item_all.value.channel_id as number,
        site_id: site_id_all.value,
        created_state: String(queryParams.created_state),

      },
      page,
      page_size: pageSize,
      ...order ? {
        sort_key: prop,
        sort_type: order
      } : {}
    }
    setLoading(true)
    const res = await getBlockList(params)
    if (res.code === 200) {
      tableRef.value?.setTotal(res.data.total)
      tableData.value = res.data.items
    } else {
      ElMessage(res.msg)
    }
    setLoading(false)
  }, () => setLoading(false))
}
const handleSelectionChange = (selections: listItem[]) => {
  tableSelections.value = selections
}
/* table methods */
const handleEdit = (row: listItem) => {
  basicStore.delCachedView('BlockManagementOperate')
  router.push({
    name: 'BlockManagementOperate',
    query: {
      type: 'edit',
      blk_id: row.blk_id,
      ...row.parent_id ? { parent_id: row.parent_id } : {}
    }
  })
}
const handleGenerateBlock = (row: listItem) => {
  useBlockGenerate({
    blk_ids: `${row.blk_id}`,
    site_id: site_id_all.value
  }, () => {
    refresh()
  })
}
const handleHistory = (row: listItem) => {
  basicStore.delCachedView('BlockHistory')
  router.push({
    name: 'BlockHistory',
    query: {
      id: row.blk_id
    }
  })
}
const handleCreateChildBlock = (row: listItem) => {
  router.push({
    name: 'CreateChildBlock',
    query: {
      blk_id: row.blk_id
    }
  })
}
const handleSortChange = ({ prop, order }: { prop: string; order: 'ascending'|'descending'|null }) => {
  const { page, pageSize } = tableRef?.value?.getPagination()
  getList( { page, pageSize }, prop, order ? order.replace('ending', '') as 'asc'|'desc' : undefined )
}
/* table methods */
onActivated(() => {
  if (basicStore.refresh) {
    basicStore.setRefresh(false)
    refresh()
  }
})
Listener(route, () => {
  reset()
  handleSearch()
})
</script>