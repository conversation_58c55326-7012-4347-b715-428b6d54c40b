<template>
  <el-dialog
    v-model="show"
    :title="`弹窗配置${props.title}`"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    append-to-body
    align-center
    width="1000px"
    @closed="emit('close')"
  >
    <template #default>
      <el-form
        ref="formRef"
        :model="formData"
        :rules="formRules"
        label-suffix=":"
        label-width="150"
        :disabled="disabled"
      >
        <h3 style="font-weight: 700; margin-bottom: 30px; font-size: 14px;"> 弹窗内容配置 </h3>
        <el-form-item label="弹窗标题" prop="title">
          <el-input v-model="formData.title" :maxlength="200" show-word-limit />
        </el-form-item>
        <el-form-item label="正文内容" prop="content">
          <el-input v-model="formData.content" type="textarea" :maxlength="300" show-word-limit />
        </el-form-item>
        <el-form-item label="弹窗按钮文案" prop="button_txt">
          <el-input v-model="formData.button_txt" :maxlength="50" show-word-limit />
        </el-form-item>
        <el-form-item label="跳转按钮URL" prop="button_url">
          <el-input v-model="formData.button_url" />
        </el-form-item>
        <h3 style="font-weight: 700; margin: 30px 0; font-size: 14px;"> 弹窗应用信息 </h3>
        <el-form-item label="弹窗应用位置" prop="channel_id">
          <template v-if="!props.row">
            <el-select v-model="site_id" placeholder="请选择应用站点" filterable @change="handleSiteChange">
              <el-option 
                v-for="d in webList"
                :value="d.cms_site_id"
                :label="d.cms_site_name"
              />
            </el-select>
            <el-select v-model="formData.channel_id" placeholder="请选择应用渠道" filterable style="margin-left: 30px;">
              <el-option 
                v-for="d in channelList"
                :value="d.channel_id"
                :label="d.channel_code"
              />
            </el-select>
          </template>
          <template v-else> {{ props.row.channel_name }} </template>
        </el-form-item>
      </el-form>
    </template>
    <template #footer>
      <div class="text-center">
        <el-button @click="show = false"> 取消 </el-button>
        <el-button type="primary" :loading="loading" :disabled="disabled" @click="handleSave(formRef)"> 确定 </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, reactive } from 'vue'
import { ElMessage } from 'element-plus'
import { useParamsStore } from '@/store'
import useTryCatch from '@/hooks/use-try-catch'
import useLoading from '@/hooks/use-loading'
import { getChannelList } from '@/api/params'
import { addRegister, editRegister } from '@/api/web-config'
import type { registerData, registerListItem } from '@/api/web-config'
import type { FormInstance } from 'element-plus'
import { formRules } from './rules'

const { webList } = useParamsStore()
const { loading, setLoading } = useLoading()
const channelList = ref<any[]>([])
const site_id = ref<string|number>('')

const props = defineProps<{
  title: string;
  row: registerListItem | null;
}>()

const disabled = props.title === '详情'
const emit = defineEmits(['close', 'save'])

const show = ref(true)
const formRef = ref<FormInstance>()

const formData_raw = {
  title: '',
  content: '',
  button_txt: '',
  button_url: '',
  channel_id: '',
}

if (props.row) {
  const row = props.row as registerListItem
  Object.keys(formData_raw).forEach((key) => {
    formData_raw[key] = row[key]
  })
}

const formData = reactive<registerData>({
  ...formData_raw,
  ...props.row ? { id: props.row.id } : {}
})

const handleSiteChange = (val: number) => {
  formData.channel_id = ''
  useTryCatch( async () => {
    const res = await getChannelList(val)
    if (res.code === 200) {
      channelList.value = res.data
    }
  } )
}

const handleSave = async ( formEl: FormInstance | undefined ) => {
  if (!formEl) {
    return
  }
  await formEl.validate( ( valid ) => {
    if (valid) {
      useTryCatch( async () => {
        setLoading(true)

        const params = {
          ...formData         
        }
        
        const api = props.row ? editRegister : addRegister
        const res = await api( params )
        if (res.code === 200) {
          show.value = false
          emit('save')
          ElMessage.success(res.msg)
        } else {
          ElMessage.error(res.msg)
        }

        setLoading(false)
      }, () => setLoading(false) )
    }
  } )
}
</script>