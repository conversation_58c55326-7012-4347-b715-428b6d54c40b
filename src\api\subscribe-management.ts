import axiosReq from './axios'
import qs from 'query-string'

export type queryParams = {
  site_id?: number|string;
  channel_id?: number|string;
  id: string;
  name: string;
  add_user: string;
  add_time_start: string;
  add_time_end: string;
  page?: number;
  page_size?: number;
}

export type listItem = {
  id: number;
  name: string;
  site_id: number;
  channel_id: number;
  add_user: number;
  add_time: string;
  channel_name: string;
  add_user_name: string;
  state: 'able'|'disable';
}

export type ruleItem = {
  module: string;
  scene: string;
  symbol: string;
  rule_value: string;
  unit: string;
}

export type subscribeData = {
  id?: number|string;
  name: string;
  channel_id: string|number;
  notice_type: string|number;    //1,站内信  2 邮件  3 站内信&邮件
  site_id: string|number;
  cycle: string;
  notice_content: string;
  notice_user_id: string|number;
  rule: ruleItem[];
}

export type detailData = {
  id: number;
  name: string;
  notice_user_id: string;
  site_id: number;
  channel_id: number;
  notice_content: string;
  cycle: string;
  type: string;
  state: string;
  add_user: string;
  edit_user: string;
  add_time: string;
  edit_time: string;
  cycle_show: string;
  rule: {
    id: number;
    module: string;
    symbol: string;
    scene: string;
    rule_value: string;
    unit: string;
    module_show: string;
    scene_show: string;
    symbol_show: string;
    unit_show: string;
  }[];
}

export type ruleData = {
  module: { key: string; val: string; scene: {key: string; val: string;}[] }[];
  unit: {key: string; val: string;}[];
  cycle: {key: string; val: string;}[];
  symbol: {key: string; val: string;}[];
  notice_type: {key: string; val: string;}[];
}

// 订阅规则枚举接口
export const getSubscribeRule = () =>
  axiosReq('get', '/api/v1/subscribe/add_conf')

// 订阅列表
export const getSubscribeList = (params: queryParams) => 
  axiosReq('get', '/api/v1/subscribe/list', {
    params,
    paramsSerializer: {
      serialize: (obj: queryParams) => qs.stringify(obj)
    }
  })

// 添加订阅 
export const addSubscribe = (data: subscribeData) =>
  axiosReq('post', '/api/v1/subscribe/add', data)

// 编辑订阅
export const editSubscribe = (data: subscribeData) =>
  axiosReq('post', '/api/v1/subscribe/edit', data)

// 订阅详情
export const getSubscribeDetail = (id: number|string) =>
  axiosReq('get', `/api/v1/subscribe/info?id=${id}`)

// 修改订阅状态
export const changeSubscribeState = (id: number|string, state: 'able'|'disable') =>
  axiosReq('post', '/api/v1/subscribe/change_state', {id, state})
