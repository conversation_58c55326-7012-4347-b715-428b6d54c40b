<template>
  <el-dialog
    v-model="show"
    title="word预览"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    append-to-body
    align-center
    width="1200"
    @closed="emit('close')"
  >
    <div v-loading="loading" style="height: 85vh; overflow: auto;">
      <div id="wordContainer" ref="wordContainer"></div>
    </div>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import useLoading from '@/hooks/use-loading'

const show = ref(false)
const wordContainer = ref<HTMLElement|null>(null)
const { loading, setLoading } = useLoading(true)
const emit = defineEmits(['close'])

const options = { 
  inWrapper: false, 
  ignoreWidth: true, 
  ignoreHeight: true,
  useBase64URL: true,
}

defineExpose({
  show: () => show.value = true,
  handlePreview: (file: Blob) => {
    // https://jstool.gitlab.io/demo/docx-preview.js_word_to_html/zh-cn.html
    docx.renderAsync(file, wordContainer.value, null, options).then( () => setLoading(false) )
  }
})

</script>

<style>
#wordContainer span{ display: inline; }
</style>