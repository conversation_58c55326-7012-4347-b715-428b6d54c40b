import axiosReq from './axios'
import qs from 'query-string'

export type msgListParams = {
  type: string;
  is_read: string;
  page_size: number;
  page: number;
}

export type msgListItem = {
  add_time: string;
  checked?: boolean;
  title: string;
  id: number;
  url: string;
  content: string;
  type?: number;
  is_read: number;
}

// 近一分钟未读消息+总的未读信息数
export const get1MinUnread = () => 
  axiosReq('get', '/api/v1/message/unread_list')

// 所有消息列表
export const getMsgList = (params: msgListParams) => 
  axiosReq('get', '/api/v1/message/list', {
    params,
    paramsSerializer: {
      serialize: (obj: msgListParams) => qs.stringify(obj)
    }
  })

// 未读消息条数
export const getUnreadNum = () => 
  axiosReq('get', '/api/v1/message/unread_count')

// 标记消息为已读
export const setMessageReaded = (data: {message_id: string}) => 
  axiosReq('post', '/api/v1/message/read', data)

// 一键已读所有消息
export const setMessageAllReaded = (data: { type: string }) => 
  axiosReq('post', '/api/v1/message/read_by_type', data)