<template>
  <el-dialog
    v-model="show"
    title="同集模板复用页面"
    width="1140px"
    align-center
    @close="emit('close')"
  >
    <template #default>
      <div v-show="step ===1">
        <p class="mb-20px">
          同一集群的模板中, 可以互相批量复用页面; 例如A模板中有50个页面, B模板中有10个页面; B模板可以从A模板批量复制不重复页面(根据url来区分)
          <br>
          PS: 如页面为文章页面, 相关文章会一起复用。
        </p>
        <div class="mb-40px">
          <p>
            Tips: 此功能可用于模板下页面的首次复制和二次复用。
          </p>
          <p>
            首次复制场景(例): 常用于模板的二次复用后，再次复用页面（首次复用一般通过复制模板时，同步选择需复制页面）
          </p>
          <p>
            例: EN渠道文章模板上新N篇新文章, 多语言渠道需要翻译复用; 批量复用相关页面及文章
          </p>
        </div>
        <div class="mb-20px">
          <strong>选中来源的模板渠道(单选): </strong>
          <el-select v-model="sourceChannelId" filterable @change="handleSourceChange">
            <el-option 
              v-for="(d) in channelList"
              :key="d.channel_id"
              :value="d.channel_id"
              :label="d.channel_code"
            />
          </el-select>
        </div>
        <el-table :data="sourceList" border class="mb-40px">
          <el-table-column prop="channel_name" label="模板渠道" width="100" />
          <el-table-column prop="tpl_id" label="模板ID" />
          <el-table-column prop="name" label="模板名称" />
          <el-table-column prop="edit_time" label="最近更新时间" width="180" />
        </el-table>
  
        <div class="mb-20px">
          <strong>复用对象的模板渠道(单选): </strong>
          <el-select v-model="targetChannelId" filterable @change="handleTargetChange" :disabled="sourceList.length === 0">
            <el-option 
              v-for="(d) in channelList"
              :key="d.channel_id"
              :value="d.channel_id"
              :label="d.channel_code"
              :disabled="d.channel_id === sourceChannelId"
            />
          </el-select>
        </div>
        <el-table :data="targetList" border>
          <el-table-column prop="channel_name" label="模板渠道" width="100" />
          <el-table-column prop="tpl_id" label="模板ID" />
          <el-table-column prop="name" label="模板名称" />
          <el-table-column prop="edit_time" label="最近更新时间" width="180" />
        </el-table>
      </div>
      <div v-show="step ===2">
        <p>
          可从来源模板中的页面中, 进行选择; 选到的页面将会进行复用。(<span style="color: var(--el-color-danger)">如果存在url重复等情况, 将会在复用过程中过滤掉</span>, 具体情况请查看复用结果)
        </p>
        <QueryForm
          hide-reset
          :form-list="formList"
          :loading="loading"
          @search="handleSearch" 
        />
        <div :loading="loading">
          <CustomTable 
            ref="tableRef"
            :columns="columns"
            :data="tableData"
            :table-method="getList"
            selectable
            skip-init
            height="600"
            @selection-change="handleSelectionChange"
          />
        </div>
        <p style="padding-top: 20px;">选中页面数：<strong style="font-size: 1.5em;color: var(--el-color-primary)">{{ selectedPages }}</strong></p>
      </div>
    </template>
    <template #footer>
      <div class="text-center">
        <el-button @click="handleClose">取消</el-button>
        <template v-if="step === 1">
          <el-button type="primary" :disabled="targetList.length === 0" @click="handleNext"> 下一步 </el-button>
        </template>
        <template v-else>
          <el-button @click="handlePrev"> 上一步 </el-button>
          <el-popconfirm
            width="220"
            confirm-button-text="确认"
            cancel-button-text="取消"
            title="请确认是否进行已选页面复用"
            @confirm="handleCopy"
            :hide-after="0"
          >
            <template #reference>
              <el-button type="primary" :loading="loading" :disabled="tableSelections.length === 0"> 复用 </el-button>
            </template>
          </el-popconfirm>
          <el-popconfirm
            width="220"
            confirm-button-text="确认"
            cancel-button-text="取消"
            title="此操作会复用该模板下所有页面(包含未在列表中展示数据)，请确认是否进行复用"
            @confirm="handleCopy"
            :hide-after="0"
          >
            <template #reference>
              <el-button type="primary" :loading="loading" @click="handleCopyAll"> 复用模板下全部页面 </el-button>
            </template>
          </el-popconfirm>
        </template>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="tsx">
import { ref, reactive, toRef, nextTick } from 'vue'
import { ElMessage } from 'element-plus'
import { useParamsStore } from '@/store'
import useTryCatch from '@/hooks/use-try-catch'
import useLoading from '@/hooks/use-loading'
import { copyTempPage, getTempGroupList } from '@/api/template-management'
import { getPagesList } from '@/api/page-management'
import type { groupListItem } from '@/api/template-management'
import type { queryParams, listItem } from '@/api/page-management'
import emitter from '@/utils/bus'

import QueryForm from '@/components/QueryForm/index.vue'
import CustomTable from '@/components/CustomTable/index.vue'

const props = defineProps<
  {
    channelList?: {channel_id: number; channel_code: string}[];
    tableData?: groupListItem[];
    currentRow?: groupListItem;
  }
>()
const emit = defineEmits(['close', 'success'])

const dataMap = new Map<number, groupListItem>()
const channelMap = new Map<number, string>()
const channelList = ref<{channel_id: number; channel_code: string}[]>([])
const grouptableData = ref<groupListItem[]>([])

const page_state_map = {
  'new': {
    color: '',
    text: '未生成'
  },
  'created': {
    color: 'var(--el-color-success)',
    text: '已生成'
  },
  'modified': {
    color: 'var(--el-color-primary)',
    text: '已修改'
  },
  'disable': {
    color: 'var(--el-color-error)',
    text: '已禁用'
  },
}

const { pageStateList } = useParamsStore()
const { loading, setLoading } = useLoading()
const show = ref(true)
const step = ref(1)
const sourceList = ref<groupListItem[]>([])
const targetList = ref<any[]>([])

const queryParams_raw = {
  tpl_page_ids: '',
  url: '',
  page_state: ['new','modified','created'],
  module: '',
  type: '',
}
const queryParams = reactive<queryParams>({...queryParams_raw})
const formList = ref([
  {
    label: '页面id',
    clearable: true,
    placeholder: '页面id',
    value: toRef(queryParams, 'tpl_page_ids'),
    component: 'input',
  },
  {
    label: '页面状态',
    clearable: true,
    placeholder: '页面状态',
    value: toRef(queryParams, 'page_state'),
    component: 'select',
    selections: pageStateList.slice(0,3),
    multiple: true
  },
  {
    label: '页面url',
    clearable: true,
    placeholder: '页面url',
    value: toRef(queryParams, 'url'),
    component: 'input',
  },
])
const tableRef = ref()
const tableData = ref<listItem[]>([])
const selectAll = ref(false)
const tableSelections = ref<listItem[]>([])

const columns = ref([
  {
    title: '页面id',
    dataKey: 'tpl_page_id',
    width: 100,
  },
  {
    title: '页面url',
    dataKey: 'url',
    minWidth: 200,
  },
  {
    title: '页面状态',
    dataKey: 'state',
    width: 100,
    cellRenderer: (scope: { row: listItem }) => (
      <span style={ { 
        color: page_state_map[scope.row.page_state]?.color
      } }>
        { page_state_map[scope.row.page_state]?.text }
      </span>
    )
  },
  {
    title: '更新人',
    dataKey: 'user_name_e',
    width: 100,
  },
  {
    title: '更新时间',
    dataKey: 'edit_time',
    width: 180,
  },
])

const selectedPages = computed(() => {
  const total = tableRef.value?.getTotal() as number
  return selectAll.value ? (total || 0) : tableSelections.value.length
})

const sourceChannelId = ref<string|number>('')
const targetChannelId = ref<string|number>('')

const reset = (all = false) => {
  targetList.value = []
  targetChannelId.value = ''
  if (all) {
    sourceChannelId.value = ''
    sourceList.value = []
  }
}

const handleSourceChange = () => {
  const item = dataMap.get(sourceChannelId.value as number)
  sourceList.value = item ? [ item ] : []
  reset()
}
const handleTargetChange = () => {
  const item = dataMap.get(targetChannelId.value as number)
  targetList.value = item ? [ item ] : []
}

const handleNext = () => {
  step.value++
  handleSearch()
}

const handlePrev = () => {
  step.value--
}

const getList = ( { page, pageSize } ) => {
  useTryCatch( async () => {
    setLoading(true)
    const params = {
      S: { 
        ...queryParams,
        tpl_id: `${sourceList.value[0].tpl_id}`,
        page_state: String(queryParams.page_state)
      },
      page,
      page_size: pageSize
    }
    const res = await getPagesList(params)
    if (res.code === 200) {
      tableData.value = res.data.items
      tableRef.value?.setTotal(res.data.total)
    } else {
      ElMessage.error(res.msg)
    }
    setLoading(false)
  }, () => setLoading(false) )
}
const handleSearch = () => {
  tableRef.value?.setPage(1)
  const { page, pageSize } = tableRef.value?.getPagination()
  getList( { page, pageSize })
}
const handleSelectionChange = (selections: listItem[]) => {
  tableSelections.value = selections
}

const handleCopy = () => {
  useTryCatch( async () => {
    setLoading(true)
    const params = {
      tpl_id: sourceList.value[0].tpl_id,
      to_tpl_id: targetList.value[0].tpl_id,
      page_ids: selectAll.value ? '' : tableSelections.value.map( ({ tpl_page_id }) => tpl_page_id ).join(',')
    }

    const res = await copyTempPage(params)
    if (res.code === 200) {
      ElMessage.success(res.msg)
      emit('success')
      emitter.emit('noticeUpdate')
      handleClose()
    } else {
      ElMessage.error(res.msg)
    }
    setLoading(false)
  }, () => setLoading(false) )
}

const handleCopyAll = () => {
  tableSelections.value = []
  selectAll.value = true
  Object.keys(queryParams_raw).forEach((key) => {
    queryParams[key] = queryParams_raw[key]
  })
  handleSearch()
}

const handleClose = () => {
  show.value = false
}

const resolveList = () => {
  channelList.value.forEach((item) => {
    channelMap.set(item.channel_id, item.channel_code)
  })
  grouptableData.value.forEach((item) => {
    dataMap.set(item.channel_id, item)
  })
  if (props.currentRow) {
    handleSourceChange()
  }
}

if (props.channelList && props.tableData) {
  channelList.value = props.channelList
  grouptableData.value = props.tableData
  resolveList()
} else {
  if (props.currentRow) {
    useTryCatch( async () => {
      const res = await getTempGroupList(props.currentRow?.tpl_id as number)
      if (res.code === 200) {
        grouptableData.value = res.data.list
        channelList.value = res.data.channel_list
        resolveList()
      } else {
        ElMessage.error(res.msg)
      }
    })
  }
}

if (props.currentRow) {
  sourceChannelId.value = props.currentRow.channel_id
  handleSourceChange()
}

defineExpose({
  show: () => show.value = true,
  handleSourceChannel: (channel_id?: number) => {
    sourceChannelId.value = channel_id || ''
    handleSourceChange()
    if (step.value === 2) {
      step.value--
    }
  }
})
</script>