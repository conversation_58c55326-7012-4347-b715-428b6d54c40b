<template>
  <div class="scroll-y with-flex">
    <el-result
      v-show="!channel_item_all.channel_id"
      icon="warning"
      title="请先选择具体渠道!" 
      sub-title="您还未选择渠道"
      style="position: absolute; width: 100%; height: 100%; z-index: 10; background: var(--bg-layout);"
    />
    <div style="overflow: hidden; margin-bottom: 20px;">
      <el-row :gutter="20">
        <el-col :span="12">
          当前站点: <strong>{{ channel_item_all.host }}</strong>
        </el-col>
      </el-row>
    </div>

    <div style="overflow: hidden; margin-bottom: 20px;">
      <el-row :gutter="20">
        <el-col :span="8">
          <div style="border-radius: 4px; border: 1px solid var(--el-border-color); padding: 10px;" :style="{ height: `${height2 + 76}px` }">
            <h3 style="margin-bottom: 20px;">Google总体表现得分</h3>
            <Pie 
              v-if="performance_ratio.length > 0"
              :data="performance_ratio"
              :height="`${height2}px`"
              :colors="[ 
                formateGreaidentColor('#0FCA7A','#76EBB9'), 
                formateGreaidentColor('#AAC5FF','#B3CBFF'), 
                formateGreaidentColor('#FFBA5F','#FFE49F'), 
              ]"
            />

            <div style="font-size: 14px;"> <strong>页面总数: {{ performance_total }}</strong> </div>
          </div>
        </el-col>
        <el-col :span="16">
          <div style="border-radius: 4px; border: 1px solid var(--el-border-color); padding: 10px;" :style="{ height: `${height2 + 76}px` }">
            <el-row gutters="20">            
              <el-col :span="12">
                <h3 style="margin-bottom: 20px;">LCP综合优良率</h3>
                <Pie 
                  v-if="lcp_ratio.length > 0"
                  :data="lcp_ratio"
                  :height="`${height2}px`"
                  :colors="[ 
                    formateGreaidentColor('#0FCA7A','#76EBB9'), 
                    formateGreaidentColor('#BCB6FFCC','#9990FFCC'), 
                    formateGreaidentColor('#FFAA89','#FF6969'), 
                  ]"
                />
                <div style="font-size: 14px;"> <strong>页面总数: {{ lcp_total }}</strong> </div>
              </el-col>
              <el-col :span="12">
                <PublicOperate :operate-list="operateList_performance" style="margin-bottom: 0;" />
                <el-table :data="performance_list" border size="small" class="show-border" height="210">
                  <el-table-column label="可优化(2.5~4.0)页面" prop="lcp_normal_url" show-overflow-tooltip>
                    <template #default="{ row }"> {{ row.lcp_normal_url || '/' }} </template>
                  </el-table-column>
                  <el-table-column label="优良率较差(得分>4.0)页面" prop="lcp_bad_url" show-overflow-tooltip>
                    <template #default="{ row }"> {{ row.lcp_bad_url || '/' }} </template>
                  </el-table-column>
                </el-table>
              </el-col>
            </el-row>
          </div>
        </el-col>
      </el-row>
    </div>

    
    <div style="overflow: hidden; margin-bottom: 10px; flex-grow: 1;">
      <div style="border-radius: 4px; border: 1px solid var(--el-border-color); padding: 10px; height: 100%; display: flex; flex-direction: column;">
        <div style="display: flex; flex-wrap: nowrap;">
          <h3 style="margin-bottom: 20px;">Semrush 质量预警</h3>
          <div style="color: rgba(36, 41, 52, 0.78); margin-left: 20px;">更新时间: <strong>{{ update_time }}</strong></div>
        </div>
        <div :style="{ height: `${height + 30}px`, }">
          <el-row :gutter="20">
            <el-col :span="12">
              <div style="margin-bottom: 10px; font-size: 18px; font-weight: 700; display: flex; align-items: center;">
                <span style="display: inline-block; padding: 0 10px; border-right: 2px solid var(--el-border-color); margin-right: 16px; ">错误</span>
                <span style="color: var(--el-color-error); margin-right: 10px;" >{{ error_num }}</span>
                <el-tag v-if="error_diff !== 0" :type="error_diff > 0 ? 'danger' : 'success'" effect="dark">
                  {{ error_diff > 0 ? `↑ +${error_diff}` : `↓ ${error_diff}` }}
                </el-tag>
              </div>
              <Line 
                v-if="data_error.length > 0"
                :x-axis="xAxis_error" 
                :data="data_error" 
                :height="`${height}px`"
                color="#F53F3F" 
              />
            </el-col>
            <el-col :span="12">
              <div style="margin-bottom: 10px; font-size: 18px; font-weight: 700; display: flex; align-items: center;">
                <span style="display: inline-block; padding: 0 10px; border-right: 2px solid var(--el-border-color); margin-right: 16px; ">警告</span>
                <span style="color: var(--el-color-warning); margin-right: 10px;" >{{ warning_num }}</span>
                <el-tag v-if="warning_diff !== 0" :type="warning_diff > 0 ? 'danger' : 'success'" effect="dark">
                  {{ warning_diff > 0 ? `↑ +${warning_diff}` : `↓ ${warning_diff}` }}
                </el-tag>
              </div>
              <Line 
                v-if="data_warning.length > 0"
                :x-axis="xAxis_warning" 
                :data="data_warning" 
                :height="`${height}px`"
                color="#FF9500" 
              />
            </el-col>
          </el-row>
        </div>

        <el-tabs v-model="activeType" @tab-change="handleTabChange">
          <el-tab-pane label="错误" name="error" />
          <el-tab-pane label="警告" name="warning" />
        </el-tabs>

        <QueryForm
          :form-list="formList"
          hide-reset
          @search="handleSearch"
        />

        <div v-loading="loading" class="sticky-table">
          <CustomTable 
            ref="tableRef"
            :data="tableData"
            :columns="columns"
            :operate-list="operateList"
            :index-method="( index ) => index + 1"
            selectable
            hide-pagination
            @selection-change="handleSelectionChange"
          />
        </div>

      </div>
    </div>

  </div>
</template>

<script setup lang="tsx">
import { ref, computed } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { ElButton, ElMessage } from 'element-plus'
import { useParamsStore } from '@/store'
import { storeToRefs } from 'pinia'
import useTryCatch from '@/hooks/use-try-catch'
import { getHost } from '@/hooks/use-env'
import useLoading from '@/hooks/use-loading'
import Listener from '@/utils/site-channel-listeners'
import { getSemrushOverview, getSemrushIssueList, getSemrushSnapshotList, getLighthouseOverview } from '@/api/web-quality-management'
import type { snapshotListItem } from '@/api/web-quality-management'

import QueryForm from '@/components/QueryForm/index.vue'
import CustomTable from '@/components/CustomTable/index.vue'
import PublicOperate from '@/components/PublicOperate/index.vue'
import Line from './components/Line.vue'
import Pie from './components/Pie.vue'

defineOptions( { name: 'EarlyWarning' } )

const height = 230
const height2 = 194
const route = useRoute()
const router = useRouter()
const { channel_item_all } = storeToRefs(useParamsStore())
const { loading, setLoading } = useLoading()

const update_time = ref('')
const warning_num = ref(0)
const warning_diff = ref(0)
const error_num = ref(0)
const error_diff = ref(0)
const activeType = ref('error')
const issue_id = ref('')
const xAxis_error = ref([])
const data_error = ref([])
const xAxis_warning = ref([])
const data_warning = ref([])
const error_list = ref<snapshotListItem[]>([])
const warning_list = ref<snapshotListItem[]>([])
const error_issue_list = ref<{ key: number; value: string }[]>([])
const warning_issue_list = ref<{ key: number; value: string }[]>([])
const snapshot_id = ref('')
const performance_ratio = ref<{ value: number; name: string }[]>([])
const performance_total = ref(0)
const lcp_ratio = ref<{ value: number; name: string }[]>([])
const lcp_total = ref(0)
const performance_list = ref<{lcp_normal_url:string;lcp_normal_point: string;lcp_bad_url: string;lcp_bad_point: string;}[]>([])

const operateList_performance = ref([
  {
    title: '导出列表',
    append: true,
    method: () => {
      location.href = `${getHost()}/api/v1/lighthouse/export_low_page?channel_id=${channel_item_all.value.channel_id}`
    }
  }
])

const formList = ref([
  {
    placeholder: '问题类型',
    value: issue_id,
    component: 'select',
    valueKey: 'key',
    labelKey: 'value',
    selections: computed(() => activeType.value === 'error' ? error_issue_list.value : warning_issue_list.value),
  }
])

const tableRef = ref()
const tableData = ref<snapshotListItem[]>([])
const tableSelections = ref<snapshotListItem[]>([])
const operateList = ref([
  {
    title: '批量导出',
    button: true,
    append: true,
    disabled: computed( () => tableSelections.value.length === 0 ),
    method: () => {
      const issue_id = tableSelections.value.map( item => item.issue_id ).join(',')
      handleExport(issue_id)
    }
  }
])
const columns = ref([
  {
    title: '问题类型',
    dataKey: 'issue_desc',
    minWidth: 150
  },
  {
    title: '问题页面数',
    dataKey: 'source_page_num',
    width: 150
  },
  {
    title: '问题链接数',
    dataKey: 'target_page_num',
    width: 150
  },
  {
    title: '操作',
    dataKey: '',
    fixed: 'right',
    width: 180,
    cellRenderer: ( scope: { row: snapshotListItem } ) => (
      <>
        <ElButton type='primary' plain onClick={() => hanldeDetail(scope.row)}>查看</ElButton>
        <ElButton type='primary' plain onClick={() => handleExport(scope.row.issue_id)}>导出</ElButton>
      </>
    )
  }
])

const setList = () => {
  if (activeType.value === 'error') {
    tableData.value = error_list.value
  } else {
    tableData.value = warning_list.value
  }
}

const reset = () => {
  data_error.value = []
  data_warning.value = []
  performance_ratio.value = []
  lcp_ratio.value = []
  issue_id.value = ''
  activeType.value = 'error'
}

const handleTabChange = () => {
  issue_id.value = ''
  setList()
}

const handleSearch = () => {
  getList()
}

const getOverview = () => {
  useTryCatch( async () => {
    const res = await getSemrushOverview(channel_item_all.value.channel_id as number)


    if (res.code === 200) {
      update_time.value = res.data.new_data.update_time
      error_num.value = res.data.new_data.error.notice_num
      error_diff.value = res.data.new_data.error.change_value
      warning_num.value = res.data.new_data.warning.notice_num
      warning_diff.value = res.data.new_data.warning.change_value

      const errorList = res.data.history.error
      const warningList = res.data.history.warning

      xAxis_error.value = errorList.map(item => item.update_time)
      data_error.value = errorList.map(item => item.notice_num)
      xAxis_warning.value = warningList.map(item => item.update_time)
      data_warning.value = warningList.map(item => item.notice_num)

    } else {
      ElMessage.error(res.msg)
    }
  } )
}

const getList = () => {
  if (!channel_item_all.value.channel_id) return
  useTryCatch( async () => {
    setLoading(true)

    const res = await getSemrushSnapshotList(channel_item_all.value.channel_id as number, issue_id.value)
    if (res.code === 200) {
      error_list.value = res.data.error_list
      warning_list.value = res.data.warning_list
      snapshot_id.value = res.data.snapshot_id
      setList()
    } else {
      ElMessage.error(res.msg)
    }

    setLoading(false)
  }, () => setLoading(false) )
}

const handleSelectionChange = ( selections: snapshotListItem[] ) => {
  tableSelections.value = selections
}

const hanldeDetail = (row : snapshotListItem) => {
  router.push({
    name: 'EarlyWarningDetail',
    query: {
      channel_id: channel_item_all.value.channel_id,
      snapshot_id: snapshot_id.value,
      issue_id: row.issue_id,
      issue_type: row.issue_desc
    }
  })
}

const handleExport = (issue_id: string|number) => {
  location.href = `${getHost()}/api/v1/semrush/download?channel_id=${channel_item_all.value.channel_id}&snapshot_id=${snapshot_id.value}&issue_id=${issue_id}`
}

const initIssueList = () => {
  useTryCatch( async () => {
    const res = await getSemrushIssueList()
    if (res.code === 200) {
      error_issue_list.value = res.data.error
      warning_issue_list.value = res.data.warning
    } 
  } )
}

const getGooglePerformance = () => {
  useTryCatch( async () => {
    const res = await getLighthouseOverview(channel_item_all.value.channel_id as number)
    if (res.code === 200) {
      performance_ratio.value = res.data.performance.list
      performance_total.value= res.data.performance.total
      lcp_ratio.value = res.data.lcp.list
      lcp_total.value = res.data.lcp.total
      performance_list.value = res.data.lcp_page_list
    } else {
      ElMessage.error(res.msg)
    }
  })
}

const formateGreaidentColor = (start: string, stop: string) => {
  return {
    type: 'linear',
    x: 0,
    y: 0,
    x2: 0,
    y2: 1,
    colorStops: [{
        offset: 0, color: start
    }, {
        offset: 1, color: stop
    }],
    global: false
  }
}


const init = () => {
  getOverview()
  getList()
  getGooglePerformance()
}

if (channel_item_all.value.channel_id) {
  init()
}
Listener(route, () => {
  if (channel_item_all.value.channel_id) {
    reset()
    init()
  }
})

initIssueList()
</script>

<style scoped lang="scss">
:deep(.sticky-table) {
  min-height: auto;
}
:deep(.show-border) {
  &::before, &::after {
    opacity: 1;
  }
  .el-table__cell  {
    border-right: var(--el-table-border)!important;
  }
}
.num-container {
  text-align: center;
  padding: 30px 0;
  font-size: 20px;
  .num-box {
    display: inline-block;
    font-size: 36px;
    font-weight: 700;
    margin-right: 12px;
  }
  .rotate {
    transform: rotateX(180deg);
  }
}
</style>