<template>
  <el-dialog
    v-model="show"
    title="链接到404的页面"
    width="800"
    align-center
    append-to-body
    @closed="emit('close')"
  >
    <template #default>
      <CustomTable
        ref="tableRef"
        :columns="columns"
        :data="tableData"
        :table-method="getList"
        :operate-list="operateList"
        height="auto"
      />
    </template>
    <template #footer>
      <div class="text-center">
        <el-button @click="show = false">关闭</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { useRouter } from 'vue-router'
import { useParamsStore } from '@/store'
import { useBasicStore } from '@/store/basic'
import useTryCatch from '@/hooks/use-try-catch'
import { site404QuoteList } from '@/api/tools-management'
import type { site404QuoteItem } from '@/api/tools-management'

import CustomTable from '@/components/CustomTable/index.vue'

const router = useRouter()
const { site_id_all } = useParamsStore()
const basicStore = useBasicStore()

const props = defineProps<
{
  link_id: number;
}>()

const emit = defineEmits(['close'])

const show = ref(true)
const tableRef = ref()
const tableData = ref<site404QuoteItem[]>([])
const columns = ref([
  {
    title: '页面ID',
    dataKey: 'tpl_page_id',
    width: 100,
  },
  {
    title: '页面ID',
    dataKey: 'file_url',
    minWidth: 200,
  },
  {
    title: '所属渠道',
    dataKey: 'channel_code',
    width: 100,
  }
])

const getList = ( { page, pageSize } ) => {
  useTryCatch( async () => {
    const params = {
      site_id: site_id_all,
      page,
      page_size: pageSize
    }

    const res = await site404QuoteList( props.link_id, params )
    if (res.code === 200) {
      tableData.value = res.data.items
      tableRef.value?.setTotal(res.data.total)
    }
  } )
}
const operateList = ref([
  {
    title: '查看页面(当前分页)',
    method: () => {
      const page_ids = tableData.value.map(item => item.tpl_page_id).join(',')
      basicStore.delCachedView('PageManagement')
      router.push({
        name: 'PageManagement',
        query: {
          entity_ids: page_ids
        }
      })
      show.value = false
    }
  }
])
</script>