<template>
  <div class="scroll-y with-flex">
    <QueryForm 
      :form-list="formList"
      :loading="loading"
      @search="handleSearch"
      @reset="reset"
    />
    <div :loading="loading" class="sticky-table">
      <CustomTable 
        ref="tableRef"
        :data="tableData"
        :columns="columns"
        :table-method="getList"
        :operate-list="operateList"
        :row-class-name="( { row } ) => row.isChildren ? 'bg-lighter' : ''"
        row-key="c_id"
        @expand-change="handleExpandChange"
        @row-click="handleRowClick"
      />
    </div>

    <OperateModal 
      v-if="loadModal"
      :type="loadType"
      :form-data="currentRow"
      @close="loadModal = false"
      @saved="handleSearch"
    />
  </div>
</template>

<script setup lang="tsx">
import { ref, reactive, toRef, defineAsyncComponent } from 'vue'
import { 
  ElMessage, 
  ElButton, 
  ElMessageBox, } from 'element-plus'
import useTryCatch from '@/hooks/use-try-catch'
import useLoading from '@/hooks/use-loading'
import { getSupplierList, deleteSupplier } from '@/api/translate-management'
import type { supplierListItem } from '@/api/translate-management'

import QueryForm from '@/components/QueryForm/index.vue'
import CustomTable from '@/components/CustomTable/index.vue'
const OperateModal = defineAsyncComponent( () => import('./components/operateModal.vue') )

defineOptions( { name: 'TranslateSupplier' } )

const { loading, setLoading } = useLoading()
const loadModal = ref(false)
const loadType = ref<'add'|'edit'|'view'>('add')
const currentRow = ref<supplierListItem>()

const queryParams_raw = {
  id: '',
  name: '',
}

const queryParams = reactive({
  ...queryParams_raw
})
const formList = ref([
  {
    label: '供应商ID',
    placeholder: '供应商ID',
    value: toRef(queryParams, 'id'),
    component: 'input',
  },
  {
    label: '供应商名称',
    placeholder: '供应商名称',
    value: toRef(queryParams, 'name'),
    component: 'input',
  }
])

const tableRef = ref()
const tableData = ref<supplierListItem[]>([])
const operateList = ref([
  {
    title: '添加供应商',
    button: true,
    append: true,
    method: () => {
      loadModal.value = true
      loadType.value = 'add'
    }
  }
])
const columns = ref([
  {
    title: '供应商ID',
    dataKey: 'id',
    width: 100,
    cellRenderer: ( scope: { row: supplierListItem } ) => (
      <>
        { scope.row.isChildren ? '' : scope.row.id }
      </>
    )
  },
  {
    title: '供应商名称',
    dataKey: 'full_name',
    minWidth: 150,
    cellRenderer: ( scope: { row: supplierListItem } ) => (
      <>
        { scope.row.isChildren ? '' : scope.row.full_name }
      </>
    )
  },
  {
    title: '服务',
    dataKey: 'services',
    minWidth: 150,
    cellRenderer: ( scope: { row: supplierListItem } ) => (
      <>
        { scope.row.isChildren 
          ? `${scope.row.service?.source_language_name} -> ${scope.row.service?.target_language_name}` 
          : scope.row.services.length > 0 ? '翻译' : ''
        }
      </>
    )
  },
  {
    title: '价格',
    dataKey: 'price',
    width: 150,
    cellRenderer: ( scope: { row: supplierListItem } ) => (
      <>
        { 
          scope.row.isChildren 
          ? `${scope.row.service?.price_per_unit} ${scope.row.service?.currency}/字` 
          : <span style='color: var(--el-color-primary); cursor: pointer;'>
              { scope.row.expanded ? '收起' : scope.row.services.length > 0 ?  '展开查看' : '' }
            </span> 
        }
      </>
    )
  },
  {
    title: '创建人',
    dataKey: 'add_user',
    width: 100,
    cellRenderer: ( scope: { row: supplierListItem } ) => (
      <> { scope.row.isChildren ? '' : scope.row.add_user } </>
    )
  },
  {
    title: '创建时间',
    dataKey: 'add_time',
    width: 180,
    cellRenderer: ( scope: { row: supplierListItem } ) => (
      <> { scope.row.isChildren ? '' : scope.row.add_time } </>
    )
  },
  {
    title: '备注',
    dataKey: 'remark',
    minWidth: 100,
    cellRenderer: ( scope: { row: supplierListItem } ) => (
      <> { scope.row.isChildren ? '' : (scope.row.remark || '/') } </>
    )
  },
  {
    title: '操作',
    dataKey: '',
    width: 240,
    fixed: 'right',
    cellRenderer: ( scope: { row: supplierListItem } ) => (
      <> 
        {
          !scope.row.isChildren && 
          <>
            <ElButton type='primary' plain onClick={() => handleView(scope.row)}>查看</ElButton>
            <ElButton type='primary' plain onClick={() => handleEdit(scope.row)}>编辑</ElButton>
            {
              scope.row.services.length > 0 && 
              <ElButton type='danger' plain onClick={() => handleDelete(scope.row)}>删除</ElButton>
            }
            
          </>
        }
      </>
    )
  }
])

const getList = ( { page, pageSize } ) => {
  useTryCatch( async () => {
    setLoading(true)

    const params = {
      id: queryParams.id,
      name: queryParams.name,
      page,
      pageSize,
    }
    const res = await getSupplierList(params)
    if (res.code === 200) {
      tableRef.value?.setTotal(res.data.total)
      const list = res.data.list.map(( item: supplierListItem ) => {
        const newItem = { ...item, c_id: item.id }
        if (newItem.services) {
          newItem.children = newItem.services.map(( service, index ) => {
            return {
              ...newItem,
              c_id: `${newItem.id}-${index}`,
              service,
              isChildren: true
            }
          })
        }
        return newItem
      })
      tableData.value = list
    } else {
      ElMessage.error(res.msg)
    }

    setLoading(false)
  }, () => setLoading(false) )
}

const reset = () => {
  Object.entries(queryParams_raw).forEach(( [ key, value ] ) => {
    queryParams[key] = value
  })
}

const refresh = () => {
  const { page, pageSize } = tableRef?.value?.getPagination()
  getList( { page, pageSize } )
}

const handleSearch = () => {
  tableRef.value.setPage(1)
  refresh()
}

const handleExpandChange = (row: supplierListItem, expanded: boolean) => {
  row.expanded = expanded
}

const handleRowClick = ( row: supplierListItem, column: any ) => {
  const { customTableRef } = tableRef.value?.$refs
  if (customTableRef && column.property === 'price') {
    customTableRef.toggleRowExpansion(row)
  }
}

const handleEdit = (row: supplierListItem) => {
  loadType.value = 'edit'
  loadModal.value = true
  currentRow.value = row
}

const handleView = (row: supplierListItem) => {
  loadType.value = 'view'
  loadModal.value = true
  currentRow.value = row
}

const handleDelete = (row: supplierListItem) => {
  ElMessageBox.confirm(`确认删除当前供应商${row.full_name}的信息?`, '删除供应商信息', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    callback: (action: string) => {
      if (action === 'confirm') {
        useTryCatch( async () => {
          const res = await deleteSupplier(row.id)
          if (res.code === 200) {
            refresh()
            ElMessage.success(res.msg)
          } else {
            ElMessage.error(res.msg)
          }
        } )
      }
    }
  })
}
</script>