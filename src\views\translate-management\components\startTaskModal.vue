<template>
  <el-dialog
    v-model="show"
    title="发起翻译任务"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    append-to-body
    align-center
    width="85%"
    @closed="emit('close')"
  >
    <template #default>
      <el-form inline>
        <el-form-item label="任务名称:" class="is-required">
          <el-input v-model="name" placeholder="任务名称" clearable />
        </el-form-item>
        <el-form-item label="翻译语言:" class="is-required">
          <el-select 
            v-model="targetLangs" 
            multiple 
            collapse-tags 
            collapse-tags-tooltip 
            clearable 
            placeholder="选择翻译语言"
          >
            <el-option 
              v-for="(d, index) in targetLangList"
              :key="index"
              :value="d.language"
              :label="d.lang_name"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="完成时间" class="is-required">
          <el-date-picker 
            v-model="deadLine"
            type="datetime"
            value-format="YYYY-MM-DD HH:mm:ss"
            placeholder="请选择期望完成时间"
          />
        </el-form-item>
        <el-form-item label="翻译方式" class="is-required">
          <el-space :size="16">
            <el-select v-model="queryParams.type" placeholder="请选择翻译方式">
              <el-option 
                v-for="(d) in typeList"
                :label="d.label"
                :value="d.value"
                :disabled="(levelAIds.size > 0 && d.value !== '1')"
              />
            </el-select>
            <el-radio-group v-if="queryParams.type === '2'" v-model="AItype">
              <el-radio label="4">OpenAI gpt-4.1</el-radio>
              <el-radio label="2">OpenAI gpt-3.5</el-radio>
              <el-radio label="3">讯飞AI</el-radio>
            </el-radio-group>
          </el-space>
          <div v-if="levelAIds.size > 0" style="font-size: 12px; color: var(--el-color-error); white-space: nowrap; position: absolute; bottom: -26px;">
            当前翻译任务包含有A级页面, 暂不支持AI翻译, 请知悉
          </div>
        </el-form-item>
      </el-form>
      <div v-loading="loading" style="min-height: 100px; padding-top: 20px;">
        <CustomTable
          v-if="tableData.length > 0"
          :data="tableData"
          :columns="columns"
          hide-pagination
          :height="height"
          :operate-list="operateList"
          selectable
          default-expand-all
          :select-method="(row: taskListItem) => row.filed_list.length > 0"
          row-key="id"
          @selection-change="handleSelectionChange"
        />
      </div>
    </template>
    <template #footer>
      <div class="text-center">
        <el-button @click="show = false">取消</el-button>
        <el-tooltip content="提交内容与勾选状态无关, 勾选仅提供批量快捷操作, 按提示完成所有必填内容填写即可" >
          <el-button type="primary" :loading="loading" @click="handleSubmit">提交</el-button>
        </el-tooltip>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="tsx">
import { ref, reactive } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage, ElSelect, ElOption, ElButton, ElMessageBox } from 'element-plus'
import { useParamsStore } from '@/store'
import { useBasicStore } from '@/store/basic'
import useTryCatch from '@/hooks/use-try-catch'
import useLoading from '@/hooks/use-loading'
import { getStartList, createJob } from '@/api/translate-management'
import type { taskListQuery, taskListItem, langItem } from '@/api/translate-management'
import { typeList } from '../data'

import CustomTable from '@/components/CustomTable/index.vue'

const props = withDefaults(defineProps<{
  entity_type?: string;
  entity_ids: string;
}>(), {
  entity_type: 'page'
})
const emit = defineEmits(['close'])

const router = useRouter()
const { site_id_all, channel_item_all } = useParamsStore()
const basicStore = useBasicStore()
const { loading, setLoading } = useLoading()
const show = ref(true)
const height = props.entity_ids.split(',').length > 15 ? '800' : 'auto'
const levelAIds = ref(new Set<number>())

const queryParams = reactive<taskListQuery>({
  site_id: site_id_all,
  channel_id: channel_item_all.channel_id as number,
  entity_type: props.entity_type,
  entity_ids: props.entity_ids,
  type: ''
})
const AItype = ref('4')
const name = ref('')
const sourceLang = ref('')
const targetLangs = ref('')
const deadLine = ref('')
const tableData = ref<taskListItem[]>([])
const targetLangList = ref<{ language: string; lang_name: string; }[]>([])
const tableSelections = ref<taskListItem[]>([])
const columns = ref([
  {
    title: '所属模板',
    dataKey: 'tpl_id',
  },
  {
    title: '模板类型',
    dataKey: 'tpl_type_name',
  },
  {
    title: '页面id',
    dataKey: 'tpl_page_id',
    width: 100,
  }, 
  {
    title: '页面等级',
    dataKey: 'level',
    width: 100,
    cellRenderer: (scope: { row: taskListItem }) => (
      <span style={ { color: scope.row.level === 'A' ? 'var(--el-color-error)' : '' } }> { scope.row.level || '/' } </span>
    )
  }, 
  {
    title: '页面URL',
    dataKey: 'url',
    minWidth: 280,
  },
  {
    title: '翻译内容',
    width: 350,
    dataKey: '',
    cellRenderer: (scope: { row: taskListItem }) => (
      <>
        {
          (scope.row.filed_list.length > 0)
          ? <ElSelect 
              v-model={scope.row.field} 
              multiple 
              collapseTags
              collapseTagsTooltip
              placeholder={ scope.row.isChildren ? '选择翻译内容, 不能为空' : '会覆盖其包含所有页面已选操作, 请留意'}
              onChange={(value) => handleFieldChange(value, scope.row)}
              style={ { width: '320px' } }
            >
              {
                {
                  default: () => scope.row.filed_list.map((item) => <ElOption value={item.field} label={item.name} />)
                }
              }
            </ElSelect>
          : '/'
        }
      </>
    )
  },
  {
    title: '操作',
    dataKey: '',
    width: 120,
    cellRenderer: (scope: { row: taskListItem, $index: number }) => (
      <>
        {
          scope.row.isChildren &&
          <>
            <ElButton type='primary' link disabled={scope.row.filed_list.length === 0} onClick={() => handlePreview(scope.row)}> 预览 </ElButton>
            <ElButton type='danger' link onClick={() => handleDelete(scope.row)}> 删除 </ElButton>
          </>
        }
      </>
    )
  }
])
const operateList = ref([
  {
    title: '批量全选字段',
    method: () => {
      tableSelections.value.forEach((row) => {
        const fields: string[] = []
        if (row.filed_list) {
          row.filed_list.forEach(( { field } ) => {
            fields.push(field)
          })
        }
        row.field = fields
      })
    },
    disabled: computed( () => tableSelections.value.length === 0 )
  },
  {
    title: '批量清空字段',
    method: () => {
      tableSelections.value.forEach((row) => {
        row.field = []
      })
    },
    disabled: computed( () => tableSelections.value.length === 0 )
  },
])

const resolveList = (list: taskListItem[]) => {
  const tplMap = new Map<number, any[]>()
  const result: any[] = []
  list.forEach(( item ) => {
    if (!tplMap.get(item.tpl_id)) {
      tplMap.set(item.tpl_id, [])
    }
    tplMap.get(item.tpl_id)?.push({...item, isChildren: true, id: item.tpl_page_id})
  })

  tplMap.forEach((value, key) => {
    result.push({
      id: key,
      tpl_id: key,
      filed_list: [...value[0].filed_list],
      tpl_type_name: value[0].tpl_type_name,
      children: value
    })
  })

  return result
}

const getList = () => {
  useTryCatch( async () => {
    setLoading(true)
    
    const res = await getStartList({...queryParams})
    if (res.code === 200) {
      tableData.value = resolveList(res.data.list)
      // targetLangList.value = res.data.target_language  // 添加一步去重操作
      sourceLang.value = res.data.source_language.language

      for ( const item of res.data.list) {
        if (item.level === 'A') {
          levelAIds.value.add(item.tpl_page_id)
        }
      }

      const langMap = new Map<string, string>()
      const list: { language: string; lang_name: string }[] = []
      res.data.target_language.forEach((item: langItem) => {
        langMap.set(item.language, item.lang_name)
      })
      langMap.forEach((value, key) => {
        list.push( { language: key, lang_name: value} )
      })
      targetLangList.value = list
    } else {
      ElMessage.error(res.msg)
    }
    setLoading(false)
  }, () => setLoading(false) )
}

const handlePreview = (row: taskListItem) => {
  const fileds = String(row.field)
  if (!fileds) {
    return ElMessage.warning('请先选择翻译内容')
  }
  window.open(`/api/v1/articles/preview?tpl_page_id=${row.tpl_page_id}&fields=${fileds}&page_check=0`, '_blank')
}

const handleDelete = (row: taskListItem) => {
  ElMessageBox.confirm(
    '请确认是否删除?',
    '提示',
    {
      confirmButtonText: '确认',
      cancelButtonText: '取消',
      callback: (action: string) => {
        if (action === 'confirm') {
          for ( const [index, item] of tableData.value.entries() ) {
            if (item.tpl_id === row.tpl_id) {
              const children = item.children as taskListItem[]
              for ( const [i, v] of children.entries() ) {
                if (v.tpl_page_id === row.tpl_page_id) {
                  item.children?.splice(i, 1)
                  if (item.children?.length === 0) {
                    tableData.value.splice(index, 1)
                  }
                  levelAIds.value.delete(v.tpl_page_id)
                  break
                }
              }
              break
            }
          }
        }
      }
    }
  )
}

const handleFieldChange = (val: string[], row: taskListItem) => {
  if (!row.isChildren) {
    const children = row.children
    if (children) {
      children.forEach((item) => {
        item.field = val
      })
    }
  }
}

const handleSubmit = () => {
  if (!name.value) {
    return ElMessage.warning('任务名称不能为空')
  }
  if (!String(targetLangs.value)) {
    return ElMessage.warning('至少选择一个翻译语言')
  }
  if (!deadLine.value) {
    return ElMessage.warning('请选择期望完成时间')
  }
  if (!queryParams.type) {
    return ElMessage.warning('请选择翻译方式')
  }

  const submitList: any[] = []
  tableData.value.forEach((item) => {
    if (item.children) {
      submitList.push(...item.children)
    }
  })

  for ( const item of submitList ) {
    if (item.filed_list.length === 0) {
      return ElMessage.warning('列表中存在翻译内容为\'/\'页面，请删除')
    }
    if (item.filed_list.length > 0) {
      if (!String(item.field)) {
        return ElMessage.warning('存在未选择翻译内容或翻译内容为\'/\'的页面，请删除或进行内容选择')
      }
    }
  }

  useTryCatch( async () => {
    setLoading(true)

    const entitys = submitList.map(( item ) => {
      return {
        entity_id: item.tpl_page_id,
        translation_field: item.field ? String(item.field) : ''
      }
    })

    const params = {
      site_id: site_id_all,
      channel_id: channel_item_all.channel_id as number,
      dead_line: deadLine.value,
      source_language: sourceLang.value,
      target_language: String(targetLangs.value),
      entity_type: props.entity_type,
      entitys,
      name: name.value,
      type: queryParams.type === '1' ? queryParams.type : AItype.value
    }
    const res = await createJob(params)
    if (res.code === 200) {
      show.value = false
      ElMessage.success(res.msg)
      basicStore.setRefresh(true)
      router.push({ name: 'TranslateManagement' })
    } else {
      ElMessage.error(res.msg)
    }
    setLoading(false)
  }, () => setLoading(false) )
}

const handleSelectionChange = (selections: taskListItem[]) => {
  const selections_real: taskListItem[] = []
  selections.forEach( (item) => {
    if (item.isChildren) {
      selections_real.push(item)
    }
  })
  tableSelections.value = selections_real
}

getList()
</script>