// 获取系统参数相关接口
import axiosReq from './axios'

// 获取站点下渠道列表
export const getChannelList = (site_id: number) => 
  axiosReq('get', `/api/v1/sites/${site_id}/channels`)

// 获取cms系统用户
export const getSysUserList = () => 
  axiosReq('get', '/api/v1/public/system_user_list')

// 获取业务模板列表
export const getTemplates = () => 
  axiosReq('get', '/api/v1/audit/manage_templates')

// 获取模板所属模块和模板类别接口
export const getTemplateModule = () => 
  axiosReq('get', '/api/v1/public/template_module_type')