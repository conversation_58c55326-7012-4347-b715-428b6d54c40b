import { createVNode, render, defineAsyncComponent } from 'vue'
import type { AppContext } from 'vue'

// 异步组件按需使用
const EditorPanelConstructor = defineAsyncComponent(() => import('../components/editor-panel.vue'))

export type propOptions = {
  code: string;
  save?: Function;
  change?: Function;
  close?: Function;
  url?: string;
  hideBottom?: boolean;
  showModal?: boolean;
  appendEl?: HTMLElement;
}

let container: HTMLElement|null = null

const loadAce = (props: propOptions, appContext: AppContext) => {
  const parnet = props.appendEl || document.body
  container = document.createElement('div')
  container.style.width = '100%'
  container.style.height = '100%'
  const { close } = props
  const vnode = createVNode(EditorPanelConstructor, {
    ...props,
    closed: () => {
      close && close()
      if (container) {
        parnet.removeChild(container)
      }
      container = null
    }
  })
  vnode.appContext = appContext  // 这句灰常重要，一定要记得在渲染之前关联应用实例上下文
  // render(vnode, container) // 编辑器渲染机制导致渲染需要位于添加dom操作之后
  parnet.appendChild(container)
  render(vnode, container)

  return vnode.component
}

export default loadAce