<template>
  <div ref="scrollRef" class="scroll-y with-flex">
    <template v-if="showExtra">
      <QueryForm 
        hide-reset
        :loading="loading"
        :form-list="formList"
        @search="handleSearch"
      />
    </template>
    <div v-loading="loading" class="sticky-table">
      <CustomTable
        ref="tableRef"
        :columns="columns"
        :data="tableData"
        :table-method="getList"
      />
    </div>

    <div class="fixed-bottom text-center">
      <el-button type="primary" plain @click="handleToList">返回(发布记录列表)</el-button>
      <el-button type="success" plain :disabled="taskStatus !== 4" @click="handleRePublish">重新发布</el-button>
    </div>
  </div>
</template>

<script setup lang="tsx">
import { ref, computed, reactive, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import { useRoute, useRouter } from 'vue-router'
import { storeToRefs } from 'pinia'
import { useParamsStore } from '@/store'
import { useBasicStore } from '@/store/basic'
import useTryCatch from '@/hooks/use-try-catch'
import useLoading from '@/hooks/use-loading'
import { getDetailList, taskPush } from '@/api/generate-manegement'
import { statusDetailMap, taskStatusMap } from '../types'
import type { detailsListItem } from '@/api/generate-manegement'

import QueryForm from '@/components/QueryForm/index.vue'
import CustomTable from '@/components/CustomTable/index.vue'

const route = useRoute()
const router = useRouter()
const { site_id_all, channel_item_all } = storeToRefs(useParamsStore())
const basicStore = useBasicStore()
const { loading, setLoading } = useLoading()
const { task_id, task_type } = route.query as unknown as {
  task_id: number;
  task_type: number;
}

const scrollRef = ref()
const height = ref('0px')
const taskType = ref(+task_type)
const taskStatus = ref(0)
const showExtra = computed( () => taskType.value === 1 || taskType.value === 2  )
const queryParams_raw = {
  status: '',
  tpl_page_ids: '',
  url: ''
}
const queryParams = reactive({
  ...queryParams_raw
})
const formList = ref([
  {
    label: '文件状态',
    placeholder: '文件状态',
    value: toRef(queryParams, 'status'),
    component: 'select',
    selections: [...statusDetailMap].map(( [ key, value ] ) => ({ label: value, value: key }))
  },
])
const tableRef = ref()
const tableData = ref<detailsListItem[]>([])
const columns = ref([
  {
    title: '所属渠道',
    dataKey: 'channel_code',
  },
  {
    title: '页面ID / 块ID',
    dataKey: 'entity_id',
    width: 120,
    hideColumn: !showExtra.value
  },
  {
    title: '文件名称',
    dataKey: 'file_name',
    hideColumn: showExtra
  },
  {
    title: showExtra.value ? '页面url / 块url' : '文件夹路径',
    dataKey: 'url',
    minWidth: 280,
  },
  {
    title: '线上地址',
    dataKey: 'line_name',
    minWidth: 280,
    cellRenderer: ( scope: { row: detailsListItem } ) => (
      scope.row.line_name
      ? <a href={scope.row.line_name} target='_blank'> { scope.row.line_name } </a>
      : '/'
    )
  },
  {
    title: '发起人',
    dataKey: 'user_name_a',
  },
  {
    title: '文件状态',
    dataKey: 'status',
    hideColumn: !showExtra.value,
    cellRenderer: ( scope: { row: detailsListItem } ) => (
      <span style={ {color: scope.row.status === 2 ? 'var(--el-color-success)' : scope.row.status === 3 ? 'var(--el-color-error)' : ''} }> 
        { statusDetailMap.get(scope.row.status) }
      </span>
    )
  },
  {
    title: '发布状态',
    dataKey: 'task_status',
    hideColumn: !showExtra.value,
    cellRenderer: ( scope: { row: detailsListItem } ) => (
      <>
        {
          <span style={ { 
            color: taskStatus.value === 3 ? 'var(--el-color-success)' : taskStatus.value === 4 ? 'var(--el-color-error)' : taskStatus.value === 2 ? 'var(--el-color-primary)' : '' 
          } }>
            { taskStatusMap.get(taskStatus.value) || '--' }
          </span>
        }
      </>
    )
  },
  {
    title: '当前状态更新时间',
    dataKey: 'edit_time',
    width: 180,
  },
  {
    title: '备注',
    dataKey: 'log_remark',
  },
])

const initHeight = () => {
  const scrollyEl = scrollRef.value as HTMLElement
  if (scrollyEl) {
    const h = scrollyEl.getBoundingClientRect().height
    height.value = `${h - 108}px`
  }
}

const getList = ( { page, pageSize } ) => {
  useTryCatch( async () => {
    setLoading(true)
    const params = {
      S: {
        ...queryParams
      },
      site_id: site_id_all.value,
      task_id,
      page,
      page_size: pageSize
    }

    const res = await getDetailList(params)
    if (res.code === 200) {
      tableData.value = res.data.items
      tableRef.value?.setTotal(res.data.total)
      taskStatus.value = res.data.task_status
    } else {
      ElMessage.error(res.msg)
    }
    setLoading(false)
  }, () => setLoading(false) )
}

const handleSearch = () => {
  tableRef.value?.setPage(1)
  const { page, pageSize } = tableRef.value ? tableRef.value : { page: 1, pageSize: 10 }
  getList( { page, pageSize } )
}

const handleToList = () => {
  router.push({ name: 'GeneratePublishRecord' })
}

const handleRePublish = () => {
  useTryCatch( async () => {
    setLoading(true)
    const res = await taskPush(task_id)
    if (res.code === 200) {
      const { page, pageSize } = tableRef?.value.getPagination?.() || { page: 1, pageSize: 10 }
      getList( { page, pageSize } )
      ElMessage.success(res.msg)
    } else {
      ElMessage.error(res.msg)
    }

    setLoading(false)
  }, () => setLoading(false) )
}

onMounted(() => {
  initHeight()
})
</script>