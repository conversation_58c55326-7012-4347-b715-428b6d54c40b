<template>
  <div ref="scrollyRef" class="scroll-y">
    <el-tabs v-model="activeName">
      <el-tab-pane label="短链生成" name="first">
        <OpereteList :operate-list="operateList" />
        <div style="overflow: hidden;" :style="{ height: `${height - 60}px` }">
          <el-row :gutter="20" style="height: 100%">
            <el-col :span="12">
              <el-input
                type="textarea" 
                v-model="long_link" 
                placeholder="请输入长链接"
                style="height: calc(100% - 70px)"
              />
              <div class="text-center" style="padding-top: 30px;">
                <el-button type="primary" :loading="loading" :disabled="!long_link" @click="handleAddLink"> 生成短链 </el-button>
              </div>
            </el-col>
            <el-col :span="12">
              <div v-loading="loading" style="height: 100%;">
                <el-input
                  type="textarea"
                  readonly
                  v-model="short_link" 
                  placeholder="此处展示生成的短链接"
                  style="height: calc(100% - 70px)"
                />
                <div class="text-center" style="padding-top: 30px;">
                  <el-button type="primary" :disabled="!short_link" @click="copyValueToClipboard(short_link)"> 复制短链 </el-button>
                </div>
              </div>
            </el-col>
          </el-row>
        </div>
      </el-tab-pane>
      <el-tab-pane label="生成记录" name="second">
        <QueryForm
          :form-list="formList"
          :loading="loading"
          @search="handleSearch"
          @reset="handleReset"
        />
        <div :style="{ height: `${height - 60}px` }" v-loading="loading">
          <CustomTable 
            ref="tableRef"
            height="100%"
            :table-method="getList"
            :columns="columns"
            :data="tableData"
          />
        </div>
      </el-tab-pane>
    </el-tabs>
  </div>
</template>

<script setup lang="tsx">
import { reactive, ref, toRef, onMounted, h } from 'vue'
import { useRoute } from 'vue-router'
import { ElMessage, ElMessageBox, ElButton, ElUpload } from 'element-plus'
import useTryCatch from '@/hooks/use-try-catch'
import { getHost } from '@/hooks/use-env'
import useLoading from '@/hooks/use-loading'
import { copyValueToClipboard } from '@/hooks/use-common'
import { shortLinkList, enableShortLink, disableShortLink, deleteShortLink, addShortLink, importShortLink } from '@/api/tools-management'
import type { shortLinkListItem, shortlinkQueryParams } from '@/api/tools-management'

import QueryForm from '@/components/QueryForm/index.vue'
import CustomTable from '@/components/CustomTable/index.vue'
import OpereteList from '@/components/PublicOperate/index.vue'

defineOptions( { name: 'ShortLinks' } )

const route = useRoute()
const { loading, setLoading } = useLoading()

const { name } = route.query as unknown as { name?: string }

const scrollyRef = ref()
const height = ref(0)
const activeName = ref(name || 'first')

const long_link = ref('')
const short_link = ref('')

const queryParams_raw = {
  status: '',
  long_link: '',
  short_code: ''
}
const queryParams = reactive<shortlinkQueryParams>({
  ...queryParams_raw
})
const formList = ref([
  {
    placeholder: '请输入长链URL',
    value: toRef(queryParams, 'long_link'),
    component: 'textarea',
  },
  {
    placeholder: '请输入短链code',
    value: toRef(queryParams, 'short_code'),
    component: 'input',
  },
  {
    placeholder: '状态',
    value: toRef(queryParams, 'status'),
    component: 'select',
    selections: [
      {
        value: '2',
        label: '已上线'
      },
      {
        value: '3',
        label: '已禁用'
      }
    ]
  }
])

const tableRef = ref()
const tableData = ref<shortLinkListItem[]>([])
const operateList = ref([
  {
    title: '批量生成链接',
    button: true,
    append: true,
    method: () => {
      ElMessageBox({
        title: '批量生成短链',
        customClass: 'dialog-button-center',
        customStyle: {
          maxWidth: 'none',
          width: '400px',
        },
        showCancelButton: false,
        showConfirmButton: false,
        closeOnPressEscape: false,
        closeOnClickModal: false,
        callback: () => {},
        message: () => h('div', { class: 'text-center rowCC' }, [
          h(ElButton, {
            style: { marginRight: '20px' },
            type: 'primary',
            link: true,
            onClick: () => handleDownload()
          }, () => '下载模板'),
          h(ElUpload, {
            action: 'string',
            accept: 'xlsx',
            autoUpload: false,
            showFileList: false,
            beforeUpload: () => true,
            onChange: (file) => {
              const params= {
                file: file.raw as Blob
              }
              useTryCatch( async () => {
                setLoading(true)
                
                const res = await importShortLink(params)
                if (res.code === 200) {
                  activeName.value = 'second'
                  handleSearch()
                  ElMessageBox.close()
                  ElMessage.success(res.msg)
                } else {
                  ElMessage.error(res.msg)
                }

                setLoading(false)
              }, () => setLoading(false) )
            }
          }, () => h(ElButton, { type: 'primary', loading: loading.value }, () => '上传文件'))
        ])
      })
    },
  }
])
const columns = ref([
  {
    title: '短链ID',
    dataKey: 'id',
    width: 100,
  },
  {
    title: '长链URL',
    dataKey: 'long_link',
    minWidth: 300,
  },
  {
    title: '短链URL',
    dataKey: 'short_link',
    minWidth: 300,
  },
  {
    title: '短链code',
    dataKey: 'short_code',
    width: 150,
  },
  {
    title: '状态',
    dataKey: 'status',
    width: 100,
    cellRenderer: (scope: { row: shortLinkListItem }) => (
      <span style={ { color: scope.row.status === 2 ? 'var(--el-color-success)' : scope.row.status === 3 ? 'var(--el-color-danger)' : 'inherit' } }>
        { scope.row.status_name }
      </span>
    )
  },
  {
    title: '操作',
    dataKey: '',
    fixed: 'right',
    width: 100,
    cellRenderer: (scope: { row: shortLinkListItem }) => (
      <>
        {
          scope.row.status === 1 ?
          <el-button plain type="success" onClick={ () => handleOperation('enable', scope.row.id) }>上架</el-button> :
          scope.row.status === 2 ?
          <el-button plain type="primary" onClick={ () => handleOperation('disable', scope.row.id) }>下架</el-button> :
          <el-button plain type="danger" onClick={ () => handleOperation('delete', scope.row.id) }>删除</el-button>
        }
      </>
    )
  }
])

const initHeight = () => {
  const scrollyEl = scrollyRef.value as HTMLElement
  if (scrollyEl) {
    const h = scrollyEl.getBoundingClientRect().height
    height.value = h - 60
  }
}

const handleDownload = () => {
  location.href = `${getHost()}/api/v1/shortlink/download_template`
}

const handleAddLink = () => {
  useTryCatch( async () => {
    setLoading(true)

    const res = await addShortLink({ long_link: long_link.value.trim() })
    if (res.code === 200) {
      short_link.value = res.data.short_link
      ElMessage.success('添加成功')
      refresh()
    } else {
      ElMessage.error(res.msg)
    }

    setLoading(false)
  }, () => setLoading(false))
}

const handleSearch = () => {
  tableRef.value?.setPage(1)
  refresh()
}

const handleReset = () => {
  Object.entries(queryParams_raw).forEach(([ key, value ]) => {
    queryParams[key] = value
  })
}

const refresh = () => {
  const { page, pageSize } = tableRef?.value?.getPagination()
  getList( { page, pageSize } )
}

/* table methods */
const getList = ( { page, pageSize } ) => {
  useTryCatch( async () => {
    setLoading(true)

    const params = {
      ...queryParams,
      page,
      page_size: pageSize
    }
    const res = await shortLinkList(params)
    if (res.code === 200) {
      tableData.value = res.data.list
      tableRef.value?.setTotal( res.data.total )
    } else {
      ElMessage.error(res.msg)
    }

    setLoading(false)
  }, () => setLoading(false) )
}

const handleOperation = (type: 'enable'|'disable'|'delete', id: number) => {
  const text = type === 'enable' ? '上架' : type === 'disable' ? '下架' : '删除'
  const api = type === 'enable' ? enableShortLink : type === 'disable' ? disableShortLink : deleteShortLink
  ElMessageBox.confirm(
    `请确认是否${text}`,
    '提示',
    {
      type: 'warning',
      confirmButtonText: '确认',
      cancelButtonText: '取消',
      callback: (action: string) => {
        if (action === 'confirm') {
          useTryCatch( async () => {
            setLoading(true)

            const res = await api( { id } )
            if (res.code === 200) {
              ElMessage.success(res.msg)
              refresh()
            } else {
              ElMessage.error(res.msg)
            }

            setLoading(false)
          }, () => setLoading(false) )
        }
      }
    }
  )
}

/* table methods */

onMounted(() => {
  initHeight()
})
</script>

<style lang="scss" scoped>
:deep(.custom-table-container) {
  height: calc(100% - 60px);
}
:deep(.el-textarea__inner) {
  height: 100%;
}
</style>