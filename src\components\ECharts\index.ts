import * as echarts from 'echarts/core'
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> } from 'echarts/charts'
import {  
  ToolboxComponent,
  TooltipComponent,
  GridComponent,
  TitleComponent,
  LegendComponent,
  DatasetComponent 
} from 'echarts/components'
import { LabelLayout, UniversalTransition } from 'echarts/features'
import { SVGRenderer } from 'echarts/renderers'

echarts.use(
  [
    <PERSON><PERSON><PERSON>,
    <PERSON><PERSON>hart,
    ToolboxComponent,
    TooltipComponent,
    GridComponent,
    TitleComponent,
    LegendComponent,
    DatasetComponent,
    LabelLayout,
    UniversalTransition,
    SVGRenderer
  ]
)

export default echarts