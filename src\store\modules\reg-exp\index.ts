import { defineStore } from 'pinia'

// 存放全局正则匹配
const useRegexpStore = defineStore('regExp', {
  state: () => {
    return {
      chineseSite: /207|206|103|1011/,
      skuReg: /<COMPONENTSTART object_id='100000'>/g,
      comReg: /<\/COMPONENTSTART>/g,
      blockReg: /<\/BLOCKSTART>/g,
      chineseReg: /[\u3002|\uff1f|\uff01|\uff0c|\u3001|\uff1b|\uff1a|\uA1B1|\uA1B0|\uA1AF|\uA1AE|\uff08|\uff09|\u300a|\u300b|\u3008|\u3009|\u3010|\u3011|\u300e|\u300f|\u300c|\u300d|\ufe43|\ufe44|\u3014|\u3015|\u2014|\uff5e|\ufe4f|\uffe5]/gi,
    }
  },

})

export default useRegexpStore