<template>
  <div id="tags-view-container" class="tags-view-container custom-layout-box">
    <div class="tags-view-wrapper">
      <router-link
        v-for="tag in visitedViews"
        ref="refTag"
        :key="tag.path"
        v-slot="{ navigate }"
        :to="{ path: tag.path, query: tag.query }"
        custom
      >
        <div
          class="tags-view-item"
          :class="isActive(tag) ? 'active' : ''"
          @click.middle="!isAffix(tag) ? closeSelectedTag(tag) : ''"
          @contextmenu.prevent="openMenu(tag, $event)"
          @click="navigate"
        >
          <SvgIcon icon-class="tag-fragment" class="left-fragment" style="width: 28px; height: 48px;" />
          <div class="main">
            {{ langTitle(tag.title) }}
            <template v-if="tag.operateHistory && tag.operateHistory.size > 1">
              <div class="dropdown-container">
                <el-dropdown style="--el-text-color-regular: var(--color);">
                  <ArrowDown />
                  <template #dropdown>
                    <el-dropdown-menu>
                      <el-dropdown-item 
                        v-for="([ v1, v2 ]) in tag.operateHistory"
                        :key="v1"
                        :disabled="route.fullPath === v1"
                        @click="handleHistoryJump(v1, v2.query, tag)"
                      >
                        {{ v2.title || v1 }}
                      </el-dropdown-item>
                    </el-dropdown-menu>
                  </template>
                </el-dropdown>
              </div>
            </template>
            <Close v-if="!isAffix(tag)" class="el-icon-close" @click.prevent.stop="closeSelectedTag(tag)" />
          </div>
          <SvgIcon icon-class="tag-fragment" class="right-fragment" style="width: 28px; height: 48px; transform: rotateY(180deg);" />
        </div>
      </router-link>
    </div>
    <ul v-show="visible" :style="{ left: left + 'px', top: (top - 60) + 'px' }" class="contextmenu">
      <li @click="refreshSelectedTag(selectedTag)">刷新</li>
      <li v-if="!isAffix(selectedTag)" @click="closeSelectedTag(selectedTag)">删除</li>
      <li @click="closeOthersTags">删除其他</li>
      <li @click="closeAllTags(selectedTag)">删除所有</li>
    </ul>
  </div>
</template>

<script setup lang="ts">
import { getCurrentInstance, nextTick, onMounted, reactive, toRefs, watch } from 'vue'
import { Close, ArrowDown } from '@element-plus/icons-vue'
import { resolve } from 'path-browserify'
import { useRoute, useRouter } from 'vue-router'
import { storeToRefs } from 'pinia/dist/pinia'
import type { RouterTypes } from '~/basic'
import { useBasicStore } from '@/store/basic'
import { useTagsViewStore, useParamsStore } from '@/store'
import { langTitle } from '@/hooks/use-common'
import emitter from '@/utils/bus'
import SvgIcon from '@/icons/SvgIcon.vue'
const route = useRoute()
const router = useRouter()
const state = reactive({
  visible: false,
  top: 0,
  left: 0,
  selectedTag: {},
  affixTags: [] as RouterTypes
})

const { visitedViews } = storeToRefs(useTagsViewStore())
const { siteChannelListeners } = useParamsStore() 

watch(
  () => route.path,
  () => {
    addTags()
  }
)

watch(
  () => state.visible,
  (value) => {
    if (value) {
      document.body.addEventListener('click', closeMenu)
    } else {
      document.body.removeEventListener('click', closeMenu)
    }
  }
)
onMounted(() => {
  initTags()
  addTags()
})

//判断当前点击的item项，是不是当前显示的路由项，如果是则高亮
const isActive = (param) => {
  return route.path === param.path
}
//当路由设置meta.affix=true,关闭按钮消失
const isAffix = (tag) => {
  return tag.meta && tag.meta.affix
}

const filterAffixTags = (routes, basePath = '/') => {
  let tags: RouterTypes = []
  routes.forEach((route) => {
    if (route.meta && route.meta.affix) {
      const tagPath = resolve(basePath, route.path)
      tags.push({
        fullPath: tagPath,
        path: tagPath,
        name: route.name,
        meta: { ...route.meta }
      })
    }
    if (route.children) {
      const tempTags = filterAffixTags(route.children, route.path)
      if (tempTags.length >= 1) {
        tags = [...tags, ...tempTags]
      }
    }
  })
  return tags
}

//初始
const tagsViewStore = useTagsViewStore()
const { allRoutes } = useBasicStore()
const initTags = () => {
  //过滤affix=true的tags数组并赋值给state.affixTags，挂载到页面上
  const affixTags = (state.affixTags = filterAffixTags(allRoutes))
  for (const tag of affixTags) {
    // Must have tag name
    if (tag.name) {
      tagsViewStore.addVisitedView(tag)
    }
  }
}
const addTags = () => {
  if (route?.name) {
    tagsViewStore.addVisitedView(route)
  }
  return false
}

/*右键菜单部分*/
const vm = getCurrentInstance()?.proxy
//右键打开菜单
const openMenu = (tag, e) => {
  const menuMinWidth = 105
  const offsetLeft = vm?.$el.getBoundingClientRect().left // container margin left
  const offsetWidth = vm?.$el.offsetWidth // container width
  const maxLeft = offsetWidth - menuMinWidth // left boundary
  const left = e.clientX - offsetLeft + 15 // 15: margin right

  if (left > maxLeft) {
    state.left = maxLeft
  } else {
    state.left = left
  }
  state.top = e.clientY
  state.visible = true
  state.selectedTag = tag
}

const basicStore = useBasicStore()

//关闭当前标签
const closeSelectedTag = (view) => {
  tagsViewStore.delVisitedView(view).then((visitedViews) => {
    if (isActive(view)) {
      toLastView(visitedViews, view)
    }
    //remove keep-alive by the closeTabRmCache
    if (view.meta?.closeTabRmCache) {
      const routerLevel = view.matched.length
      if (routerLevel === 2) {
        basicStore.delCachedView(view.name)
      }
      if (routerLevel === 3) {
        basicStore.setCacheViewDeep(view.name)
      }
    }
    // 取消缓存路由订阅事件
    emitter.off(view.name)
    siteChannelListeners.delete(view.name)
  })
}

//刷新标签
const refreshSelectedTag = (view) => {
  basicStore.delCachedView(view.name)
  const { fullPath, query } = view
  nextTick(() => {
    router.replace({
      path: `/redirect${fullPath}`,
      query
    })
  })
}

//右键关闭菜单
const closeMenu = () => {
  state.visible = false
}
//关闭其他标签
const closeOthersTags = () => {
  router.push(state.selectedTag)
  tagsViewStore.delOthersVisitedViews(state.selectedTag)
}
//关闭所有标签
const closeAllTags = (view) => {
  emitter.all.clear()
  siteChannelListeners.clear()
  tagsViewStore.delAllVisitedViews().then((visitedViews) => {
    if (state.affixTags.some((tag) => tag.path === view.path)) {
      return
    }
    toLastView(visitedViews, view)
  })
}
//跳转最后一个标签
const toLastView = (visitedViews, view) => {
  //visitedViews.at(-1)获取数组最后一个元素
  const latestView = visitedViews.at(-1)
  if (latestView) {
    router.push(latestView.fullPath)
  } else {
    if (view.name === 'Dashboard') {
      // to reload home page
      router.replace({ path: `/redirect${view.fullPath}` })
    } else {
      router.push('/')
    }
  }
}
// 历史回溯下拉跳转
const handleHistoryJump = (fullPath: string, query, view) => {
  basicStore.delCachedView(view.name)
  nextTick(() => {
    router.replace({
      path: `/redirect${fullPath}`,
      query
    })
  })
}

//export to page use
const { visible, top, left, selectedTag } = toRefs(state)
</script>

<style lang="scss" scoped>
.tags-view-container {
  height: var(--tag-view-height);
  width: 100%;
  padding-right: 16px;
  .tags-view-wrapper {
    white-space: nowrap;
    overflow: auto;
    position: relative;
    z-index: 12;
    .tags-view-item {
      --color: var(--tags-view-item-color);
      --bg-color: var(--tags-view-item-background);
      display: inline-flex;
      flex-wrap: nowrap;
      align-items: flex-start;
      position: relative;
      cursor: pointer;
      filter: drop-shadow(0px 0px 20px rgba(170, 182, 213, 0.2));
      color: var(--color);
      height: var(--tag-view-height);
      font-size: 14px;
      .main {
        display: inline-block;
        height: 100%;
        line-height: var(--tag-view-height);
        padding: 0 8px;
        background-color: var(--bg-color);
      }
      .dropdown-container {
        display: inline-flex;
        height: 100%;
        flex-direction: column;
        justify-content: center;
        margin-left: 8px;
        svg {
          width: 12px;
          color: inherit;
        }
      }
      .svg-icon {
        color: var(--bg-color);
      }
      &.active {
        --color: var(--tags-view-item-active-color);
        --bg-color: var(--tags-view-item-active-background);
        font-weight: 700;
        z-index: 1;
      }
      &:not(:first-child) {
        margin-left: -28px;
      }
      &:first-child {
        .left-fragment {
          --bg-color: var(--tags-view-item-background);
          background-color: var(--bg-color);
          border-radius: 12px 0 0 0;
          width: 16px !important;
        }
      }
      &::after {
        content: '';
        position: absolute;
        left: 0px;
        bottom: -5px;
        width: 100%;
        height: 5px;
        background-color: var(--tags-view-item-active-background);
        z-index: 1;
        opacity: var(--opacity, 1);
      }
    }
  }
  .contextmenu {
    margin: 0;
    background: var(--tags-view-contextmenu-background);
    z-index: 3000;
    position: absolute;
    list-style-type: none;
    padding: 5px 0;
    border-radius: 4px;
    font-size: 12px;
    font-weight: 400;
    color: var(--tags-view-contextmenu-color);
    box-shadow: var(--tags-view-contextmenu-box-shadow);
    li {
      margin: 0;
      padding: 7px 16px;
      cursor: pointer;
      &:hover {
        background: var(--tags-view-contextmenu-hover-background);
        color: var(--tags-view-contextmenu-hover-color);
      }
    }
  }
}
</style>

<style lang="scss">
.dark {
  .tags-view-item {
    --opacity: 0;
  }
}
//reset element css of el-icon-close
.tags-view-wrapper {
  .tags-view-item {
    border-radius: 3px;
    .el-icon-close {
      border-radius: 6px;
      width: 12px;
      height: 12px;
      transition: all 0.3s cubic-bezier(0.645, 0.045, 0.355, 1);
      transform-origin: 100% 50%;
      vertical-align: -2px;
      margin-left: 10px;
      &:hover {
        background-color: var(--tags-view-close-icon-hover-background);
        color: var(--tags-view-close-icon-hover-color);
      }
    }
  }
}
</style>
