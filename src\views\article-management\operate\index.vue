<template>
  <div class="scroll-y">
    <div 
      style="overflow: hidden;" 
      :style="{ minHeight: `calc(100% - ${fixBottomHeight}px)` }">
      <el-form
        ref="formRef"
        label-suffix=":"
        label-width="110px"
        :model="articleData"
        :rules="formRules"
      >
        <el-row :gutter="20">
          <el-col :span="8">
            <el-card shadow="never" style="height: 100%;">
              <template #header>
                <strong style="font-size: 1.25rem;">基础信息</strong>
              </template>
              <template #default>
                <template v-if="art_id && type !== 'copy'">
                  <el-form-item label="文章ID">
                    {{ art_id }}
                  </el-form-item>
                </template>
                <el-form-item label="文章标题" prop="title">
                  <el-input 
                    v-model="articleData.title"
                    placeholder="文章标题"
                    type="textarea"
                    :maxlength="maxMap.title"
                    show-word-limit
                  />
                </el-form-item>
                <el-form-item label="文章副标题" prop="subtitle">
                  <el-input 
                    v-model="articleData.subtitle"
                    placeholder="文章副标题"
                    type="textarea"
                    :maxlength="maxMap.subTitle"
                    show-word-limit
                  />
                </el-form-item>
                <el-form-item label="文章简介" prop="summary">
                  <el-input
                    v-model="articleData.summary"
                    placeholder="文章简介"
                    type="textarea"
                    :rows="3"
                    :maxlength="maxMap.summary"
                    show-word-limit
                  />
                </el-form-item>
                <el-form-item label="渠道名" prop="channel_id">
                  <el-select v-model="articleData.channel_id" disabled>
                    <el-option
                      v-for="(d, index) in channelList"
                      :key="index"
                      :label="d.channel_code"
                      :value="d.channel_id"
                    />
                  </el-select>
                </el-form-item>
                <el-form-item label="文章所属分类" prop="cat_id">
                  <el-tree-select 
                    v-model="articleData.cat_id"
                    placeholder="文章所属分类"
                    value-key="id"
                    node-key="id"
                    :data="classifyData"
                    :render-after-expand="true"
                    check-strictly
                    show-checkbox
                    check-on-click-node
                    filterable
                    auto-expand-parent
                    :default-expanded-keys="[articleData.cat_id]"
                  />
                  <div style="width: 100%">(分类ID: {{ articleData.cat_id }})</div>
                </el-form-item>
                <el-form-item label="侧边栏目块" v-if="type !== 'view'">
                  <el-select 
                    v-model="articleData.side_block"
                    placeholder="请选择侧边栏目块"
                    filterable
                    clearable
                    @focus.once="getSideBlkList"
                  >
                    <el-option 
                      v-for="(d, index) in blockSideList"
                      :key="d.blk_id"
                      :value="d.blk_id"
                      :label="d.blk_name"
                    />
                  </el-select>
                </el-form-item>
                <el-form-item label="文章图片链接">
                  <el-input v-model="articleData.image_url" placeholder="文章图片链接" />
                </el-form-item>
                <el-form-item label="文章图标链接">
                  <el-input v-model="articleData.icon_url" placeholder="文章图标链接" />
                </el-form-item>
                <el-form-item label="文章视频链接">
                  <el-input v-model="articleData.video_url" placeholder="文章视频链接" />
                </el-form-item>
                <el-form-item label="文章作者" prop="author_id">
                  <el-select v-model="articleData.author_id" placeholder="文章作者" filterable>
                    <el-option 
                      v-for="(d) in authorList"
                      :index="d.author_id"
                      :value="d.author_id"
                      :label="d.name"
                    />
                  </el-select>
                </el-form-item>
                <el-form-item label="文章排序">
                  <el-input-number v-model="articleData.sort" :step="1" step-strictly />
                </el-form-item>
                <el-form-item label="是否推荐">
                  <el-switch 
                    v-model="articleData.is_recom"
                    active-value="1"
                    inactive-value="0"
                    active-text="是"
                    inactive-text="否"
                  />
                </el-form-item>
                <el-form-item label="是否热门">
                  <el-switch 
                    v-model="articleData.is_hot"
                    active-value="1"
                    inactive-value="0"
                    active-text="是"
                    inactive-text="否"
                  />
                </el-form-item>
                <el-form-item label="备份说明" prop="mark">
                  <el-input 
                    v-model="articleData.remark"
                    placeholder="备份说明"
                    type="textarea"
                    :rows="3"
                  />
                </el-form-item>
                <el-form-item label="文章拓展分类" prop="cat_id_v" v-if="type !== 'view'">
                  <el-tree-select 
                    v-model="articleData.cat_id_v"
                    placeholder="文章拓展分类"
                    value-key="id"
                    :data="classifyData"
                    :render-after-expand="true"
                    check-strictly
                    show-checkbox
                    check-on-click-node
                  />
                </el-form-item>
                <el-form-item label="CBS产品ID">
                  <el-input v-model="articleData.rel_pro_ids" placeholder="多个英文逗号分隔" />
                </el-form-item>
                <el-form-item label="相关文章" v-if="type !== 'view'">
                  <el-button type="primary" plain @click="handleRelatedArt">相关文章</el-button>
                </el-form-item>
                <template v-if="relartList.length > 0">
                  <el-table
                    :data="relartList"
                    border
                    style="margin-bottom: 18px;"
                  >
                    <el-table-column label="相关文章ID" prop="art_id" width="100" show-overflow-tooltip />
                    <el-table-column label="文章标题" prop="title" show-overflow-tooltip />
                  </el-table>
                </template>
                <template v-else-if="articleData.rel_article_ids">
                  <el-form-item label="关联的文章ID" style="word-break: break-all;">
                    {{ articleData.rel_article_ids }}
                  </el-form-item>
                </template>
                <ExtendField :extend="articleData.extend"/>
              </template>
            </el-card>
          </el-col>
          <el-col :span="16">
            <el-card shadow="never" style="height: 100%;">
              <template #header>
                <strong style="font-size: 1.25rem;">文章正文编辑</strong>
              </template>
              <template #default>
                <el-form-item prop="content" label-width="0">
                  <div style="flex-grow: 1;">
                    <Tinymce 
                      ref="tinymceRef" 
                      v-model="articleData.content" 
                      :height="height" 
                      resize
                      @change="handleContentChange"
                    />
                  </div>
                </el-form-item>
              </template>
            </el-card>
          </el-col>
        </el-row>
      </el-form>
    </div>

    <div class="fixed-bottom text-center" v-if="type !== 'view'">
      <el-button plain :icon="Back" @click="handleBack">返回列表</el-button>
      <el-button 
        v-if="page_preview_url"
        type="primary"
        :icon="View"
        @click="handlePreview"
      >
        预览
      </el-button>
      <el-button 
        v-if="page_preview_url"
        type="primary"
        :icon="Edit"
        @click="() => loadEditPreview = true"
      >
        预览编辑模式
      </el-button>
      <el-button 
        type="primary"
        :icon="Finished"
        :loading="loading"
        @click="handleSave(formRef)"
      >
        保存
      </el-button>
      <el-button
        v-if="pageBinded"
        type="success"
        :loading="loading"
        @click="handleEditPage"
      >
        编辑已绑定页面
      </el-button>
    </div>

    <RelatedArticle 
      v-if="loadRelart"
      ref="relartRef"
      :rel-ids="articleData.rel_article_ids"
      :channel-id="articleData.channel_id"
      :classify-data="classifyData"
      :author-list="authorList"
      @confirm="handleRelartConfirm"
    />

    <BindPage 
      v-if="loadBindPageModal"
      ref="bindPageRef"
      :is-add="isAdd"
      :art_id="ArtId"
      :channel_id="articleData.channel_id"
      @close="handleClose"
      @confirm="handleBinded"
    />

    <Teleport to="body">
      <EditPreview 
        ref="editPreviewRef"
        v-if="loadEditPreview"
        :preview-url="`${page_preview_url}?site_id=${site_id_all}`"
        v-on="{
          close: () => loadEditPreview = false,
          save: handleEditPreviewSave
        }"
      >
        <Tinymce v-model="articleData.content" :height="heightPreview"/>
      </EditPreview>
    </Teleport>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, defineAsyncComponent } from 'vue'
import { ElMessage } from 'element-plus'
import { Back, Finished, View, Edit } from '@element-plus/icons-vue'
import { useRoute, useRouter } from 'vue-router'
import { storeToRefs } from 'pinia'
import { useParamsStore, useTagsViewStore } from '@/store'
import { useConfigStore } from '@/store/config'
import { useBasicStore } from '@/store/basic'
import useTryCatch from '@/hooks/use-try-catch'
import useLoading from '@/hooks/use-loading'
import { pageBindArt } from '@/api/page-management'
import { getAticleDetail, getClassifyData, getAuthorList, addArticle_new, editArticle_new, getSideBlockList, getHistoryDetail } from '@/api/article-management'
import type { articleData, classifyTreeItem, authorListItem, listItem } from '@/api/article-management'
import type { FormInstance } from 'element-plus'
import { formRules, maxMap, titleMap } from './rules'
import { formatDatetime } from '@/utils/common'

import Tinymce from '@/components/Tinymce/index.vue'
import ExtendField from './components/customField.vue'
const RelatedArticle = defineAsyncComponent( () => import('./components/relatedArticle.vue') )
const BindPage = defineAsyncComponent( () => import('./components/bindPageModal.vue') )
const EditPreview = defineAsyncComponent( () => import('@/components/EditPreview/index.vue') )

defineOptions( {  name: 'ArticleManageOperate'} )

const height = window.innerHeight - 330
const heightPreview = window.innerHeight - 100
const route = useRoute()
const router = useRouter()
const { site_id_all, channel_item_all, channelList } = storeToRefs(useParamsStore())
const { delVisitedView, changeViewTitle, updateHistoryTitle } = useTagsViewStore()
const { fixBottomHeight } = useConfigStore()
const basicStore = useBasicStore()
const { loading, setLoading } = useLoading()
const { type, art_id, channel_id, page_preview_url, page_id, version_id } = route.query as unknown as {
  type: string;
  art_id: number;
  channel_id: number;
  page_preview_url?: string;
  page_id?: number;
  version_id: number;
}
const articleData_raw = {
  site_id: 0,
  title: '',
  subtitle: '',
  summary: '',
  cat_id: '',
  channel_id: +channel_id || channel_item_all.value.channel_id as number,
  author_id: '',
  side_block: '',
  image_url: '',
  is_hot: '0',
  is_recom: '0',
  remark: formatDatetime(new Date()),
  cat_id_v: '',
  tag_ids: '',
  rel_article_ids: '',
  rel_pro_ids: '',
  content: '',
  video_url: '',
  icon_url: '',
  sort: 0,
  content_word_num: 0,
  extend: []
}
const articleData = reactive<articleData>({
  ...articleData_raw
})
const classifyData = ref<classifyTreeItem[]>([])
const blockSideList = ref<{ blk_id: number, blk_name: string }[]>([])
const authorList = ref<authorListItem[]>([])
const formRef = ref<FormInstance>()
const relartRef = ref()
const loadRelart = ref(false)
const relartList = ref<listItem[]>([])
const tinymceRef = ref()

const bindPageRef = ref()
const isAdd = ref(true)
const ArtId = ref(0)
const pageBinded = ref(false)
const pageQuery = reactive({
  type: 'edit',
  page_id: 0,
  tpl_id: 0,
  page_url: page_preview_url,
})
const loadBindPageModal = ref(false)
const editPreviewRef = ref()
const loadEditPreview = ref(false)

const handleBack = () => {
  delVisitedView(route)
  router.push({ name: 'ArticleManageList' })
}
const handleBackToList = () => {
  basicStore.setRefresh(true)
  delVisitedView(route)
  router.push({ name: 'ArticleManageList' })
}
const handleSave = async ( formEl: FormInstance | undefined ) => {
  if (!formEl) {
    return
  }
  await formEl.validate((valid) => {
    if (valid) {
      (art_id && type !== 'copy') ? handleEdit() : handleAdd()
    }
  })
}
const handleAdd = () => {
  useTryCatch( async () => {
    setLoading(true)
    const params = {
      ...articleData,
      cat_id_v: String(articleData.cat_id_v),
      site_id: site_id_all.value,
      channel_id: +channel_id || channel_item_all.value.channel_id as number,
    }

    const res = await addArticle_new(params)
    if (res.code === 200) {
      basicStore.setRefresh(true)
      ElMessage.success(res.msg)
      if (page_id) {
        const _res = await pageBindArt( { tpl_page_id: +page_id, art_id: res.data.art_id } )
        if (_res.code === 200) {
          ElMessage.success('绑定成功')
        } else {
          ElMessage.success('绑定失败,请进入编辑后保存再次绑定')
        }
        handleBackToList()
        return
      }
      loadBindPageModal.value = true
      bindPageRef.value?.show()
      ArtId.value = res.data.art_id
    } else {
      ElMessage.error(res.msg)
    }
    setLoading(false)
  }, () => setLoading(false) )
}
const handleEdit = () => {
  useTryCatch( async () => {
    setLoading(true)
    const params = {
      ...articleData,
      cat_id_v: String(articleData.cat_id_v),
      art_id
    }

    const res = await editArticle_new(params)
    if (res.code === 200) {
      basicStore.setRefresh(true)
      ElMessage.success(`保存成功`)
      if (!pageBinded.value) {
        loadBindPageModal.value = true
        bindPageRef.value?.show()
      }
    } else {
      ElMessage.error(res.msg)
    }
    setLoading(false)
  }, () => setLoading(false) )
}
const handlePreview = () => {
  window.open(`${page_preview_url}?site_id=${site_id_all.value}`)
}
const handleRelatedArt = () => {
  loadRelart.value = true
  relartRef.value?.show()
}
const handleRelartConfirm = (selection: listItem[]) => {
  articleData.rel_article_ids = selection.map(({ art_id }) => art_id).join(',')
  relartList.value = selection
}
// 上报文章内容字数
const handleContentChange = ( { count }: { count: number } ) => {
  articleData.content_word_num = count
}

const getDetail = () => {
  useTryCatch( async () => {
    setLoading(true)
    const res = await getAticleDetail(art_id)
    if (res.code === 200) {
      const data = res.data
      if (data.page_id) {
        pageBinded.value = true
        pageQuery.page_id = data.page_id
        pageQuery.tpl_id = data.tpl_id
      }
      Object.keys(articleData_raw).forEach(( key ) => {
        if (data[key] !== undefined) {
          (articleData[key] = data[key])
        }
      })
      articleData.remark = formatDatetime(new Date())
      if (articleData.side_block) {
        articleData.side_block = +articleData.side_block
        getSideBlkList()
      } else {
        articleData.side_block = ''
      }
      updateHistoryTitle(route, articleData.title)
    }
    setLoading(false)
  }, () => setLoading(false) )
}
const getClassifyTreeData = () => {
  useTryCatch( async () => {
    const channel_id_real = channel_id || (channel_item_all.value ? channel_item_all.value.channel_id : '') || ''
    const res = await getClassifyData(site_id_all.value, channel_id_real)
    if (res.code === 200) {
      classifyData.value = res.data
    }
  } )
}
const getSideBlkList = () => {
  useTryCatch( async () => {
    const channel_id_real = channel_id || (channel_item_all.value ? channel_item_all.value.channel_id : '') || ''
    const res = await getSideBlockList(channel_id_real)
    if (res.code === 200) {
      blockSideList.value = res.data.list
    } 
  } )
}
const getAuthors = () => {
  useTryCatch( async () => {
    const params = {
      S: {
        language: '',
        author_id: '',
        site_id: site_id_all.value
      },
      page: 1,
      page_size: 1000
    }

    const res = await getAuthorList(params)
    if (res.code === 200) {
      authorList.value = res.data.items
    }
  } )
}

const handleClose = (addPage: boolean, backToList: boolean) => {
  if (isAdd.value && !addPage) {
    handleBackToList()
    return
  }
  if (addPage) {
    delVisitedView(route)
  }
  if (backToList) {
    handleBackToList()
  }
}
const handleBinded = () => {
  handleBackToList()
}
const handleEditPage = () => {
  basicStore.delCachedView('PageManagementOperate')
  router.push({
    name: 'PageManagementOperate', query: { ...pageQuery }
  })
}
const handleEditPreviewSave = () => {
  handleEdit()
  setTimeout(() => {
    editPreviewRef.value?.reload?.()
  }, 300)
}

titleMap[type] && changeViewTitle(route, titleMap[type])
if (art_id && type !== 'view') {
  isAdd.value = false
  ArtId.value = +art_id
  getDetail()
}
getClassifyTreeData()
getAuthors()

if (page_id) {
  changeViewTitle(route, '创建文章并绑定页面')
}
if (art_id && type === 'view') {
  changeViewTitle(route, '文章历史详情')
  useTryCatch( async () => {
    const res = await getHistoryDetail(art_id, version_id, site_id_all.value)
    if (res.code === 200) {
      const data = res.data
      Object.keys(articleData_raw).forEach(( key ) => {
        if (data[key] !== undefined) {
          (articleData[key] = data[key])
        }
        if (key === 'extend') {
          articleData[key] = data[key] ? JSON.parse(data[key]) : []
        }
      })
      if (articleData.side_block) {
        articleData.side_block = +articleData.side_block
        getSideBlkList()
      }
      updateHistoryTitle(route, articleData.title)
    }
  } )
}
if (type === 'add') {
  updateHistoryTitle(route, '添加文章')
}
</script>