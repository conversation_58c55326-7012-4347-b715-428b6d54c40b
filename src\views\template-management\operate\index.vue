<template>
  <div class="scroll-y">
    <div style="overflow: hidden;" :style="{ minHeight: `calc(100% - ${fixBottomHeight}px)` }">
      <el-form
        ref="formRef"
        label-suffix=":"
        label-width="130px"
        :model="submitData"
        :rules="formRules"
      >
        <el-row :gutter="20">
          <el-col :span="8">
            <el-card shadow="never" style="height: 100%;">
              <template #header>
                <strong style="font-size: 1.25rem;">基础信息</strong>
              </template>
              <template #default>
                <el-form-item v-if="detailData.tpl_id && type === 'edit'" label="模板ID">
                  {{ detailData.tpl_id }}
                </el-form-item>
                <el-form-item v-if="type === 'edit'" label="模板集ID">
                  {{ detailData.parent_id || detailData.tpl_id }}
                </el-form-item>
                <el-form-item label="模板名称" prop="tpl_name">
                  <el-input v-model="submitData.tpl_name" placehlder="模板名称" />
                </el-form-item>
                <el-form-item label="模板渠道" prop="channel_id">
                  <el-select v-model="submitData.channel_id" placehoder="模板渠道" :disabled="type === 'edit'">
                    <el-option
                      v-for="(d, index) in channelList"
                      :key="index"
                      :label="d.channel_code"
                      :value="d.channel_id"
                      :disabled="detailData.channel_id === d.channel_id"
                    />
                  </el-select>
                </el-form-item>
                <el-form-item label="模板所属模块" prop="tpl_category">
                  <el-select v-model="submitData.tpl_category" placeholder="模板所属模块" :disabled="type === 'edit'" @change="handleCategoryChange">
                    <el-option 
                    v-for="(d, index) in moduleList"
                    :key="index"
                    :label="d.label"
                    :value="d.value"
                    />
                  </el-select>
                </el-form-item>
                <el-form-item label="模板类型" prop="tpl_type">
                  <el-select v-model="submitData.tpl_type" placeholder="模板所属模块" :disabled="type === 'edit'">
                    <el-option 
                    v-for="(d, index) in moduleTypeMap[submitData.tpl_category]"
                    :key="index"
                    :label="d.label"
                    :value="d.value"
                    />
                  </el-select>
                </el-form-item>
                <el-form-item label="模板级别" prop="tpl_level">
                  <el-select v-model="submitData.tpl_level" placeholder="模板级别" :disabled="type === 'edit'">
                    <el-option 
                    v-for="(d, index) in [ { label: 'A', value: 'A' }, { label: 'B', value: 'B' } ]"
                    :key="index"
                    :label="d.label"
                    :value="d.value"
                    />
                  </el-select>
                </el-form-item>
                <el-form-item label="所属站点" prop="site_id">
                  <el-input v-model="site_name_all" placeholder="所属站点" readonly />
                </el-form-item>
                <el-form-item label="模板发布开关" prop="tpl_pub_switch">
                  <el-switch
                    v-model="submitData.tpl_pub_switch"
                    active-value="able"
                    inactive-value="disable"
                    active-text="是"
                    inactive-text="否"
                  />
                </el-form-item>
                <el-form-item label="版本备份" prop="remark">
                  <el-input
                    v-model="submitData.remark"
                    type="textarea"
                    placeholder="版本备份"
                    :autosize="{ minRows: 1, maxRows: 3 }"
                  />
                </el-form-item>
                <el-form-item label="用途备份">
                  <el-input
                    v-model="submitData.use_remark"
                    type="textarea"
                    placeholder="用途备份"
                    :autosize="{ minRows: 1, maxRows: 3 }"
                  />
                </el-form-item>
              </template>
            </el-card>
          </el-col>
          <el-col :span="16">
            <el-card shadow="never" style="height: 100%;">
              <template #header>
                <div class="rowBC">
                  <strong style="font-size: 1.25rem;">模板内容配置</strong>
                </div>
                <el-input
                  style="padding-top: 12px;"
                  v-if="chineseSite.test(`${site_id_all}`)"
                  v-model="chineseText"
                  type="textarea"
                  placeholder="此处显示中文符号结果"
                  readonly
                  :row="1"
                />
              </template>
              <template #default>
                <div class="mb-12px">
                  已插入<span style="color: var(--el-color-primary)">{{ chanContent.sku_len }}</span>SKU;
                  已插入<span style="color: var(--el-color-primary)">{{ chanContent.com_len - chanContent.sku_len }}</span>数据组件;
                  已插入<span style="color: var(--el-color-primary)">{{ chanContent.block_len }}</span>块文件:
                </div>
                <el-form-item label-width="0" prop="tpl_content">
                  <el-input
                    v-model="submitData.tpl_content"
                    type="textarea"
                    placeholder="模板内容"
                    :autosize="{ minRows: 20, maxRows: 30 }"
                    v-ace="{ 
                      getVal: () => submitData.tpl_content, 
                      save: (val: string) => submitData.tpl_content = val, 
                      showTips: true 
                    }"
                  />
                </el-form-item>
                <div style="color: var(--el-color-error); padding: 20px 0;">
                  <el-button type="primary" plain @click="handleAddFiled">插入字段</el-button>
                  <el-button type="primary" plain @click="handleFieldAddTest">字段引入检测</el-button>
                </div>
                <el-table :data="submitData.custom_fields" :row-class-name="fieldTableRowClassName">
                  <el-table-column prop="field_id" label="字段ID" show-overflow-tooltip />
                  <el-table-column prop="field_name" label="表字段名称" show-overflow-tooltip />
                  <el-table-column prop="input_label" label="字段名称" show-overflow-tooltip />
                  <el-table-column prop="input_type" label="输入框类型" show-overflow-tooltip />
                  <el-table-column prop="field_type" label="字段类型" show-overflow-tooltip />
                  <el-table-column label="操作" width="310" fixed="right" v-if="type !== 'view'">
                    <template #default=" { row, $index }: { row: tplField; $index: number } ">
                      <el-button 
                        type="primary" 
                        size="default" 
                        link
                        @click="handleFieldCopy(row)"
                      >  
                        复制字段 
                      </el-button>
                      <el-button
                        v-if="row.field_id"
                        type="primary"
                        size="default"
                        link
                        :disabled="row.state === 'deleted'"
                        @click="handleBatchFieldEdit(row, 1)"
                      >
                        批量编辑字段内容
                      </el-button>
                      <template v-if="true">
                        <el-button
                          type="primary"
                          size="default"
                          link
                          @click="handleFieldEdit(row)"
                        >
                          编辑
                        </el-button>
                        <el-button
                          type="danger"
                          size="default"
                          link
                          @click="handleFieldDelete(row, $index)"
                        >
                          删除
                        </el-button>
                      </template>
                    </template>
                  </el-table-column>
                </el-table>
              </template>
            </el-card>
          </el-col>
        </el-row>
      </el-form>
    </div>
    <div class="fixed-bottom text-center" v-if="type !== 'view'">
      <el-button plain :icon="Back" @click="handleBack">返回列表</el-button>
      <el-button
        v-if="type !== 'view'"
        type="primary"
        :icon="Finished"
        :loading="loading"
        @click="handleSubmit(formRef)"
      >
        保存
      </el-button>
    </div>

    <FieldModal
      v-if="loadFieldModal"
      ref="fieldModalRef"
      @confirm="handleFieldModalConfirm"
    />
    <BatchFieldModal
      v-if="loadBatchField"
      ref="batchFieldRef"
      :tpl-id="`${tpl_id}`"
      :field-id="batchFieldId"
      :field-label="bactchFieldLabel"
      @confirm="handleBatchFieldConfirm"
      @closed="() => loadBatchField = false"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, h, defineAsyncComponent } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { ElForm, ElFormItem, ElInput, ElSwitch, ElMessage, ElMessageBox, ElButton } from 'element-plus'
import { Back, Finished, Delete } from '@element-plus/icons-vue'
import { storeToRefs } from 'pinia'
import { useParamsStore, useRegexpStore, useTagsViewStore } from '@/store'
import { useConfigStore } from '@/store/config'
import { useBasicStore } from '@/store/basic'
import useTryCatch from '@/hooks/use-try-catch'
import useLoading from '@/hooks/use-loading'
import { formatDatetime } from '@/utils/common'
import { getTemplateDetail, addTemplate, editTemplate, deleteField, getHistoryDetail } from '@/api/template-management'
import type { templateDetail, templateData, tplField } from '@/api/template-management'
import type { FormInstance } from 'element-plus'
import { formRules } from './rules'
import { copyValueToClipboard } from '@/hooks/use-common'

const FieldModal = defineAsyncComponent( () => import('./components/fieldModal.vue') )
const BatchFieldModal = defineAsyncComponent( () => import('./components/batchFiledEdit.vue') )

defineOptions( { name: 'TemplateManageOperate' } )

const route = useRoute()
const router = useRouter()
const { site_id_all, site_name_all, channelList, moduleList, moduleTypeMap, siteDetailInfo, channel_item_all } = storeToRefs(useParamsStore())
const { chineseSite, skuReg, comReg, blockReg, chineseReg } = useRegexpStore()
const { fixBottomHeight } = useConfigStore()
const { delVisitedView, changeViewTitle, updateHistoryTitle } = useTagsViewStore()
const basicStore = useBasicStore()
const { loading, setLoading } = useLoading()
const { type, tpl_id, parent_id, channel_id, version_id } = route.query as unknown as {
  type: string;
  tpl_id: number;
  parent_id: string;
  channel_id: number;
  tabIndex: string;
  version_id?: number;
}

const submitData_raw = {
  site_id: site_id_all.value,
  channel_id: channel_id || channel_item_all.value.channel_id || '',
  tpl_name: '',
  tpl_content: '',
  tpl_category: '',
  tpl_type: '',
  tpl_level: '',
  remark: formatDatetime(new Date()),
  use_remark: '',
  tpl_state: 'able',
  tpl_pub_switch: 'able',
  parent_id: +parent_id || 0
}
const formRef = ref<FormInstance>()
const submitData = reactive<templateData>({
  ...submitData_raw,
  custom_fields: [] as tplField[],
})
const detailData = reactive<templateDetail>({ ...submitData_raw })
const chineseText = ref('')
const chanContent = computed(() => {
  const content = submitData.tpl_content
  const skuArr = content.match(skuReg)
  const comArr = content.match(comReg)
  const blockArr = content.match(blockReg)
  return {
    sku_len: skuArr ? skuArr.length : 0,
    com_len: comArr ? comArr.length : 0,
    block_len: blockArr ? blockArr.length : 0,
  }
})
/* 交互弹窗相关参数 */
const fieldModalRef = ref()
const loadFieldModal = ref(false)
const batchFieldRef = ref()
const loadBatchField = ref(false)
const batchFieldId = ref(0)
const bactchFieldLabel = ref('')
/* 交互弹窗相关参数 */

const getDetail = () => {
  useTryCatch( async () => {
    setLoading(true)
    const res = await getTemplateDetail(tpl_id, { site_id: site_id_all.value })
    if (res.code === 200) {
      const data = res.data as templateDetail
      Object.keys(data).forEach(( key ) => {
        detailData[key] = data[key]
        if (Object.hasOwn(submitData_raw, key)) {
          submitData[key] = data[key]
        }
      })
      submitData.remark = formatDatetime(new Date())
      submitData.custom_fields = data.tpl_fields as tplField[]
      submitData.is_in_draft = data.draft_version_id ? 'yes' : 'no'
      if (parent_id && type === 'add') {
        detailData.parent_id = parent_id
        submitData.channel_id = ''
        submitData.parent_id = +parent_id
      }
      updateHistoryTitle(route, submitData.tpl_name)
    } else {
      ElMessage.error(res.msg)
    }
    setLoading(false)
  }, () => setLoading(false) )
}
const handleCategoryChange = () => {
  submitData.tpl_type = ''
}
const handleChineseCheck = () => {
  const content = submitData.tpl_content
  if (!content) {
    return
  }
  const chineseArr_raw = content.match(chineseReg)
  if (!chineseArr_raw) {
    return ElMessage.warning('未检测到中文符号')
  }
  const chineseSet = new Set(chineseArr_raw)
  if (chineseSet.has('|')) {
    chineseSet.delete('|')
  }
  chineseText.value = [...chineseSet].join(' ')
}
const handleAddFiled = () => {
  if (!loadFieldModal.value) {
    loadFieldModal.value = true
    return
  }
  fieldModalRef.value?.show()
}
const handleFieldModalConfirm = (item: tplField) => {
  submitData.custom_fields.push(item)
}
const handleBatchFieldEdit = (row: tplField, step: number) => {
  batchFieldId.value = row.field_id as number
  bactchFieldLabel.value = row.input_label
  batchFieldRef.value?.setStep(step)
  loadBatchField.value = true
}
const handleFieldEdit = (row: tplField) => {
  const { input_value, is_null } = row
  const options = ref<{ k: string; v:string }[]>(row.input_option ? JSON.parse(row.input_option) : [])
  ElMessageBox({
    title: '操作字段',
    customClass: 'dialog-button-center',
    customStyle: {
      maxWidth: 'none',
      width: '500px',
    },
    showCancelButton: true,
    showConfirmButton: true,
    cancelButtonText: '取消',
    confirmButtonText: '确认',
    callback: (action: string) => {
      if (action === 'confirm') {
        const real_options = options.value.filter( ( { v } ) => !!v )
        if (row.input_type === 'select' && real_options.length > 0) {
          row.input_option = JSON.stringify(real_options)
        }
        ElMessage.success('编辑成功, 保存模板后生效')
      } else {
        row.input_value = input_value || ''
        row.is_null = is_null || ''
      }
    },
    message: () => h(ElForm, {
      labelWidth: '130',
      labelSuffix: ':'
    }, {
      default: () => [
        h(ElFormItem, { label: '表字段名称' }, { default: () => h(ElInput, {
          placeholder: '请输入表字段名称',
          disabled: row.field_id ? true : false,
          modelValue: row.field_name || '',
          onInput: (value) => (row.field_name = value)
        }) }),
        h(ElFormItem, { label: '字段名称' }, { default: () => h(ElInput, {
          placeholder: '请输入字段名称',
          modelValue: row.input_label || '',
          onInput: (value) => (row.input_label = value)
        }) }),
        h(ElFormItem, { label: '输入框类型' }, { default: () => row.input_type }),
        row.input_type === 'select' && 
        [
          ...options.value.map(( option, index ) => h(ElFormItem, { label: `${index + 1}` }, { default: () => [
            h(ElInput, { 
              style: {
                width: 'calc(100% - 20px)'
              },
              placeholder: '请输入选项值',
              disabled: false,
              modelValue: option.v,
              onInput: (value) => (option.v = value)
            }),
            h(Delete, {
              style: {
                marginLeft: '4px'
              },
              width: '16px',
              class: 'with-hand',
              onClick: () => options.value.splice(index, 1)
            })
          ] })),
          h(ElFormItem, { label: '' }, { default: () => h(ElButton, {
            type: 'primary',
            onClick: () => {
              options.value.push({
                k: '',
                v: ''
              })
            }
          }, () => '添加选项')})
        ],
        h(ElFormItem, { label: '字段类型' }, { default: () => row.field_type }),
        h(ElFormItem, { label: '默认值' },  { default: () => h(ElInput, {
          placeholder: '请输入默认值',
          modelValue: row.input_value || '',
          onInput: (value) => (row.input_value = value)
        }) }),
        h(ElFormItem, { label: '是否必填' },  { default: () => h(ElSwitch, { 
          activeValue: 'N',
          inactiveValue: 'Y',
          activeText: '是',
          inactiveText: '否',
          modelValue: row.is_null,
          onChange: (value: any) => ( row.is_null = value)
        }) })
      ]
    })
  })
}
const handleFieldDelete = (row: tplField, index: number) => {
  if (!row.field_id) {
    submitData.custom_fields.splice(index, 1)
    return
  }
  ElMessageBox.confirm('请确认是否删除该字段', '提示', {
    callback: (action: string) => {
      if (action === 'confirm') {
        useTryCatch( async () => {
          const res = await deleteField(row.tpl_id as number, row.field_id as number)
          if (res.code === 200) {
            submitData.custom_fields.splice(index, 1)
            ElMessage.success('删除成功')
          } else {
            ElMessage.error(res.msg)
          }
        } )
      }
    }
  })
}
const handleBatchFieldConfirm = () => {}
const handleFieldAddTest = () => {
  const fieldSet = new Set(submitData.tpl_content.match(/\{-\s?[a-z\d\_]+\s?-\}/ig))
  let count = 0
  submitData.custom_fields.forEach((field) => {
    if (fieldSet.has(`{-${field.field_name}-}`) || fieldSet.has(`{- ${field.field_name} -}`)) {
      field.is_quto = 1
    } else {
      field.is_quto = 0
      count++
    }
  })
  if (count > 0) {
    ElMessageBox.confirm(
      `检测到有自定义字段未插入模板内容中, 请核对后插入正确位置, 未插入字段已在表格中高亮展示`,
      '提示',
      {
        showCancelButton: false,
        confirmButtonText: '确认',
        callback: () => {},
      }
    )
  }
}
const handleFieldCopy = (row: tplField) => {
  const field = `{-${row.field_name}-}`
  copyValueToClipboard(field)
  ElMessageBox.confirm(
    `您复制的字段为：${field}, 请插入至模板内容正确位置`,
    '复制成功',
    {
      showCancelButton: false,
      confirmButtonText: '确认',
      callback: () => {},
    }
  )
}
const fieldTableRowClassName = ({ row }: { row: tplField }) => {
  if (row.is_quto === 0) {
    return 'warning-row'
  }
  return ''
}
const handleSubmit = async ( formEl: FormInstance | undefined ) => {
  if (!formEl) return
  await formEl.validate(( valid ) => {
    if (valid) {
      useTryCatch( async () => {
        setLoading(true)
        const api = type === 'edit' ? editTemplate : addTemplate
        const params = { ...submitData }

        const res = await api(params, tpl_id)
        if (res.code === 200) {
          ElMessage.success(`${type === 'edit' ? '编辑' : '添加'}成功`)
          basicStore.setRefresh(true)
          if (type === 'add') {
            handleBack()
          } else {
            getDetail()
          }
        } else {
          ElMessage.error(res.msg)
        }
        setLoading(false)
      }, () => setLoading(false) )
    }
  })
}
const handleBack = () => {
  delVisitedView(route)
  router.push('/page-management/template-manage')
}

const historyDetail = () => {
  useTryCatch( async () => {
    const res = await getHistoryDetail(tpl_id, version_id as number, site_id_all.value)
    if (res.code === 200) {
      const data = res.data
      Object.keys(data).forEach(( key ) => {
        const raw_key = `tpl_${key}`
        if (Object.hasOwn(submitData_raw, raw_key)) {
          detailData[raw_key] = data[key]
          submitData[raw_key] = data[key]
        } else {
          detailData[key]  = data[key]
          submitData[key] = data[key]
        }
      })
      submitData.tpl_category = data.module
      submitData.custom_fields = data.custom_fields as tplField[]
      submitData.is_in_draft = data.draft_version_id ? 'yes' : 'no'
      updateHistoryTitle(route, submitData.tpl_name)
    } else {
      ElMessage.error(res.msg)
    }
  } )
}

changeViewTitle(route, type === 'view' ? '模板历史详情' : type === 'edit' ? '编辑模板' : '添加模板')

if (type === 'edit') {
  getDetail()
}

if (type === 'view') {
  historyDetail()
}

if (type === 'add') {
  updateHistoryTitle(route, '添加模板')
}
</script>

<style>
.el-table .warning-row {
  --el-table-tr-bg-color: var(--el-color-danger-light-9);
}
.el-table .warning-row .el-table-fixed-column--right {
  background: var(--el-color-danger-light-9);
}
</style>
