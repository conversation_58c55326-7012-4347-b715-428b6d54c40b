#The defined variable must start with VITE_APP_
VITE_APP_ENV = 'dev'
VITE_APP_BASE_URL = 'https://github.jzfai.top/micro-service-api'

#image or oss address
VITE_APP_IMAGE_URL = 'https://github.jzfai.top/gofast-image'

#proxy,   use this to test proxy
#VITE_APP_BASE_URL = '/api'
#VITE_APP_PROXY_URL = 'https://github.jzfai.top/micro-service-api'

# just a flag
ENV = 'development'

# base api
VUE_APP_BASE_API = '/api'
VUE_APP_OABASE = '//oacenter.wondershare.com'
#VUE_APP_OABASE = '/dev-api'
VUE_APP_OABASE_API = '//oacenter.wondershare.com/api'
VUE_APP_B_ID = 42
VUE_APP_SITE_ID = 55

VUE_CLI_BABEL_TRANSPILE_MODULES = true
VUE_APP_ACCOUNT_BASE_API = 'https://account.300624.cn'


