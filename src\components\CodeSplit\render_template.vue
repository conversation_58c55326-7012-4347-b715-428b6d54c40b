<script lang="ts">
import { defineComponent, ref, h } from 'vue'
import type { PropType } from 'vue'
import { parse } from '@vue/compiler-dom/dist/compiler-dom.esm-bundler'
import { dfsAll, isSelfCloseTag, beautifyHtml, resolveProps, hasProp, setPropValue, isTextNodeOnly } from './tool'
import type { child } from './tool'

export default defineComponent({
  name: 'CodeSplitCompile',
  props: {
    htmlStr: {
      type: String,
      required: true
    },
    texts: { type: Array as PropType<string[]>, required: true },
    hrefs: { type: Array as PropType<string[]>, required: true },
    imgs: { type: Array as PropType<string[]>, required: true },
    editIndex: { type: Number, required: true },
    activeIndex: { 
      type: Object as PropType<{
        text: number;
        href: number;
        img: number;
      }>,
      required: true
    },
    highlightColor: { type: String },
    bgColor: { type: String },
    textColor: { type: String },
    bgColorArea: { type: String }
  },
  emits: [ 'updateVal', 'updateObj', 'updateArr' ],
  setup(props, { emit, expose }) {
    const hasEdit = ref(false)
    const output_container = ref<HTMLElement|null>(null)
    const textSapns = ref<NodeList|null>(null)
    const hrefSpans = ref<NodeList|null>(null)
    const imgSpans = ref<NodeList|null>(null)

    let children: child[]|null = null   //ast语法数顶层children

    let textResults: string[] = []
    let hrefResults: string[] = []
    let imgResults: string[] = []

    const markIndex = {
      rednerIndex: {
        content: 0,
        href: 0,
        img: 0
      },
      index : {
        content: 0,
        href: 0,
        img: 0
      }
    }

    // 用于处理渲染操作的正则对象
    const renderReg = /(.*)\{\{\{(.*)\}\}\}/
    const hrefExecReg = /<a\s+href/
    const imgExecReg = /<img\s+src/

    let timer: any = null
    let rednerTemplates2: any[] = []

    // 工具函数 将匹配的标签中匹配的属性及值全部取出并替换为标记符号，配合渲染函数完成模板构建
    const parseRenderArr = ( content: string, reg: RegExp ) => {
      const arr = content.split(reg)
      arr.splice(1, 0, '***')
      return arr
    }

    /* 方法定义 */
    // 编辑元素时自动滚动到可见区域
    const handleFocus = (focusKey: string, focusIndex: number, spans: NodeList, type: string) => {
      if (props.activeIndex) {
        for( let key of Object.keys(props.activeIndex)) {
          emit('updateObj', 'activeIndex', key, key === focusKey ? focusIndex : -1)
        }
      }
      !output_container.value && ( output_container.value = document.querySelector('#output-container') )
      if (output_container.value) {
        !spans && ( spans = output_container.value.querySelectorAll(`[data-${type}]`) )
        if (output_container.value.clientHeight < output_container.value.scrollHeight) {
          const cHeight = output_container.value.clientHeight
          const parentNode = spans[focusIndex].parentNode as HTMLElement
          const offsetTop = parentNode.offsetTop
          output_container.value.scrollTop = offsetTop <= cHeight ? 0 : (offsetTop - 100)
        }
      }
    }
    // 输入更新视图内容
    const handleInput = ($event: InputEvent, dataKey: string, index: number) => {
      hasEdit.value = true
      timer && clearTimeout(timer)
      timer = setTimeout(() => {
        const target = $event.target as HTMLInputElement
        emit('updateArr', dataKey, index, target.value)
      }, 500)
    }
    // 将ast语法数重新构建为html模板，仅传值使用，保存时独立执行
    const recursiveChild = (child: child) => {
      return child.type === 1 
      ? `<${child.tag}${child.props.length > 0 ? ' ' : ''}${ (child.tag === 'a' || child.tag === 'img') && hasProp(child.props) ? setPropValue(child.tag, child.props, child.tag === 'a' ? props.hrefs[markIndex.index.href++] : props.imgs[markIndex.index.img++] ) : ''}${resolveProps(child.tag, child.props)}${isSelfCloseTag(child.tag) ? '/' : '' }>${isTextNodeOnly(child) ? '\n' : ''}${child.children.map(child => recursiveChild(child)).join('\n')}${isSelfCloseTag(child.tag) ? '' : (isTextNodeOnly(child) ? '\n' : '') +  '</' + child.tag + '>' }`
      : child.type === 2 
        ? child.content.trim() 
          ? props.texts[markIndex.index.content++]
          : '' 
        : child.loc.source
    }
    // 获取最后编辑完成的html字符串模板
    const getCompletHtmlStr = () => {
      if (!hasEdit.value) return false
      for ( let k in markIndex.index ) {
        markIndex.index[k] = 0
      }
      return beautifyHtml( children ? children.map(child => recursiveChild(child)).join('\n') : '')
    }
    // 导入等场景强制触发编辑
    const forceEdit = () => hasEdit.value = true 
    // 将ast语法数重新构建为html模板，用于视图更新及交互，文本节点用三花括号标记
    const renderRecursiveChild = (child: child) => {
      return child.type === 1 
      ? `<${child.tag}${child.props.length > 0 ? ' ' : ''}${ (child.tag === 'a' || child.tag === 'img') && hasProp(child.props) ? setPropValue(child.tag, child.props, child.tag === 'a' ? props.hrefs[markIndex.rednerIndex.href++] : props.imgs[markIndex.rednerIndex.img++] ) : ''}${resolveProps(child.tag, child.props)}${isSelfCloseTag(child.tag) ? '/' : '' }>${child.children.length > 0 ? '\n' : ''}${child.children.map(child => renderRecursiveChild(child)).join('\n')}${isSelfCloseTag(child.tag) ? '' :  (child.children.length > 0 ? '\n' : '') + '</' + child.tag + '>' }`
      : child.type === 2 
        ? child.content.trim()  ? 
          `{{{${props.texts[markIndex.rednerIndex.content++]}}}}`
          : ''
        : child.loc.source
    }
    // 渲染高亮色交互区
    const renderHighlightArea = () => {
      return h('div', {
        style: {
          marginBottom: '8px'
        }
      }, [
        '文本高亮色：',
        h('input', {
          style: {
            marginRight: '8px'
          },
          type: 'color',
          value: props.highlightColor,
          onInput: $event => emit('updateVal', 'highlightColor', $event.target.value )
        }),
        '高亮背景色：',
        h('input', {
          style: {
            marginRight: '8px'
          },
          type: 'color',
          value: props.bgColor,
          onInput: $event => emit('updateVal', 'bgColor', $event.target.value )
        }),
        '文字颜色：',
        h('input', {
          style: {
            marginRight: '8px'
          },
          type: 'color',
          value: props.textColor,
          onInput: $event => emit('updateVal', 'textColor', $event.target.value )
        }),
        '背景色：',
        h('input', {
          style: {
            marginRight: '8px'
          },
          type: 'color',
          value: props.bgColorArea,
          onInput: $event => emit('updateVal', 'bgColorArea', $event.target.value )
        })
      ])
    }
    // 代码渲染区
    const renderCodeArea = (rednerTemplates: any[]) => {
      return h('div', {
        id: 'output-container',
        style: {
          'scroll-behavior': 'smooth',
          height: 'calc(100% - 60px)',
          overflow: 'auto',
          border: '1px solid var(--el-border-color)',
          marginBottom: '16px',
          backgroundColor: 'var(--bg-color-area, var(--el-bg-color))',
          color: 'var(--text-color, var(--el-text-color-primary))',
          padding: '8px'
        }
      },
      rednerTemplates.map((item) => h('div', {
        class: 'pre',
      }, [
        item.gaps,
        item.contentIndex === null 
        ? item.hrefIndex === null
          ? item.imgIndex === null
            ? item.content
            : parseRenderArr(item.content, /src=["|'].*?["|']/).map(content => 
                content === '***' 
                ? h('span', {
                  'data-img': 'true',
                  class: props.activeIndex.img === item.imgIndex ? 'active' : ''
                }, [ 'src="', props.imgs[item.imgIndex], '"' ]) 
                : content
              )
          : parseRenderArr(item.content, /href=["|'].*?["|']/).map(content => 
            content === '***' 
            ? h('span', {
              'data-href': 'true',
              class: props.activeIndex.href === item.hrefIndex ? 'active' : ''
            }, [ 'href="', props.hrefs[item.hrefIndex], '"' ]) 
            : content)
          : h('span', {
            'data-text': 'true',
            class: props.activeIndex.text === item.contentIndex ? 'active' : ''
          }, props.texts[item.contentIndex])
        ]))
      )
    }
    // tab切换渲染区
    const renderTab = () => {
      return h('div', {
        class: 'tab-switch'
      }, [ '文本', '超链接', '图片引用链接' ].map((text, index) => 
        h('button', {
          class: props.editIndex === index ? 'active' : '',
          onClick: () => emit('updateVal', 'editIndex', index)
        }, text)
      ))
    }
    // 编辑框渲染区
    const renderEditArea = () => {
      return h('div', {
        style: {
          height: 'calc(100% - 80px)',
          overflow: 'auto',
          paddingRight: '16px',
          marginTop: '16px'
        },
      }, [
        h('div', { style: props.editIndex === 0 ? '' : 'display: none' }, 
          props.texts.map((content, index) => h('div', {
            style: {
              marginBottom: '8px'
            },
          }, [
            `${index+1}.原文本: ${textResults[index]}`, h('textarea', {
              value: content,
              class: 'form-control',
              onInput: ($event) => handleInput($event, 'texts', index),
              onFocus: () => handleFocus('text', index, textSapns.value as NodeList, 'text')
            })
          ]))
        ),
        h('div', { style: props.editIndex === 1 ? '' : 'display: none' },
          props.hrefs.map((content, index) => h('div', {
            style: {
              marginBottom: '8px'
            },
          }, [
            `${index+1}.原超链接：`,
            h('a', { target: '_blank', href: hrefResults[index], class: 'text-underline' }, hrefResults[index]),
            h('textarea', {
              value: content,
              class: 'form-control',
              onInput: ($event) => handleInput($event, 'hrefs', index),
              onFocus: () => handleFocus('href', index, hrefSpans.value as NodeList, 'href')
            })
          ]))
        ),
        h('div', { style: props.editIndex === 2 ? '' : 'display: none' }, 
          props.imgs.map((content, index) => h('div', {
            style: {
              marginBottom: '8px'
            },
          }, [
            `${index+1}.原图片引用链接：`,
            h('a', { target: '_blank', href: imgResults[index], class: 'hover-img'}, imgResults[index]),
            h('textarea', {
              value: content,
              class: 'form-control',
              onInput: ($event) => handleInput($event, 'imgs', index),
              onFocus: () => handleFocus('img', index, imgSpans.value as NodeList, 'img')
            })
          ]))
        )
      ])
    }
    // 返回原始值
    const getOrigin = () => ({
      textResults,
      hrefResults,
      imgResults
    })
    /* 方法定义 */

    expose({
      getCompletHtmlStr,
      getOrigin,
      forceEdit
    })

    /* 初始化参数 */
    try {
      const ast: any = parse(props.htmlStr)
      children = ast.children as child[]

      // 深度优先遍历工具函数，将需要编辑的值以数组形式保存并返回
      const results = dfsAll(children)
      textResults = results.text
      hrefResults = results.href
      imgResults = results.img

      props.texts.length === 0 && emit('updateVal', 'texts', [...textResults])
      props.hrefs.length === 0 && emit('updateVal', 'hrefs', [...hrefResults])
      props.imgs.length === 0 && emit('updateVal', 'imgs', [...imgResults])

      // 将所有换行处转化为数组结构， 并将文本、链接以及图片三类节点打好索引标记
      const renderTemplates: string[] = beautifyHtml(children.map(child => renderRecursiveChild(child)).join('\n')).split(/\n/g)
      let contentIndex = 0, hrefIndex = 0, imgIndex = 0
      rednerTemplates2 = renderTemplates.map(content => ({
        content,
        gaps: renderReg.test(content) ? renderReg.exec(content)?.[1] : '',  //文本重构需要提前记录并保留空格符号
        contentIndex: renderReg.test(content) ? contentIndex++ : null,
        hrefIndex: hrefExecReg.test(content) ? hrefIndex++ : null,
        imgIndex: imgExecReg.test(content) ? imgIndex++ : null
      }))
    } catch(e) {}
    /* 初始化参数 */
    return () => {
      if (children) {
        return h('div', {
          style: {
            display: 'flex',
            lineHeight: '1.5',
            padding: '0 15px',
            height: '100%'
          }
        }, [
          h('div', {
            style: {
              '--hl-color': props.highlightColor,
              '--bg-color': props.bgColor,
              '--text-color': props.textColor,
              '--bg-color-area': props.bgColorArea,
              flex: '0 0 50%',
              maxWidth: '50%',
              padding: '0 15px',
              height: '100%'
            }
          }, [
            renderHighlightArea(),
            renderCodeArea(rednerTemplates2)
          ]),
          h('div', {
            style: {
              flex: '0 0 50%',
              maxWidth: '50%',
              padding: '0 15px',
              height: '100%'
            },
          }, [
            renderTab(),
            renderEditArea()
          ])
        ])
      } else {
        return h('div', { style: { color: 'var(--el-color-error)' } }, 'html模板解析错误, 请先修复语法错误')
      }
    }
  }
})
</script>

<style>
  .text-underline { text-decoration: underline; }
  .hover-img { position: relative; }
  .hover-img img { position: absolute; left: 0; top: 100%; max-width: 500px; opacity: 0; pointer-events: none; transition: opacity .3s; z-index: 10; }
  .hover-img:hover img { opacity: 1; }
  .pre { white-space: pre; }
  .pre [data-text] { white-space: pre-wrap; }
  .pre .active { 
    color: var(--hl-color, var(--el-color-primary));
    background-color: var(--bg-color, var(--el-bg-color));
    font-weight: 700; 
  }
  .form-control {
    display: block;
    width: 100%;
    padding: 0.375rem 0.75rem;
    font-size: 1rem;
    font-weight: 400;
    line-height: 1.5;
    color: var(--el-text-color-primary);
    background-color: var(--el-bg-color);
    background-clip: padding-box;
    border: 1px solid var(--el-border-color);
    -webkit-appearance: none;
    -moz-appearance: none;
    appearance: none;
    border-radius: 0.25rem;
  }
  .form-control:focus {
    border-color: var(--el-text-color-primary);
  }
  .tab-switch {
    display: flex;
    flex-wrap: nowrap;
    border-bottom: 1px solid var(--el-border-color);
  }
  .tab-switch button {
    border: none;
    background: none;
    text-align: center;
    flex: 1 0 auto;
    cursor: pointer;
    padding: 8px 0;
    position: relative;
  }
  .tab-switch button.active::after {
    content: '';
    position: absolute;
    width: 100%;
    height: 2px;
    border-radius: 1px;
    background-color: var(--el-color-primary);
    left: 0;
    bottom: 0;
  }
</style>