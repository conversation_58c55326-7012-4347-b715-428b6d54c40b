<template>
  <el-dialog 
    v-model="show"
    title="子渠道"
    width="900"
    append-to-body
    @closed="emit('close')"
  >
    <el-form 
      ref="formRef"
      label-suffix=":"
      label-width="150px"
      :rules="formRules"
      :model="submitData"
    >
      <el-form-item label="站点渠道" prop="channel_code">
        <el-select v-model="submitData.channel_code" filterable placeholder="请选择渠道" @focus="handleGetChannelList">
          <el-option 
            v-for="d in channelList"
            :key="d.id"
            :label="d.channel_name"
            :value="d.channel_code"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="渠道名称" prop="name">
        <el-input v-model="submitData.name" placeholder="请输入渠道名称" maxlength="100" show-word-limit />
      </el-form-item>
      <el-form-item label="站点域名" prop="host">
          <el-input v-model="submitData.host" placeholder="请输入站点域名" maxlength="150" show-word-limit />
      </el-form-item>
      <el-form-item label="静态文件路径" prop="html_path">
        <el-input v-model="submitData.html_path" placeholder="请输入静态文件路径" maxlength="100" show-word-limit />
      </el-form-item>
      <el-form-item label="图片主机域名" prop="host_image">
        <el-input v-model="submitData.host_image" placeholder="请输入图片主机域名" maxlength="100" show-word-limit />
      </el-form-item>
      <el-form-item label="图片文件存放路径" prop="image_path">
        <el-input v-model="submitData.image_path" placeholder="请输入图片文件存放路径" maxlength="100" show-word-limit />
      </el-form-item>
      <el-form-item label="视频主机域名" prop="host_video">
        <el-input v-model="submitData.host_video" placeholder="请输入图片主机域名" maxlength="100" show-word-limit />
      </el-form-item>
      <el-form-item label="视频文件存放路径" prop="video_path">
        <el-input v-model="submitData.video_path" placeholder="请输入图片文件存放路径" maxlength="100" show-word-limit />
      </el-form-item>
      <el-form-item label="js主机路径" prop="host_js">
        <el-input v-model="submitData.host_js" placeholder="请输入js主机路径" maxlength="100" show-word-limit />
      </el-form-item>
      <el-form-item label="css主机路径" prop="host_css">
        <el-input v-model="submitData.host_css" placeholder="请输入css主机路径" maxlength="100" show-word-limit />
      </el-form-item>
      <el-form-item label="站点语言" prop="language">
        <el-select v-model="submitData.language" filterable clearable placeholder="请选择语言" @focus="handleGetLangList">
          <el-option 
            v-for="d in langList"
            :key="d.lang_id"
            :label="d.name"
            :value="d.lang_code"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="渠道状态" prop="state">
        <el-switch 
          v-model="submitData.state" 
          active-value="able"
          inactive-value="disable" 
          active-text="启用" 
          inactive-text="禁用"
        />
      </el-form-item>
      <el-form-item v-if="!detailData" label="创建审核流程模板" prop="is_create_audit">
        <el-switch 
          v-model="submitData.is_create_audit" 
          :active-value="1"
          :inactive-value="0" 
          active-text="是" 
          inactive-text="否"
        />
      </el-form-item>
    </el-form>

    <div class="text-center">
      <el-button @click="handleCancel"> 取消 </el-button>
      <el-button type="primary" :loading="loading" @click="handleSave"> 保存 </el-button>
    </div>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, reactive } from 'vue'
import useTryCatch from '@/hooks/use-try-catch'
import useLoading from '@/hooks/use-loading'
import { getChannelList, getLangList, addSiteChannel, editSiteChannel } from '@/api/site-config'
import type { channelListItem, langListItem, siteChannelItem, siteChannelData } from '@/api/site-config'
import { formRules } from '../operate/rules'
import { ElMessage } from 'element-plus'

const props = defineProps<{
  detailData: siteChannelItem|null;
  site_id: string|number;
}>()

const emit = defineEmits([ 'save', 'close' ])

const { loading, setLoading } = useLoading()
const show = ref(true)
const formRef = ref()

const channelList = ref<channelListItem[]>([])
const langList = ref<langListItem[]>([])

const submitData_raw = {
  channel_code: '',
  name: '',
  language: '',
  host: '',
  html_path: '',
  image_path: '',
  video_path: '',
  host_js: '',
  host_css: '',
  host_image: '',
  host_video: '',
  state: 'able',
  is_create_audit: 1,
  site_id: props.site_id as number
}
const submitData = reactive<siteChannelData>({
  ...submitData_raw
})

const handleGetChannelList = () => {
  if (channelList.value.length === 0) {
    useTryCatch(async () => {
      const params = {
        page: 1,
        page_size: 100
      }
      const res = await getChannelList(params)
      if (res.code === 200) {
        const list = res.data.items as channelListItem[]
        channelList.value = list.reverse()
      }
    })
  }
}
const handleGetLangList = () => {
  if (langList.value.length === 0) {
    useTryCatch(async () => {
      const res = await getLangList()
      if (res.code === 200) {
        const list = res.data
        langList.value = list
      }
    })
  }
}

const setData = () => {
  const detailData = props.detailData || submitData_raw
  if (detailData) {
    Object.keys(submitData).forEach((key) => {
      if (detailData[key] !== undefined) {
        submitData[key] = detailData[key]
      }
    })
  }
}

const handleCancel = () => {
  show.value = false
}

const handleSave = () => {
  useTryCatch( async () => {
    setLoading(true)
    const detailData = props.detailData
    const api = detailData ? editSiteChannel : addSiteChannel
    const params = {
      ...submitData,
    }
    const res = await api(params, detailData?.channel_id || 0)
    if (res.code === 200) {
      show.value = false
      ElMessage.success(res.msg)
      emit('save')
    } else {
      ElMessage.error(res.msg)
    }
    setLoading(false)
  }, () => setLoading(false))
}

if (props.detailData) {
  setData()
}

defineExpose({
  setData,
  show: () => show.value = true
})
</script>