<template>
  <div class="edit-preview-container">
    <div class="function-panel" :style="{ 'flex-direction': flexDirection === 'h' ? 'row' : 'column' }">
      <div class="edit-area" :style=" { 'width': flexDirection === 'h' ? (ratio + '%') : '100%', 'height': flexDirection === 'v' ? ( (100 - ratio) + '%') : '100%' } ">
        <div style="width:100%;height:100%; padding: 50px 0;box-sizing:border-box;position:relative">
          <div class="top-func-bar">
            <el-button type="primary" @click="handleChangeDirection">{{ flexDirection === 'h' ? '垂直模式' : '水平模式' }}</el-button>
            <el-button type="primary" @click="handleClient"> 切换{{ width === '100%' ? '移动端' : '桌面端' }}</el-button>
            <el-dropdown trigger="hover" style="margin: 0 10px">
              <el-button type="primary" >调整视图比例</el-button>
              <template #dropdown>
                <el-dropdown-menu style="padding: 20px">
                  <template v-if="flexDirection === 'h'">
                    <el-slider v-model="ratio" :min="10" :max="90" :format-tooltip="(val:number) => `${val}%`" style="width: 300px" />
                  </template>
                  <template v-else>
                    <el-slider v-model="ratio" :min="10" :max="90" :format-tooltip="(val:number) => `${val}%`" vertical height="300px" />
                  </template>
                </el-dropdown-menu>
              </template>
            </el-dropdown>
          </div>
          <div class="bottom-func-bar">
            <el-button type="success" @click="handleSave" :loading="saving">保存</el-button>
            <el-button type="primary" @click="reload">刷新页面</el-button>
            <el-button type="primary" @click="handleClose">退出</el-button>
          </div>
          <slot />
        </div>
      </div>
      <div class="preview-area">
        <div class="frame-container">
          <div class="frame-wrapper" :style="{ width, height }">
            <iframe :src="realUrl"></iframe>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, nextTick } from 'vue'

const props = defineProps<
{
  previewUrl: string;
}>()

const emit = defineEmits(['directionChange', 'close', 'save'])

const flexDirection = ref<'h'|'v'>('h')
const ratio = ref(50)
const width = ref('100%')
const height = ref('100%')
const realUrl = ref(props.previewUrl)
const saving = ref(false)

const handleChangeDirection = () => {
  ratio.value = 50
  flexDirection.value = flexDirection.value === 'h' ? 'v' : 'h'
  nextTick(() => {
    emit('directionChange', flexDirection.value)
  })
}

const reload = () => {
  const url = realUrl.value
  realUrl.value = ''
  nextTick(() => {
    realUrl.value = url
    saving.value = false
  })
}

const handleClient = () => {
  width.value = width.value === '100%' ? '396px' : '100%'
  height.value = height.value === '100%' ? '667px' : '100%'
}

const handleSave = () => {
  if (saving.value) {
    return
  }
  saving.value = true
  emit('save')
}

const handleClose = () => emit('close')

defineExpose({
  reload
})
</script>

<style scoped>
.edit-preview-container {
  position: fixed;
  width: 100vw;
  height: 100vh;
  left: 0;
  top: 0;
  background-color: var(--el-bg-color);
  z-index: 1999;
}
.function-panel {
  display: flex;
  height: 100%;
}
.top-func-bar,.bottom-func-bar {
  position: absolute;
  height: 50px;
  top: 0;
  left: 50%;
  transform: translateX(-50%);
  display: flex;
  align-items: center;
  z-index: 1;
}
.bottom-func-bar {
  top: auto;
  bottom: 0;
}
.edit-area, .preview-area {
  position: relative;
}
.preview-area { flex-grow: 1; }
.preview-area iframe {
  width: 100%;
  height: 100%;
}
.frame-container {
  display: flex;
  width: 100%;
  height: 100%;
  align-items: center;
  justify-content: center;
}
</style>