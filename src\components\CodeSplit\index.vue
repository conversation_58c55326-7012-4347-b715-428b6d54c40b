<template>
  <div class="code-split-container" style="height: 100%">
    <renderTemplate 
      ref="renderTempRef"
      :htmlStr="originHtmlCode"  
      :texts="texts"
      :hrefs="hrefs"
      :imgs="imgs"
      :editIndex="editIndex"
      :activeIndex="activeIndex"
      :highlightColor="highlightColor"
      :bgColor="bgColor"
      :textColor="textColor"
      :bgColorArea="bgColorArea"
      @update-val="updateVal"
      @update-arr="updateArr"
      @update-obj="updateObj"
    />

    <Excel ref="excelRef" v-if="openExcel" @change="handleFileChange" />
  </div>
</template>

<script setup lang="ts">
import { ref, defineAsyncComponent, nextTick } from 'vue'

import renderTemplate from './render_template.vue'
const Excel = defineAsyncComponent(() => import('./Excel.vue'))

defineOptions( { name: 'CodeSplit' } )

const props = withDefaults(defineProps<{
  htmlStr: string;
  height?: string;
  openExcel: boolean;
}>(), {
  height: '100%',
  openExcel: false
})
const emit = defineEmits([ 'fail', 'success' ])

const themeRoot = document.querySelector('.lighting-theme')

const renderTempRef = ref()
const excelRef = ref()
const texts = ref<string[]>([]) //文本值数组容器
const hrefs = ref<string[]>([]) //超链接值数组容器
const imgs = ref<string[]>([]) //图片链接值数组容器
const editIndex = ref(0) //编辑类型索引，目前支持文本、超链接以及图片，默认为文本
const highlightColor = ref(themeRoot ? '#409eff' : '#589ef8') //文本高亮字体颜色色
const bgColor = ref(themeRoot ? '#ffffff' : '#000000') //文本高亮背景色
const textColor = ref(themeRoot ? '#303133' : '#E5EAF3')
const bgColorArea = ref(themeRoot ? '#ffffff' : '#000000')

// 高亮当前编辑区域，对应不同类型索引
const activeIndex = ref({
  text: -1,
  href: -1,
  img: -1
})
const originHtmlCode = ref(props.htmlStr)

const keyMap = {
  texts,
  hrefs,
  imgs,
  highlightColor,
  bgColor,
  textColor,
  bgColorArea,
  editIndex
}
// 更新响应式数据值
const updateVal = (dataKey: string, val: string[]) => {
  keyMap[dataKey].value = val
}
// 更新数组内单个索引值
const updateArr = (dataKey: string, index: number, val: string) => {
  keyMap[dataKey].value[index] = val
}
// 更新对象内单个键值
const updateObj = (dataKey: string, propKey: string, val: number) => {
  activeIndex.value[propKey] = val
}
const getCompletHtmlStr = () => renderTempRef.value?.getCompletHtmlStr()
const handleExport = (type = 1) => {
  // 0: 全部导出, 1: 仅导出文本， 2：仅导出链接，3：仅导出图片
  const { textResults, hrefResults, imgResults } = renderTempRef.value?.getOrigin()
  const sheets = {
    split_text_sheet: [], 
    split_link_sheet: [], 
    split_img_sheet: []
  }

  Object.keys(sheets).forEach(key => {
    if (type === 0 || type === 1) {
      sheets.split_text_sheet = textResults.map((rowText, index) => [ rowText, texts.value[index] ])
    }
    if (type === 0 || type === 2) {
      sheets.split_link_sheet = hrefResults.map((rowText, index) => [ rowText, hrefs.value[index] ])
    }
    if (type === 0 || type === 3) {
      sheets.split_img_sheet = imgResults.map((rowText, index) => [ rowText, imgs.value[index] ])
    }
  })

  return excelRef.value?.handleExport(sheets, props.htmlStr)
}
const handleImport  = () => excelRef.value?.handleImport()
const handleFileChange = (htmlString: string, excel_texts: string[], excel_hrefs: string[], excel_imgs: string[]) => {
  if (!htmlString) {
    emit('fail')
    return
  }
  originHtmlCode.value = htmlString
  nextTick(() => {
    renderTempRef.value?.forceEdit()
    excel_texts && ( texts.value = excel_texts )
    excel_hrefs && ( hrefs.value = excel_hrefs )
    excel_imgs && ( imgs.value = excel_imgs )

    emit('success')
  })
}

defineExpose({
  getCompletHtmlStr,
  handleExport,
  handleImport,
  exportTypeList: [
    {
      label: '全部导出',
      value: 0
    },
    {
      label: '仅导出文本',
      value: 1
    },
    {
      label: '仅导出超链接',
      value: 2
    },
    {
      label: '仅导出图片链接',
      value: 3
    }
  ]
})
</script>