<template>
  <div class="scroll-y">
    <div style="overflow: hidden;" :style="{ minHeight: `calc(100% - ${fixBottomHeight}px)` }">
      <el-row :gutter="20">
        <el-col :span="18">
          <el-card shadow="hover" style="margin-bottom: 30px;">
            <el-tag :type="detailData.status === 1 ? 'info' : detailData.status === 2 ? 'warning' : detailData.status === 3 ? 'success' : 'danger'">
              {{detailData.status_name}}
            </el-tag>
            <span style="font-size: 18px; font-weight: 700; margin-left: 10px;">{{ detailData.name }}</span>
          </el-card>
          <el-card shadow="hover" style="margin-bottom: 30px;">
            <template #header>
              <span style="font-size: 18px; font-weight: 700;">基础信息</span>
            </template>
            <el-descriptions>
              <el-descriptions-item label="流程ID">
                <el-tag>{{detailData.task_id}}</el-tag>
              </el-descriptions-item>
              <el-descriptions-item label="流程名称">
                <el-tag>{{detailData.entity_name}}</el-tag>
              </el-descriptions-item>
              <el-descriptions-item label="所属业务">
                <el-tag>{{detailData.type_name}}</el-tag>
              </el-descriptions-item>
              <el-descriptions-item label="站点名称">
                <el-tag>{{detailData.channel_name}}</el-tag>
              </el-descriptions-item>
              <el-descriptions-item label="申请人">
                <el-tag>{{detailData.user_name}}</el-tag>
              </el-descriptions-item>
              <el-descriptions-item label="申请时间">
                <el-tag>{{detailData.add_time}}</el-tag>
              </el-descriptions-item>
            </el-descriptions>
          </el-card>
          <el-card shadow="hover">
            <template #header>
              <span style="font-size: 18px; font-weight: 700;">审核内容</span>
            </template>
            <div class="content-table el-table" v-html="detailData.content"></div>
          </el-card>

          <el-card v-if="showAudit" shadow="hover" style="margin-top: 30px;">
            <Audit ref="auditRef"  :task-id="taskId" @success="handleAuditSuccess" />
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card shadow="hover">
            <template #header>
              <span style="font-size: 18px; font-weight: 700;">流程轨迹</span>
            </template>
            <div :style="{height: (120 * ( detailData.logs ? detailData.logs.length : 0)) + 'px'}">
              <el-steps direction="vertical" :active="detailData.logs ? detailData.logs.length : 0" finish-status="process">
                <el-step
                  v-for="(d, index) in detailData.logs"
                  :key="index"
                  class="step-primary"
                  :class="{'step-warning': detailData.status === 2 && index === 1, 'step-danger': detailData.status === 4 && index === 1}"
                >
                  <template #icon>
                    <template v-if="index === 0">
                      <Select />
                    </template>
                    <template v-else>
                      <Select v-if="detailData.status === 3 || detailData.status === 5" />
                      <CloseBold v-else-if="detailData.status === 4" />
                      <i v-else style="width: 16px; height: 16px;"></i>
                    </template>
                  </template>
                  <template #title>
                    {{ d.type_name }}
                  </template>
                  <template #description>
                    <div v-if="index < 2"> {{index === 0 ? '申请人：' : '审核人：'}} {{ d.name }} </div>
                    <template v-if="index === 1 && detailData.status !== 5">
                      <div>
                        审核状态：
                        <span :style="{color: detailData.status === 4 ? 'var(--el-color-danger)' : detailData.status === 3 ? 'var(--el-color-success)' : 'inherit'}">
                          {{ detailData.status_name }}
                        </span>
                      </div>
                      <div>审核意见： {{ d.content }}</div>
                    </template>
                    <div v-if="index < 2"> {{index === 0 ? '申请时间：' : '审核时间：'}} {{ d.add_time }} </div>
                    <div v-else-if="detailData.status !== 5"> {{ d.remark }} </div>
                    <div v-if="index === 0"> 备注: {{ detailData.remark }} </div>
                    <div v-if=" detailData.logs && detailData.logs.length === (index + 1) && detailData.status === 5">
                      <span style="color: var(--el-color-primary);"> {{ d.name }} </span>{{ d.user_id === '0'  ? '自动撤销, 超30天未处理' : '已撤销当前流程' }}
                    </div>
                  </template>
                </el-step>
              </el-steps>
            </div>
          </el-card>
        </el-col>
      </el-row>
    </div>

    <div class="fixed-bottom text-center">
      <el-button 
        v-if="detailData.user_id === userInfo.wsid && ( detailData.status === 5 || detailData.status === 2 )"
        :type="detailData.status === 5 ? 'info' : 'danger'"
        :disabled="detailData.status === 5"
        :loading="loading"
        plain
        @click="handleRevoke"
      >
        {{ detailData.status === 5 ? '已撤销' : '撤销' }}
      </el-button>
      <el-button plain :icon="Back" @click="handleBack">返回</el-button>
      <el-button 
        type="primary"
        :icon="View"
        :loading="loading"
        @click="handleAudit"
        v-if="showAudit"
      >
        审核
      </el-button>
    </div>

    <!-- <AuditDialog 
      ref="auditDialogRef" 
      v-if="loadAuditDiglog" 
      :task-id="taskId"
      @success="handleAuditSuccess"
    /> -->
  </div>
</template>

<script setup lang="ts">
import { ref, computed, defineAsyncComponent } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Select, CloseBold, View, Back } from '@element-plus/icons-vue'
import { useRoute, useRouter } from 'vue-router'
import { useUserStroe, useTagsViewStore } from '@/store'
import { useConfigStore } from '@/store/config'
import useLoading from '@/hooks/use-loading'
import useTryCatch from '@/hooks/use-try-catch'
import { getTaskDetail, taskRevoke } from '@/api/audit-management'
import type { taskDetail } from '@/api/audit-management'

const AuditDialog = defineAsyncComponent(() => import('../components/audit-dialog.vue'))
import Audit from '../components/audit.vue'
// 不缓存此路由
defineOptions( { name: 'ReviewDetail' } )

const route = useRoute()
const router = useRouter()
const { userInfo } = useUserStroe()
const { delVisitedView } = useTagsViewStore()
const { fixBottomHeight } = useConfigStore()
const { loading, setLoading } = useLoading(true)

const taskId: string = route.query.task_id as string || route.query.id as string

const loadAuditDiglog = ref(false)
const auditDialogRef = ref()
const auditRef = ref()
const auditId = ref('')
const detailData = ref<taskDetail>({
  status: 1,
  status_name: '',
  entity_name: '',
  task_id: '',
  name: '',
  type_name: '',
  channel_name: '',
  user_name: '',
  add_time: '',
  content: '',
  logs: [],
})

const hasCheckRight = computed(() => auditId.value.indexOf(userInfo.wsid as string) > -1)

const showAudit = computed(() => (detailData.value.status === 2) && (detailData.value.is_super === 1 || hasCheckRight.value))

const getDetail = () => {
  useTryCatch( async () => {
    setLoading(true)
    const res = await getTaskDetail(taskId)
    if (res.code === 200) {
      detailData.value = Object.assign(detailData.value, res.data)
      const { current_node } = detailData.value as taskDetail
      if (current_node) {
        detailData.value.logs?.push(current_node)
        auditId.value = current_node.user_id
      }
    } else {
      handleBack()
      ElMessage.error(res.msg)
    }
    setLoading(false)
  }, () => {
    handleBack()
    setLoading(false)
  } )
}
const handleAudit = () => {
  auditRef.value?.handleSubmit()
  // if (!loadAuditDiglog.value) {
  //   loadAuditDiglog.value = true
  //   return
  // }
  // auditDialogRef.value.handleShow()
}
const handleAuditSuccess = () => {
  getDetail()
  ElMessage.success('提交审核成功!')
}
const handleBack = () => {
  delVisitedView(route)
  router.go(-1)
}
const handleRevoke = () => {
  ElMessageBox.confirm('确认撤销当前流程吗?', '提示', {
    type: 'warning',
    callback: (action: string) => {
      if (action === 'confirm') {
        useTryCatch( async () => {
          setLoading(true)

          const task_id = detailData.value.task_id as string
          const res = await taskRevoke( { task_ids: task_id } )
          if (res.code === 200) {
            ElMessage.success('撤销成功!')
            getDetail()
          } else {
            ElMessage.error(res.msg)
          }
          setLoading(false)
        }, () => setLoading(false) )
      }
    }
  })
}

getDetail()
</script>