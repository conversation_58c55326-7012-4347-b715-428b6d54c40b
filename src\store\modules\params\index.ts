import { defineStore } from 'pinia'
import { getAccessibleSitesWcw } from '@/api/authority'
import { getChannelList, getSysUserList, getTemplates } from '@/api/params'
import { getSiteDetail } from '@/api/common-management'
import type { RouteRecordName } from 'vue-router'
import type { siteItem, user, channelItem, manage_template, siteDetail } from './types'

import { moduleList, moduleTypeMap, fieldTypes, inputTypes, pageStateList, artPageStateList, page_state_map, blockTypeList } from './templateType'

// 存放全局公共参数，及表筛选条件
const useParamsStore = defineStore('params', {
  state: () => {
    return {
      site_id_all: +(localStorage.getItem('site_id') || 0),
      siteDetailInfo: {} as siteDetail,
      channel_item_all: {} as channelItem,
      webList: [] as siteItem[],
      channelList: [] as channelItem[],
      userList: [] as user[],
      manage_templateList: [] as manage_template[],
      siteChannelListeners: new Map<RouteRecordName, boolean>(),
      moduleList,
      moduleTypeMap,
      fieldTypes,
      inputTypes,
      pageStateList,
      artPageStateList,
      page_state_map,
      blockTypeList,
    }
  },
  getters: {
    site_name_all: (state): string => {
      if (state.site_id_all) {
        for ( const { cms_site_name, cms_site_id } of state.webList) {
          if (state.site_id_all === cms_site_id) {
            return cms_site_name
          }
        }
      }
      return ''
    }
  },
  actions: {
    setSiteIDAll(val: number) {
      this.site_id_all = val
    },
    setLocalChannelItemAll() {
      window.localStorage.setItem('channel_item_all', JSON.stringify(this.channel_item_all))
    },
    setChannelItemAll(val: channelItem) {
      this.channel_item_all = val
    },
    async getWebList() {
      try {
        const res = await getAccessibleSitesWcw()
        const { code, data } = res
        if (code === 0) {
          const webList = [] as siteItem[]
          data.forEach((item) => {
            webList.push({
              id: item.id,
              name: item.name,
              cms_site_id: +item.settings.cms_site_id,
              cms_site_name: item.settings.cms_site_name,
            })
          })
          this.webList = webList
          const site_id = window.localStorage.getItem('site_id')

          this.setSiteIDAll( site_id ? +site_id : webList[0].cms_site_id)
          !site_id && window.localStorage.setItem('site_id', `${webList[0].cms_site_id}`)
          
          this.getChannelList()

          const channelItemStr = window.localStorage.getItem('channel_item_all')
          if (channelItemStr) {
            const channelItemAll = JSON.parse(channelItemStr) as channelItem
            this.setChannelItemAll(channelItemAll)
          }
          this.getSiteDetailInfo()
        }
      } catch (error) {
        
      }
    },
    async getChannelList(item?: channelItem) {
      this.channel_item_all = item || {}
      try {
        const res = await getChannelList(this.site_id_all)
        const { code, data } = res
        if (code === 200) {
          this.channelList = data
        }
      } catch (error) {

      }
    },
    async getUserList() {
      try {
        const res = await getSysUserList()
        if (res.code === 200) {
          this.userList = res.data
        }
      } catch (error: any) {
        throw new Error(error)
      }
    },
    async getTemplates() {
      try {
        const res = await getTemplates()
        if (res.code === 200) {
          this.manage_templateList = res.data
        }
      } catch (error: any) {
        throw new Error(error)
      }
    },
    async getSiteDetailInfo() {
      try {
        const res = await getSiteDetail(this.site_id_all)
        if (res.code === 200) {
          this.siteDetailInfo = res.data as siteDetail
        }
      } catch (error: any) {
        throw new Error(error)
      }
    }
  }
})

export default useParamsStore