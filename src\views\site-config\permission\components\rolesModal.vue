<template>
  <el-dialog
    v-model="show"
    :title=" props.row ? `${props.row?.real_name}的角色权限详情` : '角色权限菜单配置'"
    width="1000"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    append-to-body
    align-center
    @closed="emit('close')"
  >
    <template #default>
      <el-row :gutter="30">
        <el-col :span="24">
          <el-card shadow="never" style="height: 100%;">
            <template #header>
              <div class="rowBC">
                <strong style="font-size: 1.25rem;">
                  <span v-if="props.row">{{ props.row?.real_name }}的</span>系统角色
                </strong>
                <el-button v-if="!props.row" type="primary" :icon="Plus" @click="formShow = true"> 新增角色 </el-button>
              </div>
            </template>
            <template #default>
              <div style="display: flex; flex-wrap: nowrap;">
                <el-table 
                  :data="tableData" 
                  border 
                  highlight-current-row 
                  style="width: 500px;" max-height="600"
                >
                  <el-table-column label="角色名称" prop="val" />
                  <el-table-column label="操作" width="210">
                    <template #default="scope">
                      <el-button type="primary" plain @click="getRolePermissionTree(scope.row.key, scope.row.val)"> 角色菜单 </el-button>
                      <el-button v-if="!props.row" type="danger" plain @click="handleDelete(scope.row.key, scope.row.val)" :disabled="roleId === scope.row.key"> 删除 </el-button>
                    </template>
                  </el-table-column>
                </el-table>
                <div v-show="roleId" v-loading="loading" style="min-height: 300px; flex-grow: 1; margin-left: 30px;">
                  <strong style="color: var(--el-color-primary)">{{ roleName }}</strong>角色菜单

                  <el-scrollbar max-height="600">
                    <el-tree
                      ref="treeRef"
                      :data="menuList"
                      node-key="id"
                      empty-text="请选择角色菜单"
                      default-expand-all
                      show-checkbox
                      :props="{ children: 'children', label: 'name'}"
                      :default-checked-keys="checkedKeys"
                    />

                    <div v-if="!props.row" class="text-center" style="padding-top: 15px;">
                      <el-button type="primary" :disabled="!roleId" @click="handleSave"> 保存菜单 </el-button>
                    </div>
                  </el-scrollbar>
                </div>
                <div v-show="!roleId" style="flex-grow: 1; font-size: 24px; display: flex; justify-content: center; align-items: center;"> 
                  请选择角色菜单 
                </div>
              </div>
            </template>
          </el-card>
        </el-col>
      </el-row>

      <el-dialog
        v-model="formShow"
        title="新增角色"
        append-to-body
        align-center
        width="500px"
      >
        <template #default>
          <el-form ref="formRef" :model="submitData" label-width="100" label-suffix=":">
            <el-form-item label="角色名称" prop="cms_name" :rules="{ required: true, message: '角色名称不能为空', trigger: 'blur' }">
              <el-input v-model="submitData.cms_name" placeholder="角色名称" />
            </el-form-item>
            <el-form-item label="角色描述" prop="cms_desc">
              <el-input v-model="submitData.cms_desc" placeholder="角色描述" />
            </el-form-item>
            <el-form-item label="角色兴云ID" prop="role_id">
              <el-input v-model="submitData.role_id" placeholder="角色兴云ID" />
            </el-form-item>
          </el-form>
          <div class="text-center">
            <el-button type="primary" :loading="loading" @click="handleAddRole"> 保存 </el-button>
          </div>
        </template>
      </el-dialog>
    </template>
    <template #footer>
      <div class="text-center">
        <el-button @click="show = false">关闭</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, reactive } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Plus } from '@element-plus/icons-vue'
import { storeToRefs } from 'pinia'
import { useBasicStore } from '@/store/basic'
import useTryCatch from '@/hooks/use-try-catch'
import useLoading from '@/hooks/use-loading'
import { saveRole, deleteRole, rolePermission, editRolePermission } from '@/api/site-config'
import type { permissionListItem } from '@/api/site-config'

const { menuList, roleList } = storeToRefs(useBasicStore())
const { getMenuList, getRoleList } = useBasicStore()

const show = ref(true)
const formShow = ref(false)

const props = defineProps<{
  row: permissionListItem|null
}>()

const emit = defineEmits(['close'])

const { loading, setLoading } = useLoading()

const formRef = ref()
const submitData = reactive({
  cms_name: '',
  cms_desc: '',
  role_id: ''
})

const tableData = ref<{key: number; val: string}[]>([])
const roleId = ref(0)
const roleName = ref('')
const treeRef = ref()
const checkedKeys = ref<number[]>([])

const handleAddRole = () => {
  if (!formRef.value) {
    return
  }

  formRef.value.validate( ( valid: boolean ) => {
    if (valid) {
      useTryCatch( async () => {
        setLoading(true)
        const res = await saveRole(submitData)
        if (res.code === 200) {
          formRef.value.resetFields()
          ElMessage.success('保存成功')
          formShow.value = false
          refreshRoleList()
        } else {
          ElMessage.error(res.msg)
        }
        setLoading(false)
      }, () => setLoading(false) )
    }
  } )
}

const getRolePermissionTree = (role_id: number, role_name: string) => {
  useTryCatch( async () => {
    setLoading(true)
    const res = await rolePermission(role_id)
    if (res.code === 200) {
      checkedKeys.value = res.data
      roleId.value = role_id
      roleName.value = role_name
      treeRef.value!.setCheckedKeys(checkedKeys.value)
    } else {
      ElMessage.error(res.msg)
    }
    setLoading(false)
  }, () => setLoading(false))
}

const handleDelete = (role_id: number, role_name: string) => {
  ElMessageBox.confirm(
    `请确认是否删除角色: ${role_name}`,
    '提示',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
      callback: (action: string) => {
        if (action === 'confirm') {
          useTryCatch( async () => {
            const res = await deleteRole(role_id)
            if (res.code === 200) {
              ElMessage.success('删除成功')
              refreshRoleList()
            } else {
              ElMessage.error(res.msg)
            }
          } )
        }
      }
    }
  )
}

const getCheckedKeys = (): number[] => {
  return treeRef.value!.getCheckedKeys()
}

const handleSave = () => {
  const ids = getCheckedKeys().join(',')
  useTryCatch( async () => {
    setLoading(true)
    const res = await editRolePermission({ role_id: roleId.value, route_id: ids })
    if (res.code === 200) {
      ElMessage.success('保存成功')
    } else {
      ElMessage.error(res.msg)
    }
    setLoading(false)
  }, () => setLoading(false) )
}

const refreshRoleList = async () => {
  await getRoleList()
  tableData.value = roleList.value
}

if (menuList.value.length === 0) {
  getMenuList()
}

( async () => {
  if (roleList.value.length === 0) {
    await refreshRoleList()
  } else {
    tableData.value = roleList.value
  }
  if(props.row) {
    const role_ids = props.row.role_ids as string
    const ids_set = new Set( role_ids.split(',') )
    tableData.value = roleList.value.filter(item => ids_set.has(`${item.key}`))
  }
})()

</script>