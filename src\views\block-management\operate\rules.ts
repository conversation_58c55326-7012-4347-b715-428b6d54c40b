import { reactive } from 'vue'
import type { FormRules } from 'element-plus'

export const formRules = reactive<FormRules>({
  blk_name: [
    { required: true, message: `块名称不能为空`, trigger: 'blur' },
  ],
  blk_type: [
    { required: true, message: `块类型不能为空`, trigger: 'change' },
  ],
  file_path: [
    { required: true, validator: (rule, value, callback) => {
      const patt1 = /^[/[a-zA-Z\d\/_-]+?\.html$/
      if (value === '') {
          callback(new Error(`文件路径不能为空`))
      } else if (value.match(patt1) === null) {
          callback(new Error('以.html结尾, 只允许包含英文、数字、/、_和-'))
      } else {
          callback()
      }
    }, trigger: 'blur' }
  ],
  content: [
    { required: true, message: `块内容不能为空`, trigger: 'blur' },
  ],
  level: [
    { required: true, message: `块等级不能为空`, trigger: 'blur' },
  ]
})